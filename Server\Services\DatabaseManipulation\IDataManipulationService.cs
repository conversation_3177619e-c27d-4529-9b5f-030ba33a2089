﻿using ProgramadorGeneralBLZ.Server.Data.DatoLita01;
using ProgramadorGeneralBLZ.Server.Models.DatoLita01;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;
using Matped = ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa.Matped;

namespace ProgramadorGeneralBLZ.Server.Services.DatabaseManipulation
{
    public interface IDataManipulationService
    {
        string GetNombreCliente(int idCliente);
        string GetTextoEstadoCodigosAplicacion(int idPedido, bool soloProcesos = false, bool descripcionCliente = false, 
            List<Maquinas> maquinas = null, List<TablaProgramacion> tp=null);
        string GetEstadoLitografia(int idPedido);
        int CalcularPesoTotal(int numHojas, double pesoHoja);
        double CalcularPesoPorHoja(int idPedido, int ancho, int largo, int espesor);
        string GetIdentificadorEstadoPedido(int hojasProcesadas, int hojasTotales);
        int? GetPedidoReproceso(int idPedido);
        string GetCaracteristicasHojalata(int idPedido, PedidoProcesado pp=null, Matped mp=null);
        MultiResponseTypes GetDatosPedido(int idPedido, string tipoDato);
        string GetDatosPlano(int idCliente, string plano, string tipoDato);
        string GetObservacionesAlmacen(int idPedido, bool esPrimerProceso, bool esUltimoProceso, bool? yaProgramado);
        string GetDestinoCrown(string cadena);
        bool GetDatosMuestraSH(int largo, int ancho, string tipoDato);
        string DevuelveDatosAplicacion(int idAplicacion, string tipoDato);
        int GetNumeroTintas(int idPedido, int numTintaMate, bool contarTintasMate = false, bool restarTintaMate = false);
        string GetEscuadra(int idPedido);
        MultiResponseTypes GetDatosTipoHojalata(int idHojalata, string tipoDato, int idPedido = 0);
        Task<string> GetObservacionesCalidadLite(int idPedido, int? idAplicacion);
        Task<double?> GetVelocidadMaxima(int? idProducto, int idLinea, CancellationToken ct);
        Task<int?> GetTemperaturaSecado_LITO(int? idProducto, int idLinea, CancellationToken ct);
        string GetTratamientosByPedido(int idPedido);
        Task<double> GetTemperaturaSecado(int? idProducto, int? idPedido, MaquinaDTO maquina, CancellationToken cancellationToken);
        Task<VelocidadesMaquina> GetTemperaturaSecadoV2(int? idProducto, int? idPedido, int idMaquina,
            CancellationToken cancellationToken);
        Task<double> GetTemperaturaSecadoBarnizado(int? idProducto, int idLinea, CancellationToken cancellationToken);
        Task<DatosFasesDTO> DevuelveOrdenProcesos(int idPedido, int codigoAplicacion, int totalProcesos, string lugar = null);
        string DevuelvePosicionRayas(string cara, string observaciones);
        Task<double> DevuelveDesarrollo(int idCliente, float formato, string tipoElemento, EEorden eeorden, string tipo = "");
        Task<string> DevuelveFormaBarnizado(int idCliente, string plano, string tipoElemento, float formato,
            float anchoHjlta, float largoHjlta, int idPedido);

        Task<double> DevuelveAnchuraDesarrollos(int idCliente, string plano, string tipoElemento, float formato, EEorden eEorden);
        double DevuelveHojasMax(int idPedido);
        string BuscarColorEspecial(int numColor);
        string DColor(int numColor);
        int DevuelveNumeroColores(int idPedido);
        double DevuelveHojasMin(int idPedido);
        string DevuelveObservacionesFlejado(int idPedido);
        string ObtenerMarcadoTamponExternalInternal(int idPedido, int idLinea, bool esPrimerProceso, int ordenProceso);
        string AñadeObservacionesCodigosYBarnices(string codigoAplicacion, string idProducto, string idCliente);
        string DevuelveString(string textoCompleto, string entre, string y);
        float DevuelveBarnizNecesarioConCalculoDePeso(int idPedido, int idProducto, float gramaje, int hojasYaTiradas);
        int[] VolcaTintas2Vector(int idPedido);
        Task RenumerarProgramacionAsync(long desde, int idMaquina);
        Task GenerarDatosImpresionBarnizado(ImprimirPedidosDTO datosImpresion, bool esProgramacionPorPantalla, string nombreReporte, CancellationToken cancellationToken);
        Task GenerarDatosImpresionLitografia(ImprimirPedidosDTO datosImpresion, bool esProgramacionPorPantalla, string nombreReporte, CancellationToken cancellationToken);
        string GetPropertyValueByName(object obj, string propName);
        Task<SingleResult<Enums.TipoLimpias>> ConsultaLavadas(int productoPrevio, int productoActual, CancellationToken ct);
        //Task<string> GetTextoLavadaImpresion(int? itemIdlinea, int? itemIdproducto, CancellationToken cancellationToken);
    }
}
