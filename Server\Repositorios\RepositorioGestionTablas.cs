﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Nelibur.ObjectMapper;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;

namespace ProgramadorGeneralBLZ.Server.Repositorios;

public class RepositorioGestionTablas : IRepositorioGestionTablas
{
    private readonly ProgramadorLitalsaContext _context;

    public RepositorioGestionTablas(ProgramadorLitalsaContext context)
    {
        _context = context;
    }

    public async Task<ActionResult<List<TDto>>> GetAllEntities<TEntity, TDto>() where TEntity : class
    {
        var entities = await _context.Set<TEntity>().ToListAsync();
        var result = TinyMapper.Map<List<TDto>>(entities);
        return result;
    }


    public async Task<ActionResult> AddEntity<TEntity, TDto>(TDto dto, string getIdMethodName, string idProperty) where TEntity : class
    {
        var entity = TinyMapper.Map<TEntity>(dto);
        _context.Set<TEntity>().Add(entity);
        await _context.SaveChangesAsync();

        //var id = dto.GetType().GetProperty(idProperty).GetValue(dto);
        return new OkResult();
    }

    public async Task<ActionResult> UpdateEntity<TEntity, TDto>(int id, TDto dto, string idProperty) where TEntity : class
    {
        var dtoId = (int)dto.GetType().GetProperty(idProperty).GetValue(dto);

        if (id != dtoId)
        {
            return new BadRequestResult();
        }

        var entity = await _context.Set<TEntity>().FindAsync(id);
        _context.Entry(entity).CurrentValues.SetValues(dto);

        try
        {
            await _context.SaveChangesAsync();
        }
        catch (DbUpdateConcurrencyException)
        {
            if (!_context.Set<TEntity>().Any(e => (int)e.GetType().GetProperty(idProperty).GetValue(e) == id))
            {
                return new NotFoundResult();
            }
            else
            {
                throw;
            }
        }

        return new NoContentResult();
    }

    public async Task<ActionResult> DeleteEntity<TEntity>(int id, string idProperty) where TEntity : class
    {
        var entity = await _context.Set<TEntity>().FindAsync(id);
        if (entity == null)
        {
            return new NotFoundResult();
        }

        _context.Set<TEntity>().Remove(entity);
        await _context.SaveChangesAsync();

        return new NoContentResult();
    }
}