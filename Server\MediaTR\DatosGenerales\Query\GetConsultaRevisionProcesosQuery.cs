﻿using System.Text;
using MediatR;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.DatosGenerales.Query;

public class GetConsultaRevisionProcesosQuery : IRequest<SingleResult<string>>  
{
}

internal class GetConsultaRevisionProcesosQueryHandler : IRequestHandler<GetConsultaRevisionProcesosQuery, SingleResult<string>>
{
    
    private readonly ProgramadorLitalsaContext _contextProg;
    public GetConsultaRevisionProcesosQueryHandler(ProgramadorLitalsaContext contextProg)
    {
        _contextProg = contextProg;
    }

    public async Task<SingleResult<string>> Handle(GetConsultaRevisionProcesosQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var result = new SingleResult<string>
            {
                Data = "",
                Errors = new List<string>()
            };
            var data = _contextProg.DboPedproceso
                .Where(p => p.Pedido > 2300000 &&
                            string.IsNullOrEmpty(p.Posicion) &&
                            p.Pedido.ToString().Substring(2, 1) != "3" &&
                            p.Pedido.ToString().Substring(2, 1) != "6")
                .Select(p => new
                {
                    Expr1 = p.Pedido,
                    Expr2 = p.Proceso,
                    Expr3 = p.Lugar,
                    Expr4 = p.Barniz,
                    Expr5 = p.Posicion
                })
                .OrderBy(i => i.Expr1)
                .ToList();

            if (!data.Any())
                return result;


            var stringBuilder = new StringBuilder();
            stringBuilder.AppendLine("<style>");
            stringBuilder.AppendLine("table { border-collapse: collapse; }");
            stringBuilder.AppendLine("td, th { border: 1px solid white; padding-left: 8px; padding-right: 8px; }");
            stringBuilder.AppendLine("th { font-weight: bold; }");
            stringBuilder.AppendLine("</style>");
            stringBuilder.AppendLine("<h1>PEDPROCESO SIN POSICIONES</h1></br>");
            stringBuilder.AppendLine("<table border=\"1\">");
            stringBuilder.AppendLine("<thead>");
            stringBuilder.AppendLine("<tr>");
            stringBuilder.AppendLine("<th>Pedido    </th>");
            stringBuilder.AppendLine("<th>Proceso   </th>");
            stringBuilder.AppendLine("<th>Lugar     </th>");
            stringBuilder.AppendLine("<th>Barniz    </th>");
            stringBuilder.AppendLine("<th>Posición  </th>");
            stringBuilder.AppendLine("</tr>");
            stringBuilder.AppendLine("</thead>");
            stringBuilder.AppendLine("<tbody>");

            foreach (var item in data)
            {
                stringBuilder.AppendLine("<tr>");
                stringBuilder.AppendLine($"<td>{item.Expr1}</td>");
                stringBuilder.AppendLine($"<td>{item.Expr2}</td>");
                stringBuilder.AppendLine($"<td>{item.Expr3}</td>");
                stringBuilder.AppendLine($"<td>{item.Expr4}</td>");
                stringBuilder.AppendLine($"<td>{item.Expr5}</td>");
                stringBuilder.AppendLine("</tr>");
            }

            stringBuilder.AppendLine("</tbody>");
            stringBuilder.AppendLine("</table>");

            result.Data = stringBuilder.ToString();
            return result;
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: GetConsultaRevisionProcesosQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            throw new Exception(errorText, e);
        }
    }
}