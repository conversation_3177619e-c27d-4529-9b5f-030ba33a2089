using System.Text.Json.Serialization;
using ProgramadorGeneralBLZ.Shared.DTO;

namespace ProgramadorGeneralBLZ.Shared.ResponseModels
{
    [Serializable]
    public class PrimitiveResultHub
    {
        [JsonPropertyName("errors")]
        public IList<ProgramacionesPantallaLogDTO> Errors { get; set; }

        [JsonPropertyName("info")]
        public IList<ProgramacionesPantallaLogDTO> Info { get; set; }

        public PrimitiveResultHub()
        {
            Errors = new List<ProgramacionesPantallaLogDTO>();
            Info = new List<ProgramacionesPantallaLogDTO>();
        }

        [JsonPropertyName("hasErrors")]
        public bool HasErrors => Errors.Any();

        [JsonPropertyName("hasInfo")]
        public bool HasInfo => Info.Any();
    }
}
