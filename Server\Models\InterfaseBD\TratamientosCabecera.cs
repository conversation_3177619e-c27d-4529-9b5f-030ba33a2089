﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace ProgramadorGeneralBLZ.Server.Models.InterfaseBD;

public partial class TratamientosCabecera
{
    public string IdTratamiento { get; set; }

    public string Descripcion { get; set; }

    public string IdTratamientoPrecedente { get; set; }

    public string TipoTratamiento { get; set; }

    public bool Activo { get; set; }

    public DateTime FechaCreacion { get; set; }

    public TimeSpan HoraCreacion { get; set; }

    public string UsuarioCreacion { get; set; }

    public DateTime? FechaMod { get; set; }

    public TimeSpan? HoraMod { get; set; }

    public string UsuarioMod { get; set; }

    public DateTime? FechaObsoleto { get; set; }

    public string UsuarioObsoleto { get; set; }

    public virtual ICollection<Motivos> Motivos { get; set; } = new List<Motivos>();

    public virtual TipoTratamiento TipoTratamientoNavigation { get; set; }

    public virtual ICollection<TratamientosDetalle> TratamientosDetalle { get; set; } = new List<TratamientosDetalle>();
}