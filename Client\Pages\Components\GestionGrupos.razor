﻿@using ProgramadorGeneralBLZ.Shared
@using ProgramadorGeneralBLZ.Shared.DTO
@inject HttpClient Http
@inject SpinnerService SpinnerService
@inject IToastService ToastService

<div class="h-100 overflow-auto px-2 py-1">
    <DxGrid Data="@grupos" SizeMode="SizeMode.Medium"
            CssClass="ch-320 smallFont progGrid" PageSize="50"
            KeyFieldName="Id" Context="GridGrupos"
            AllowSort="true" EditNewRowPosition="GridEditNewRowPosition.Top"
            ShowFilterRow="true" ValidationEnabled="false"
            EditMode="GridEditMode.EditRow" @ref="Grid"
            EditorRenderMode="GridEditorRenderMode.Integrated"
            EditModelSaving="GridGrupos_EditModelSaving"
            DataItemDeleting="Grid_DataItemDeleting">
        <Columns>
            <DxGridCommandColumn Width="80px">
                <HeaderTemplate>
                    <a class="oi oi-plus" @onclick="@(() => Grid.StartEditNewRowAsync())" style="text-decoration: none;color: lightskyblue;" href="javascript:void(0);"></a>
                </HeaderTemplate>
                <CellDisplayTemplate>
                    <a class="oi oi-pencil" @onclick="@(() => Grid.StartEditRowAsync(context.VisibleIndex))" style="text-decoration: none; padding-right: 15px; color: #c75fff;" href="javascript:void(0);"></a>
                    <a class="oi oi-x" @onclick="@(() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex))" style="text-decoration: none; color: red;" href="javascript:void(0);"></a>
                </CellDisplayTemplate>
                <CellEditTemplate>
                    <a class="oi oi-arrow-thick-bottom" @onclick="@(() => Grid.SaveChangesAsync())" style="text-decoration: none; padding-right: 15px; color: greenyellow; margin-right: 6px; margin-top: 3px;" href="javascript:void(0);"></a>
                    <a class="oi oi-x" @onclick="@(() => Grid.CancelEditAsync())" style="text-decoration: none; color: red;" href="javascript:void(0);"></a>
                </CellEditTemplate>
            </DxGridCommandColumn>
            <DxGridDataColumn FieldName="Id" Visible="false" />
            <DxGridDataColumn FieldName="@nameof(GrupoNotificacionesDTO.Descripcion)" Caption="Descripción" Width="250" />
        </Columns>
        <DataColumnCellEditTemplate>
            @{
                var prog = (GrupoNotificacionesDTO)GridGrupos.EditModel;
            }
            @switch (GridGrupos.DataColumn.FieldName)
            {
                case "Descripcion":
                    <DxTextBox @bind-Text="@prog.Descripcion"
                               ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                               ShowValidationIcon="true" />
                    break;
            }
        </DataColumnCellEditTemplate>
    </DxGrid>
</div>
@code
{
    DxGrid? Grid;
    List<GrupoNotificacionesDTO> grupos = new List<GrupoNotificacionesDTO>();

    protected override async Task OnInitializedAsync()
    {
        SpinnerService.Show();
        await LoadData();
        SpinnerService.Hide();
    }

    async Task LoadData()
    {
        grupos = await Http.GetFromJsonAsync<List<GrupoNotificacionesDTO>>("GestionTablasV2/GrupoNotificaciones");
    }

    async Task GridGrupos_EditModelSaving(GridEditModelSavingEventArgs e)
    {
        SpinnerService.Show();
        var dest = (GrupoNotificacionesDTO)e.EditModel;
        var response = e.IsNew == false
            ? await Http.PutAsJsonAsync($"GestionTablasV2/GrupoNotificaciones/{dest.Id}", dest)
            : await Http.PostAsJsonAsync("GestionTablasV2/GrupoNotificaciones", dest);
        if (response.IsSuccessStatusCode)
            await LoadData();
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            ToastService.ShowError(error);
        }
        SpinnerService.Hide();
    }
    async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        SpinnerService.Show();
        var item = (GrupoNotificacionesDTO)e.DataItem;
        var response = await Http.DeleteAsync($"GestionTablasV2/GrupoNotificaciones/{item.Id}");
        if (response.IsSuccessStatusCode)
            await LoadData();
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            ToastService.ShowError(error);
        }
        SpinnerService.Hide();
    }

    async Task Delete(GrupoNotificacionesDTO item)
    {
        SpinnerService.Show();
        var response = await Http.DeleteAsync($"GestionTablasV2/GrupoNotificaciones/{item.Id}");
        if (response.IsSuccessStatusCode)
            await LoadData();
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            ToastService.ShowError(error);
        }
        SpinnerService.Hide();
    }

}
