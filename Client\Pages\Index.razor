﻿@page "/"

@using Microsoft.AspNetCore.Components.WebAssembly.Hosting
@inject IWebAssemblyHostEnvironment hostEnv
@inject IHttpClientFactory HttpClientFactory


<PageTitle>Inicio</PageTitle>

<h1>Programador de Litalsa</h1>

Aplicación para la programación de las máquinas de litografía, barnizado, corte y selección.

@if (hostEnv.IsDevelopment())
{
    <h1>BlazorWasmApp - In Debug</h1>
}
else
{
    <h1>BlazorWasmApp - Not Debug</h1>
}

<h2>Tu conexión es a @currentDatabase</h2>
@code {
    private string currentDatabase;
    protected override async Task OnInitializedAsync()
    {
        var client = HttpClientFactory.CreateClient("DatabaseAPI");
        var esDesarrollo = Convert.ToBoolean(await client.GetStringAsync("ConsultaDB/current-database"));
        currentDatabase = esDesarrollo ? "DESARROLLO" : "PRODUCCION - CUIDADO!!!!!";
    }
}