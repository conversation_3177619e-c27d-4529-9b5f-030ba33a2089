﻿@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.WebAssembly.Authentication

@inject NavigationManager Navigation
@*@inject SignOutSessionStateManager SignOutManager*@

<nav class="navbar header-navbar p-0">
    <button class="navbar-toggler bg-primary d-block" @onclick="OnToggleClick">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="w-100">
        <div class="p-0">
@*            @if (Navigation.Uri.Contains("Programador"))
            {
            }
            <AuthorizeView>
                <Authorized></Authorized>
                <NotAuthorized></NotAuthorized>
            </AuthorizeView>*@
            <DxMenu Title="Programador"
                    Orientation="Orientation.Horizontal"
                    ItemsPosition="ItemPosition.Center"
                    ItemsStretched="false"
                    DropDownActionMode="MenuDropDownActionMode.Click"
                    CollapseItemToIconMode="MenuCollapseItemToIconMode.Sequentially"
                    CollapseItemsToHamburgerMenu="false">
                <Items>
                    <AuthorizeView>
                        <NotAuthorized Context="contextNotAuth">
                            <DxMenuItem CssClass="search-menu-item" BeginGroup="true" Position="ItemPosition.Start">
                                <Template>
                                    <DxButton RenderStyle="ButtonRenderStyle.Info"
                                              Text="Acceder" SizeMode="SizeMode.Large"
                                              IconCssClass="oi oi-account-login iconInButton"
                                              CssClass="mx-3"
                                              Click="@NavigateToLogin" />
                                </Template>
                            </DxMenuItem>
                        </NotAuthorized>

                        <Authorized Context="contextAuth">

                            <DxMenuItem CssClass="notoggle" BeginGroup="true" Position="ItemPosition.End">
                                <TextTemplate>
                                    <div class="menu-icon-user-profile menu-icon" @onclick="BeginSignOut"/>
                                </TextTemplate>
                            </DxMenuItem>
                        </Authorized>


                    </AuthorizeView>

                </Items>
            </DxMenu>

        </div>
    </div>
</nav>

@code {
    [Parameter] public bool ToggleOn { get; set; }
    [Parameter] public EventCallback<bool> ToggleOnChanged { get; set; }


    async Task OnToggleClick() => await Toggle();

    async Task Toggle(bool? value = null)
    {
        var newValue = value ?? !ToggleOn;
        if (ToggleOn != newValue)
        {
            ToggleOn = newValue;
            await ToggleOnChanged.InvokeAsync(ToggleOn);
        }
    }

    private async Task BeginSignOut(MouseEventArgs args)
    {
        //await SignOutManager.SetSignOutState();
        //Navigation.NavigateTo("authentication/logout");
        Navigation.NavigateToLogout("authentication/logout");
    }

    private void NavigateToLogin()
    {
        Navigation.NavigateTo("authentication/login");
    }
}