USE [ProgramadorLitalsa]
GO

/****** Object:  StoredProcedure [dbo].[sp_GenerarDatosImpresionProgramacionLitografia]    Script Date: 23/07/2025 19:11:52 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

ALTER PROCEDURE [dbo].[sp_GenerarDatosImpresionProgramacionLitografia]
    @idLinea INT,
    @posicionDesde INT,
    @posicionHasta INT,
	@esProgramacionPorPantalla BIT,
	@nombreReporte nvarchar(50)
AS
BEGIN
    SELECT 
		tp.Posicion as Id,
		tp.Idprogramacion as IdProgramacion,
		tp.Idpedido AS Idpedido, 
		tp.Idaplicacion AS Idaplicacion, 
		tp.Idlinea AS Idlinea, 
		tp.Posicion AS Posicion, 
		tp.Orden AS Orden, 
		tp.Producto AS Producto, 
		pp.IdCliente AS IdCliente, 
		c.NombreCliente AS NombreCliente, 
		pp.HojasPedido AS HojasPedido, 
		pp.TipoElemento AS TipoElemento, 
		pp.Formato AS Formato, 
		pp.Plano AS Plano, 
		(CAST(pp.Ancho_hjlta AS decimal)) AS AnchoHjlta,
		(CAST(pp.Largo_hjlta AS decimal)) AS LargoHjlta,
		(CAST(pp.Espesor_hjlta AS decimal)) AS EspesorHjlta,
		pp.Motivos AS Motivos,
		pp.Tipo_hjlta AS TipoHjlta, 
		'' AS ApliProducto, 
		tp.Flejar AS Flejar, 
		'' AS T01ext, 
		'' AS T02ext, 
		'' AS T03ext, 
		'' AS T04ext, 
		'' AS T05ext, 
		'' AS T06ext, 
		'' AS T07ext, 
		'' AS T08ext, 
		'' AS T09ext, 
		'' AS T10ext, 
		'' AS T11ext, 
		'' AS T01int, 
		'' AS T02int, 
		'' AS T03int, 
		'' AS T04int,
		'' AS Ft01ext,
		'' AS Ft02ext,
		'' AS Ft03ext,
		'' AS Ft04ext,
		'' AS Ft05ext,
		'' AS Ft06ext,
		'' AS Ft07ext,
		'' AS Ft08ext,
		'' AS Ft09ext,
		'' AS Ft10ext,
		'' AS Ft11ext,
		CAST(0 as bit) AS Maculas,
		pp.Motivos AS Motivo,
		(CAST(0 AS decimal)) AS Sup, 
		tp.HoraComienzoEstimada AS HoraComienzoEstimada, 
		tp.HoraFinEstimada AS HoraFinEstimada, 
		tp.TiposCambio AS TiposCambio, 
		pp.RequeridoEnFecha AS RequeridoEnFecha, 
		'' AS Escuadra, 
		tp.Idproducto AS Idproducto, 
		CAST(tp.Peso AS decimal) AS Peso,
		CAST(tp.BarnizNecesario AS decimal(18,2)) AS BarnizNecesario,
		--tp.BarnizNecesario AS BarnizNecesario,
		tp.ObsCalidad AS ObsCalidad, 
		tp.HojasAProcesar AS HojasAProcesar, 
		'' AS CaractHjlta, 
		tp.TipoLavada AS TipoLavada,
		pp.URGENTE AS Urgente,
		tp.Obspaseposterior AS Obspaseposterior, 
		tp.Observaciones AS Observaciones, 
		tp.ObsAlmacen AS ObsAlmacen, 
		'' AS TextoEstadoCodApli,
		'' AS CogerDe,
		0 AS MinHojas,
		tp.AplicacionSimultanea AS ApliSimul,
		'' AS FormaFlejado,
		'' AS CalleAPQ, 
		'' AS MuestraSH,
		'' AS PosEscuadra,
		CAST(0 as bit) AS Tuberia,
		hp.Rodillo AS Rodillo,
		hp.NotaJefeTurno AS NotaJefeTurno,
		'' AS TipoPedido,
		@esProgramacionPorPantalla AS EsTipoProgramacionPorPantalla,
		@nombreReporte AS NombreReporte
	FROM 
		TablaProgramacion tp 
		INNER JOIN PedidoProcesado pp ON tp.Idpedido = pp.IdPedido 
		INNER JOIN Clientes c ON pp.IdCliente = c.CodigoCliente
		INNER JOIN Maquinas m on tp.Idlinea=m.Idmaquina
		--LEFT JOIN HistorialProgramacionesPantalla hp ON 
		--	hp.IdPedido = tp.Idpedido
		--	AND hp.IdAplicacion = tp.Idaplicacion
		--	AND hp.IdLinea = tp.Idlinea
		--	AND hp.IdPosicion = tp.Posicion
		LEFT JOIN ProgramacionesPantalla hp ON 
			hp.Idprogramacion = tp.Idprogramacion
	WHERE 
		tp.idlinea = @Idlinea AND 
		tp.posicion >= @posicionDesde AND 
		tp.posicion <= @posicionHasta AND
		((tp.ImpresoraComoBarnizadora = 0 OR ISNULL(tp.ImpresoraComoBarnizadora,0)=0) AND m.TipoMaquina='Impresora') AND
		-- Condición adicional: si es programación por pantalla, incluir solo los que NO estén terminados (IdEstado != 4)
		(@esProgramacionPorPantalla = 0 OR (@esProgramacionPorPantalla = 1 AND (hp.IdEstado IS NULL OR hp.IdEstado != 4)))
	ORDER BY 
		tp.Idlinea, 
		tp.Posicion;

END;
GO
