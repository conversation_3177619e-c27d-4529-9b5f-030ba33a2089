﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Nelibur.ObjectMapper;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Pedidos.Command
{
    public class ActualizarLineaTintasCommand : IRequest<SingleResult<int>>
    {
        public ActualizarLineaTintasCommand(int idPedido, int idMaquina)
        {
            IdPedido = idPedido;
            IdMaquina = idMaquina;
        }

        public int IdPedido { get; set; }
        public int IdMaquina { get; set; }
    }
    public class ActualizarLineaTintasCommandHandler : IRequestHandler<ActualizarLineaTintasCommand, SingleResult<int>>
    {
        private readonly ProgramadorLitalsaContext _contextProg;
        public ActualizarLineaTintasCommandHandler(ProgramadorLitalsaContext contextProg)
        {
            _contextProg = contextProg;
        }


        public async Task<SingleResult<int>> Handle(ActualizarLineaTintasCommand request, CancellationToken cancellationToken)
        {
            var result = new SingleResult<int> { Errors = new List<string>(), Data = 0 };
            try
            {
                var pedidosParaActualizar = _contextProg.TablaCodigosPedido
                    .Where(p => p.Idpedido == request.IdPedido && p.Idcodigoaplicacion < 1000).ToList();
                if (!pedidosParaActualizar.Any())
                {
                    result.Errors.Add($"No se han encontrado datos en TablaCodigosPedido para el pedido {request.IdPedido}");
                    return result;
                }

                if (request.IdMaquina == 0)
                {
                    foreach (var pedido in pedidosParaActualizar)
                    {
                        pedido.EstadoTintas = null;
                        pedido.FechaAsignacionImpresora = null;
                        pedido.Sincarpeta = false;
                        pedido.LineaTintas = null;
                    }

                }
                else
                {
                    if (pedidosParaActualizar.Count() > 1)
                    {
                        result.Data = -1;
                        result.Errors.Add("Verificar planchas asignadas, hay varios procesos lito.");
                    }
                    foreach (var pedido in pedidosParaActualizar)
                    {
                        pedido.EstadoTintas = "Sin Aplicar";
                        pedido.FechaAsignacionImpresora = DateTime.Now;
                        pedido.Sincarpeta = false;
                        pedido.LineaTintas = request.IdMaquina;
                    }
                }

                var dbResult = await _contextProg.SaveChangesAsync(cancellationToken);
                result.Data = dbResult;

            }
            catch (Exception e)
            {
                var error = $"ERROR: ActualizarLineaTintasCommand - {e.Message}--{(!string.IsNullOrWhiteSpace(e.InnerException?.Message) ? e.InnerException : string.Empty)}";
                result.Errors.Add(error);
            }
            return result;
        }
    }
}
