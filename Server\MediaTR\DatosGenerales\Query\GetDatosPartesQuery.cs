﻿using System.Globalization;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Nelibur.ObjectMapper;
using ProgramadorGeneralBLZ.Server.Data.DatoLita01;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.DatoLita01;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.DatosGenerales.Query;

public class GetDatosPartesQuery : IRequest<ListResult<DatosPartesGridDTO>>
{
    public GetDatosPartesQuery(int idPedido)
    {
        IdPedido = idPedido;
    }
    public int IdPedido { get; set; }
}

internal class GetDatosPartesQueryHandler : IRequestHandler<GetDatosPartesQuery, ListResult<DatosPartesGridDTO>>
{
    private readonly DatoLita01Context _datoLita01Context;
    private readonly ProgramadorLitalsaContext _programadorLitalsaContext;
    public GetDatosPartesQueryHandler(DatoLita01Context datoLita01Context, ProgramadorLitalsaContext programadorLitalsaContext)
    {
        _datoLita01Context = datoLita01Context;
        _programadorLitalsaContext = programadorLitalsaContext;
    }

    public async Task<ListResult<DatosPartesGridDTO>> Handle(GetDatosPartesQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<DatosPartesGridDTO>()
        {
            Data = new List<DatosPartesGridDTO>(),
            Errors = new List<string>()
        };
        try
        {
            var datosPedido =
                await _programadorLitalsaContext.PedidoProcesado.SingleOrDefaultAsync(o => o.IdPedido == request.IdPedido,
                    cancellationToken);
            if (datosPedido == null)
            {
                result.Errors.Add("Pedido no encontrado.");
                return result;
            }
            var datosPartes = await _programadorLitalsaContext.HojasTrabajoG21.Where(o => o.Orden == request.IdPedido)
                .ToListAsync(cancellationToken);

            if (!datosPartes.Any())
            {
                var partesDelErp = await _datoLita01Context.Parteshorariosstr
                    .Where(o => o.Orden == request.IdPedido).ToListAsync(cancellationToken);
                if (!partesDelErp.Any())
                {
                    result.Errors.Add("Datos de Partes no encontrados (Ni en ERP ni en Programador).");
                    return result;
                }
                datosPartes = TinyMapper.Map<List<HojasTrabajoG21>>(partesDelErp);
            }

            var datosCliente = await _programadorLitalsaContext.Clientes.SingleOrDefaultAsync(o => o.CodigoCliente == datosPedido.IdCliente, cancellationToken);
            if (datosCliente == null)
            {
                result.Errors.Add("Datos de cliente no encontrados.");
                return result;
            }
            foreach (var parteGrid in datosPartes.Select(parte => new DatosPartesGridDTO
                     {
                         Motivos = datosPedido.Motivos,
                         Cantidad = parte.Cantidad,
                         Ccoste = parte.Ccoste,
                         Cliente = datosCliente.NombreCliente,
                         Dato = parte.Dato,
                         FechaInicio = parte.Fechaini.Value,
                         HoraInicio = TimeSpan.Parse(parte.Horaini),
                         HoraFin = TimeSpan.Parse(parte.Horaini)+TimeSpan.FromMinutes(parte.Minutos.Value),
                         Minutos = parte.Minutos.Value,
                         GrupoInfo = parte.Grupoinfo,
                         Maquina = parte.Nombrecen,
                         NombreEmp = parte.Nombreemp,
                         NombreOp = parte.Nombreope,
                         Operacion = parte.Operacion,
                         Orden = parte.Orden.Value,
                         Posicion = parte.Posicion.Value
                     }))
            {
                result.Data.Add(parteGrid);
            }

            return result;
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: GetDatosPartesQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            throw new Exception(errorText, e);
        }
    }
}