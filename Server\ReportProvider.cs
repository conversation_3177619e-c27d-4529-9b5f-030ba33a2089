﻿using System.Web;
using DevExpress.XtraReports.Parameters;
using DevExpress.XtraReports.Services;
using DevExpress.XtraReports.UI;

namespace ProgramadorGeneralBLZ.Server
{
    public class ReportProvider : IReportProvider
    {
        XtraReport IReportProvider.GetReport(string id, ReportProviderContext context)
        {
            string[] parts = id.Split('?');
            string reportName = parts[0];
            string parametersQueryString = parts.Length > 1 ? parts[1] : String.Empty;

            // Create a report instance.
            XtraReport report = null;

            if (ReportFactory.Reports.ContainsKey(reportName))
            {
                report = ReportFactory.Reports[reportName]();
            }
            else
            {
                throw new DevExpress.XtraReports.Web.ClientControls.FaultException(
                    string.Format("Could not find report '{0}'.", reportName)
                );
            }

            // Apply the parameter values to the report.
            var parameters = HttpUtility.ParseQueryString(parametersQueryString);

            foreach (string parameterName in parameters.AllKeys)
            {
                report.Parameters[parameterName].Value = Convert.ChangeType(
                    parameters.Get(parameterName), report.Parameters[parameterName].Type);
            }

            // Disable the Visible property for all report parameters
            // to hide the Parameters Panel in the viewer.
            foreach (var parameter in report.Parameters)
            {
                parameter.Visible = false;
            }
            //TEMPORAL
            //report.ParametersRequestBeforeShow += report_ParametersRequestBeforeShow;
            //FIN TEMPORAL
            // If you do not hide the panel, disable the report's RequestParameters property.
            // report.RequestParameters = false;

            return report;
            
        }

    }
}
