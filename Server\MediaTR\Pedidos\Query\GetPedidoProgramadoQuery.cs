﻿using System.Runtime.InteropServices.JavaScript;
using MediatR;
using Nelibur.ObjectMapper;
using ProgramadorGeneralBLZ.Server.Data.DatoLita01;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;
using System.Text.RegularExpressions;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared;
using System.Reflection;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Services.DatabaseManipulation;
using System;
using DevExpress.XtraPrinting;
using System.Diagnostics;
using System.Reactive.Concurrency;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Pedidos.Query
{
    public class GetPedidoProgramadoQuery : IRequest<SingleResult<ListadoPedidosProcesadosFiltradosDTO>>
    {
        /// <summary>
        /// Query para obtener el pedido, con distintas combinaciones:
        /// El último
        /// Por ID de Litalsa
        /// Por ID del Cliente y/o IDCliente ya que dos clientes pueden tener el mismo código de pedido.
        /// </summary>
        /// <param name="idPedido"></param>
        /// <param name="idPedidoCliente"></param>
        /// <param name="idCliente"></param>
        /// <param name="ultimo"></param>
        public GetPedidoProgramadoQuery(int? idPedido, string idCliente, string idPedidoCliente = null, bool ultimo = false)
        {
            IdCliente = idCliente;
            IdPedidoCliente = idPedidoCliente;
            IdPedido = idPedido;
            Ultimo = ultimo;
        }

        public int? IdPedido { get; set; }
        public bool Ultimo { get; set; }
        public string IdCliente { get; set; }
        public string IdPedidoCliente { get; set; }

    }
    public class GetPedidoProgramadoQueryHandler : IRequestHandler<GetPedidoProgramadoQuery, SingleResult<ListadoPedidosProcesadosFiltradosDTO>>
    {
        private readonly ProgramadorLitalsaContext _contextProg;
        private readonly DatoLita01Context _contextLita01;
        private readonly IDataManipulationService _dataManipulationService;


        public GetPedidoProgramadoQueryHandler(ProgramadorLitalsaContext contextProg,
            DatoLita01Context contextLita01, IDataManipulationService dataManipulationService)
        {
            _contextProg = contextProg;
            _contextLita01 = contextLita01;
            _dataManipulationService = dataManipulationService;
        }

        /// <summary>
        /// Recuperamos de la BD los datos del pedido indicado.
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<SingleResult<ListadoPedidosProcesadosFiltradosDTO>> Handle(GetPedidoProgramadoQuery request, CancellationToken cancellationToken)
        {
            var result = new SingleResult<ListadoPedidosProcesadosFiltradosDTO>
            {
                Data = new ListadoPedidosProcesadosFiltradosDTO
                {
                    ListadoIdPedidos = new List<int?>(),
                    Pedidos = new List<PedidoProcesadoDTO>()
                },
                Errors = new List<string>()
            };
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            try
            {
                int? pedidoEsteAnyo = 0;
                int? pedidoAnyoPasado = 0;
                var listPedProcesados = new List<PedidoProcesado>();

                Debug.WriteLine($"Llamada identificar pedido - INICIO: {stopwatch.ElapsedMilliseconds}");
                if (request.Ultimo)
                {
                    //Obtener el ultimo pedido que no es 3, 6 ni 9.
                    var pattern = "^2.[0|3|6]....";
                    Regex rg = new Regex(pattern);
                    //var f = from pedidoProcesado in _contextProg.PedidoProcesado
                    //        let matches = !rg.IsMatch(pedidoProcesado.IdPedido.ToString())
                    //        where matches.Count > 0
                    //            orderby Id
                    listPedProcesados.Add(await _contextProg.PedidoProcesado.Where(o =>
                        o.IdPedido.ToString().Substring(2, 1) != "3" &&
                        o.IdPedido.ToString().Substring(2, 1) != "6" &&
                        o.IdPedido.ToString().Substring(2, 1) != "9").OrderByDescending(o => o.Id).FirstOrDefaultAsync(cancellationToken));
                }
                else if (request.IdPedido != null)
                {
                    if (request.IdPedido.ToString().Length < 7)
                    {
                        pedidoEsteAnyo = int.Parse(request.IdPedido.Value.ToString($"{DateTime.Now:yy}00000"));
                        pedidoAnyoPasado = int.Parse(request.IdPedido.Value.ToString($"{DateTime.Now.AddYears(-1):yy}00000"));
                        var pedidoPorAnyo = _contextProg.PedidoProcesado.Where(o =>
                            o.IdPedido == pedidoEsteAnyo || o.IdPedido == pedidoAnyoPasado).OrderByDescending(o => o.IdPedido).FirstOrDefault();
                        if (pedidoPorAnyo != null)
                        {
                            listPedProcesados.Add(pedidoPorAnyo);

                        }

                    }
                    else
                    {
                        listPedProcesados.Add(_contextProg.PedidoProcesado.FirstOrDefault(o => o.IdPedido == request.IdPedido));
                    }
                }
                else if (request.IdPedidoCliente != null)
                {
                    var queryIdPedidoCliente = _contextProg.PedidoProcesado.Where(o => o.Supedido.Contains(request.IdPedidoCliente)).AsQueryable();
                    listPedProcesados.AddRange(request.IdCliente != null
                        ? queryIdPedidoCliente.Where(o => o.IdCliente == int.Parse(request.IdCliente))
                        : queryIdPedidoCliente);
                }
                else
                {
                    listPedProcesados = null;
                }

                //if (request.IdPedido.ToString().Length < 7 && !request.Ultimo)
                //{
                //    pedidoEsteAnyo = int.Parse(request.IdPedido.Value.ToString($"{DateTime.Now:yy}00000"));
                //    pedidoAnyoPasado = int.Parse(request.IdPedido.Value.ToString($"{DateTime.Now.AddYears(-1):yy}00000"));
                //    datos =  _contextProg.PedidoProcesado.FirstOrDefault(o => o.IdPedido == pedidoEsteAnyo || o.IdPedido == pedidoAnyoPasado);
                //}
                //else
                //{
                //    datos = request.Ultimo
                //        ? _contextProg.PedidoProcesado.OrderByDescending(o => o.Id).First()
                //        :  _contextProg.PedidoProcesado.FirstOrDefault(o => o.IdPedido == request.IdPedido);
                //}
                if (listPedProcesados == null || listPedProcesados.Count == 0 || listPedProcesados.Any(o => o == null))
                {
                    result.Errors.Add("Código de pedido no encontrado.");
                    return result;
                }

                Debug.WriteLine($"Llamada identificar pedido - FIN: {stopwatch.ElapsedMilliseconds}");


                Debug.WriteLine($"Llamada FOREACH - INICIO: {stopwatch.ElapsedMilliseconds}");
                foreach (var pp in listPedProcesados)
                {
                    Debug.WriteLine($"Llamada P1 - INICIO: {stopwatch.ElapsedMilliseconds}");
                    var datosCliente = _contextProg.Clientes.FirstOrDefault(o => o.CodigoCliente == pp.IdCliente);
                    //if (datosCliente == null)
                    //{
                    //    result.Errors.Add("Datos de cliente para el último pedido no encontrados.");
                    //    return result;
                    //}

                    List<FasePedidoDTO> objFasesDto = null;
                    var datosFasesPedido = _contextProg.TablaCodigosPedido.Where(o => o.Idpedido == pp.IdPedido).OrderBy(o => o.Fase);

                    if (datosFasesPedido.Any())
                    {
                        //Mapear a DTO
                        objFasesDto = TinyMapper.Map<List<FasePedidoDTO>>(datosFasesPedido.ToList());
                        foreach (var fase in objFasesDto)
                        {
                            //fase.TextoIdCodigoAplicacion = _contextProg.CodApliAll.Where(o => o.Codbaz == fase.Idcodigoaplicacion).Select(s => new { texto = $"{s.Codbaz},{s.Nombaz}" }).FirstOrDefault()?.texto ?? "N/A";
                            fase.TextoIdCodigoAplicacion = _contextProg.Codapli.Where(o => o.Codbaz == fase.Idcodigoaplicacion)
                                .Select(s => new { texto = $"{s.Codbaz},{s.Nombaz}" })
                                .FirstOrDefault()?.texto ?? "N/A";
                            fase.TextoIdbarniz = _contextProg.TablaProductos.Where(o => o.Idproducto == fase.Idbarniz)
                                .Select(s => new { texto = $"{s.Idproducto},{s.Denominacion}" }).FirstOrDefault()?.texto ?? "N/A";
                        }
                    }

                    Debug.WriteLine($"Llamada P1 - FIN: {stopwatch.ElapsedMilliseconds}");
                    Debug.WriteLine($"Llamada P2 - INICIO: {stopwatch.ElapsedMilliseconds}");
                    //Mapear a DTO
                    var objDto = TinyMapper.Map<PedidoProcesadoDTO>(pp);
                    Debug.WriteLine($"Llamada P2 - FIN: {stopwatch.ElapsedMilliseconds}");

                    Debug.WriteLine($"Llamada P3 - INICIO: {stopwatch.ElapsedMilliseconds}");
                    //Rellenar campos restantes del DTO
                    objDto.FasesPedido = objFasesDto;
                    Debug.WriteLine($"Llamada P31 - INICIO: {stopwatch.ElapsedMilliseconds}");
                    objDto.NombreCliente = _dataManipulationService.GetNombreCliente(pp.IdCliente.Value);
                    Debug.WriteLine($"Llamada P31 - FIN: {stopwatch.ElapsedMilliseconds}");
                    Debug.WriteLine($"Llamada P32 - INICIO: {stopwatch.ElapsedMilliseconds}");
                    objDto.PesoHoja = _dataManipulationService.CalcularPesoPorHoja(pp.IdPedido.Value, pp.AnchoHjlta.Value, pp.LargoHjlta.Value, pp.EspesorHjlta.Value);
                    Debug.WriteLine($"Llamada P32 - FIN: {stopwatch.ElapsedMilliseconds}");
                    Debug.WriteLine($"Llamada P33 - INICIO: {stopwatch.ElapsedMilliseconds}");
                    objDto.PesoTotalHojas = _dataManipulationService.CalcularPesoTotal(objDto.HojasPedido.Value, objDto.PesoHoja.Value);
                    Debug.WriteLine($"Llamada P33 - FIN: {stopwatch.ElapsedMilliseconds}");

                    Debug.WriteLine($"Llamada P34 - INICIO: {stopwatch.ElapsedMilliseconds}");
                    var maquinas = _contextProg.Maquinas.ToList();
                    var programaciones = _contextProg.TablaProgramacion
                        .Where(o => o.Idpedido == objDto.IdPedido)
                        .ToList();
                    Debug.WriteLine($"Llamada P34 - FIN: {stopwatch.ElapsedMilliseconds}");
                    Debug.WriteLine($"Llamada P35 - INICIO: {stopwatch.ElapsedMilliseconds}");
                    objDto.TxtEstado = _dataManipulationService.GetTextoEstadoCodigosAplicacion(objDto.IdPedido.Value, false, false, maquinas, programaciones);
                    Debug.WriteLine($"Llamada P35 - FIN: {stopwatch.ElapsedMilliseconds}");
                    Debug.WriteLine($"Llamada P36 - INICIO: {stopwatch.ElapsedMilliseconds}");
                    objDto.Mermas = objDto.HojasTerminadas > 0
                        ? (1.0 - (double)objDto.HojasTerminadas / objDto.HojasPedido)*100
                        : null;
                    Debug.WriteLine($"Llamada P36 - FIN: {stopwatch.ElapsedMilliseconds}");

                    Debug.WriteLine($"Llamada P3 - FIN: {stopwatch.ElapsedMilliseconds}");
                    //Los campos de fechas se obtienen de DATO01LITA, en el access se obtienen de una tabla vinculada directamente a dato01Lita
                    //pero con c# puedo conectar directamente de forma más cómoda.
                    Debug.WriteLine($"Llamada FECHAS - INICIO: {stopwatch.ElapsedMilliseconds}");
                    var datosFechas = _contextLita01.Fechas.FirstOrDefault(o => o.Codigo == objDto.IdPedido);
                    if (datosFechas != null)
                    {
                        objDto.FechaFtp = string.IsNullOrWhiteSpace(datosFechas.Fechaftp) ? null : Convert.ToDateTime(datosFechas.Fechaftp);
                        objDto.FechaHojalata = string.IsNullOrWhiteSpace(datosFechas.Fechahojalata) ? null : Convert.ToDateTime(datosFechas.Fechahojalata);
                        objDto.FechaLanzamiento = string.IsNullOrWhiteSpace(datosFechas.Fechamaxima) ? null : Convert.ToDateTime(datosFechas.Fechamaxima);
                        objDto.FechaContrato = string.IsNullOrWhiteSpace(datosFechas.Fcontrato) ? null : Convert.ToDateTime(datosFechas.Fcontrato);
                        objDto.FechaAcordada = string.IsNullOrWhiteSpace(datosFechas.Facordada) ? null : Convert.ToDateTime(datosFechas.Facordada);
                    }

                    //objDto.FechaPedido = !string.IsNullOrEmpty(datosFechas.Fechapedido) ? Convert.ToDateTime(datosFechas.Fechapedido) : null;
                    //objDto.FechaFin = datosFechas.Fechaftp;
                    //objDto.FechaEntregaSolicitada = datosFechas.Fechaftp;

                    Debug.WriteLine($"Llamada FECHAS - FIN: {stopwatch.ElapsedMilliseconds}");
                    //Buscamos si tiene reprocesos, ya sean 3 o 6.
                    Debug.WriteLine($"Llamada LLAMADAS1 - INICIO: {stopwatch.ElapsedMilliseconds}");
                    objDto.IdPedidoReproceso = _dataManipulationService.GetPedidoReproceso(pp.IdPedido.Value);
                    objDto.CaracteristicasHojalata = _dataManipulationService.GetCaracteristicasHojalata(pp.IdPedido.Value);
                    objDto.Esc = datosCliente==null ? "?" : _dataManipulationService.GetDatosPlano(datosCliente.Id, objDto.Plano, "Es");
                    Debug.WriteLine($"Llamada LLAMADAS1 - FIN: {stopwatch.ElapsedMilliseconds}");

                    Debug.WriteLine($"Llamada TINTAS - INICIO: {stopwatch.ElapsedMilliseconds}");
                    var ppdto = new PedidoProcesadoDTO();
                    var type = ppdto.GetType();

                    var campoC0_PEDNames = Enumerable.Range(1, 11).Select(i => i < 10 ? $"C0{i}ped" : $"Co{i}ped").ToList();

                    //TINTAS
                    foreach (var campoC0_PEDName in campoC0_PEDNames)
                    {
                        var campoC0_PEDValue = type.GetProperty(campoC0_PEDName)?.GetValue(objDto);
                        var codTintas = campoC0_PEDValue != null ? int.Parse(campoC0_PEDValue.ToString()) : 0;

                        if (codTintas == 0) continue;

                        var aarticuTinta = _contextLita01.AArticu
                            .FirstOrDefault(o => o.Estadis.Contains("TI") && o.Codigo2 != null && o.Codigo2 == codTintas.ToString());
                        var valorTinta = $"{aarticuTinta?.Codigo2 ?? string.Empty},{aarticuTinta?.Descrip ?? string.Empty}";
                        var campoTexto = $"{campoC0_PEDName}Text";
                        var propertyInfo = objDto.GetType().GetProperty(campoTexto);

                        if (propertyInfo != null)
                        {
                            propertyInfo.SetValue(objDto, Convert.ChangeType(valorTinta, propertyInfo.PropertyType), null);
                        }
                    }

                    Debug.WriteLine($"Llamada TINTAS - FIN: {stopwatch.ElapsedMilliseconds}");
                    Debug.WriteLine($"Llamada TINTAS2 - INICIO: {stopwatch.ElapsedMilliseconds}");
                    for (var i = 1; i <= 4; i++)
                    {
                        // hay que localizar el codigo de las tintas que estén en la posicion i
                        // y ver si es distinto de 0 o no nulo
                        var campoCD_PEDName = $"Cd{i}ped";

                        var campoCD_PEDValue = type.GetProperty(campoCD_PEDName)?.GetValue(objDto);
                        var codTintas = campoCD_PEDValue != null ? int.Parse(campoCD_PEDValue.ToString()) : 0;
                        if (codTintas == 0) continue;
                        //Codtintas bebe de a_articu y no se está actualizando en los jobs de actualizaciones así que la 
                        //sustituimos por su verdadero origen que es A_ARTICU
                        var aarticuTinta = _contextLita01.AArticu
                            .FirstOrDefault(o => o.Estadis.Contains("TI") && o.Codigo2 != null && o.Codigo2 == codTintas.ToString());
                        var valorTinta = $"{aarticuTinta?.Codigo2 ?? string.Empty},{aarticuTinta?.Descrip ?? string.Empty}";
                        var campoTexto = $"{campoCD_PEDName}Text";
                        PropertyInfo propertyInfo = objDto.GetType().GetProperty(campoTexto);
                        propertyInfo.SetValue(objDto, Convert.ChangeType(valorTinta, propertyInfo.PropertyType), null);
                    }
                    Debug.WriteLine($"Llamada TINTAS2 - FIN: {stopwatch.ElapsedMilliseconds}");

                    Debug.WriteLine($"Llamada SECCION FINAL - INICIO: {stopwatch.ElapsedMilliseconds}");
                    Debug.WriteLine($"Llamada SECCION FINAL1 - INICIO: {stopwatch.ElapsedMilliseconds}");
                    objDto.ObsAlmacen = _dataManipulationService.GetObservacionesAlmacen(pp.IdPedido.Value, true, false, null);
                    //Prueba temporal para obtener el texto (YP)
                    Debug.WriteLine($"Llamada SECCION FINAL1 - INICIO: {stopwatch.ElapsedMilliseconds}");
                    if (objDto.HojasTerminadas > 0 && !objDto.ObsAlmacen.Contains("(YP)"))
                    {
                        objDto.ObsAlmacen += " (YP) ";
                    }
                    Debug.WriteLine($"Llamada SECCION FINAL2 - INICIO: {stopwatch.ElapsedMilliseconds}");
                    objDto.ObsCalidad = await _dataManipulationService.GetObservacionesCalidadLite(pp.IdPedido.Value, null);
                    Debug.WriteLine($"Llamada SECCION FINAL2 - INICIO: {stopwatch.ElapsedMilliseconds}");
                    Debug.WriteLine($"Llamada SECCION FINAL3 - INICIO: {stopwatch.ElapsedMilliseconds}");
                    //objDto.ExisteFicheroFotomecanica = Directory.EnumerateFiles(@"\\dc1\Company\COPIA APOGEEX\", $"{objDto.IdPedido}*.pdf").Any();
                    Debug.WriteLine($"Llamada SECCION FINAL3 - INICIO: {stopwatch.ElapsedMilliseconds}");

                    var motivos = await _contextLita01.Motivos.Where(o => o.Codigo == objDto.IdPedido.ToString())
                        .Select(o => o.Modelo)
                        .ToListAsync(cancellationToken);

                    var fotolitos = motivos.Any()
                        ? await _contextLita01.MotivosDetallados.Where(o => motivos.Contains(o.Codigo))
                            .Select(s => $"{s.Fotolitos} - Motivo: {s.Codnuevo}")
                            .ToListAsync(cancellationToken)
                        : new List<string>();
                    objDto.Carpetas = fotolitos;



                    result.Data.Pedidos.Add(objDto);
                    result.Data.ListadoIdPedidos.Add(objDto.IdPedido.Value);
                    Debug.WriteLine($"Llamada SECCION FINAL - FIN: {stopwatch.ElapsedMilliseconds}");
                }

                //result.Data = objDto;

                Debug.WriteLine($"Llamada FOREACH - FIN: {stopwatch.ElapsedMilliseconds}");
            }
            catch (Exception e)
            {
                result.Errors.Add($"{e.Message}--{(!string.IsNullOrWhiteSpace(e.InnerException?.Message) ? e.InnerException : string.Empty)}");
                return result;
            }

            result.Data.Pedidos = result.Data.Pedidos.OrderByDescending(o => o.IdPedido).ToList();
            result.Data.ListadoIdPedidos = result.Data.ListadoIdPedidos.OrderByDescending(o => o).ToList();
            stopwatch.Stop();
            return result;
        }
    }
}
