﻿@using ProgramadorGeneralBLZ.Shared.DTO
@using ProgramadorGeneralBLZ.Shared
@inject HttpClient Http
@inject SpinnerService SpinnerService
@inject IToastService ToastService

<div class="h-100 overflow-auto px-2 py-1">
    <DxGrid Data="@maquinas" SizeMode="SizeMode.Medium"
            CssClass="progGrid" PageSize="18"
            KeyFieldName="Idmaquina" Context="GridMaquinas"
            AllowSort="true" EditNewRowPosition="GridEditNewRowPosition.Top"
            ShowFilterRow="true" ValidationEnabled="true"
            EditMode="GridEditMode.EditRow" @ref="Grid"
            EditorRenderMode="GridEditorRenderMode.Integrated"
            EditModelSaving="GridMaquinas_EditModelSaving"
            DataItemDeleting="Grid_DataItemDeleting">
        <Columns>
            <!-- Columna de Comandos -->
            <DxGridCommandColumn Width="80px" FixedPosition="GridColumnFixedPosition.Left">
                <HeaderTemplate>
                    <a class="oi oi-plus" @onclick="@(() => Grid.StartEditNewRowAsync())" style="text-decoration: none;color: lightskyblue;" href="javascript:void(0);"></a>
                </HeaderTemplate>
                <CellDisplayTemplate>
                    <a class="oi oi-pencil" @onclick="@(() => Grid.StartEditRowAsync(context.VisibleIndex))" style="text-decoration: none; padding-right: 15px; color: #c75fff;" href="javascript:void(0);"></a>
                    <a class="oi oi-x" @onclick="@(() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex))" style="text-decoration: none; color: red;" href="javascript:void(0);"></a>
                </CellDisplayTemplate>
                <CellEditTemplate>
                    <a class="oi oi-arrow-thick-bottom" @onclick="@(() => Grid.SaveChangesAsync())" style="text-decoration: none; padding-right: 15px; color: greenyellow; margin-right: 6px; margin-top: 3px;" href="javascript:void(0);"></a>
                    <a class="oi oi-x" @onclick="@(() => Grid.CancelEditAsync())" style="text-decoration: none; color: red;" href="javascript:void(0);"></a>
                </CellEditTemplate>
            </DxGridCommandColumn>
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.Idmaquina)" Visible="false" 
                              FixedPosition="GridColumnFixedPosition.Left" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.IdmaquinaG21)" Caption="ID G21" 
                              FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" 
                              FixedPosition="GridColumnFixedPosition.Left" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.TipoMaquina)" Caption="Tipo de Máquina" 
                              FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.Nombremaquina)" Caption="Nombre de la Máquina" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.Velocidadmd)" Caption="Velocidad MD" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.VelocidadScroll)" Caption="Velocidad Scroll" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.Velocidad2pasadas)" Caption="Velocidad 2 Pasadas" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.VelocidadAlu)" Caption="Velocidad Alu" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.CambioRodillo)" Caption="Cambio Rodillo" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.CambioPlanchas)" Caption="Cambio Planchas" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.CambioFormato)" Caption="Cambio Formato" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.CambioEscuadra)" Caption="Cambio Escuadra" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.NumeroCuerpos)" Caption="Número Cuerpos" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.PasesHora)" Caption="Pases por Hora" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.PorcentajeTiempoTirada)" Caption="Porcentaje Tiempo Tirada" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.Idmaquinainformix)" Caption="ID Informix" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.IdmaquinaG21)" Caption="ID G21" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.PosicionDesde)" Caption="Posición Desde" DisplayFormat="F0" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.PosicionHasta)" Caption="Posición Hasta" DisplayFormat="F0" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.Idaplicacion)" Caption="ID Aplicación" DisplayFormat="F0" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.Idproducto)" Caption="ID Producto" DisplayFormat="F0" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.CambioGuias)" Caption="Cambio de Guías" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.Numerofotocopias)" Caption="Número de Fotocopias" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.AnchoMinimo)" Caption="Ancho Mínimo" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.AnchoMaximo)" Caption="Ancho Máximo" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.LargoMinimo)" Caption="Largo Mínimo" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.LargoMaximo)" Caption="Largo Máximo" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.Abreviatura)" Caption="Abreviatura" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.RepartoPasesxImpresora)" Caption="Reparto de Pases por Impresora" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.Retirada)" Caption="Retirada" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120">
                <CellDisplayTemplate Context="checkbox">
                    <DxCheckBox Checked="@((bool)checkbox.Value)" ReadOnly="true"/>
                </CellDisplayTemplate>
            </DxGridDataColumn>
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.ProductosXsistema)" Caption="Productos por Sistema" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.MaxDisolvente)" Caption="Máximo Disolvente" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.WetonWet)" Caption="Wet on Wet" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120">
                <CellDisplayTemplate Context="checkbox">
                    <DxCheckBox Checked="@((bool)checkbox.Value)" ReadOnly="true"/>
                </CellDisplayTemplate>
            </DxGridDataColumn>
            <DxGridDataColumn FieldName="@nameof(MaquinaDTO.PosicionTandem)" Caption="Posición Tándem" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
        </Columns>

        <DataColumnCellEditTemplate>
            @{
                var maquina = (MaquinaDTO)GridMaquinas.EditModel;
            }
            @switch (GridMaquinas.DataColumn.FieldName)
            {
                case "TipoMaquina":
                    <DxTextBox @bind-Text="@maquina.TipoMaquina" />
                    break;
                case "Nombremaquina":
                    <DxTextBox @bind-Text="@maquina.Nombremaquina" />
                    break;
                case "Velocidadmd":
                    <DxSpinEdit @bind-Value="@maquina.Velocidadmd" ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "VelocidadScroll":
                    <DxSpinEdit @bind-Value="@maquina.VelocidadScroll" ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "Velocidad2pasadas":
                    <DxSpinEdit @bind-Value="@maquina.Velocidad2pasadas" ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "VelocidadAlu":
                    <DxSpinEdit @bind-Value="@maquina.VelocidadAlu" ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "CambioRodillo":
                    <DxSpinEdit @bind-Value="@maquina.CambioRodillo" ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "CambioPlanchas":
                    <DxSpinEdit @bind-Value="@maquina.CambioPlanchas" ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "CambioFormato":
                    <DxSpinEdit @bind-Value="@maquina.CambioFormato" ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "CambioEscuadra":
                    <DxSpinEdit @bind-Value="@maquina.CambioEscuadra" ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "NumeroCuerpos":
                    <DxSpinEdit @bind-Value="@maquina.NumeroCuerpos" ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "PasesHora":
                    <DxSpinEdit @bind-Value="@maquina.PasesHora" ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "PorcentajeTiempoTirada":
                    <DxSpinEdit @bind-Value="@maquina.PorcentajeTiempoTirada" ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "Idmaquinainformix":
                    <DxSpinEdit @bind-Value="@maquina.Idmaquinainformix" ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "IdmaquinaG21":
                    <DxTextBox @bind-Text="@maquina.IdmaquinaG21" />
                    break;
                case "PosicionDesde":
                    <DxSpinEdit @bind-Value="@maquina.PosicionDesde" ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "PosicionHasta":
                    <DxSpinEdit @bind-Value="@maquina.PosicionHasta" ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "Idaplicacion":
                    <DxSpinEdit @bind-Value="@maquina.Idaplicacion" ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "Idproducto":
                    <DxSpinEdit @bind-Value="@maquina.Idproducto" ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "CambioGuias":
                    <DxSpinEdit @bind-Value="@maquina.CambioGuias" ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "Numerofotocopias":
                    <DxSpinEdit @bind-Value="@maquina.Numerofotocopias" ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "AnchoMinimo":
                    <DxSpinEdit @bind-Value="@maquina.AnchoMinimo" ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "AnchoMaximo":
                    <DxSpinEdit @bind-Value="@maquina.AnchoMaximo" ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "LargoMinimo":
                    <DxSpinEdit @bind-Value="@maquina.LargoMinimo" ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "LargoMaximo":
                    <DxSpinEdit @bind-Value="@maquina.LargoMaximo" ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "Abreviatura":
                    <DxTextBox @bind-Text="@maquina.Abreviatura" />
                    break;
                case "RepartoPasesxImpresora":
                    <DxSpinEdit @bind-Value="@maquina.RepartoPasesxImpresora" ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "Retirada":
                    <DxCheckBox CssClass="d-inline-block" @bind-Checked="@maquina.Retirada" ValueChecked=true ValueUnchecked=false />
                    break;
                case "ProductosXsistema":
                    <DxTextBox @bind-Text="@maquina.ProductosXsistema" />
                    break;
                case "MaxDisolvente":
                    <DxSpinEdit @bind-Value="@maquina.MaxDisolvente" ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "WetonWet":
                    <DxCheckBox CssClass="d-inline-block" @bind-Checked="@maquina.WetonWet" ValueChecked=true ValueUnchecked=false />
                    break;
                case "PosicionTandem":
                    <DxSpinEdit @bind-Value="@maquina.PosicionTandem" ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
            }
        </DataColumnCellEditTemplate>
    </DxGrid>
</div>

@code {
    DxGrid? Grid;
    List<MaquinaDTO> maquinas = new List<MaquinaDTO>();

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    async Task LoadData()
    {
        SpinnerService.Show();
        maquinas = await Http.GetFromJsonAsync<List<MaquinaDTO>>("GestionTablasV2/Maquina");
        SpinnerService.Hide();
    }

    async Task GridMaquinas_EditModelSaving(GridEditModelSavingEventArgs e)
    {
        SpinnerService.Show();
        var dest = (MaquinaDTO)e.EditModel;
        var response = e.IsNew == false
            ? await Http.PutAsJsonAsync($"GestionTablasV2/Maquina/{dest.Idmaquina}", dest)
            : await Http.PostAsJsonAsync("GestionTablasV2/Maquina", dest);
        if (response.IsSuccessStatusCode)
            await LoadData();
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            ToastService.ShowError(error);
        }
        SpinnerService.Hide();
    }

    async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        SpinnerService.Show();
        var item = (MaquinaDTO)e.DataItem;
        var response = await Http.DeleteAsync($"GestionTablasV2/Maquina/{item.Idmaquina}");
        if (response.IsSuccessStatusCode)
            await LoadData();
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            ToastService.ShowError(error);
        }
        SpinnerService.Hide();
    }
    async Task Delete(MaquinaDTO item)
    {
        SpinnerService.Show();
        var response = await Http.DeleteAsync($"GestionTablasV2/Maquina/{item.Idmaquina}");
        if (response.IsSuccessStatusCode)
            await LoadData();
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            ToastService.ShowError(error);
        }
        SpinnerService.Hide();
    }
}
