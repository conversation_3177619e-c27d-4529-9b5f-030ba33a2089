﻿@using ProgramadorGeneralBLZ.Shared.ResponseModels
@using ProgramadorGeneralBLZ.Shared.DTO
@using ProgramadorGeneralBLZ.Shared

@inject IToastService toastService
@inject HttpClient http
@inject SpinnerService _spinnerService

<h3>Datos Partes</h3>
<DxGrid Data="@datosPartes" CssClass="ch-800" SizeMode="SizeMode.Small"
        Context="ContextGridPartes" @ref="GridPartes"AllowSort="true">
    <Columns>
        <DxGridDataColumn FieldName="Orden" Width="90px" DisplayFormat="F0"
                          TextAlignment="GridTextAlignment.Center" />
        <DxGridDataColumn FieldName="NombreEmp"
                          TextAlignment="GridTextAlignment.Left"
                          Caption="Nombre Emp." Width="120px" />
        <DxGridDataColumn FieldName="Cantidad" Width="70px" TextAlignment="GridTextAlignment.Center"/>
        <DxGridDataColumn FieldName="FechaInicio" DisplayFormat="g" Width="100px" />
        <DxGridDataColumn FieldName="HoraInicio" DisplayFormat="g" Width="80px" />
        <DxGridDataColumn FieldName="HoraFin" DisplayFormat="g" Width="80px" />
        <DxGridDataColumn FieldName="Ccoste" Width="60px" TextAlignment="GridTextAlignment.Center" />
        <DxGridDataColumn FieldName="Maquina" Width="70px" TextAlignment="GridTextAlignment.Center" />
        <DxGridDataColumn FieldName="Operacion" Width="80px" TextAlignment="GridTextAlignment.Center" />
        <DxGridDataColumn FieldName="NombreOp" Caption="Nombre Op." Width="120px" />
        <DxGridDataColumn FieldName="Minutos" DisplayFormat="n5" Width="60px" />
        <DxGridDataColumn FieldName="GrupoInfo" Width="60px" TextAlignment="GridTextAlignment.Center" />
        <DxGridDataColumn FieldName="Posicion" Caption="Pos" Width="40px" TextAlignment="GridTextAlignment.Center" />
        <DxGridDataColumn FieldName="Dato" Width="100%" />
        <DxGridDataColumn FieldName="Cliente"  />
        <DxGridDataColumn FieldName="Motivos" Width="100%"/>
    </Columns>
</DxGrid>
@code {
    [Parameter]
    public int? IdPedido { get; set; }

    DxGrid? GridPartes;
    ListResult<DatosPartesGridDTO> resultDatosPartes { get; set; }
    List<DatosPartesGridDTO> datosPartes { get; set; }
    
    protected override async Task OnInitializedAsync()
    {
        resultDatosPartes = await http.GetFromJsonAsync<ListResult<DatosPartesGridDTO>>($"DatosGenerales/partes/{IdPedido.Value}");
        if (resultDatosPartes.Errors.Any())
        {
            toastService.ShowInfo($"{resultDatosPartes.Errors.First()}");
        }
        else
        {
            datosPartes = resultDatosPartes.Data;
        }
        StateHasChanged();
    }
}
