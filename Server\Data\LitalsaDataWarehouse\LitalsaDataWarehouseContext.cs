﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable di**ble
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using ProgramadorGeneralBLZ.Server.Models.Lital**DataWarehouse;

namespace ProgramadorGeneralBLZ.Server.Data.Lital**DataWarehouse
{
    public partial class Lital**DataWarehouseContext : DbContext
    {
        public Lital**DataWarehouseContext()
        {
        }

        public Lital**DataWarehouseContext(DbContextOptions<Lital**DataWarehouseContext> options)
            : base(options)
        {
        }

        public virtual DbSet<NotasProgramacion> NotasProgramacion { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
#warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see http://go.microsoft.com/fwlink/?LinkId=723263.
                optionsBuilder.UseSqlServer("Data Source=QPLANT1;Initial Catalog=Lital**DataWarehouse;Persist Security Info=True;User ID=**;Password=**; Encrypt=False");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<NotasProgramacion>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("NotasProgramacion", "calidad");

                entity.Property(e => e.NotaId).HasColumnName("NotaID");

                entity.Property(e => e.Texto).IsRequired();
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}