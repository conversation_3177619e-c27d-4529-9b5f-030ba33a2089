﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace ProgramadorGeneralBLZ.Server.Models.InterfaseBD;

public partial class CodigosAplicacion
{
    public string IdCodigoAplicacion { get; set; }

    public string IdBarniz { get; set; }

    public decimal GramajeMax { get; set; }

    public decimal GramajeMin { get; set; }

    public decimal GramajeNominal { get; set; }

    public decimal TemperaturaSecado { get; set; }

    public string DescripcionCodigo1 { get; set; }

    public string DescripcionCodigo2 { get; set; }

    public string DescripcionCodigo3 { get; set; }

    public bool Activo { get; set; }

    public DateTime FechaCreacion { get; set; }

    public TimeSpan HoraCreacion { get; set; }

    public string UsuarioCreacion { get; set; }

    public DateTime? FechaMod { get; set; }

    public TimeSpan? HoraMod { get; set; }

    public string UsuarioMod { get; set; }

    public DateTime? FechaObsoleto { get; set; }

    public string UsuarioObsoleto { get; set; }

    public virtual Matmas BarnizNavigation { get; set; }

    public virtual ICollection<MotivosColores> MotivosColores { get; set; } = new List<MotivosColores>();

    public virtual ICollection<TratamientosDetalle> TratamientosDetalle { get; set; } = new List<TratamientosDetalle>();
}