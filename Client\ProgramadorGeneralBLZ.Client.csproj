﻿<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>disable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<!--<AssemblyName>ProgramadorGeneralBlazor.Client</AssemblyName>-->
	</PropertyGroup>

	<ItemGroup>
	  <None Remove="Files\Pdf\ReporteCalidad.pdf" />
	  <None Remove="Pages\ConsultaFechas.razor.css" />
	  <None Remove="Pages\ProgramacionBarnizado.razor.css" />
	  <None Remove="Pages\ProgramacionLitografia.razor.css" />
	  <None Remove="Shared\SplashScreen.razor.css" />
	</ItemGroup>

	<ItemGroup>
	  <Content Include="Files\Pdf\ReporteCalidad.pdf">
	    <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
	    <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
	  </Content>
	  <Content Include="Pages\ConsultaFechas.razor.css" />
	  <Content Include="Pages\ProgramacionLitografia.razor.css" />
	  <Content Include="Pages\ProgramacionBarnizado.razor.css" />
	  <Content Include="Shared\SplashScreen.razor.css" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="DevExpress.Blazor.Reporting.JSBasedControls.WebAssembly" Version="24.2.8" />
		<!--<PackageReference Include="DevExpress.Blazor.Reporting.JSBasedControls.WebAssembly" Version="23.2.3" />-->
		<PackageReference Include="Blazored.LocalStorage" Version="4.5.0" />
		<PackageReference Include="Blazored.Toast" Version="4.2.1" />
		<PackageReference Include="BlazorPro.Spinkit" Version="1.2.0" />
		<PackageReference Include="DevExpress.Blazor" Version="24.2.8" />
		<!--<PackageReference Include="DevExpress.Blazor" Version="23.2.3" />-->
		<PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="8.0.14" />
		<PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="8.0.14" PrivateAssets="all" />
		<PackageReference Include="Microsoft.Authentication.WebAssembly.Msal" Version="7.0.2" />
		<PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
		<PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="8.0.14" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Shared\ProgramadorGeneralBLZ.Shared.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Files\Images\" />
	</ItemGroup>

	<ItemGroup>
		<Content Update="wwwroot\js\reporting_ViewerCustomization.js">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="wwwroot\scripts\reconnectTimer.js">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="wwwroot\scripts\extraScripts.js">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

</Project>