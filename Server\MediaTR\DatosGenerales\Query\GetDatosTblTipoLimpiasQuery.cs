﻿using System.Globalization;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Nelibur.ObjectMapper;
using ProgramadorGeneralBLZ.Server.Data.DatoLita01;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.DatosGenerales.Query;

public class GetDatosTblTipoLimpiasQuery : IRequest<ListResult<TblTipoLimpiezaDTO>>
{
    public GetDatosTblTipoLimpiasQuery()
    {
    }
}

internal class GetDatosTblTipoLimpiasQueryHandler : IRequestHandler<GetDatosTblTipoLimpiasQuery, ListResult<TblTipoLimpiezaDTO>>
{
    private readonly ProgramadorLitalsaContext _programadorLitalsaContext;
    public GetDatosTblTipoLimpiasQueryHandler(ProgramadorLitalsaContext programadorLitalsaContext)
    {
        _programadorLitalsaContext = programadorLitalsaContext;
    }

    public async Task<ListResult<TblTipoLimpiezaDTO>> Handle(GetDatosTblTipoLimpiasQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<TblTipoLimpiezaDTO>()
        {
            Data = new List<TblTipoLimpiezaDTO>(),
            Errors = new List<string>()
        };
        try
        {
            var datosLimpias =
                await _programadorLitalsaContext.TblTipoLimpieza.ToListAsync(cancellationToken);
            
            result.Data = TinyMapper.Map<List<TblTipoLimpiezaDTO>>(datosLimpias);
            return result;
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: GetDatosTblTipoLimpiasQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            throw new Exception(errorText, e);
        }
    }
}