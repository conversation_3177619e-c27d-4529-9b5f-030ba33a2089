﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Texto136.ExpressionBindings" xml:space="preserve">
    <value>Iif(True, '#NOT_SUPPORTED#', '="&lt;-" &amp; devuelve_posicion([mn_posicion],[idlinea],''ant'') &amp; IIf(devuelve_posicion([mx_posicion],[idlinea],''post'')&gt;0,"/" &amp; devuelve_posicion([mx_posicion],[idlinea],''post'') &amp; "-&gt;","")')</value>
  </data>
  <data name="sqlDataSource1.ResultSchemaSerializable" xml:space="preserve">
    <value>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</value>
  </data>
</root>