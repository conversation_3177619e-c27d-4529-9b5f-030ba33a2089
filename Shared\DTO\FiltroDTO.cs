﻿
namespace ProgramadorGeneralBLZ.Shared.DTO
{
    public class FiltroDTO
    {
        public bool General { get; set; }
        public bool VerHojalata { get; set; }
        public int? IdCliente { get; set; }
        public int? IdPedido { get; set; }
        public string? Supedido { get; set; }
        public string? Wo { get; set; }
        public int? CodApli { get; set; }
        public int? CodBarniz { get; set; }
        public IEnumerable<int>? ListCodBarniz { get; set; }
        public IEnumerable<int>? ListCodBarniz1 { get; set; }
        public IEnumerable<int>? ListCodBarniz2 { get; set; }
        public bool OcultarReprocesos { get; set; }
        public bool IncluirLote { get; set; }
        public bool AgruparCodApli { get; set; }
        public string? LoteBarniz { get; set; }
        public string? DatosCliente { get; set; }
        public int? Pi1ped { get; set; }
        public int? Pi2ped { get; set; }
        public int? Pi3ped { get; set; }
        public int? Pe1ped { get; set; }
        public int? Pe2ped { get; set; }
        public int? Pe3ped { get; set; }
        public DateTime? FechaPedido { get; set; }
        public DateTime? FechaMinPedido { get; set; }
        public DateTime? FechaMaxPedido { get; set; }
        public TimeSpan? HoraPedido { get; set; }
        public string? TipoPedido { get; set; }
        public string? Motivos { get; set; }
        public string? TipoElemento { get; set; }
        public float? Formato { get; set; }
        public string? Plano { get; set; }
        public int? AnchoHjlta { get; set; }
        public int? LargoHjlta { get; set; }
        public int? EspesorHjlta { get; set; }
        public int? TipoHjlta { get; set; }
        public string? EstadoTintas { get; set; }
        public int? LineaTintas { get; set; }
        public string? ObsArt { get; set; }
        public int? Tinta1 { get; set; }
        public int? Tinta2 { get; set; }
        public int? Tinta3 { get; set; }
        public int? Tinta4 { get; set; }
        public int? Tinta5 { get; set; }
        public int? Tinta6 { get; set; }
        public int? Tinta7 { get; set; }
        public int? Cd1ped { get; set; }
        public int? Cd2ped { get; set; }
        public int? Cd3ped { get; set; }
        public int? Cd4ped { get; set; }
    }
}