﻿using System.Reflection;

namespace ProgramadorGeneralBLZ.Server
{
    public static class Extensions
    {
        /// <summary>
        ///     A generic extension method that aids in reflecting 
        ///     and retrieving any attribute that is applied to an `Enum`.
        /// </summary>
        public static TAttribute? GetAttribute<TAttribute>(this Enum enumValue)
            where TAttribute : Attribute
        {
            return enumValue.GetType()
                .GetMember(enumValue.ToString())
                .First()
                .GetCustomAttribute<TAttribute>();
        }
        public static string GetDescripcion<TEntity>(this TEntity entidad, string textoId, string textoDesc)
        {
            if (entidad == null)
            {
                throw new ArgumentNullException(nameof(entidad), "La instancia de entidad no puede ser nula.");
            }

            Type tipoEntidad = typeof(TEntity);
            var propiedades = tipoEntidad.GetProperties();

            PropertyInfo campoID = propiedades.FirstOrDefault(p => p.Name.Contains(textoId, StringComparison.OrdinalIgnoreCase));
            PropertyInfo campoDescripcion = propiedades.FirstOrDefault(p => p.Name.ToLower().Contains(textoDesc));

            if (campoID == null || campoDescripcion == null)
            {
                throw new ArgumentException("No se pudo encontrar el campo de ID o el campo de descripción en la entidad.");
            }

            string valorID = campoID.GetValue(entidad)?.ToString();
            string valorDescripcion = campoDescripcion.GetValue(entidad)?.ToString();

            return !string.IsNullOrEmpty(valorID) && !string.IsNullOrEmpty(valorDescripcion)
                ? $"{valorID}, {valorDescripcion}"
                : string.Empty;
        }
    }
}
