﻿using DevExpress.CodeParser;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Nelibur.ObjectMapper;
using ProgramadorGeneralBLZ.Server.Data.DatoLita01;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Pedidos.Query;

public class GetProgResumenByPedidoQuery : IRequest<ListResult<TablaProgramacionDTO>>
{
    public GetProgResumenByPedidoQuery(int idPedido)
    {
        IdPedido = idPedido;
    }

    public int IdPedido { get; set; }
    internal class GetProgResumenByPedidoQueryHandler : IRequestHandler<GetProgResumenByPedidoQuery, ListResult<TablaProgramacionDTO>>
    {
        private readonly ProgramadorLitalsaContext _programadorLitalsaContext;
        private readonly DatoLita01Context _datoLita01Context;

        public GetProgResumenByPedidoQueryHandler(ProgramadorLitalsaContext programadorLitalsaContext, DatoLita01Context datoLita01Context)
        {
            _programadorLitalsaContext = programadorLitalsaContext;
            _datoLita01Context = datoLita01Context;
        }

        public async Task<ListResult<TablaProgramacionDTO>> Handle(GetProgResumenByPedidoQuery request,
            CancellationToken cancellationToken)
        {
            var result = new ListResult<TablaProgramacionDTO>
            {
                Data = new List<TablaProgramacionDTO>(),
                Errors = new List<string>()
            };
            try
            {
                var maquinas =
                    await _programadorLitalsaContext.Maquinas.ToListAsync(cancellationToken: cancellationToken);

                var pedidoProg = await _programadorLitalsaContext.TablaProgramacion
                    .Include(t => t.IdlineaNavigation)
                    .Include(t => t.IdaplicacionNavigation)
                    .Include(t => t.IdproductoNavigation)
                    .Where(o => o.Idpedido == request.IdPedido).ToListAsync(cancellationToken);

                foreach (var item in pedidoProg)
                {
                    var dto = new TablaProgramacionDTO()
                    {
                        Idprogramacion = item.Idprogramacion,
                        Idpedido = item.Idpedido,
                        Idaplicacion = item.Idaplicacion,
                        Idlinea = item.Idlinea,
                        Posicion = item.Posicion,
                        HojasAprocesar = item.HojasAprocesar,
                        Producto = item.Producto,
                        Flejar = item.Flejar,
                        HoraComienzoEstimada = item.HoraComienzoEstimada,
                        HoraFinEstimada = item.HoraFinEstimada,
                        DuracionEstimada = item.DuracionEstimada,
                        HoraReal = item.HoraReal,
                        DiaReal = item.DiaReal,
                        VarCambios = item.VarCambios,
                        TiposCambio = item.TiposCambio,
                        Orden = item.Orden,
                        Revisar = item.Revisar,
                        Idproducto = item.Idproducto,
                        Peso = item.Peso,
                        PesoMin = item.PesoMin,
                        BarnizNecesario = item.BarnizNecesario,
                        Archivado = item.Archivado,
                        TipoLavada = item.TipoLavada,
                        CaraAaplicar = item.CaraAaplicar,
                        Idaplicacionposterior = item.Idaplicacionposterior,
                        Posicionaplicacionposterior = item.Posicionaplicacionposterior,
                        Idaplicacionanterior = item.Idaplicacionanterior,
                        Posicionaplicacionanterior = item.Posicionaplicacionanterior,
                        Volteado = item.Volteado,
                        AplicacionSimultanea = item.AplicacionSimultanea,
                        Observaciones = item.Observaciones,
                        Obspaseposterior = item.Obspaseposterior,
                        ObsCalidad = item.ObsCalidad,
                        ObsAlmacen = item.ObsAlmacen,
                        TemperaturaSecado = item.TemperaturaSecado,
                        VelocidadMaxima = item.VelocidadMaxima,
                        OrdenProcesoAplicacion = item.OrdenProcesoAplicacion,
                        PasadasAdicionales = item.PasadasAdicionales,
                        Programador = item.Programador,
                        TiempoEstimadoCambio = item.TiempoEstimadoCambio,
                        TiempoEstimadoTirada = item.TiempoEstimadoTirada,
                        DatosPedido = item.DatosPedido,
                        NombreG21 = item.IdlineaNavigation?.IdmaquinaG21,
                        TextoIdAplicacion = item.IdaplicacionNavigation?.Nombreprogramacion,
                        TextoIdProducto = item.IdproductoNavigation?.Denominacion,
                    };

                    result.Data.Add(dto);
                }


                //TEMPORAL ya que los pedidos programados viejos no lo tienen y así lo mostramos al vuelo
                var datosPedidos = await _programadorLitalsaContext.PedidoProcesado
                    .Where(o => o.IdPedido==request.IdPedido).ToListAsync(cancellationToken);
                foreach (var p in result.Data.Where(o=>string.IsNullOrEmpty(o.DatosPedido)))
                {
                    var pedido = datosPedidos.FirstOrDefault(o => o.IdPedido == p.Idpedido);
                    var cliente =
                        _programadorLitalsaContext.Clientes.FirstOrDefault(o => o.CodigoCliente == pedido.IdCliente);
                    p.DatosPedido = cliente == null
                        ? "N/A"
                        : $"{cliente.NombreCliente[..3]} {pedido.Formato} - {pedido.EspesorHjlta}x{pedido.AnchoHjlta}x{pedido.LargoHjlta}";
                    p.NombreG21 = maquinas.FirstOrDefault(o => o.Idmaquina == p.Idlinea.Value)
                        .IdmaquinaG21;
                }
                //FIN TEMPORAL
            }
            catch (Exception e)
            {
                var errorText =
                    $"ERROR: GetProgResumenByPedidoQueryHandler - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
                result.Errors.Add(errorText);
            }
            return result;
        }
    }
}