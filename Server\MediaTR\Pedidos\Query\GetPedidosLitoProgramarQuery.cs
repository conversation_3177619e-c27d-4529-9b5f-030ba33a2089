﻿using System.Data;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.DatoLita01;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Services.DatabaseManipulation;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Pedidos.Query;

public class GetPedidosLitoProgramarQuery : IRequest<ListResult<DatosGeneralesPedidoFotomecanicaLitoDTO>>
{

    /// <summary>
    /// Obtiene el listado de pedidos programados para barnizado según los parámetros del filtro
    /// </summary>
    public GetPedidosLitoProgramarQuery(int idMaquina)
    {
        IdMaquina = idMaquina;
    }
    public int IdMaquina { get; set; }
}

internal class GetPedidosLitoProgramarQueryHandler(
    ProgramadorLitalsaContext programadorLitalsaContext,
    IDbContextFactory<ProgramadorLitalsaContext> programadorLitalsaContextFactory,
    IDataManipulationService dataManipulationService,
    DatoLita01Context datoLita01Context)
    : IRequestHandler<GetPedidosLitoProgramarQuery, ListResult<DatosGeneralesPedidoFotomecanicaLitoDTO>>
{
    private readonly DatoLita01Context _datoLita01Context = datoLita01Context;

    public async Task<ListResult<DatosGeneralesPedidoFotomecanicaLitoDTO>> Handle(GetPedidosLitoProgramarQuery request, CancellationToken cancellationToken)
    {
        //ATENTO A LAS OBS1!!!!! Igual hay que construirlas a mano, porque se ha dejado fuera de los pasos de actualizaciones la ñapa que hace el access
        //de leer y traer la información, concatenar y re-actualizar el campo en la tabla de los pedidos.
        //Stopwatch stopwatch = new Stopwatch();
        //stopwatch.Start(); 
        var result = new ListResult<DatosGeneralesPedidoFotomecanicaLitoDTO>()
        {
            Data = new List<DatosGeneralesPedidoFotomecanicaLitoDTO>(),
            Errors = new List<string>()
        };
        try
        {
            var param = new SqlParameter()
            {
                ParameterName = "@LineaTintas",
                SqlDbType = System.Data.SqlDbType.Int,
                Value = request.IdMaquina
            };
            var programador = await programadorLitalsaContextFactory.CreateDbContextAsync(cancellationToken);
            var pedidos = await programador.Set<DatosGeneralesPedidoFotomecanicaLitoDTO>().FromSqlRaw("[dbo].[sp_PedidosLitoParaProgramar] @LineaTintas", param)
                .ToListAsync(cancellationToken);
            var listaPedidos = pedidos.Select(o => o.IdPedido).ToList();
            if (listaPedidos.Any())
            {
                // Crear la cadena con los Ids para la consulta SQL
                string idList = string.Join(", ", listaPedidos);
                var datosPedido = programadorLitalsaContext.PedidoProcesado
                    .FromSqlRaw($"SELECT * FROM PedidoProcesado WHERE IdPedido IN ({idList})")
                    .ToList();
                var datosMatPed = programadorLitalsaContext.Matped
                    .FromSqlRaw($"SELECT * FROM Matped WHERE Nummpe IN ({idList})")
                    .ToList();
                var ppdto = new DatosGeneralesPedidoFotomecanicaLitoDTO();
                var type = ppdto.GetType();
                var campoC0_PEDNames = Enumerable.Range(1, 11).Select(i => i < 10 ? $"C0{i}ped" : $"Co{i}ped").ToList();
                foreach (var p in pedidos)
                {
                    //p.PasesTotales = _programadorLitalsaContext.Database.SqlQuery<int>($"SELECT dbo.DevuelveNumeroColores({p.IdPedido})").AsEnumerable().First() * p.HojasPedido;

                    //TINTAS
                    var textoTintas = string.Empty;
                    var tintasPrueba = string.Empty;
                    var listColores = new List<int>();
                    var multiplicadorHojas = 1;
                    foreach (var campoC0_PEDName in campoC0_PEDNames)
                    {
                        var campoC0_PEDValue = type.GetProperty(campoC0_PEDName)?.GetValue(p);
                        var codTintas = campoC0_PEDValue != null ? int.Parse(campoC0_PEDValue.ToString()) : 0;
                        tintasPrueba +=
                            $"{programadorLitalsaContext.Database.SqlQuery<string>($"SELECT dbo.dcolor({codTintas})").AsEnumerable().First()}";
                        if (codTintas!=0) listColores.Add(codTintas);
                        if (codTintas is <= 4 or 900 or 9000) continue;

                        textoTintas +=
                            $"{programadorLitalsaContext.Database.SqlQuery<string>($"SELECT dbo.busca_color_especial_v2({codTintas},1)").AsEnumerable().First()} - ";
                    }
                    var maquina = await programadorLitalsaContext.Maquinas.FirstOrDefaultAsync(o => o.Idmaquina == request.IdMaquina, cancellationToken);
                    //Sólo se cuenta el blanco doble si hay 2B o BB y la máquina NO es M2 o M3
                    var blancoDoble = (listColores.Contains(9000) || listColores.Count(o => o == 900) > 2) &&
                                            (maquina.Idmaquina != 6 && maquina.Idmaquina != 13);
                    p.MultiplicadorHojas = listColores.Count > maquina.NumeroCuerpos || blancoDoble ? 2 : 1;

                    p.VelocidadParaPedido = maquina.Velocidadmd;
                    if (p.DescHojalata?.Contains("scroll", StringComparison.OrdinalIgnoreCase) ?? false)
                        p.VelocidadParaPedido =  maquina.VelocidadScroll < maquina.Velocidadmd ? maquina.VelocidadScroll : maquina.Velocidadmd;
                    if (p.MultiplicadorHojas == 2)
                        p.VelocidadParaPedido = maquina.Velocidad2pasadas < maquina.Velocidadmd ? maquina.Velocidad2pasadas : maquina.Velocidadmd;
                    if (p.DescHojalata?.Contains("alu", StringComparison.OrdinalIgnoreCase) ?? false)
                        p.VelocidadParaPedido = maquina.VelocidadAlu < maquina.Velocidadmd ? maquina.VelocidadAlu : maquina.Velocidadmd;
                    
                    p.TextoTintas = textoTintas;
                    p.Tintas = tintasPrueba;

                    p.TextEstado = dataManipulationService.GetTextoEstadoCodigosAplicacion(p.IdPedido.Value);
                    p.DescHojalata = dataManipulationService.GetCaracteristicasHojalata(p.IdPedido.Value,
                        datosPedido.Single(o => o.IdPedido == p.IdPedido),
                        datosMatPed.FirstOrDefault(o => o.Codigo == p.IdPedido.Value.ToString()));
                }
                result.Data = pedidos;
            }

            await programador.DisposeAsync();
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: GetPedidosLitoProgramarQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }
        return result;
    }
}