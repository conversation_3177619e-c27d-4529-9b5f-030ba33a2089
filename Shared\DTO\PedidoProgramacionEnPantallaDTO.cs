﻿
using System.ComponentModel.DataAnnotations;

namespace ProgramadorGeneralBLZ.Shared.DTO
{
    public class PedidoProgramacionEnPantallaDTO
    {
        public int Id { get; set; }
        public int IdProgramacion { get; set; }
        public int Idpedido { get; set; }
        public int? Idaplicacion { get; set; }
        public int? Idlinea { get; set; }
        public int? Posicion { get; set; }
        public int? Orden { get; set; }
        [StringLength(2147483647)]
        public string? Producto { get; set; }
        public int? IdCliente { get; set; }
        [StringLength(2147483647)]
        public string? NombreCliente { get; set; }
        public int? HojasPedido { get; set; }
        [StringLength(2147483647)]
        public string? TipoElemento { get; set; }
        public double? Formato { get; set; }
        [StringLength(2147483647)]
        public string? Plano { get; set; }
        public double? AnchoHjlta { get; set; }
        public double? LargoHjlta { get; set; }
        public double? EspesorHjlta { get; set; }
        public int? TipoHjlta { get; set; }
        [StringLength(2147483647)]
        public string? ApliProducto { get; set; }
        public bool? Flejar { get; set; }
        public double? Sup { get; set; }
        public DateTime? HoraComienzoEstimada { get; set; }
        public DateTime? HoraFinEstimada { get; set; }
        [StringLength(2147483647)]
        public string? TiposCambio { get; set; }
        public DateTime? RequeridoEnFecha { get; set; }
        [StringLength(2147483647)]
        public string? PosEscuadra { get; set; }
        public int? Idproducto { get; set; }
        public double? Peso { get; set; }
        public double? BarnizNecesario { get; set; }
        public bool? Tuberia { get; set; }
        [StringLength(2147483647)]
        public string? ObsCalidad { get; set; }
        public int? HojasAprocesar { get; set; }
        [StringLength(2147483647)]
        public string? CalleApq { get; set; }
        [StringLength(2147483647)]
        public string? CaractHjlta { get; set; }
        [StringLength(2147483647)]
        public string? MuestraSh { get; set; }
        [StringLength(2147483647)]
        public string? TipoLavada { get; set; }
        public bool? Urgente { get; set; }
        [StringLength(2147483647)]
        public string? Obspaseposterior { get; set; }
        [StringLength(2147483647)]
        public string? Observaciones { get; set; }
        [StringLength(2147483647)]
        public string? ObsAlmacen { get; set; }
        [StringLength(2147483647)]
        public string? TextoEstadoCodApli { get; set; }
        [StringLength(50)]
        public string? T01Ext { get; set; }
        [StringLength(50)]
        public string? T02Ext { get; set; }
        [StringLength(50)]
        public string? T03Ext { get; set; }
        [StringLength(50)]
        public string? T04Ext { get; set; }
        [StringLength(50)]
        public string? T05Ext { get; set; }
        [StringLength(50)]
        public string? T06Ext { get; set; }
        [StringLength(50)]
        public string? T07Ext { get; set; }
        [StringLength(50)]
        public string? T08Ext { get; set; }
        [StringLength(50)]
        public string? T09Ext { get; set; }
        [StringLength(50)]
        public string? T10Ext { get; set; }
        [StringLength(50)]
        public string? T11Ext { get; set; }
        [StringLength(50)]
        public string? T01Int { get; set; }
        [StringLength(50)]
        public string? T02Int { get; set; }
        [StringLength(50)]
        public string? T03Int { get; set; }
        [StringLength(50)]
        public string? T04Int { get; set; }
        [StringLength(50)]
        public string? FT01Ext { get; set; }
        [StringLength(50)]
        public string? FT02Ext { get; set; }
        [StringLength(50)]
        public string? FT03Ext { get; set; }
        [StringLength(50)]
        public string? FT04Ext { get; set; }
        [StringLength(50)]
        public string? FT05Ext { get; set; }
        [StringLength(50)]
        public string? FT06Ext { get; set; }
        [StringLength(50)]
        public string? FT07Ext { get; set; }
        [StringLength(50)]
        public string? FT08Ext { get; set; }
        [StringLength(50)]
        public string? FT09Ext { get; set; }
        [StringLength(50)]
        public string? FT10Ext { get; set; }
        [StringLength(50)]
        public string? FT11Ext { get; set; }
        public int? MinHojas { get; set; }
        public int? ApliSimul { get; set; }
        [StringLength(2147483647)]
        public string? FormaFlejado { get; set; }
        public bool? Maculas { get; set; }
        [StringLength(2147483647)]
        public string? Motivo { get; set; }
        [StringLength(2147483647)]
        public string? CogerDe { get; set; }
        [StringLength(2147483647)]
        public string? Escuadra { get; set; }
        [StringLength(50)]
        public string? TipoPedido { get; set; }
        public string NombreReporte { get; set; }
        // OTRAS PROPS
        public bool? MarcadoVerde { get; set; }
        public string? Rodillo { get; set; }
        public string? NotaJefeTurno { get; set; }
        public long? IdUltimoEstado { get; set; }
        public int? IdEstado { get; set; }
        public string? NombreEstado { get; set; }
    }
}