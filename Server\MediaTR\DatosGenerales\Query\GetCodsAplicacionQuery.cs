﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.DatosGenerales.Query
{
    public class GetCodsAplicacionQuery : IRequest<ListResult<CodigoAplicacionDTO>>
    {
        public string Tipo { get; }

        public GetCodsAplicacionQuery( string tipo)
        {
            Tipo = tipo;
        }
    }

    public class GetCodsAplicacionQueryHandler : IRequestHandler<GetCodsAplicacionQuery, ListResult<CodigoAplicacionDTO>>
    {
        private readonly ProgramadorLitalsaContext _contextProg;
        public GetCodsAplicacionQueryHandler(ProgramadorLitalsaContext contextProg)
        {
            _contextProg = contextProg;
        }
        public async Task<ListResult<CodigoAplicacionDTO>> Handle(GetCodsAplicacionQuery request, CancellationToken cancellationToken)
        {
            var result = new ListResult<CodigoAplicacionDTO>()
            {
                Data = new List<CodigoAplicacionDTO>(),
                Errors = new List<string>()
            };
            try
            {
                var query = _contextProg.Codapli.AsQueryable();
                switch (request.Tipo)
                {
                    case "B":
                        query = query.Where(o => o.Codbaz >= 1000).AsQueryable();
                        break;
                    case "L":
                        query = query.Where(o => o.Codbaz < 1000).AsQueryable();
                        break;
                    case "T":
                        break;
                }

                var resQuery = await query.OrderBy(o=>o.Codbaz).ToListAsync(cancellationToken);

                if (!resQuery.Any())
                {
                    result.Errors.Add("No se han encontrado los datos de Códigos de Aplicación");
                }
                else
                {
                    result.Data = resQuery.Select(s => new CodigoAplicacionDTO
                    {
                        Idcodigoaplicacion = s.Codbaz,
                        Descripcion = s.Txtbaz,
                        CodBarniz = s.Codbarniz ?? 0
                    }).ToList();
                }

                return result;
            }
            catch (Exception e)
            {
                var errorText = $"ERROR: GetCodsAplicacionQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
                throw new Exception(errorText, e);
            }

        }
    }
}
