﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Pedidos.Command;

public class UpdateDatosPedidoCommand : IRequest<SingleResult<int>>
{
    public UpdateDatosPedidoCommand(PedidoProcesadoDTO datosPedido)
    {
        DatosPedido = datosPedido;
    }

    public PedidoProcesadoDTO DatosPedido { get; set; }
}

public class UpdateDatosPedidoCommandHandler : IRequestHandler<UpdateDatosPedidoCommand, SingleResult<int>>
{
    private readonly ProgramadorLitalsaContext _programadorLitalsaContext;
    public UpdateDatosPedidoCommandHandler(ProgramadorLitalsaContext programadorLitalsaContext)
    {
        _programadorLitalsaContext = programadorLitalsaContext;
    }

    public async Task<SingleResult<int>> Handle(UpdateDatosPedidoCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<int> { Errors = new List<string>(), Data = 0 };
        try
        {
            var pedido = request.DatosPedido;
            var currentPedido = _programadorLitalsaContext.PedidoProcesado.FirstOrDefault(o => o.IdPedido == pedido.IdPedido);
            if (pedido.RequeridoEnFecha == null && pedido.RequeridoEnFecha != currentPedido.RequeridoEnFecha)
            {
                pedido.ObsACliente += $"FECHA ELIMINADA EL {DateTime.Today:dd/MM} ";
            }
            else if (pedido.RequeridoEnFecha != null && pedido.RequeridoEnFecha != currentPedido.RequeridoEnFecha)
            {
                pedido.ObsACliente += $"FECHA ACTUALIZADA EL {DateTime.Today:dd/MM} A {pedido.RequeridoEnFecha.Value.Date:dd/MM} ";
            }
            if (pedido.ActualizacionFechaRequerida == null && pedido.ActualizacionFechaRequerida != currentPedido.ActualizacionFechaRequerida)
            {
                pedido.ObsACliente += $"FECHA ELIMINADA EL {DateTime.Today:dd/MM} ";
            }
            else if (pedido.ActualizacionFechaRequerida != null && pedido.ActualizacionFechaRequerida != currentPedido.ActualizacionFechaRequerida)
            {
                pedido.ObsACliente += $"FECHA ACTUALIZADA EL {DateTime.Today:dd/MM} A {pedido.RequeridoEnFecha.Value.Date:dd/MM} ";
            }
            _programadorLitalsaContext.Entry(currentPedido).CurrentValues.SetValues(pedido);
            //20240712 - Añadido el guardar los datos en tabla auxiliar para combatir el pisado de datos cuando se actualizan los datos
            //en IN2 mientras se está haciendo un update del programador
            var dato = await _programadorLitalsaContext.ControlDatosActualizables
                .FirstOrDefaultAsync(o => o.IdPedido == pedido.IdPedido, cancellationToken);

            dato ??= new ControlDatosActualizables { IdPedido = pedido.IdPedido.Value };

            dato.FechaEntrega = pedido.RequeridoEnFecha;
            dato.Urgente = pedido.Urgente;
            dato.MantenerAbierto = pedido.MantenerAbierto;
            dato.Cerrado = pedido.Anulado;
            dato.NoIncluirListado = pedido.NoIncluirEnListado;
            dato.ObsProg = pedido.ObsProgramacion;
            dato.ObsCliente = pedido.ObsACliente;
            dato.FechaCierrePedido = pedido.FechaFin;
            dato.Maquina = pedido.LineaTintas;

            if (dato.Id == 0)
            {
                dato.FechaCreacion = DateTime.Now;
                await _programadorLitalsaContext.ControlDatosActualizables.AddAsync(dato, cancellationToken);
            }
            else
            {
                dato.FechaModificacion = DateTime.Now;
                _programadorLitalsaContext.ControlDatosActualizables.Update(dato);
            }

            var dbResult = await _programadorLitalsaContext.SaveChangesAsync(cancellationToken);
            result.Data = dbResult;
        }
        catch (Exception e)
        {
            var error = $"ERROR: UpdateDatosPedidoCommand - {e.Message}--{(!string.IsNullOrWhiteSpace(e.InnerException?.Message) ? e.InnerException : string.Empty)}";
            result.Errors.Add(error);
        }
        return result;
    }
}