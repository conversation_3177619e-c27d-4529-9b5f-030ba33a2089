﻿@using ProgramadorGeneralBLZ.Shared
@inject SpinnerService _spinnerService

<style>
    .loading-container {
        z-index: 5000;
        width: 100vw;
        height: 100vh;
    }
    .loading-container i {
        color: white;
    }
    .loading-icon {
        position: fixed;
        top: 30%;
        left: 50%;
        margin-left: -55px; /* Negative half of width. */
    }
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        background: rgba(0,0,0,0.8);
        z-index: 4000;
        width: 100%;
        height: 100%;
    }
    @@-webkit-keyframes infiniteRotate {
        0% { -webkit-transform: rotate(0deg); }
        100% { -webkit-transform: rotate(360deg); }
    }
    /* Standard syntax */
    @@keyframes infinite-rotate {
        0% { -webkit-transform: rotate(0deg); }
        100% { -webkit-transform: rotate(360deg); }
    }
    .spin {
        -webkit-animation: infiniteRotate 2s linear infinite; /* Safari */
        animation: infiniteRotate 2s linear infinite;
    }
</style>
@if (isVisible)
{

    <div class="loading-overlay">
        <div class="loading-container">
            <svg color="white" width="100" height="100" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="circle-notch" class="spin loading-icon" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M288 39.056v16.659c0 10.804 7.281 20.159 17.686 23.066C383.204 100.***********.518 440 256c0 101.689-82.*********** 184-101.689 0-184-82.*********** 0-84.47 56.786-155.564 134.312-177.219C216.719 75.874 224 66.517 224 55.712V39.064c0-15.709-14.834-27.153-30.046-23.234C86.603 43.482 7.394 141.206 8.003 257.332c.72 137.052 111.477 246.956 248.531 246.667C393.255 503.711 504 392.788 504 256c0-115.633-79.14-212.779-186.211-240.236C302.678 11.889 288 23.456 288 39.056z"></path></svg>
        </div>
    </div>
}
@code
{
    protected bool isVisible { get; set; }

    protected override void OnInitialized()
    {
        _spinnerService.OnShow += ShowSpinner;
        _spinnerService.OnHide += HideSpinner;
    }

    public void ShowSpinner()
    {
        isVisible = true;
        StateHasChanged();
    }

    public void HideSpinner()
    {
        isVisible = false;
        StateHasChanged();
    }
}