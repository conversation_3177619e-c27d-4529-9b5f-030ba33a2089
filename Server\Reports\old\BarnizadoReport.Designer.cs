﻿namespace ProgramadorGeneralBLZ.Server.Reports
{
    partial class BarnizadoReport
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraReports.UI.XRSummary xrSummary1 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary2 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary3 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary4 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary5 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary6 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary7 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary8 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary9 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary10 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary11 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary12 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary13 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary14 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary15 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary16 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary17 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary18 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary19 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary20 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary21 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary22 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary23 = new DevExpress.XtraReports.UI.XRSummary();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(BarnizadoReport));
            DevExpress.XtraReports.UI.XRSummary xrSummary24 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary25 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary26 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary27 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary28 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary29 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary30 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary31 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary32 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary33 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.DataAccess.Sql.SelectQuery selectQuery1 = new DevExpress.DataAccess.Sql.SelectQuery();
            DevExpress.DataAccess.Sql.Column column1 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression1 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Table table1 = new DevExpress.DataAccess.Sql.Table();
            DevExpress.DataAccess.Sql.Column column2 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression2 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column3 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression3 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column4 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression4 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column5 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression5 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column6 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression6 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column7 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression7 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column8 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression8 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column9 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression9 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column10 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression10 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column11 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression11 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column12 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression12 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column13 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression13 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column14 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression14 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column15 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression15 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column16 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression16 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column17 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression17 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column18 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression18 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column19 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression19 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column20 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression20 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column21 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression21 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column22 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression22 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column23 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression23 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column24 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression24 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column25 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression25 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column26 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression26 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column27 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression27 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column28 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression28 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column29 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression29 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column30 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression30 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column31 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression31 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column32 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression32 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column33 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression33 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column34 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression34 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column35 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression35 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column36 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression36 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column37 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression37 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column38 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression38 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column39 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression39 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column40 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression40 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column41 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression41 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.SelectQuery selectQuery2 = new DevExpress.DataAccess.Sql.SelectQuery();
            DevExpress.DataAccess.Sql.Column column42 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression42 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Table table2 = new DevExpress.DataAccess.Sql.Table();
            DevExpress.DataAccess.Sql.Column column43 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression43 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column44 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression44 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column45 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression45 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column46 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression46 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column47 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression47 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.SelectQuery selectQuery3 = new DevExpress.DataAccess.Sql.SelectQuery();
            DevExpress.DataAccess.Sql.Column column48 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression48 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Table table3 = new DevExpress.DataAccess.Sql.Table();
            DevExpress.DataAccess.Sql.Column column49 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression49 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column50 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression50 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column51 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression51 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column52 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression52 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column53 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression53 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column54 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression54 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column55 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression55 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column56 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression56 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column57 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression57 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column58 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression58 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column59 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression59 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column60 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression60 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column61 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression61 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column62 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression62 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column63 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression63 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column64 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression64 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column65 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression65 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column66 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression66 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column67 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression67 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column68 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression68 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column69 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression69 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column70 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression70 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column71 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression71 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column72 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression72 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column73 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression73 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column74 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression74 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column75 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression75 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column76 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression76 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column77 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression77 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column78 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression78 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column79 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression79 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column80 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression80 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.SelectQuery selectQuery4 = new DevExpress.DataAccess.Sql.SelectQuery();
            DevExpress.DataAccess.Sql.Column column81 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression81 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Table table4 = new DevExpress.DataAccess.Sql.Table();
            DevExpress.DataAccess.Sql.Column column82 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression82 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column83 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression83 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column84 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression84 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column85 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression85 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column86 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression86 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column87 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression87 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column88 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression88 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column89 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression89 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column90 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression90 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column91 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression91 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column92 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression92 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column93 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression93 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column94 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression94 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column95 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression95 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column96 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression96 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column97 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression97 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column98 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression98 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column99 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression99 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column100 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression100 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column101 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression101 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column102 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression102 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column103 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression103 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column104 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression104 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column105 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression105 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column106 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression106 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column107 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression107 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column108 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression108 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column109 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression109 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column110 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression110 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column111 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression111 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column112 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression112 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column113 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression113 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column114 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression114 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column115 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression115 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column116 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression116 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column117 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression117 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column118 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression118 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column119 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression119 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column120 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression120 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column121 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression121 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column122 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression122 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column123 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression123 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column124 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression124 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column125 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression125 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column126 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression126 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column127 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression127 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column128 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression128 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column129 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression129 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column130 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression130 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column131 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression131 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column132 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression132 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column133 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression133 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column134 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression134 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column135 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression135 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column136 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression136 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column137 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression137 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column138 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression138 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column139 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression139 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column140 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression140 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column141 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression141 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column142 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression142 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column143 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression143 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column144 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression144 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column145 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression145 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column146 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression146 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column147 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression147 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column148 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression148 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column149 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression149 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column150 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression150 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column151 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression151 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column152 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression152 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column153 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression153 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column154 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression154 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column155 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression155 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column156 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression156 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column157 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression157 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column158 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression158 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column159 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression159 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column160 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression160 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column161 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression161 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column162 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression162 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column163 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression163 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column164 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression164 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column165 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression165 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column166 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression166 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column167 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression167 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column168 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression168 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column169 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression169 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column170 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression170 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column171 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression171 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column172 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression172 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column173 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression173 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column174 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression174 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column175 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression175 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column176 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression176 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column177 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression177 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column178 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression178 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column179 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression179 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column180 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression180 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column181 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression181 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column182 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression182 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column183 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression183 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column184 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression184 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column185 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression185 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column186 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression186 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column187 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression187 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column188 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression188 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column189 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression189 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column190 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression190 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column191 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression191 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column192 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression192 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column193 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression193 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column194 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression194 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column195 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression195 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column196 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression196 = new DevExpress.DataAccess.Sql.ColumnExpression();
            this.Detalle = new DevExpress.XtraReports.UI.DetailBand();
            this.EncabezadoDelInforme = new DevExpress.XtraReports.UI.ReportHeaderBand();
            this.PieDelInforme = new DevExpress.XtraReports.UI.ReportFooterBand();
            this.SecciónEncabezadoDePágina = new DevExpress.XtraReports.UI.PageHeaderBand();
            this.SecciónPieDePágina = new DevExpress.XtraReports.UI.PageFooterBand();
            this.EncabezadoDelGrupo2 = new DevExpress.XtraReports.UI.GroupHeaderBand();
            this.groupHeaderBand1 = new DevExpress.XtraReports.UI.GroupHeaderBand();
            this.groupHeaderBand2 = new DevExpress.XtraReports.UI.GroupHeaderBand();
            this.EncabezadoDelGrupo1 = new DevExpress.XtraReports.UI.GroupHeaderBand();
            this.PieDelGrupo0 = new DevExpress.XtraReports.UI.GroupFooterBand();
            this.groupHeaderBand3 = new DevExpress.XtraReports.UI.GroupHeaderBand();
            this.groupHeaderBand4 = new DevExpress.XtraReports.UI.GroupHeaderBand();
            this.Posicion = new DevExpress.XtraReports.UI.XRLabel();
            this.Idpedido = new DevExpress.XtraReports.UI.XRLabel();
            this.IdCliente = new DevExpress.XtraReports.UI.XRLabel();
            this.Formato = new DevExpress.XtraReports.UI.XRLabel();
            this.Plano = new DevExpress.XtraReports.UI.XRLabel();
            this.Texto49 = new DevExpress.XtraReports.UI.XRLabel();
            this.Línea55 = new DevExpress.XtraReports.UI.XRLine();
            this.txt_flejar = new DevExpress.XtraReports.UI.XRLabel();
            this.txt_HoraComienzoEstimada = new DevExpress.XtraReports.UI.XRLabel();
            this.txt_HoraFinEstimada = new DevExpress.XtraReports.UI.XRLabel();
            this.TiposCambio = new DevExpress.XtraReports.UI.XRLabel();
            this.Etiqueta97 = new DevExpress.XtraReports.UI.XRLabel();
            this.txt_estado_pedido = new DevExpress.XtraReports.UI.XRLabel();
            this.RequeridoEnFecha = new DevExpress.XtraReports.UI.XRLabel();
            this.Texto102 = new DevExpress.XtraReports.UI.XRLabel();
            this.pos_escuadra = new DevExpress.XtraReports.UI.XRLabel();
            this.Texto113 = new DevExpress.XtraReports.UI.XRLabel();
            this.Cuadro116 = new DevExpress.XtraReports.UI.XRPanel();
            this.txt_barniz_por_pedido = new DevExpress.XtraReports.UI.XRLabel();
            this.ObsCalidad = new DevExpress.XtraReports.UI.XRLabel();
            this.Línea129 = new DevExpress.XtraReports.UI.XRLine();
            this.txt_muestra_SH = new DevExpress.XtraReports.UI.XRLabel();
            this.TxtHojas = new DevExpress.XtraReports.UI.XRLabel();
            this.Etiqueta144 = new DevExpress.XtraReports.UI.XRLabel();
            this.TEXTBOX4 = new DevExpress.XtraReports.UI.XRLabel();
            this.Texto164 = new DevExpress.XtraReports.UI.XRLabel();
            this.mx_posicion = new DevExpress.XtraReports.UI.XRLabel();
            this.mn_posicion = new DevExpress.XtraReports.UI.XRLabel();
            this.Línea44 = new DevExpress.XtraReports.UI.XRLine();
            this.Etiqueta45 = new DevExpress.XtraReports.UI.XRLabel();
            this.Etiqueta46 = new DevExpress.XtraReports.UI.XRLabel();
            this.Texto31 = new DevExpress.XtraReports.UI.XRLabel();
            this.Etiqueta47 = new DevExpress.XtraReports.UI.XRLabel();
            this.Línea62 = new DevExpress.XtraReports.UI.XRLine();
            this.Etiqueta132 = new DevExpress.XtraReports.UI.XRLabel();
            this.Texto136 = new DevExpress.XtraReports.UI.XRLabel();
            this.Texto149 = new DevExpress.XtraReports.UI.XRLabel();
            this.Etiqueta80 = new DevExpress.XtraReports.UI.XRLabel();
            this.Línea81 = new DevExpress.XtraReports.UI.XRLine();
            this.Etiqueta82 = new DevExpress.XtraReports.UI.XRLabel();
            this.Etiqueta83 = new DevExpress.XtraReports.UI.XRLabel();
            this.Etiqueta84 = new DevExpress.XtraReports.UI.XRLabel();
            this.Etiqueta85 = new DevExpress.XtraReports.UI.XRLabel();
            this.Etiqueta86 = new DevExpress.XtraReports.UI.XRLabel();
            this.Línea87 = new DevExpress.XtraReports.UI.XRLine();
            this.Línea88 = new DevExpress.XtraReports.UI.XRLine();
            this.xrLabel2 = new DevExpress.XtraReports.UI.XRLabel();
            this.txt_tipo_limpieza = new DevExpress.XtraReports.UI.XRLabel();
            this.Línea128 = new DevExpress.XtraReports.UI.XRLine();
            this.Idproducto = new DevExpress.XtraReports.UI.XRLabel();
            this.Etiqueta155 = new DevExpress.XtraReports.UI.XRLabel();
            this.txt_sup_total = new DevExpress.XtraReports.UI.XRLabel();
            this.Texto107 = new DevExpress.XtraReports.UI.XRLabel();
            this.Texto126 = new DevExpress.XtraReports.UI.XRLabel();
            this.Línea145 = new DevExpress.XtraReports.UI.XRLine();
            this.Línea130 = new DevExpress.XtraReports.UI.XRLine();
            this.Texto147 = new DevExpress.XtraReports.UI.XRLabel();
            this.Texto148 = new DevExpress.XtraReports.UI.XRLabel();
            this.sqlDataSource1 = new DevExpress.DataAccess.Sql.SqlDataSource(this.components);
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // Detalle
            // 
            this.Detalle.BackColor = System.Drawing.Color.Transparent;
            this.Detalle.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.Posicion,
            this.Idpedido,
            this.IdCliente,
            this.Formato,
            this.Plano,
            this.Texto49,
            this.Línea55,
            this.txt_flejar,
            this.txt_HoraComienzoEstimada,
            this.txt_HoraFinEstimada,
            this.TiposCambio,
            this.Etiqueta97,
            this.txt_estado_pedido,
            this.RequeridoEnFecha,
            this.Texto102,
            this.pos_escuadra,
            this.Texto113,
            this.Cuadro116,
            this.txt_barniz_por_pedido,
            this.ObsCalidad,
            this.Línea129,
            this.txt_muestra_SH,
            this.TxtHojas,
            this.Etiqueta144,
            this.TEXTBOX4,
            this.Texto164});
            this.Detalle.HeightF = 75F;
            this.Detalle.Name = "Detalle";
            // 
            // EncabezadoDelInforme
            // 
            this.EncabezadoDelInforme.BackColor = System.Drawing.Color.Transparent;
            this.EncabezadoDelInforme.HeightF = 0F;
            this.EncabezadoDelInforme.Name = "EncabezadoDelInforme";
            // 
            // PieDelInforme
            // 
            this.PieDelInforme.BackColor = System.Drawing.Color.Transparent;
            this.PieDelInforme.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.mx_posicion,
            this.mn_posicion});
            this.PieDelInforme.HeightF = 23F;
            this.PieDelInforme.Name = "PieDelInforme";
            // 
            // SecciónEncabezadoDePágina
            // 
            this.SecciónEncabezadoDePágina.BackColor = System.Drawing.Color.Transparent;
            this.SecciónEncabezadoDePágina.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.Línea44,
            this.Etiqueta45,
            this.Etiqueta46,
            this.Texto31,
            this.Etiqueta47,
            this.Línea62,
            this.Etiqueta132,
            this.Texto136,
            this.Texto149});
            this.SecciónEncabezadoDePágina.HeightF = 50F;
            this.SecciónEncabezadoDePágina.Name = "SecciónEncabezadoDePágina";
            // 
            // SecciónPieDePágina
            // 
            this.SecciónPieDePágina.BackColor = System.Drawing.Color.Transparent;
            this.SecciónPieDePágina.HeightF = 0F;
            this.SecciónPieDePágina.Name = "SecciónPieDePágina";
            // 
            // EncabezadoDelGrupo2
            // 
            this.EncabezadoDelGrupo2.BackColor = System.Drawing.Color.Transparent;
            this.EncabezadoDelGrupo2.GroupFields.AddRange(new DevExpress.XtraReports.UI.GroupField[] {
            new DevExpress.XtraReports.UI.GroupField("Idlinea", DevExpress.XtraReports.UI.XRColumnSortOrder.Ascending)});
            this.EncabezadoDelGrupo2.HeightF = 0F;
            this.EncabezadoDelGrupo2.Level = 5;
            this.EncabezadoDelGrupo2.Name = "EncabezadoDelGrupo2";
            // 
            // groupHeaderBand1
            // 
            this.groupHeaderBand1.GroupFields.AddRange(new DevExpress.XtraReports.UI.GroupField[] {
            new DevExpress.XtraReports.UI.GroupField("Orden", DevExpress.XtraReports.UI.XRColumnSortOrder.Ascending)});
            this.groupHeaderBand1.HeightF = 0F;
            this.groupHeaderBand1.Level = 4;
            this.groupHeaderBand1.Name = "groupHeaderBand1";
            // 
            // groupHeaderBand2
            // 
            this.groupHeaderBand2.GroupFields.AddRange(new DevExpress.XtraReports.UI.GroupField[] {
            new DevExpress.XtraReports.UI.GroupField("Apli+Producto", DevExpress.XtraReports.UI.XRColumnSortOrder.Ascending)});
            this.groupHeaderBand2.HeightF = 0F;
            this.groupHeaderBand2.Level = 3;
            this.groupHeaderBand2.Name = "groupHeaderBand2";
            // 
            // EncabezadoDelGrupo1
            // 
            this.EncabezadoDelGrupo1.BackColor = System.Drawing.Color.Transparent;
            this.EncabezadoDelGrupo1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.Etiqueta80,
            this.Línea81,
            this.Etiqueta82,
            this.Etiqueta83,
            this.Etiqueta84,
            this.Etiqueta85,
            this.Etiqueta86,
            this.Línea87,
            this.Línea88,
            this.xrLabel2,
            this.txt_tipo_limpieza,
            this.Línea128,
            this.Idproducto,
            this.Etiqueta155});
            this.EncabezadoDelGrupo1.GroupFields.AddRange(new DevExpress.XtraReports.UI.GroupField[] {
            new DevExpress.XtraReports.UI.GroupField("Idaplicacion", DevExpress.XtraReports.UI.XRColumnSortOrder.Ascending)});
            this.EncabezadoDelGrupo1.HeightF = 69F;
            this.EncabezadoDelGrupo1.Level = 2;
            this.EncabezadoDelGrupo1.Name = "EncabezadoDelGrupo1";
            // 
            // PieDelGrupo0
            // 
            this.PieDelGrupo0.BackColor = System.Drawing.Color.Transparent;
            this.PieDelGrupo0.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.txt_sup_total,
            this.Texto107,
            this.Texto126,
            this.Línea145,
            this.Línea130,
            this.Texto147,
            this.Texto148});
            this.PieDelGrupo0.HeightF = 23F;
            this.PieDelGrupo0.Name = "PieDelGrupo0";
            // 
            // groupHeaderBand3
            // 
            this.groupHeaderBand3.GroupFields.AddRange(new DevExpress.XtraReports.UI.GroupField[] {
            new DevExpress.XtraReports.UI.GroupField("Posicion", DevExpress.XtraReports.UI.XRColumnSortOrder.Ascending)});
            this.groupHeaderBand3.HeightF = 0F;
            this.groupHeaderBand3.Level = 1;
            this.groupHeaderBand3.Name = "groupHeaderBand3";
            // 
            // groupHeaderBand4
            // 
            this.groupHeaderBand4.GroupFields.AddRange(new DevExpress.XtraReports.UI.GroupField[] {
            new DevExpress.XtraReports.UI.GroupField("Idproducto", DevExpress.XtraReports.UI.XRColumnSortOrder.Ascending)});
            this.groupHeaderBand4.HeightF = 0F;
            this.groupHeaderBand4.Name = "groupHeaderBand4";
            // 
            // Posicion
            // 
            this.Posicion.BackColor = System.Drawing.Color.Transparent;
            this.Posicion.BorderColor = System.Drawing.Color.Black;
            this.Posicion.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Posicion.BorderWidth = 1F;
            this.Posicion.CanGrow = false;
            this.Posicion.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Posicion]")});
            this.Posicion.Font = new DevExpress.Drawing.DXFont("Arial", 7F);
            this.Posicion.LocationFloat = new DevExpress.Utils.PointFloat(1035F, 0F);
            this.Posicion.Name = "Posicion";
            this.Posicion.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Posicion.SizeF = new System.Drawing.SizeF(43F, 19F);
            xrSummary1.FormatString = "{0}";
            this.Posicion.Summary = xrSummary1;
            this.Posicion.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight;
            this.Posicion.TextFormatString = "{0}";
            // 
            // Idpedido
            // 
            this.Idpedido.BackColor = System.Drawing.Color.Transparent;
            this.Idpedido.BorderColor = System.Drawing.Color.Black;
            this.Idpedido.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Idpedido.BorderWidth = 1F;
            this.Idpedido.CanGrow = false;
            this.Idpedido.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Idpedido]")});
            this.Idpedido.Font = new DevExpress.Drawing.DXFont("Arial", 10F);
            this.Idpedido.LocationFloat = new DevExpress.Utils.PointFloat(441F, 2F);
            this.Idpedido.Name = "Idpedido";
            this.Idpedido.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Idpedido.SizeF = new System.Drawing.SizeF(79F, 20F);
            xrSummary2.FormatString = "{0:00-0-0000}";
            this.Idpedido.Summary = xrSummary2;
            this.Idpedido.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            this.Idpedido.TextFormatString = "{0:00-0-0000}";
            // 
            // IdCliente
            // 
            this.IdCliente.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[IdCliente]")});
            this.IdCliente.LocationFloat = new DevExpress.Utils.PointFloat(0F, 2F);
            this.IdCliente.Name = "IdCliente";
            this.IdCliente.SizeF = new System.Drawing.SizeF(135.75F, 23.00002F);
            this.IdCliente.Text = "The \'ComboBoxClass\' control is not supported.";
            // 
            // Formato
            // 
            this.Formato.BackColor = System.Drawing.Color.Transparent;
            this.Formato.BorderColor = System.Drawing.Color.Black;
            this.Formato.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Formato.BorderWidth = 1F;
            this.Formato.CanGrow = false;
            this.Formato.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Formato]")});
            this.Formato.Font = new DevExpress.Drawing.DXFont("Arial", 10F);
            this.Formato.LocationFloat = new DevExpress.Utils.PointFloat(520F, 1.999982F);
            this.Formato.Name = "Formato";
            this.Formato.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Formato.SizeF = new System.Drawing.SizeF(52F, 19F);
            xrSummary3.FormatString = "{0}";
            this.Formato.Summary = xrSummary3;
            this.Formato.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            this.Formato.TextFormatString = "{0}";
            // 
            // Plano
            // 
            this.Plano.BackColor = System.Drawing.Color.Transparent;
            this.Plano.BorderColor = System.Drawing.Color.Black;
            this.Plano.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Plano.BorderWidth = 1F;
            this.Plano.CanGrow = false;
            this.Plano.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Plano]")});
            this.Plano.Font = new DevExpress.Drawing.DXFont("Arial", 10F);
            this.Plano.LocationFloat = new DevExpress.Utils.PointFloat(244F, 24F);
            this.Plano.Name = "Plano";
            this.Plano.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Plano.SizeF = new System.Drawing.SizeF(149F, 18F);
            xrSummary4.FormatString = "{0}";
            this.Plano.Summary = xrSummary4;
            this.Plano.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            this.Plano.TextFormatString = "{0}";
            // 
            // Texto49
            // 
            this.Texto49.BackColor = System.Drawing.Color.Transparent;
            this.Texto49.BorderColor = System.Drawing.Color.Black;
            this.Texto49.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Texto49.BorderWidth = 1F;
            this.Texto49.CanGrow = false;
            this.Texto49.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[CaractHjlta]")});
            this.Texto49.Font = new DevExpress.Drawing.DXFont("Arial", 10F);
            this.Texto49.LocationFloat = new DevExpress.Utils.PointFloat(225F, 4F);
            this.Texto49.Name = "Texto49";
            this.Texto49.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Texto49.SizeF = new System.Drawing.SizeF(213F, 16F);
            xrSummary5.FormatString = "{0}";
            this.Texto49.Summary = xrSummary5;
            this.Texto49.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            this.Texto49.TextFormatString = "{0}";
            // 
            // Línea55
            // 
            this.Línea55.BorderColor = System.Drawing.Color.Navy;
            this.Línea55.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Línea55.BorderWidth = 1F;
            this.Línea55.ForeColor = System.Drawing.Color.Navy;
            this.Línea55.LineStyle = DevExpress.Drawing.DXDashStyle.Dash;
            this.Línea55.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.Línea55.Name = "Línea55";
            this.Línea55.SizeF = new System.Drawing.SizeF(1074F, 2F);
            // 
            // txt_flejar
            // 
            this.txt_flejar.BackColor = System.Drawing.Color.Transparent;
            this.txt_flejar.BorderColor = System.Drawing.Color.Black;
            this.txt_flejar.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.txt_flejar.BorderWidth = 1F;
            this.txt_flejar.CanGrow = false;
            this.txt_flejar.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Iif(True, \'#NOT_SUPPORTED#\', \'=IIf([flejar]=-1,\"SI\",\"NO\")\')")});
            this.txt_flejar.Font = new DevExpress.Drawing.DXFont("Arial", 8F);
            this.txt_flejar.LocationFloat = new DevExpress.Utils.PointFloat(1035F, 12F);
            this.txt_flejar.Name = "txt_flejar";
            this.txt_flejar.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.txt_flejar.SizeF = new System.Drawing.SizeF(32F, 24F);
            xrSummary6.FormatString = "{0}";
            this.txt_flejar.Summary = xrSummary6;
            this.txt_flejar.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // txt_HoraComienzoEstimada
            // 
            this.txt_HoraComienzoEstimada.BackColor = System.Drawing.Color.Transparent;
            this.txt_HoraComienzoEstimada.BorderColor = System.Drawing.Color.Black;
            this.txt_HoraComienzoEstimada.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.txt_HoraComienzoEstimada.BorderWidth = 1F;
            this.txt_HoraComienzoEstimada.CanGrow = false;
            this.txt_HoraComienzoEstimada.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Iif(True, \'#NOT_SUPPORTED#\', \'=Format([HoraComienzoEstimada],\"dd hh:nn\")\')")});
            this.txt_HoraComienzoEstimada.Font = new DevExpress.Drawing.DXFont("Arial", 8F);
            this.txt_HoraComienzoEstimada.LocationFloat = new DevExpress.Utils.PointFloat(342F, 43F);
            this.txt_HoraComienzoEstimada.Name = "txt_HoraComienzoEstimada";
            this.txt_HoraComienzoEstimada.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.txt_HoraComienzoEstimada.SizeF = new System.Drawing.SizeF(79F, 17F);
            xrSummary7.FormatString = "{0:General Date}";
            this.txt_HoraComienzoEstimada.Summary = xrSummary7;
            this.txt_HoraComienzoEstimada.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            this.txt_HoraComienzoEstimada.Visible = false;
            // 
            // txt_HoraFinEstimada
            // 
            this.txt_HoraFinEstimada.BackColor = System.Drawing.Color.Transparent;
            this.txt_HoraFinEstimada.BorderColor = System.Drawing.Color.Black;
            this.txt_HoraFinEstimada.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.txt_HoraFinEstimada.BorderWidth = 1F;
            this.txt_HoraFinEstimada.CanGrow = false;
            this.txt_HoraFinEstimada.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Iif(True, \'#NOT_SUPPORTED#\', \'=Format([HoraFinEstimada],\"dd hh:nn\")\')")});
            this.txt_HoraFinEstimada.Font = new DevExpress.Drawing.DXFont("Arial", 8F);
            this.txt_HoraFinEstimada.LocationFloat = new DevExpress.Utils.PointFloat(461F, 43F);
            this.txt_HoraFinEstimada.Name = "txt_HoraFinEstimada";
            this.txt_HoraFinEstimada.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.txt_HoraFinEstimada.SizeF = new System.Drawing.SizeF(82F, 17F);
            xrSummary8.FormatString = "{0:General Date}";
            this.txt_HoraFinEstimada.Summary = xrSummary8;
            this.txt_HoraFinEstimada.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            this.txt_HoraFinEstimada.Visible = false;
            // 
            // TiposCambio
            // 
            this.TiposCambio.BackColor = System.Drawing.Color.Transparent;
            this.TiposCambio.BorderColor = System.Drawing.Color.Black;
            this.TiposCambio.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.TiposCambio.BorderWidth = 1F;
            this.TiposCambio.CanShrink = true;
            this.TiposCambio.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[TiposCambio]")});
            this.TiposCambio.Font = new DevExpress.Drawing.DXFont("Arial", 8F);
            this.TiposCambio.LocationFloat = new DevExpress.Utils.PointFloat(4F, 47F);
            this.TiposCambio.Name = "TiposCambio";
            this.TiposCambio.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.TiposCambio.SizeF = new System.Drawing.SizeF(450F, 17F);
            xrSummary9.FormatString = "{0}";
            this.TiposCambio.Summary = xrSummary9;
            this.TiposCambio.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            this.TiposCambio.TextFormatString = "{0}";
            this.TiposCambio.Visible = false;
            // 
            // Etiqueta97
            // 
            this.Etiqueta97.BackColor = System.Drawing.Color.Transparent;
            this.Etiqueta97.BorderColor = System.Drawing.Color.Black;
            this.Etiqueta97.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Etiqueta97.BorderWidth = 1F;
            this.Etiqueta97.CanGrow = false;
            this.Etiqueta97.Font = new DevExpress.Drawing.DXFont("Times New Roman", 11F, ((DevExpress.Drawing.DXFontStyle)((DevExpress.Drawing.DXFontStyle.Bold | DevExpress.Drawing.DXFontStyle.Italic))));
            this.Etiqueta97.ForeColor = System.Drawing.Color.Navy;
            this.Etiqueta97.LocationFloat = new DevExpress.Utils.PointFloat(425F, 39F);
            this.Etiqueta97.Name = "Etiqueta97";
            this.Etiqueta97.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Etiqueta97.SizeF = new System.Drawing.SizeF(30F, 24F);
            this.Etiqueta97.Text = "--->";
            this.Etiqueta97.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            this.Etiqueta97.Visible = false;
            // 
            // txt_estado_pedido
            // 
            this.txt_estado_pedido.BackColor = System.Drawing.Color.Transparent;
            this.txt_estado_pedido.BorderColor = System.Drawing.Color.Black;
            this.txt_estado_pedido.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.txt_estado_pedido.BorderWidth = 1F;
            this.txt_estado_pedido.CanGrow = false;
            this.txt_estado_pedido.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Iif(True, \'#NOT_SUPPORTED#\', \'=devuelve_estado_pedido2([Idpedido])\')")});
            this.txt_estado_pedido.Font = new DevExpress.Drawing.DXFont("Arial", 7F);
            this.txt_estado_pedido.LocationFloat = new DevExpress.Utils.PointFloat(122F, 39F);
            this.txt_estado_pedido.Name = "txt_estado_pedido";
            this.txt_estado_pedido.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.txt_estado_pedido.SizeF = new System.Drawing.SizeF(467F, 12F);
            xrSummary10.FormatString = "{0}";
            this.txt_estado_pedido.Summary = xrSummary10;
            this.txt_estado_pedido.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight;
            // 
            // RequeridoEnFecha
            // 
            this.RequeridoEnFecha.BackColor = System.Drawing.Color.Transparent;
            this.RequeridoEnFecha.BorderColor = System.Drawing.Color.Black;
            this.RequeridoEnFecha.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.RequeridoEnFecha.BorderWidth = 1F;
            this.RequeridoEnFecha.CanGrow = false;
            this.RequeridoEnFecha.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[RequeridoEnFecha]")});
            this.RequeridoEnFecha.Font = new DevExpress.Drawing.DXFont("Arial", 10F, DevExpress.Drawing.DXFontStyle.Bold);
            this.RequeridoEnFecha.LocationFloat = new DevExpress.Utils.PointFloat(83F, 28F);
            this.RequeridoEnFecha.Name = "RequeridoEnFecha";
            this.RequeridoEnFecha.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.RequeridoEnFecha.SizeF = new System.Drawing.SizeF(58F, 19F);
            xrSummary11.FormatString = "{0:dd/mm}";
            this.RequeridoEnFecha.Summary = xrSummary11;
            this.RequeridoEnFecha.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            this.RequeridoEnFecha.TextFormatString = "{0:dd/mm}";
            // 
            // Texto102
            // 
            this.Texto102.BackColor = System.Drawing.Color.Transparent;
            this.Texto102.BorderColor = System.Drawing.Color.Black;
            this.Texto102.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Texto102.BorderWidth = 1F;
            this.Texto102.CanGrow = false;
            this.Texto102.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Iif(True, \'#NOT_SUPPORTED#\', \'=Format([HoraFinEstimada],\"dd hh:nn\")\')")});
            this.Texto102.Font = new DevExpress.Drawing.DXFont("Arial", 9F);
            this.Texto102.LocationFloat = new DevExpress.Utils.PointFloat(1028F, 28F);
            this.Texto102.Name = "Texto102";
            this.Texto102.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Texto102.SizeF = new System.Drawing.SizeF(51F, 19F);
            xrSummary12.FormatString = "{0:Fixed}";
            this.Texto102.Summary = xrSummary12;
            this.Texto102.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight;
            // 
            // pos_escuadra
            // 
            this.pos_escuadra.BackColor = System.Drawing.Color.Transparent;
            this.pos_escuadra.BorderColor = System.Drawing.Color.Black;
            this.pos_escuadra.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.pos_escuadra.BorderWidth = 1F;
            this.pos_escuadra.CanGrow = false;
            this.pos_escuadra.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[pos_escuadra]")});
            this.pos_escuadra.Font = new DevExpress.Drawing.DXFont("Arial", 8F);
            this.pos_escuadra.LocationFloat = new DevExpress.Utils.PointFloat(573F, 2F);
            this.pos_escuadra.Name = "pos_escuadra";
            this.pos_escuadra.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.pos_escuadra.SizeF = new System.Drawing.SizeF(16F, 16F);
            xrSummary13.FormatString = "{0}";
            this.pos_escuadra.Summary = xrSummary13;
            this.pos_escuadra.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            this.pos_escuadra.TextFormatString = "{0}";
            this.pos_escuadra.Visible = false;
            // 
            // Texto113
            // 
            this.Texto113.BackColor = System.Drawing.Color.Transparent;
            this.Texto113.BorderColor = System.Drawing.Color.Black;
            this.Texto113.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Texto113.BorderWidth = 1F;
            this.Texto113.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[ObsAlmacen]")});
            this.Texto113.Font = new DevExpress.Drawing.DXFont("Arial Baltic", 7F);
            this.Texto113.LocationFloat = new DevExpress.Utils.PointFloat(914F, 0F);
            this.Texto113.Name = "Texto113";
            this.Texto113.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Texto113.SizeF = new System.Drawing.SizeF(129F, 52F);
            xrSummary14.FormatString = "{0}";
            this.Texto113.Summary = xrSummary14;
            this.Texto113.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            this.Texto113.TextFormatString = "{0}";
            // 
            // Cuadro116
            // 
            this.Cuadro116.BackColor = System.Drawing.Color.Black;
            this.Cuadro116.BorderColor = System.Drawing.Color.Black;
            this.Cuadro116.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.Cuadro116.BorderWidth = 1F;
            this.Cuadro116.LocationFloat = new DevExpress.Utils.PointFloat(416F, 22F);
            this.Cuadro116.Name = "Cuadro116";
            this.Cuadro116.SizeF = new System.Drawing.SizeF(35F, 15F);
            // 
            // txt_barniz_por_pedido
            // 
            this.txt_barniz_por_pedido.BackColor = System.Drawing.Color.Transparent;
            this.txt_barniz_por_pedido.BorderColor = System.Drawing.Color.Black;
            this.txt_barniz_por_pedido.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.txt_barniz_por_pedido.BorderWidth = 1F;
            this.txt_barniz_por_pedido.CanGrow = false;
            this.txt_barniz_por_pedido.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[BarnizNecesario]")});
            this.txt_barniz_por_pedido.Font = new DevExpress.Drawing.DXFont("Arial", 6F);
            this.txt_barniz_por_pedido.LocationFloat = new DevExpress.Utils.PointFloat(996F, 35F);
            this.txt_barniz_por_pedido.Name = "txt_barniz_por_pedido";
            this.txt_barniz_por_pedido.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.txt_barniz_por_pedido.SizeF = new System.Drawing.SizeF(29F, 13F);
            xrSummary15.FormatString = "{0:Fixed}";
            this.txt_barniz_por_pedido.Summary = xrSummary15;
            this.txt_barniz_por_pedido.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight;
            this.txt_barniz_por_pedido.TextFormatString = "{0:Fixed}";
            // 
            // ObsCalidad
            // 
            this.ObsCalidad.BackColor = System.Drawing.Color.Transparent;
            this.ObsCalidad.BorderColor = System.Drawing.Color.Black;
            this.ObsCalidad.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.ObsCalidad.BorderWidth = 1F;
            this.ObsCalidad.CanShrink = true;
            this.ObsCalidad.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Iif(True, \'#NOT_SUPPORTED#\', \'=devuelve_obs_calidad([idpedido],[Idaplicacion],[id" +
                    "linea],[posicion])\')")});
            this.ObsCalidad.Font = new DevExpress.Drawing.DXFont("Arial", 10F);
            this.ObsCalidad.LocationFloat = new DevExpress.Utils.PointFloat(0F, 43F);
            this.ObsCalidad.Name = "ObsCalidad";
            this.ObsCalidad.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.ObsCalidad.SizeF = new System.Drawing.SizeF(442F, 17F);
            xrSummary16.FormatString = "{0}";
            this.ObsCalidad.Summary = xrSummary16;
            this.ObsCalidad.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // Línea129
            // 
            this.Línea129.BorderColor = System.Drawing.Color.Navy;
            this.Línea129.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Línea129.BorderWidth = 1F;
            this.Línea129.ForeColor = System.Drawing.Color.Navy;
            this.Línea129.LineStyle = DevExpress.Drawing.DXDashStyle.Dash;
            this.Línea129.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.Línea129.Name = "Línea129";
            this.Línea129.SizeF = new System.Drawing.SizeF(1074F, 2F);
            // 
            // txt_muestra_SH
            // 
            this.txt_muestra_SH.BackColor = System.Drawing.Color.Transparent;
            this.txt_muestra_SH.BorderColor = System.Drawing.Color.Black;
            this.txt_muestra_SH.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.txt_muestra_SH.BorderWidth = 1F;
            this.txt_muestra_SH.CanGrow = false;
            this.txt_muestra_SH.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[MuestraSH]")});
            this.txt_muestra_SH.Font = new DevExpress.Drawing.DXFont("Arial", 10F);
            this.txt_muestra_SH.LocationFloat = new DevExpress.Utils.PointFloat(362F, 20F);
            this.txt_muestra_SH.Name = "txt_muestra_SH";
            this.txt_muestra_SH.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.txt_muestra_SH.SizeF = new System.Drawing.SizeF(43F, 19F);
            xrSummary17.FormatString = "{0}";
            this.txt_muestra_SH.Summary = xrSummary17;
            this.txt_muestra_SH.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            this.txt_muestra_SH.TextFormatString = "{0}";
            // 
            // TxtHojas
            // 
            this.TxtHojas.BackColor = System.Drawing.Color.Transparent;
            this.TxtHojas.BorderColor = System.Drawing.Color.Black;
            this.TxtHojas.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.TxtHojas.BorderWidth = 1F;
            this.TxtHojas.CanGrow = false;
            this.TxtHojas.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Iif(True, \'#NOT_SUPPORTED#\', \'=IIf(Nz([HojasAProcesar],0)=0,Format([HojasPedido]," +
                    "\"#,##0\"),Format([HojasAProcesar],\"#,#00\") & \" / \" & Format([HojasPedido],\"#,#00\"" +
                    "))\')")});
            this.TxtHojas.Font = new DevExpress.Drawing.DXFont("Arial", 10F);
            this.TxtHojas.LocationFloat = new DevExpress.Utils.PointFloat(157F, 4F);
            this.TxtHojas.Name = "TxtHojas";
            this.TxtHojas.NavigateUrl = ".";
            this.TxtHojas.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.TxtHojas.SizeF = new System.Drawing.SizeF(63F, 39F);
            xrSummary18.FormatString = "{0}";
            this.TxtHojas.Summary = xrSummary18;
            this.TxtHojas.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight;
            // 
            // Etiqueta144
            // 
            this.Etiqueta144.BackColor = System.Drawing.Color.Transparent;
            this.Etiqueta144.BorderColor = System.Drawing.Color.Black;
            this.Etiqueta144.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Etiqueta144.BorderWidth = 1F;
            this.Etiqueta144.CanGrow = false;
            this.Etiqueta144.Font = new DevExpress.Drawing.DXFont("Times New Roman", 8F, DevExpress.Drawing.DXFontStyle.Italic);
            this.Etiqueta144.ForeColor = System.Drawing.Color.Navy;
            this.Etiqueta144.LocationFloat = new DevExpress.Utils.PointFloat(207F, 28F);
            this.Etiqueta144.Name = "Etiqueta144";
            this.Etiqueta144.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Etiqueta144.SizeF = new System.Drawing.SizeF(37F, 16F);
            this.Etiqueta144.Text = "Plano:";
            this.Etiqueta144.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // TEXTBOX4
            // 
            this.TEXTBOX4.BackColor = System.Drawing.Color.Transparent;
            this.TEXTBOX4.BorderColor = System.Drawing.Color.Black;
            this.TEXTBOX4.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.TEXTBOX4.BorderWidth = 1F;
            this.TEXTBOX4.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Iif(True, \'#NOT_SUPPORTED#\', \'=[Observaciones] & Chr$(13) & Chr$(10) & [Obspasepo" +
                    "sterior]\')")});
            this.TEXTBOX4.Font = new DevExpress.Drawing.DXFont("Arial", 8F);
            this.TEXTBOX4.LocationFloat = new DevExpress.Utils.PointFloat(590F, 2F);
            this.TEXTBOX4.Name = "TEXTBOX4";
            this.TEXTBOX4.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.TEXTBOX4.SizeF = new System.Drawing.SizeF(319F, 53F);
            xrSummary19.FormatString = "{0}";
            this.TEXTBOX4.Summary = xrSummary19;
            this.TEXTBOX4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // Texto164
            // 
            this.Texto164.BackColor = System.Drawing.Color.Transparent;
            this.Texto164.BorderColor = System.Drawing.Color.Black;
            this.Texto164.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Texto164.BorderWidth = 1F;
            this.Texto164.CanGrow = false;
            this.Texto164.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Iif(True, \'#NOT_SUPPORTED#\', \'=IIf([Urgente],\"URGENTE:\",\"\")\')")});
            this.Texto164.Font = new DevExpress.Drawing.DXFont("Arial", 10F, DevExpress.Drawing.DXFontStyle.Bold);
            this.Texto164.LocationFloat = new DevExpress.Utils.PointFloat(0F, 28F);
            this.Texto164.Name = "Texto164";
            this.Texto164.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Texto164.SizeF = new System.Drawing.SizeF(94F, 22F);
            xrSummary20.FormatString = "{0}";
            this.Texto164.Summary = xrSummary20;
            this.Texto164.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // mx_posicion
            // 
            this.mx_posicion.BackColor = System.Drawing.Color.Transparent;
            this.mx_posicion.BorderColor = System.Drawing.Color.Black;
            this.mx_posicion.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.mx_posicion.BorderWidth = 1F;
            this.mx_posicion.CanShrink = true;
            this.mx_posicion.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Iif(True, \'#NOT_SUPPORTED#\', \'=Max([Posicion])\')")});
            this.mx_posicion.Font = new DevExpress.Drawing.DXFont("Arial", 8F);
            this.mx_posicion.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.mx_posicion.Name = "mx_posicion";
            this.mx_posicion.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.mx_posicion.SizeF = new System.Drawing.SizeF(102F, 16F);
            xrSummary21.FormatString = "{0}";
            this.mx_posicion.Summary = xrSummary21;
            this.mx_posicion.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            this.mx_posicion.Visible = false;
            // 
            // mn_posicion
            // 
            this.mn_posicion.BackColor = System.Drawing.Color.Transparent;
            this.mn_posicion.BorderColor = System.Drawing.Color.Black;
            this.mn_posicion.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.mn_posicion.BorderWidth = 1F;
            this.mn_posicion.CanShrink = true;
            this.mn_posicion.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Iif(True, \'#NOT_SUPPORTED#\', \'=Min([Posicion])\')")});
            this.mn_posicion.Font = new DevExpress.Drawing.DXFont("Arial", 8F);
            this.mn_posicion.LocationFloat = new DevExpress.Utils.PointFloat(209F, 0F);
            this.mn_posicion.Name = "mn_posicion";
            this.mn_posicion.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.mn_posicion.SizeF = new System.Drawing.SizeF(102F, 16F);
            xrSummary22.FormatString = "{0}";
            this.mn_posicion.Summary = xrSummary22;
            this.mn_posicion.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            this.mn_posicion.Visible = false;
            // 
            // Línea44
            // 
            this.Línea44.BorderColor = System.Drawing.Color.Navy;
            this.Línea44.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Línea44.BorderWidth = 1F;
            this.Línea44.ForeColor = System.Drawing.Color.Navy;
            this.Línea44.LocationFloat = new DevExpress.Utils.PointFloat(0F, 28F);
            this.Línea44.Name = "Línea44";
            this.Línea44.SizeF = new System.Drawing.SizeF(1082F, 2F);
            // 
            // Etiqueta45
            // 
            this.Etiqueta45.BackColor = System.Drawing.Color.Transparent;
            this.Etiqueta45.BorderColor = System.Drawing.Color.Black;
            this.Etiqueta45.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Etiqueta45.BorderWidth = 1F;
            this.Etiqueta45.CanGrow = false;
            this.Etiqueta45.Font = new DevExpress.Drawing.DXFont("Times New Roman", 11F, ((DevExpress.Drawing.DXFontStyle)((DevExpress.Drawing.DXFontStyle.Bold | DevExpress.Drawing.DXFontStyle.Italic))));
            this.Etiqueta45.ForeColor = System.Drawing.Color.Navy;
            this.Etiqueta45.LocationFloat = new DevExpress.Utils.PointFloat(153F, 4F);
            this.Etiqueta45.Name = "Etiqueta45";
            this.Etiqueta45.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Etiqueta45.SizeF = new System.Drawing.SizeF(80F, 21F);
            this.Etiqueta45.Text = "                   Máquina: ";
            this.Etiqueta45.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight;
            // 
            // Etiqueta46
            // 
            this.Etiqueta46.BackColor = System.Drawing.Color.Transparent;
            this.Etiqueta46.BorderColor = System.Drawing.Color.Black;
            this.Etiqueta46.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Etiqueta46.BorderWidth = 1F;
            this.Etiqueta46.CanGrow = false;
            this.Etiqueta46.Font = new DevExpress.Drawing.DXFont("Times New Roman", 11F, ((DevExpress.Drawing.DXFontStyle)((DevExpress.Drawing.DXFontStyle.Bold | DevExpress.Drawing.DXFontStyle.Italic))));
            this.Etiqueta46.ForeColor = System.Drawing.Color.Navy;
            this.Etiqueta46.LocationFloat = new DevExpress.Utils.PointFloat(531F, 4F);
            this.Etiqueta46.Name = "Etiqueta46";
            this.Etiqueta46.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Etiqueta46.SizeF = new System.Drawing.SizeF(67F, 21F);
            this.Etiqueta46.Text = "FP-02              ";
            this.Etiqueta46.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // Texto31
            // 
            this.Texto31.BackColor = System.Drawing.Color.Transparent;
            this.Texto31.BorderColor = System.Drawing.Color.Black;
            this.Texto31.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Texto31.BorderWidth = 1F;
            this.Texto31.CanGrow = false;
            this.Texto31.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Iif(True, \'#NOT_SUPPORTED#\', \'=Now()\')")});
            this.Texto31.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F, ((DevExpress.Drawing.DXFontStyle)((DevExpress.Drawing.DXFontStyle.Bold | DevExpress.Drawing.DXFontStyle.Italic))));
            this.Texto31.LocationFloat = new DevExpress.Utils.PointFloat(917F, 4F);
            this.Texto31.Name = "Texto31";
            this.Texto31.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Texto31.SizeF = new System.Drawing.SizeF(118F, 20F);
            xrSummary23.FormatString = "{0:dd-mm-yy / hh:nn}";
            this.Texto31.Summary = xrSummary23;
            this.Texto31.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // Etiqueta47
            // 
            this.Etiqueta47.BackColor = System.Drawing.Color.Transparent;
            this.Etiqueta47.BorderColor = System.Drawing.Color.Black;
            this.Etiqueta47.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Etiqueta47.BorderWidth = 1F;
            this.Etiqueta47.CanGrow = false;
            this.Etiqueta47.Font = new DevExpress.Drawing.DXFont("Times New Roman", 11F, ((DevExpress.Drawing.DXFontStyle)((DevExpress.Drawing.DXFontStyle.Bold | DevExpress.Drawing.DXFontStyle.Italic))));
            this.Etiqueta47.ForeColor = System.Drawing.Color.Navy;
            this.Etiqueta47.LocationFloat = new DevExpress.Utils.PointFloat(858F, 4F);
            this.Etiqueta47.Name = "Etiqueta47";
            this.Etiqueta47.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Etiqueta47.SizeF = new System.Drawing.SizeF(59F, 21F);
            this.Etiqueta47.Text = "Fecha:";
            this.Etiqueta47.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight;
            // 
            // Línea62
            // 
            this.Línea62.BorderColor = System.Drawing.Color.Navy;
            this.Línea62.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Línea62.BorderWidth = 1F;
            this.Línea62.ForeColor = System.Drawing.Color.Navy;
            this.Línea62.LocationFloat = new DevExpress.Utils.PointFloat(0F, 31F);
            this.Línea62.Name = "Línea62";
            this.Línea62.SizeF = new System.Drawing.SizeF(1083F, 2F);
            // 
            // Etiqueta132
            // 
            this.Etiqueta132.BackColor = System.Drawing.Color.Transparent;
            this.Etiqueta132.BorderColor = System.Drawing.Color.Black;
            this.Etiqueta132.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Etiqueta132.BorderWidth = 1F;
            this.Etiqueta132.CanGrow = false;
            this.Etiqueta132.Font = new DevExpress.Drawing.DXFont("Arial", 14F, ((DevExpress.Drawing.DXFontStyle)((DevExpress.Drawing.DXFontStyle.Bold | DevExpress.Drawing.DXFontStyle.Italic))));
            this.Etiqueta132.ForeColor = System.Drawing.Color.Navy;
            this.Etiqueta132.LocationFloat = new DevExpress.Utils.PointFloat(4F, 0F);
            this.Etiqueta132.Name = "Etiqueta132";
            this.Etiqueta132.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Etiqueta132.SizeF = new System.Drawing.SizeF(137F, 27F);
            this.Etiqueta132.Text = "LITALSA";
            this.Etiqueta132.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // Texto136
            // 
            this.Texto136.BackColor = System.Drawing.Color.Transparent;
            this.Texto136.BorderColor = System.Drawing.Color.Black;
            this.Texto136.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Texto136.BorderWidth = 1F;
            this.Texto136.CanShrink = true;
            this.Texto136.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", resources.GetString("Texto136.ExpressionBindings"))});
            this.Texto136.Font = new DevExpress.Drawing.DXFont("Arial", 7F);
            this.Texto136.LocationFloat = new DevExpress.Utils.PointFloat(665F, 12F);
            this.Texto136.Name = "Texto136";
            this.Texto136.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Texto136.SizeF = new System.Drawing.SizeF(114F, 14F);
            xrSummary24.FormatString = "{0}";
            this.Texto136.Summary = xrSummary24;
            this.Texto136.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // Texto149
            // 
            this.Texto149.BackColor = System.Drawing.Color.Transparent;
            this.Texto149.BorderColor = System.Drawing.Color.Black;
            this.Texto149.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.Texto149.BorderWidth = 1F;
            this.Texto149.CanGrow = false;
            this.Texto149.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Iif(True, \'#NOT_SUPPORTED#\', \'=[Page]+Forms!Frm_programacion_ppal!comienzo_pagina" +
                    "\')")});
            this.Texto149.Font = new DevExpress.Drawing.DXFont("Times New Roman", 12F, ((DevExpress.Drawing.DXFontStyle)((DevExpress.Drawing.DXFontStyle.Bold | DevExpress.Drawing.DXFontStyle.Italic))));
            this.Texto149.LocationFloat = new DevExpress.Utils.PointFloat(1039F, 0F);
            this.Texto149.Name = "Texto149";
            this.Texto149.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Texto149.SizeF = new System.Drawing.SizeF(44F, 24F);
            xrSummary25.FormatString = "{0}";
            this.Texto149.Summary = xrSummary25;
            this.Texto149.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // Etiqueta80
            // 
            this.Etiqueta80.BackColor = System.Drawing.Color.Transparent;
            this.Etiqueta80.BorderColor = System.Drawing.Color.Black;
            this.Etiqueta80.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Etiqueta80.BorderWidth = 1F;
            this.Etiqueta80.CanGrow = false;
            this.Etiqueta80.Font = new DevExpress.Drawing.DXFont("Times New Roman", 11F, ((DevExpress.Drawing.DXFontStyle)((DevExpress.Drawing.DXFontStyle.Bold | DevExpress.Drawing.DXFontStyle.Italic))));
            this.Etiqueta80.ForeColor = System.Drawing.Color.Navy;
            this.Etiqueta80.LocationFloat = new DevExpress.Utils.PointFloat(8F, 39F);
            this.Etiqueta80.Name = "Etiqueta80";
            this.Etiqueta80.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Etiqueta80.SizeF = new System.Drawing.SizeF(94F, 21F);
            this.Etiqueta80.Text = "Cliente";
            this.Etiqueta80.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // Línea81
            // 
            this.Línea81.BorderColor = System.Drawing.Color.Navy;
            this.Línea81.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Línea81.BorderWidth = 1F;
            this.Línea81.ForeColor = System.Drawing.Color.Navy;
            this.Línea81.LocationFloat = new DevExpress.Utils.PointFloat(0F, 35F);
            this.Línea81.Name = "Línea81";
            this.Línea81.SizeF = new System.Drawing.SizeF(1083F, 2F);
            // 
            // Etiqueta82
            // 
            this.Etiqueta82.BackColor = System.Drawing.Color.Transparent;
            this.Etiqueta82.BorderColor = System.Drawing.Color.Black;
            this.Etiqueta82.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Etiqueta82.BorderWidth = 1F;
            this.Etiqueta82.CanGrow = false;
            this.Etiqueta82.Font = new DevExpress.Drawing.DXFont("Times New Roman", 11F, ((DevExpress.Drawing.DXFontStyle)((DevExpress.Drawing.DXFontStyle.Bold | DevExpress.Drawing.DXFontStyle.Italic))));
            this.Etiqueta82.ForeColor = System.Drawing.Color.Navy;
            this.Etiqueta82.LocationFloat = new DevExpress.Utils.PointFloat(157F, 39F);
            this.Etiqueta82.Name = "Etiqueta82";
            this.Etiqueta82.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Etiqueta82.SizeF = new System.Drawing.SizeF(64F, 21F);
            this.Etiqueta82.Text = "Hojas";
            this.Etiqueta82.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight;
            // 
            // Etiqueta83
            // 
            this.Etiqueta83.BackColor = System.Drawing.Color.Transparent;
            this.Etiqueta83.BorderColor = System.Drawing.Color.Black;
            this.Etiqueta83.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Etiqueta83.BorderWidth = 1F;
            this.Etiqueta83.CanGrow = false;
            this.Etiqueta83.Font = new DevExpress.Drawing.DXFont("Times New Roman", 11F, ((DevExpress.Drawing.DXFontStyle)((DevExpress.Drawing.DXFontStyle.Bold | DevExpress.Drawing.DXFontStyle.Italic))));
            this.Etiqueta83.ForeColor = System.Drawing.Color.Navy;
            this.Etiqueta83.LocationFloat = new DevExpress.Utils.PointFloat(236F, 39F);
            this.Etiqueta83.Name = "Etiqueta83";
            this.Etiqueta83.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Etiqueta83.SizeF = new System.Drawing.SizeF(95F, 21F);
            this.Etiqueta83.Text = "Hojalata";
            this.Etiqueta83.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // Etiqueta84
            // 
            this.Etiqueta84.BackColor = System.Drawing.Color.Transparent;
            this.Etiqueta84.BorderColor = System.Drawing.Color.Black;
            this.Etiqueta84.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Etiqueta84.BorderWidth = 1F;
            this.Etiqueta84.CanGrow = false;
            this.Etiqueta84.Font = new DevExpress.Drawing.DXFont("Times New Roman", 11F, ((DevExpress.Drawing.DXFontStyle)((DevExpress.Drawing.DXFontStyle.Bold | DevExpress.Drawing.DXFontStyle.Italic))));
            this.Etiqueta84.ForeColor = System.Drawing.Color.Navy;
            this.Etiqueta84.LocationFloat = new DevExpress.Utils.PointFloat(382F, 39F);
            this.Etiqueta84.Name = "Etiqueta84";
            this.Etiqueta84.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Etiqueta84.SizeF = new System.Drawing.SizeF(63F, 21F);
            this.Etiqueta84.Text = "Pedido";
            this.Etiqueta84.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // Etiqueta85
            // 
            this.Etiqueta85.BackColor = System.Drawing.Color.Transparent;
            this.Etiqueta85.BorderColor = System.Drawing.Color.Black;
            this.Etiqueta85.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Etiqueta85.BorderWidth = 1F;
            this.Etiqueta85.CanGrow = false;
            this.Etiqueta85.Font = new DevExpress.Drawing.DXFont("Times New Roman", 11F, ((DevExpress.Drawing.DXFontStyle)((DevExpress.Drawing.DXFontStyle.Bold | DevExpress.Drawing.DXFontStyle.Italic))));
            this.Etiqueta85.ForeColor = System.Drawing.Color.Navy;
            this.Etiqueta85.LocationFloat = new DevExpress.Utils.PointFloat(520F, 39F);
            this.Etiqueta85.Name = "Etiqueta85";
            this.Etiqueta85.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Etiqueta85.SizeF = new System.Drawing.SizeF(66F, 21F);
            this.Etiqueta85.Text = "Diámetro";
            this.Etiqueta85.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // Etiqueta86
            // 
            this.Etiqueta86.BackColor = System.Drawing.Color.Transparent;
            this.Etiqueta86.BorderColor = System.Drawing.Color.Black;
            this.Etiqueta86.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Etiqueta86.BorderWidth = 1F;
            this.Etiqueta86.CanGrow = false;
            this.Etiqueta86.Font = new DevExpress.Drawing.DXFont("Times New Roman", 11F, ((DevExpress.Drawing.DXFontStyle)((DevExpress.Drawing.DXFontStyle.Bold | DevExpress.Drawing.DXFontStyle.Italic))));
            this.Etiqueta86.ForeColor = System.Drawing.Color.Navy;
            this.Etiqueta86.LocationFloat = new DevExpress.Utils.PointFloat(594F, 39F);
            this.Etiqueta86.Name = "Etiqueta86";
            this.Etiqueta86.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Etiqueta86.SizeF = new System.Drawing.SizeF(134F, 21F);
            this.Etiqueta86.Text = "Observaciones";
            this.Etiqueta86.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // Línea87
            // 
            this.Línea87.BorderColor = System.Drawing.Color.Navy;
            this.Línea87.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Línea87.BorderWidth = 1F;
            this.Línea87.ForeColor = System.Drawing.Color.Navy;
            this.Línea87.LocationFloat = new DevExpress.Utils.PointFloat(0F, 63F);
            this.Línea87.Name = "Línea87";
            this.Línea87.SizeF = new System.Drawing.SizeF(1083F, 2F);
            // 
            // Línea88
            // 
            this.Línea88.BorderColor = System.Drawing.Color.Navy;
            this.Línea88.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Línea88.BorderWidth = 1F;
            this.Línea88.ForeColor = System.Drawing.Color.Navy;
            this.Línea88.LocationFloat = new DevExpress.Utils.PointFloat(0F, 67F);
            this.Línea88.Name = "Línea88";
            this.Línea88.SizeF = new System.Drawing.SizeF(1083F, 2F);
            // 
            // xrLabel2
            // 
            this.xrLabel2.BackColor = System.Drawing.Color.Transparent;
            this.xrLabel2.BorderColor = System.Drawing.Color.Black;
            this.xrLabel2.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel2.BorderWidth = 1F;
            this.xrLabel2.CanGrow = false;
            this.xrLabel2.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Apli+Producto]")});
            this.xrLabel2.Font = new DevExpress.Drawing.DXFont("Arial", 12F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrLabel2.LocationFloat = new DevExpress.Utils.PointFloat(0F, 4F);
            this.xrLabel2.Name = "xrLabel2";
            this.xrLabel2.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.xrLabel2.SizeF = new System.Drawing.SizeF(814F, 27F);
            xrSummary26.FormatString = "{0}";
            this.xrLabel2.Summary = xrSummary26;
            this.xrLabel2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            this.xrLabel2.TextFormatString = "{0}";
            // 
            // txt_tipo_limpieza
            // 
            this.txt_tipo_limpieza.BackColor = System.Drawing.Color.Transparent;
            this.txt_tipo_limpieza.BorderColor = System.Drawing.Color.Black;
            this.txt_tipo_limpieza.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.txt_tipo_limpieza.BorderWidth = 1F;
            this.txt_tipo_limpieza.CanGrow = false;
            this.txt_tipo_limpieza.Font = new DevExpress.Drawing.DXFont("Arial", 14F);
            this.txt_tipo_limpieza.LocationFloat = new DevExpress.Utils.PointFloat(819F, 4F);
            this.txt_tipo_limpieza.Name = "txt_tipo_limpieza";
            this.txt_tipo_limpieza.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.txt_tipo_limpieza.SizeF = new System.Drawing.SizeF(264F, 27F);
            xrSummary27.FormatString = "{0}";
            this.txt_tipo_limpieza.Summary = xrSummary27;
            this.txt_tipo_limpieza.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight;
            // 
            // Línea128
            // 
            this.Línea128.BorderColor = System.Drawing.Color.Navy;
            this.Línea128.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Línea128.BorderWidth = 1F;
            this.Línea128.ForeColor = System.Drawing.Color.Navy;
            this.Línea128.LocationFloat = new DevExpress.Utils.PointFloat(0F, 67F);
            this.Línea128.Name = "Línea128";
            this.Línea128.SizeF = new System.Drawing.SizeF(1083F, 2F);
            // 
            // Idproducto
            // 
            this.Idproducto.BackColor = System.Drawing.Color.Transparent;
            this.Idproducto.BorderColor = System.Drawing.Color.Black;
            this.Idproducto.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Idproducto.BorderWidth = 1F;
            this.Idproducto.CanGrow = false;
            this.Idproducto.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Idproducto]")});
            this.Idproducto.Font = new DevExpress.Drawing.DXFont("Arial", 8F);
            this.Idproducto.LocationFloat = new DevExpress.Utils.PointFloat(630F, 16F);
            this.Idproducto.Name = "Idproducto";
            this.Idproducto.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Idproducto.SizeF = new System.Drawing.SizeF(118F, 16F);
            xrSummary28.FormatString = "{0}";
            this.Idproducto.Summary = xrSummary28;
            this.Idproducto.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            this.Idproducto.TextFormatString = "{0}";
            this.Idproducto.Visible = false;
            // 
            // Etiqueta155
            // 
            this.Etiqueta155.BackColor = System.Drawing.Color.Transparent;
            this.Etiqueta155.BorderColor = System.Drawing.Color.Black;
            this.Etiqueta155.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Etiqueta155.BorderWidth = 1F;
            this.Etiqueta155.CanGrow = false;
            this.Etiqueta155.Font = new DevExpress.Drawing.DXFont("Times New Roman", 11F, ((DevExpress.Drawing.DXFontStyle)((DevExpress.Drawing.DXFontStyle.Bold | DevExpress.Drawing.DXFontStyle.Italic))));
            this.Etiqueta155.ForeColor = System.Drawing.Color.Navy;
            this.Etiqueta155.LocationFloat = new DevExpress.Utils.PointFloat(512F, 16F);
            this.Etiqueta155.Name = "Etiqueta155";
            this.Etiqueta155.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Etiqueta155.SizeF = new System.Drawing.SizeF(80F, 21F);
            this.Etiqueta155.Text = "Idproducto:";
            this.Etiqueta155.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // txt_sup_total
            // 
            this.txt_sup_total.BackColor = System.Drawing.Color.Transparent;
            this.txt_sup_total.BorderColor = System.Drawing.Color.Black;
            this.txt_sup_total.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.txt_sup_total.BorderWidth = 1F;
            this.txt_sup_total.CanGrow = false;
            this.txt_sup_total.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Iif(True, \'#NOT_SUPPORTED#\', \'=Format(Sum([sup])*1000,\"0,000\") & \" m2\"\')")});
            this.txt_sup_total.Font = new DevExpress.Drawing.DXFont("Arial", 6F);
            this.txt_sup_total.LocationFloat = new DevExpress.Utils.PointFloat(697F, 2F);
            this.txt_sup_total.Name = "txt_sup_total";
            this.txt_sup_total.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.txt_sup_total.SizeF = new System.Drawing.SizeF(78F, 13F);
            xrSummary29.FormatString = "{0}";
            this.txt_sup_total.Summary = xrSummary29;
            this.txt_sup_total.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // Texto107
            // 
            this.Texto107.BackColor = System.Drawing.Color.Transparent;
            this.Texto107.BorderColor = System.Drawing.Color.Black;
            this.Texto107.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Texto107.BorderWidth = 1F;
            this.Texto107.CanGrow = false;
            this.Texto107.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Iif(True, \'#NOT_SUPPORTED#\', \'=Format(Sum([HojasPedido]),\"0,000\") & \" hojas\"\')")});
            this.Texto107.Font = new DevExpress.Drawing.DXFont("Arial", 6F);
            this.Texto107.LocationFloat = new DevExpress.Utils.PointFloat(787F, 2F);
            this.Texto107.Name = "Texto107";
            this.Texto107.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Texto107.SizeF = new System.Drawing.SizeF(67F, 12F);
            xrSummary30.FormatString = "{0:Fixed}";
            this.Texto107.Summary = xrSummary30;
            this.Texto107.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // Texto126
            // 
            this.Texto126.BackColor = System.Drawing.Color.Transparent;
            this.Texto126.BorderColor = System.Drawing.Color.Black;
            this.Texto126.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Texto126.BorderWidth = 1F;
            this.Texto126.CanGrow = false;
            this.Texto126.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Iif(True, \'#NOT_SUPPORTED#\', \'=Sum([BarnizNecesario])\')")});
            this.Texto126.Font = new DevExpress.Drawing.DXFont("Arial", 6F);
            this.Texto126.LocationFloat = new DevExpress.Utils.PointFloat(977F, 2F);
            this.Texto126.Name = "Texto126";
            this.Texto126.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Texto126.SizeF = new System.Drawing.SizeF(67F, 13F);
            xrSummary31.FormatString = "{0:Fixed}";
            this.Texto126.Summary = xrSummary31;
            this.Texto126.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // Línea145
            // 
            this.Línea145.BorderColor = System.Drawing.Color.Navy;
            this.Línea145.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Línea145.BorderWidth = 1F;
            this.Línea145.ForeColor = System.Drawing.Color.Navy;
            this.Línea145.LineStyle = DevExpress.Drawing.DXDashStyle.Dash;
            this.Línea145.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.Línea145.Name = "Línea145";
            this.Línea145.SizeF = new System.Drawing.SizeF(1074F, 2F);
            // 
            // Línea130
            // 
            this.Línea130.BorderColor = System.Drawing.Color.Navy;
            this.Línea130.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Línea130.BorderWidth = 1F;
            this.Línea130.ForeColor = System.Drawing.Color.Navy;
            this.Línea130.LineStyle = DevExpress.Drawing.DXDashStyle.Dash;
            this.Línea130.LocationFloat = new DevExpress.Utils.PointFloat(4F, 0F);
            this.Línea130.Name = "Línea130";
            this.Línea130.SizeF = new System.Drawing.SizeF(1074F, 2F);
            // 
            // Texto147
            // 
            this.Texto147.BackColor = System.Drawing.Color.Transparent;
            this.Texto147.BorderColor = System.Drawing.Color.Black;
            this.Texto147.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Texto147.BorderWidth = 1F;
            this.Texto147.CanGrow = false;
            this.Texto147.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Iif(True, \'#NOT_SUPPORTED#\', \'=\"DE: \" & Min([Posicion]) & \" A  \" & Max([Posicion]" +
                    ")\')")});
            this.Texto147.Font = new DevExpress.Drawing.DXFont("Arial", 6F);
            this.Texto147.LocationFloat = new DevExpress.Utils.PointFloat(614F, 2F);
            this.Texto147.Name = "Texto147";
            this.Texto147.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Texto147.SizeF = new System.Drawing.SizeF(78F, 13F);
            xrSummary32.FormatString = "{0}";
            this.Texto147.Summary = xrSummary32;
            this.Texto147.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // Texto148
            // 
            this.Texto148.BackColor = System.Drawing.Color.Transparent;
            this.Texto148.BorderColor = System.Drawing.Color.Black;
            this.Texto148.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Texto148.BorderWidth = 1F;
            this.Texto148.CanGrow = false;
            this.Texto148.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Iif(True, \'#NOT_SUPPORTED#\', \'=Format(Max([HoraFinEstimada]),\"dd hh:nn\")\')")});
            this.Texto148.Font = new DevExpress.Drawing.DXFont("Arial", 6F);
            this.Texto148.LocationFloat = new DevExpress.Utils.PointFloat(528F, 2F);
            this.Texto148.Name = "Texto148";
            this.Texto148.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Texto148.SizeF = new System.Drawing.SizeF(66F, 12F);
            xrSummary33.FormatString = "{0:Fixed}";
            this.Texto148.Summary = xrSummary33;
            this.Texto148.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // sqlDataSource1
            // 
            this.sqlDataSource1.ConnectionName = "DefaultConnectionProgramador";
            this.sqlDataSource1.Name = "sqlDataSource1";
            columnExpression1.ColumnName = "Idprogramacion";
            table1.Name = "TablaProgramacion";
            columnExpression1.Table = table1;
            column1.Expression = columnExpression1;
            columnExpression2.ColumnName = "Idpedido";
            columnExpression2.Table = table1;
            column2.Expression = columnExpression2;
            columnExpression3.ColumnName = "Idaplicacion";
            columnExpression3.Table = table1;
            column3.Expression = columnExpression3;
            columnExpression4.ColumnName = "Idlinea";
            columnExpression4.Table = table1;
            column4.Expression = columnExpression4;
            columnExpression5.ColumnName = "Posicion";
            columnExpression5.Table = table1;
            column5.Expression = columnExpression5;
            columnExpression6.ColumnName = "HojasAProcesar";
            columnExpression6.Table = table1;
            column6.Expression = columnExpression6;
            columnExpression7.ColumnName = "Producto";
            columnExpression7.Table = table1;
            column7.Expression = columnExpression7;
            columnExpression8.ColumnName = "Flejar";
            columnExpression8.Table = table1;
            column8.Expression = columnExpression8;
            columnExpression9.ColumnName = "HoraComienzoEstimada";
            columnExpression9.Table = table1;
            column9.Expression = columnExpression9;
            columnExpression10.ColumnName = "HoraFinEstimada";
            columnExpression10.Table = table1;
            column10.Expression = columnExpression10;
            columnExpression11.ColumnName = "DuracionEstimada";
            columnExpression11.Table = table1;
            column11.Expression = columnExpression11;
            columnExpression12.ColumnName = "HoraReal";
            columnExpression12.Table = table1;
            column12.Expression = columnExpression12;
            columnExpression13.ColumnName = "DiaReal";
            columnExpression13.Table = table1;
            column13.Expression = columnExpression13;
            columnExpression14.ColumnName = "VarCambios";
            columnExpression14.Table = table1;
            column14.Expression = columnExpression14;
            columnExpression15.ColumnName = "TiposCambio";
            columnExpression15.Table = table1;
            column15.Expression = columnExpression15;
            columnExpression16.ColumnName = "Orden";
            columnExpression16.Table = table1;
            column16.Expression = columnExpression16;
            columnExpression17.ColumnName = "Revisar";
            columnExpression17.Table = table1;
            column17.Expression = columnExpression17;
            columnExpression18.ColumnName = "Idproducto";
            columnExpression18.Table = table1;
            column18.Expression = columnExpression18;
            columnExpression19.ColumnName = "Peso";
            columnExpression19.Table = table1;
            column19.Expression = columnExpression19;
            columnExpression20.ColumnName = "PesoMin";
            columnExpression20.Table = table1;
            column20.Expression = columnExpression20;
            columnExpression21.ColumnName = "BarnizNecesario";
            columnExpression21.Table = table1;
            column21.Expression = columnExpression21;
            columnExpression22.ColumnName = "Archivado";
            columnExpression22.Table = table1;
            column22.Expression = columnExpression22;
            columnExpression23.ColumnName = "TipoLavada";
            columnExpression23.Table = table1;
            column23.Expression = columnExpression23;
            columnExpression24.ColumnName = "CaraAAplicar";
            columnExpression24.Table = table1;
            column24.Expression = columnExpression24;
            columnExpression25.ColumnName = "Idaplicacionposterior";
            columnExpression25.Table = table1;
            column25.Expression = columnExpression25;
            columnExpression26.ColumnName = "Posicionaplicacionposterior";
            columnExpression26.Table = table1;
            column26.Expression = columnExpression26;
            columnExpression27.ColumnName = "Idaplicacionanterior";
            columnExpression27.Table = table1;
            column27.Expression = columnExpression27;
            columnExpression28.ColumnName = "Posicionaplicacionanterior";
            columnExpression28.Table = table1;
            column28.Expression = columnExpression28;
            columnExpression29.ColumnName = "Volteado";
            columnExpression29.Table = table1;
            column29.Expression = columnExpression29;
            columnExpression30.ColumnName = "AplicacionSimultanea";
            columnExpression30.Table = table1;
            column30.Expression = columnExpression30;
            columnExpression31.ColumnName = "Observaciones";
            columnExpression31.Table = table1;
            column31.Expression = columnExpression31;
            columnExpression32.ColumnName = "Obspaseposterior";
            columnExpression32.Table = table1;
            column32.Expression = columnExpression32;
            columnExpression33.ColumnName = "ObsCalidad";
            columnExpression33.Table = table1;
            column33.Expression = columnExpression33;
            columnExpression34.ColumnName = "ObsAlmacen";
            columnExpression34.Table = table1;
            column34.Expression = columnExpression34;
            columnExpression35.ColumnName = "TemperaturaSecado";
            columnExpression35.Table = table1;
            column35.Expression = columnExpression35;
            columnExpression36.ColumnName = "VelocidadMaxima";
            columnExpression36.Table = table1;
            column36.Expression = columnExpression36;
            columnExpression37.ColumnName = "OrdenProcesoAplicacion";
            columnExpression37.Table = table1;
            column37.Expression = columnExpression37;
            columnExpression38.ColumnName = "PasadasAdicionales";
            columnExpression38.Table = table1;
            column38.Expression = columnExpression38;
            columnExpression39.ColumnName = "Programador";
            columnExpression39.Table = table1;
            column39.Expression = columnExpression39;
            columnExpression40.ColumnName = "TiempoEstimadoCambio";
            columnExpression40.Table = table1;
            column40.Expression = columnExpression40;
            columnExpression41.ColumnName = "TiempoEstimadoTirada";
            columnExpression41.Table = table1;
            column41.Expression = columnExpression41;
            selectQuery1.Columns.Add(column1);
            selectQuery1.Columns.Add(column2);
            selectQuery1.Columns.Add(column3);
            selectQuery1.Columns.Add(column4);
            selectQuery1.Columns.Add(column5);
            selectQuery1.Columns.Add(column6);
            selectQuery1.Columns.Add(column7);
            selectQuery1.Columns.Add(column8);
            selectQuery1.Columns.Add(column9);
            selectQuery1.Columns.Add(column10);
            selectQuery1.Columns.Add(column11);
            selectQuery1.Columns.Add(column12);
            selectQuery1.Columns.Add(column13);
            selectQuery1.Columns.Add(column14);
            selectQuery1.Columns.Add(column15);
            selectQuery1.Columns.Add(column16);
            selectQuery1.Columns.Add(column17);
            selectQuery1.Columns.Add(column18);
            selectQuery1.Columns.Add(column19);
            selectQuery1.Columns.Add(column20);
            selectQuery1.Columns.Add(column21);
            selectQuery1.Columns.Add(column22);
            selectQuery1.Columns.Add(column23);
            selectQuery1.Columns.Add(column24);
            selectQuery1.Columns.Add(column25);
            selectQuery1.Columns.Add(column26);
            selectQuery1.Columns.Add(column27);
            selectQuery1.Columns.Add(column28);
            selectQuery1.Columns.Add(column29);
            selectQuery1.Columns.Add(column30);
            selectQuery1.Columns.Add(column31);
            selectQuery1.Columns.Add(column32);
            selectQuery1.Columns.Add(column33);
            selectQuery1.Columns.Add(column34);
            selectQuery1.Columns.Add(column35);
            selectQuery1.Columns.Add(column36);
            selectQuery1.Columns.Add(column37);
            selectQuery1.Columns.Add(column38);
            selectQuery1.Columns.Add(column39);
            selectQuery1.Columns.Add(column40);
            selectQuery1.Columns.Add(column41);
            selectQuery1.Name = "TablaProgramacion";
            selectQuery1.Tables.Add(table1);
            columnExpression42.ColumnName = "Idaplicacion";
            table2.Name = "TablaProductosAplicaciones";
            columnExpression42.Table = table2;
            column42.Expression = columnExpression42;
            columnExpression43.ColumnName = "CodigoAplicacion";
            columnExpression43.Table = table2;
            column43.Expression = columnExpression43;
            columnExpression44.ColumnName = "CodigoProducto";
            columnExpression44.Table = table2;
            column44.Expression = columnExpression44;
            columnExpression45.ColumnName = "Peso";
            columnExpression45.Table = table2;
            column45.Expression = columnExpression45;
            columnExpression46.ColumnName = "Prioritario";
            columnExpression46.Table = table2;
            column46.Expression = columnExpression46;
            columnExpression47.ColumnName = "Retirado";
            columnExpression47.Table = table2;
            column47.Expression = columnExpression47;
            selectQuery2.Columns.Add(column42);
            selectQuery2.Columns.Add(column43);
            selectQuery2.Columns.Add(column44);
            selectQuery2.Columns.Add(column45);
            selectQuery2.Columns.Add(column46);
            selectQuery2.Columns.Add(column47);
            selectQuery2.Name = "TablaProductosAplicaciones";
            selectQuery2.Tables.Add(table2);
            columnExpression48.ColumnName = "Idproducto";
            table3.Name = "Tabla_Productos";
            columnExpression48.Table = table3;
            column48.Expression = columnExpression48;
            columnExpression49.ColumnName = "TipoProducto";
            columnExpression49.Table = table3;
            column49.Expression = columnExpression49;
            columnExpression50.ColumnName = "Existencias";
            columnExpression50.Table = table3;
            column50.Expression = columnExpression50;
            columnExpression51.ColumnName = "Stock_Minimo";
            columnExpression51.Table = table3;
            column51.Expression = columnExpression51;
            columnExpression52.ColumnName = "NombreProducto";
            columnExpression52.Table = table3;
            column52.Expression = columnExpression52;
            columnExpression53.ColumnName = "Observaciones";
            columnExpression53.Table = table3;
            column53.Expression = columnExpression53;
            columnExpression54.ColumnName = "Denominacion";
            columnExpression54.Table = table3;
            column54.Expression = columnExpression54;
            columnExpression55.ColumnName = "Activo";
            columnExpression55.Table = table3;
            column55.Expression = columnExpression55;
            columnExpression56.ColumnName = "SolidosViejo";
            columnExpression56.Table = table3;
            column56.Expression = columnExpression56;
            columnExpression57.ColumnName = "Precio";
            columnExpression57.Table = table3;
            column57.Expression = columnExpression57;
            columnExpression58.ColumnName = "Tóxico";
            columnExpression58.Table = table3;
            column58.Expression = columnExpression58;
            columnExpression59.ColumnName = "Inflamable";
            columnExpression59.Table = table3;
            column59.Expression = columnExpression59;
            columnExpression60.ColumnName = "Corrosivo";
            columnExpression60.Table = table3;
            column60.Expression = columnExpression60;
            columnExpression61.ColumnName = "Punto Ebullición";
            columnExpression61.Table = table3;
            column61.Expression = columnExpression61;
            columnExpression62.ColumnName = "Punto Inflamacion";
            columnExpression62.Table = table3;
            column62.Expression = columnExpression62;
            columnExpression63.ColumnName = "Idficha";
            columnExpression63.Table = table3;
            column63.Expression = columnExpression63;
            columnExpression64.ColumnName = "CantidadPedida";
            columnExpression64.Table = table3;
            column64.Expression = columnExpression64;
            columnExpression65.ColumnName = "FechaEntrega";
            columnExpression65.Table = table3;
            column65.Expression = columnExpression65;
            columnExpression66.ColumnName = "CantidadRechazada";
            columnExpression66.Table = table3;
            column66.Expression = columnExpression66;
            columnExpression67.ColumnName = "Stockminimo";
            columnExpression67.Table = table3;
            column67.Expression = columnExpression67;
            columnExpression68.ColumnName = "Tratamientos";
            columnExpression68.Table = table3;
            column68.Expression = columnExpression68;
            columnExpression69.ColumnName = "Solidos";
            columnExpression69.Table = table3;
            column69.Expression = columnExpression69;
            columnExpression70.ColumnName = "Obsoleto";
            columnExpression70.Table = table3;
            column70.Expression = columnExpression70;
            columnExpression71.ColumnName = "UltAvisoInsuficiente";
            columnExpression71.Table = table3;
            column71.Expression = columnExpression71;
            columnExpression72.ColumnName = "CalleAPQ";
            columnExpression72.Table = table3;
            column72.Expression = columnExpression72;
            columnExpression73.ColumnName = "TemperaturaSecado1";
            columnExpression73.Table = table3;
            column73.Expression = columnExpression73;
            columnExpression74.ColumnName = "TemperaturaSecado2";
            columnExpression74.Table = table3;
            column74.Expression = columnExpression74;
            columnExpression75.ColumnName = "TemperaturaSecado3";
            columnExpression75.Table = table3;
            column75.Expression = columnExpression75;
            columnExpression76.ColumnName = "TemperaturaSecado4";
            columnExpression76.Table = table3;
            column76.Expression = columnExpression76;
            columnExpression77.ColumnName = "Velocidad1";
            columnExpression77.Table = table3;
            column77.Expression = columnExpression77;
            columnExpression78.ColumnName = "Velocidad2";
            columnExpression78.Table = table3;
            column78.Expression = columnExpression78;
            columnExpression79.ColumnName = "Velocidad3";
            columnExpression79.Table = table3;
            column79.Expression = columnExpression79;
            columnExpression80.ColumnName = "Velocidad4";
            columnExpression80.Table = table3;
            column80.Expression = columnExpression80;
            selectQuery3.Columns.Add(column48);
            selectQuery3.Columns.Add(column49);
            selectQuery3.Columns.Add(column50);
            selectQuery3.Columns.Add(column51);
            selectQuery3.Columns.Add(column52);
            selectQuery3.Columns.Add(column53);
            selectQuery3.Columns.Add(column54);
            selectQuery3.Columns.Add(column55);
            selectQuery3.Columns.Add(column56);
            selectQuery3.Columns.Add(column57);
            selectQuery3.Columns.Add(column58);
            selectQuery3.Columns.Add(column59);
            selectQuery3.Columns.Add(column60);
            selectQuery3.Columns.Add(column61);
            selectQuery3.Columns.Add(column62);
            selectQuery3.Columns.Add(column63);
            selectQuery3.Columns.Add(column64);
            selectQuery3.Columns.Add(column65);
            selectQuery3.Columns.Add(column66);
            selectQuery3.Columns.Add(column67);
            selectQuery3.Columns.Add(column68);
            selectQuery3.Columns.Add(column69);
            selectQuery3.Columns.Add(column70);
            selectQuery3.Columns.Add(column71);
            selectQuery3.Columns.Add(column72);
            selectQuery3.Columns.Add(column73);
            selectQuery3.Columns.Add(column74);
            selectQuery3.Columns.Add(column75);
            selectQuery3.Columns.Add(column76);
            selectQuery3.Columns.Add(column77);
            selectQuery3.Columns.Add(column78);
            selectQuery3.Columns.Add(column79);
            selectQuery3.Columns.Add(column80);
            selectQuery3.Name = "Tabla_Productos";
            selectQuery3.Tables.Add(table3);
            columnExpression81.ColumnName = "Id";
            table4.Name = "PedidoProcesado";
            columnExpression81.Table = table4;
            column81.Expression = columnExpression81;
            columnExpression82.ColumnName = "IdPedido";
            columnExpression82.Table = table4;
            column82.Expression = columnExpression82;
            columnExpression83.ColumnName = "Supedido";
            columnExpression83.Table = table4;
            column83.Expression = columnExpression83;
            columnExpression84.ColumnName = "IdCliente";
            columnExpression84.Table = table4;
            column84.Expression = columnExpression84;
            columnExpression85.ColumnName = "FechaPedido";
            columnExpression85.Table = table4;
            column85.Expression = columnExpression85;
            columnExpression86.ColumnName = "RequeridoEnFecha";
            columnExpression86.Table = table4;
            column86.Expression = columnExpression86;
            columnExpression87.ColumnName = "ActualizacionFechaRequerida";
            columnExpression87.Table = table4;
            column87.Expression = columnExpression87;
            columnExpression88.ColumnName = "HojasPedido";
            columnExpression88.Table = table4;
            column88.Expression = columnExpression88;
            columnExpression89.ColumnName = "HojasTerminadas";
            columnExpression89.Table = table4;
            column89.Expression = columnExpression89;
            columnExpression90.ColumnName = "HojasLlevadas";
            columnExpression90.Table = table4;
            column90.Expression = columnExpression90;
            columnExpression91.ColumnName = "Motivos";
            columnExpression91.Table = table4;
            column91.Expression = columnExpression91;
            columnExpression92.ColumnName = "TipoElemento";
            columnExpression92.Table = table4;
            column92.Expression = columnExpression92;
            columnExpression93.ColumnName = "Formato";
            columnExpression93.Table = table4;
            column93.Expression = columnExpression93;
            columnExpression94.ColumnName = "Plano";
            columnExpression94.Table = table4;
            column94.Expression = columnExpression94;
            columnExpression95.ColumnName = "Pi1ped";
            columnExpression95.Table = table4;
            column95.Expression = columnExpression95;
            columnExpression96.ColumnName = "Hojaspi1ped";
            columnExpression96.Table = table4;
            column96.Expression = columnExpression96;
            columnExpression97.ColumnName = "Pi2ped";
            columnExpression97.Table = table4;
            column97.Expression = columnExpression97;
            columnExpression98.ColumnName = "Hojaspi2ped";
            columnExpression98.Table = table4;
            column98.Expression = columnExpression98;
            columnExpression99.ColumnName = "Pi3ped";
            columnExpression99.Table = table4;
            column99.Expression = columnExpression99;
            columnExpression100.ColumnName = "Hojaspi3ped";
            columnExpression100.Table = table4;
            column100.Expression = columnExpression100;
            columnExpression101.ColumnName = "Pe1ped";
            columnExpression101.Table = table4;
            column101.Expression = columnExpression101;
            columnExpression102.ColumnName = "Hojaspe1ped";
            columnExpression102.Table = table4;
            column102.Expression = columnExpression102;
            columnExpression103.ColumnName = "Pe2ped";
            columnExpression103.Table = table4;
            column103.Expression = columnExpression103;
            columnExpression104.ColumnName = "Hojaspe2ped";
            columnExpression104.Table = table4;
            column104.Expression = columnExpression104;
            columnExpression105.ColumnName = "Pe3ped";
            columnExpression105.Table = table4;
            column105.Expression = columnExpression105;
            columnExpression106.ColumnName = "Hojaspe3ped";
            columnExpression106.Table = table4;
            column106.Expression = columnExpression106;
            columnExpression107.ColumnName = "c01ped";
            columnExpression107.Table = table4;
            column107.Expression = columnExpression107;
            columnExpression108.ColumnName = "hojasco1ped";
            columnExpression108.Table = table4;
            column108.Expression = columnExpression108;
            columnExpression109.ColumnName = "c02ped";
            columnExpression109.Table = table4;
            column109.Expression = columnExpression109;
            columnExpression110.ColumnName = "hojasco2ped";
            columnExpression110.Table = table4;
            column110.Expression = columnExpression110;
            columnExpression111.ColumnName = "c03ped";
            columnExpression111.Table = table4;
            column111.Expression = columnExpression111;
            columnExpression112.ColumnName = "hojasco3ped";
            columnExpression112.Table = table4;
            column112.Expression = columnExpression112;
            columnExpression113.ColumnName = "c04ped";
            columnExpression113.Table = table4;
            column113.Expression = columnExpression113;
            columnExpression114.ColumnName = "hojasco4ped";
            columnExpression114.Table = table4;
            column114.Expression = columnExpression114;
            columnExpression115.ColumnName = "c05ped";
            columnExpression115.Table = table4;
            column115.Expression = columnExpression115;
            columnExpression116.ColumnName = "hojasco5ped";
            columnExpression116.Table = table4;
            column116.Expression = columnExpression116;
            columnExpression117.ColumnName = "c06ped";
            columnExpression117.Table = table4;
            column117.Expression = columnExpression117;
            columnExpression118.ColumnName = "hojasco6ped";
            columnExpression118.Table = table4;
            column118.Expression = columnExpression118;
            columnExpression119.ColumnName = "c07ped";
            columnExpression119.Table = table4;
            column119.Expression = columnExpression119;
            columnExpression120.ColumnName = "hojasco7ped";
            columnExpression120.Table = table4;
            column120.Expression = columnExpression120;
            columnExpression121.ColumnName = "c08ped";
            columnExpression121.Table = table4;
            column121.Expression = columnExpression121;
            columnExpression122.ColumnName = "hojasco8ped";
            columnExpression122.Table = table4;
            column122.Expression = columnExpression122;
            columnExpression123.ColumnName = "c09ped";
            columnExpression123.Table = table4;
            column123.Expression = columnExpression123;
            columnExpression124.ColumnName = "hojasco9ped";
            columnExpression124.Table = table4;
            column124.Expression = columnExpression124;
            columnExpression125.ColumnName = "co10ped";
            columnExpression125.Table = table4;
            column125.Expression = columnExpression125;
            columnExpression126.ColumnName = "hojasco10ped";
            columnExpression126.Table = table4;
            column126.Expression = columnExpression126;
            columnExpression127.ColumnName = "co11ped";
            columnExpression127.Table = table4;
            column127.Expression = columnExpression127;
            columnExpression128.ColumnName = "hojasco11ped";
            columnExpression128.Table = table4;
            column128.Expression = columnExpression128;
            columnExpression129.ColumnName = "cd1ped";
            columnExpression129.Table = table4;
            column129.Expression = columnExpression129;
            columnExpression130.ColumnName = "hojascd1ped";
            columnExpression130.Table = table4;
            column130.Expression = columnExpression130;
            columnExpression131.ColumnName = "cd2ped";
            columnExpression131.Table = table4;
            column131.Expression = columnExpression131;
            columnExpression132.ColumnName = "hojascd2ped";
            columnExpression132.Table = table4;
            column132.Expression = columnExpression132;
            columnExpression133.ColumnName = "cd3ped";
            columnExpression133.Table = table4;
            column133.Expression = columnExpression133;
            columnExpression134.ColumnName = "hojascd3ped";
            columnExpression134.Table = table4;
            column134.Expression = columnExpression134;
            columnExpression135.ColumnName = "cd4ped";
            columnExpression135.Table = table4;
            column135.Expression = columnExpression135;
            columnExpression136.ColumnName = "hojascd4ped";
            columnExpression136.Table = table4;
            column136.Expression = columnExpression136;
            columnExpression137.ColumnName = "IdPri1";
            columnExpression137.Table = table4;
            column137.Expression = columnExpression137;
            columnExpression138.ColumnName = "Capai1";
            columnExpression138.Table = table4;
            column138.Expression = columnExpression138;
            columnExpression139.ColumnName = "IdPri2";
            columnExpression139.Table = table4;
            column139.Expression = columnExpression139;
            columnExpression140.ColumnName = "Capai2";
            columnExpression140.Table = table4;
            column140.Expression = columnExpression140;
            columnExpression141.ColumnName = "IdPri3";
            columnExpression141.Table = table4;
            column141.Expression = columnExpression141;
            columnExpression142.ColumnName = "Capai3";
            columnExpression142.Table = table4;
            column142.Expression = columnExpression142;
            columnExpression143.ColumnName = "IdPre1";
            columnExpression143.Table = table4;
            column143.Expression = columnExpression143;
            columnExpression144.ColumnName = "Capae1";
            columnExpression144.Table = table4;
            column144.Expression = columnExpression144;
            columnExpression145.ColumnName = "IdPre2";
            columnExpression145.Table = table4;
            column145.Expression = columnExpression145;
            columnExpression146.ColumnName = "Capae2";
            columnExpression146.Table = table4;
            column146.Expression = columnExpression146;
            columnExpression147.ColumnName = "IdPre3";
            columnExpression147.Table = table4;
            column147.Expression = columnExpression147;
            columnExpression148.ColumnName = "Capae3";
            columnExpression148.Table = table4;
            column148.Expression = columnExpression148;
            columnExpression149.ColumnName = "Ancho_hjlta";
            columnExpression149.Table = table4;
            column149.Expression = columnExpression149;
            columnExpression150.ColumnName = "Largo_hjlta";
            columnExpression150.Table = table4;
            column150.Expression = columnExpression150;
            columnExpression151.ColumnName = "Espesor_hjlta";
            columnExpression151.Table = table4;
            column151.Expression = columnExpression151;
            columnExpression152.ColumnName = "Tipo_hjlta";
            columnExpression152.Table = table4;
            column152.Expression = columnExpression152;
            columnExpression153.ColumnName = "ClaseSustrato";
            columnExpression153.Table = table4;
            column153.Expression = columnExpression153;
            columnExpression154.ColumnName = "Corte2";
            columnExpression154.Table = table4;
            column154.Expression = columnExpression154;
            columnExpression155.ColumnName = "Diferencial";
            columnExpression155.Table = table4;
            column155.Expression = columnExpression155;
            columnExpression156.ColumnName = "Obs1";
            columnExpression156.Table = table4;
            column156.Expression = columnExpression156;
            columnExpression157.ColumnName = "Obs2";
            columnExpression157.Table = table4;
            column157.Expression = columnExpression157;
            columnExpression158.ColumnName = "Obs3";
            columnExpression158.Table = table4;
            column158.Expression = columnExpression158;
            columnExpression159.ColumnName = "Obsrayas";
            columnExpression159.Table = table4;
            column159.Expression = columnExpression159;
            columnExpression160.ColumnName = "Obsflejado";
            columnExpression160.Table = table4;
            column160.Expression = columnExpression160;
            columnExpression161.ColumnName = "Obsarticulo";
            columnExpression161.Table = table4;
            column161.Expression = columnExpression161;
            columnExpression162.ColumnName = "WO";
            columnExpression162.Table = table4;
            column162.Expression = columnExpression162;
            columnExpression163.ColumnName = "EstadoTintas";
            columnExpression163.Table = table4;
            column163.Expression = columnExpression163;
            columnExpression164.ColumnName = "LineaTintas";
            columnExpression164.Table = table4;
            column164.Expression = columnExpression164;
            columnExpression165.ColumnName = "PlanchasPedidas";
            columnExpression165.Table = table4;
            column165.Expression = columnExpression165;
            columnExpression166.ColumnName = "FechaPlanchas";
            columnExpression166.Table = table4;
            column166.Expression = columnExpression166;
            columnExpression167.ColumnName = "Terminado";
            columnExpression167.Table = table4;
            column167.Expression = columnExpression167;
            columnExpression168.ColumnName = "ObservacionesPedido";
            columnExpression168.Table = table4;
            column168.Expression = columnExpression168;
            columnExpression169.ColumnName = "TipoPedido";
            columnExpression169.Table = table4;
            column169.Expression = columnExpression169;
            columnExpression170.ColumnName = "Anulado";
            columnExpression170.Table = table4;
            column170.Expression = columnExpression170;
            columnExpression171.ColumnName = "PlanchasPasadas";
            columnExpression171.Table = table4;
            column171.Expression = columnExpression171;
            columnExpression172.ColumnName = "MantenerAbierto";
            columnExpression172.Table = table4;
            column172.Expression = columnExpression172;
            columnExpression173.ColumnName = "Obs_a_cliente";
            columnExpression173.Table = table4;
            column173.Expression = columnExpression173;
            columnExpression174.ColumnName = "TipoBarnizado";
            columnExpression174.Table = table4;
            column174.Expression = columnExpression174;
            columnExpression175.ColumnName = "TipoEnvase";
            columnExpression175.Table = table4;
            column175.Expression = columnExpression175;
            columnExpression176.ColumnName = "Embuticion";
            columnExpression176.Table = table4;
            column176.Expression = columnExpression176;
            columnExpression177.ColumnName = "SINCARPETA";
            columnExpression177.Table = table4;
            column177.Expression = columnExpression177;
            columnExpression178.ColumnName = "ObsProgramacion";
            columnExpression178.Table = table4;
            column178.Expression = columnExpression178;
            columnExpression179.ColumnName = "VistoObsProgramacion";
            columnExpression179.Table = table4;
            column179.Expression = columnExpression179;
            columnExpression180.ColumnName = "Triptico";
            columnExpression180.Table = table4;
            column180.Expression = columnExpression180;
            columnExpression181.ColumnName = "FechaEnvioTriptico";
            columnExpression181.Table = table4;
            column181.Expression = columnExpression181;
            columnExpression182.ColumnName = "FechaOKTriptico";
            columnExpression182.Table = table4;
            column182.Expression = columnExpression182;
            columnExpression183.ColumnName = "ObsTriptico";
            columnExpression183.Table = table4;
            column183.Expression = columnExpression183;
            columnExpression184.ColumnName = "NoIncluirEnListado";
            columnExpression184.Table = table4;
            column184.Expression = columnExpression184;
            columnExpression185.ColumnName = "IdpedidoInformix";
            columnExpression185.Table = table4;
            column185.Expression = columnExpression185;
            columnExpression186.ColumnName = "UltimaModificacion";
            columnExpression186.Table = table4;
            column186.Expression = columnExpression186;
            columnExpression187.ColumnName = "Estado";
            columnExpression187.Table = table4;
            column187.Expression = columnExpression187;
            columnExpression188.ColumnName = "Revisado";
            columnExpression188.Table = table4;
            column188.Expression = columnExpression188;
            columnExpression189.ColumnName = "FechaUltRevision";
            columnExpression189.Table = table4;
            column189.Expression = columnExpression189;
            columnExpression190.ColumnName = "Altura_cuerpos";
            columnExpression190.Table = table4;
            column190.Expression = columnExpression190;
            columnExpression191.ColumnName = "Hojalata_YA_sacada";
            columnExpression191.Table = table4;
            column191.Expression = columnExpression191;
            columnExpression192.ColumnName = "URGENTE";
            columnExpression192.Table = table4;
            column192.Expression = columnExpression192;
            columnExpression193.ColumnName = "BarnizadoWoW";
            columnExpression193.Table = table4;
            column193.Expression = columnExpression193;
            columnExpression194.ColumnName = "PrecioHoja";
            columnExpression194.Table = table4;
            column194.Expression = columnExpression194;
            columnExpression195.ColumnName = "FechaFin";
            columnExpression195.Table = table4;
            column195.Expression = columnExpression195;
            columnExpression196.ColumnName = "FechaEntregaSolicitada";
            columnExpression196.Table = table4;
            column196.Expression = columnExpression196;
            selectQuery4.Columns.Add(column81);
            selectQuery4.Columns.Add(column82);
            selectQuery4.Columns.Add(column83);
            selectQuery4.Columns.Add(column84);
            selectQuery4.Columns.Add(column85);
            selectQuery4.Columns.Add(column86);
            selectQuery4.Columns.Add(column87);
            selectQuery4.Columns.Add(column88);
            selectQuery4.Columns.Add(column89);
            selectQuery4.Columns.Add(column90);
            selectQuery4.Columns.Add(column91);
            selectQuery4.Columns.Add(column92);
            selectQuery4.Columns.Add(column93);
            selectQuery4.Columns.Add(column94);
            selectQuery4.Columns.Add(column95);
            selectQuery4.Columns.Add(column96);
            selectQuery4.Columns.Add(column97);
            selectQuery4.Columns.Add(column98);
            selectQuery4.Columns.Add(column99);
            selectQuery4.Columns.Add(column100);
            selectQuery4.Columns.Add(column101);
            selectQuery4.Columns.Add(column102);
            selectQuery4.Columns.Add(column103);
            selectQuery4.Columns.Add(column104);
            selectQuery4.Columns.Add(column105);
            selectQuery4.Columns.Add(column106);
            selectQuery4.Columns.Add(column107);
            selectQuery4.Columns.Add(column108);
            selectQuery4.Columns.Add(column109);
            selectQuery4.Columns.Add(column110);
            selectQuery4.Columns.Add(column111);
            selectQuery4.Columns.Add(column112);
            selectQuery4.Columns.Add(column113);
            selectQuery4.Columns.Add(column114);
            selectQuery4.Columns.Add(column115);
            selectQuery4.Columns.Add(column116);
            selectQuery4.Columns.Add(column117);
            selectQuery4.Columns.Add(column118);
            selectQuery4.Columns.Add(column119);
            selectQuery4.Columns.Add(column120);
            selectQuery4.Columns.Add(column121);
            selectQuery4.Columns.Add(column122);
            selectQuery4.Columns.Add(column123);
            selectQuery4.Columns.Add(column124);
            selectQuery4.Columns.Add(column125);
            selectQuery4.Columns.Add(column126);
            selectQuery4.Columns.Add(column127);
            selectQuery4.Columns.Add(column128);
            selectQuery4.Columns.Add(column129);
            selectQuery4.Columns.Add(column130);
            selectQuery4.Columns.Add(column131);
            selectQuery4.Columns.Add(column132);
            selectQuery4.Columns.Add(column133);
            selectQuery4.Columns.Add(column134);
            selectQuery4.Columns.Add(column135);
            selectQuery4.Columns.Add(column136);
            selectQuery4.Columns.Add(column137);
            selectQuery4.Columns.Add(column138);
            selectQuery4.Columns.Add(column139);
            selectQuery4.Columns.Add(column140);
            selectQuery4.Columns.Add(column141);
            selectQuery4.Columns.Add(column142);
            selectQuery4.Columns.Add(column143);
            selectQuery4.Columns.Add(column144);
            selectQuery4.Columns.Add(column145);
            selectQuery4.Columns.Add(column146);
            selectQuery4.Columns.Add(column147);
            selectQuery4.Columns.Add(column148);
            selectQuery4.Columns.Add(column149);
            selectQuery4.Columns.Add(column150);
            selectQuery4.Columns.Add(column151);
            selectQuery4.Columns.Add(column152);
            selectQuery4.Columns.Add(column153);
            selectQuery4.Columns.Add(column154);
            selectQuery4.Columns.Add(column155);
            selectQuery4.Columns.Add(column156);
            selectQuery4.Columns.Add(column157);
            selectQuery4.Columns.Add(column158);
            selectQuery4.Columns.Add(column159);
            selectQuery4.Columns.Add(column160);
            selectQuery4.Columns.Add(column161);
            selectQuery4.Columns.Add(column162);
            selectQuery4.Columns.Add(column163);
            selectQuery4.Columns.Add(column164);
            selectQuery4.Columns.Add(column165);
            selectQuery4.Columns.Add(column166);
            selectQuery4.Columns.Add(column167);
            selectQuery4.Columns.Add(column168);
            selectQuery4.Columns.Add(column169);
            selectQuery4.Columns.Add(column170);
            selectQuery4.Columns.Add(column171);
            selectQuery4.Columns.Add(column172);
            selectQuery4.Columns.Add(column173);
            selectQuery4.Columns.Add(column174);
            selectQuery4.Columns.Add(column175);
            selectQuery4.Columns.Add(column176);
            selectQuery4.Columns.Add(column177);
            selectQuery4.Columns.Add(column178);
            selectQuery4.Columns.Add(column179);
            selectQuery4.Columns.Add(column180);
            selectQuery4.Columns.Add(column181);
            selectQuery4.Columns.Add(column182);
            selectQuery4.Columns.Add(column183);
            selectQuery4.Columns.Add(column184);
            selectQuery4.Columns.Add(column185);
            selectQuery4.Columns.Add(column186);
            selectQuery4.Columns.Add(column187);
            selectQuery4.Columns.Add(column188);
            selectQuery4.Columns.Add(column189);
            selectQuery4.Columns.Add(column190);
            selectQuery4.Columns.Add(column191);
            selectQuery4.Columns.Add(column192);
            selectQuery4.Columns.Add(column193);
            selectQuery4.Columns.Add(column194);
            selectQuery4.Columns.Add(column195);
            selectQuery4.Columns.Add(column196);
            selectQuery4.Name = "PedidoProcesado";
            selectQuery4.Tables.Add(table4);
            this.sqlDataSource1.Queries.AddRange(new DevExpress.DataAccess.Sql.SqlQuery[] {
            selectQuery1,
            selectQuery2,
            selectQuery3,
            selectQuery4});
            this.sqlDataSource1.ResultSchemaSerializable = resources.GetString("sqlDataSource1.ResultSchemaSerializable");
            // 
            // BarnizadoReport
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detalle,
            this.EncabezadoDelInforme,
            this.PieDelInforme,
            this.SecciónEncabezadoDePágina,
            this.SecciónPieDePágina,
            this.EncabezadoDelGrupo2,
            this.groupHeaderBand1,
            this.groupHeaderBand2,
            this.EncabezadoDelGrupo1,
            this.PieDelGrupo0,
            this.groupHeaderBand3,
            this.groupHeaderBand4});
            this.ComponentStorage.AddRange(new System.ComponentModel.IComponent[] {
            this.sqlDataSource1});
            this.DataMember = "PedidoProcesado";
            this.DataSource = this.sqlDataSource1;
            this.Margins = new DevExpress.Drawing.DXMargins(20F, 20F, 17F, 17F);
            this.PageHeight = 1615;
            this.PageWidth = 1169;
            this.PaperKind = DevExpress.Drawing.Printing.DXPaperKind.Custom;
            this.Version = "22.2";
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private DevExpress.XtraReports.UI.DetailBand Detalle;
        private DevExpress.XtraReports.UI.XRLabel Posicion;
        private DevExpress.XtraReports.UI.XRLabel Idpedido;
        private DevExpress.XtraReports.UI.XRLabel IdCliente;
        private DevExpress.XtraReports.UI.XRLabel Formato;
        private DevExpress.XtraReports.UI.XRLabel Plano;
        private DevExpress.XtraReports.UI.XRLabel Texto49;
        private DevExpress.XtraReports.UI.XRLine Línea55;
        private DevExpress.XtraReports.UI.XRLabel txt_flejar;
        private DevExpress.XtraReports.UI.XRLabel txt_HoraComienzoEstimada;
        private DevExpress.XtraReports.UI.XRLabel txt_HoraFinEstimada;
        private DevExpress.XtraReports.UI.XRLabel TiposCambio;
        private DevExpress.XtraReports.UI.XRLabel Etiqueta97;
        private DevExpress.XtraReports.UI.XRLabel txt_estado_pedido;
        private DevExpress.XtraReports.UI.XRLabel RequeridoEnFecha;
        private DevExpress.XtraReports.UI.XRLabel Texto102;
        private DevExpress.XtraReports.UI.XRLabel pos_escuadra;
        private DevExpress.XtraReports.UI.XRLabel Texto113;
        private DevExpress.XtraReports.UI.XRPanel Cuadro116;
        private DevExpress.XtraReports.UI.XRLabel txt_barniz_por_pedido;
        private DevExpress.XtraReports.UI.XRLabel ObsCalidad;
        private DevExpress.XtraReports.UI.XRLine Línea129;
        private DevExpress.XtraReports.UI.XRLabel txt_muestra_SH;
        private DevExpress.XtraReports.UI.XRLabel TxtHojas;
        private DevExpress.XtraReports.UI.XRLabel Etiqueta144;
        private DevExpress.XtraReports.UI.XRLabel TEXTBOX4;
        private DevExpress.XtraReports.UI.XRLabel Texto164;
        private DevExpress.XtraReports.UI.ReportHeaderBand EncabezadoDelInforme;
        private DevExpress.XtraReports.UI.ReportFooterBand PieDelInforme;
        private DevExpress.XtraReports.UI.XRLabel mx_posicion;
        private DevExpress.XtraReports.UI.XRLabel mn_posicion;
        private DevExpress.XtraReports.UI.PageHeaderBand SecciónEncabezadoDePágina;
        private DevExpress.XtraReports.UI.XRLine Línea44;
        private DevExpress.XtraReports.UI.XRLabel Etiqueta45;
        private DevExpress.XtraReports.UI.XRLabel Etiqueta46;
        private DevExpress.XtraReports.UI.XRLabel Texto31;
        private DevExpress.XtraReports.UI.XRLabel Etiqueta47;
        private DevExpress.XtraReports.UI.XRLine Línea62;
        private DevExpress.XtraReports.UI.XRLabel Etiqueta132;
        private DevExpress.XtraReports.UI.XRLabel Texto136;
        private DevExpress.XtraReports.UI.XRLabel Texto149;
        private DevExpress.XtraReports.UI.PageFooterBand SecciónPieDePágina;
        private DevExpress.XtraReports.UI.GroupHeaderBand EncabezadoDelGrupo2;
        private DevExpress.XtraReports.UI.GroupHeaderBand groupHeaderBand1;
        private DevExpress.XtraReports.UI.GroupHeaderBand groupHeaderBand2;
        private DevExpress.XtraReports.UI.GroupHeaderBand EncabezadoDelGrupo1;
        private DevExpress.XtraReports.UI.XRLabel Etiqueta80;
        private DevExpress.XtraReports.UI.XRLine Línea81;
        private DevExpress.XtraReports.UI.XRLabel Etiqueta82;
        private DevExpress.XtraReports.UI.XRLabel Etiqueta83;
        private DevExpress.XtraReports.UI.XRLabel Etiqueta84;
        private DevExpress.XtraReports.UI.XRLabel Etiqueta85;
        private DevExpress.XtraReports.UI.XRLabel Etiqueta86;
        private DevExpress.XtraReports.UI.XRLine Línea87;
        private DevExpress.XtraReports.UI.XRLine Línea88;
        private DevExpress.XtraReports.UI.XRLabel xrLabel2;
        private DevExpress.XtraReports.UI.XRLabel txt_tipo_limpieza;
        private DevExpress.XtraReports.UI.XRLine Línea128;
        private DevExpress.XtraReports.UI.XRLabel Idproducto;
        private DevExpress.XtraReports.UI.XRLabel Etiqueta155;
        private DevExpress.XtraReports.UI.GroupFooterBand PieDelGrupo0;
        private DevExpress.XtraReports.UI.XRLabel txt_sup_total;
        private DevExpress.XtraReports.UI.XRLabel Texto107;
        private DevExpress.XtraReports.UI.XRLabel Texto126;
        private DevExpress.XtraReports.UI.XRLine Línea145;
        private DevExpress.XtraReports.UI.XRLine Línea130;
        private DevExpress.XtraReports.UI.XRLabel Texto147;
        private DevExpress.XtraReports.UI.XRLabel Texto148;
        private DevExpress.XtraReports.UI.GroupHeaderBand groupHeaderBand3;
        private DevExpress.XtraReports.UI.GroupHeaderBand groupHeaderBand4;
        private DevExpress.DataAccess.Sql.SqlDataSource sqlDataSource1;
    }
}
