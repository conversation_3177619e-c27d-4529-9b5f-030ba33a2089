﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Nelibur.ObjectMapper;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.DatosGenerales.Query;

public class GetDatosCodTintasQuery : IRequest<ListResult<CodtintasDTO>>
{
}

internal class GetDatosCodTintasQueryHandler : IRequestHandler<GetDatosCodTintasQuery, ListResult<CodtintasDTO>>
{
    private readonly ProgramadorLitalsaContext _programadorLitalsaContext;
    public GetDatosCodTintasQueryHandler(ProgramadorLitalsaContext programadorLitalsaContext)
    {
        _programadorLitalsaContext = programadorLitalsaContext;
    }

    public async Task<ListResult<CodtintasDTO>> Handle(GetDatosCodTintasQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var result = new ListResult<CodtintasDTO>()
            {
                Data = new List<CodtintasDTO>(),
                Errors = new List<string>()
            };

            var datosProd = await _programadorLitalsaContext.Codtintas.OrderBy(o=>o.Codtin).ToListAsync(cancellationToken);
            result.Data = TinyMapper.Map<List<CodtintasDTO>>(datosProd);
            return result;
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: GetDatosCodTintasQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            throw new Exception(errorText, e);
        }
    }
}