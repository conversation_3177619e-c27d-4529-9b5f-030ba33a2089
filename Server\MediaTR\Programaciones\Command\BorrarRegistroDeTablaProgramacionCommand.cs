﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Data.ProteoLitalsa;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Programaciones.Command;

public class BorrarRegistroDeTablaProgramacionCommand : IRequest<SingleResult<int>>
{

    public BorrarRegistroDeTablaProgramacionCommand(int idProg)
    {
        IdProg = idProg;
    }

    public int IdProg;
}

public class BorrarRegistroDeTablaProgramacionCommandHandler : IRequestHandler<BorrarRegistroDeTablaProgramacionCommand, SingleResult<int>>
{
    private readonly ProgramadorLitalsaContext _context;
    private readonly ProteoLitalsaContext _proteoContext;
    public BorrarRegistroDeTablaProgramacionCommandHandler(ProgramadorLitalsaContext context, ProteoLitalsaContext proteoContext)
    {
        _context = context;
        _proteoContext = proteoContext;
    }
    /// <summary>
    /// Este no es el método de borrado de registros en PROTEO. Es para borrar solo los registros de la tabla Programación desde
    /// la vista Grid_TblProgramacion
    /// </summary>
    /// <param name="request"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task<SingleResult<int>> Handle(BorrarRegistroDeTablaProgramacionCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<int> { Errors = new List<string>(), Data = 0 };

        using var transaction = await _context.Database.BeginTransactionAsync(cancellationToken);
        try
        {
            var registroLitoIds = await _context.TablaProgramacion
                .Where(o => o.Idprogramacion == request.IdProg)
                .Select(o => new { o.Idpedido, o.Idaplicacion, o.Idlinea, o.Posicion, o.Idprogramacion })
                .FirstOrDefaultAsync(cancellationToken);

            // Eliminar registros de las nuevas tablas para esta programación
            if (registroLitoIds != null)
            {
                // Eliminar logs relacionados con esta programación
                await _context.ProgramacionesPantallaLog
                    .Where(l => l.IdProgramacion == registroLitoIds.Idprogramacion)
                    .ExecuteDeleteAsync(cancellationToken);

                // Eliminar registro principal de ProgramacionesPantalla
                await _context.ProgramacionesPantalla
                    .Where(p => p.IdProgramacion == registroLitoIds.Idprogramacion)
                    .ExecuteDeleteAsync(cancellationToken);
            }

            var datosPedido =
                await _context.PedidoProcesado.FirstOrDefaultAsync(o => o.IdPedido == registroLitoIds.Idpedido,
                    cancellationToken);
            if (datosPedido is { TipoPedido: "L" })
            {
                var res = await _context.TablaCodigosPedido
                    .Where(o => o.Idpedido == registroLitoIds.Idpedido && o.Idcodigoaplicacion == registroLitoIds.Idaplicacion)
                    .ToListAsync(cancellationToken);

                res.ForEach(o => o.EstadoTintas = "Sin Aplicar");

                await _context.SaveChangesAsync(cancellationToken);
            }

            var progsBorrar = await _context.TablaProgramacion.Where(o => o.Idprogramacion == request.IdProg)
                .ExecuteDeleteAsync(cancellationToken);
            //TEMPORALMENTE deshabilitado porque no está en uso aún e interfiere con las programaciones
            //ADEMAS EN ESTE METODO SE MANTENDRÁ DESACTIVADO
            //PORQUE PUEDE INTERFERIR CON PROTEO AL USARSE PARA ELIMINAR,A VECES, REGISTROS MUY ANTIGUOS.
            //var cabProteo = await _proteoContext.AccMesExportWorkOrders
            //    .Where(o => pedidos.Contains((int)o.WorkOrder))
            //    .ExecuteDeleteAsync(cancellationToken);
            //var posProteo = await _proteoContext.AccMesExportPhasesIdprog
            //    .Where(o => progs.Contains(o.Idprogramacion)).ExecuteDeleteAsync(cancellationToken);

            await transaction.CommitAsync(cancellationToken);
            result.Data = 1;
        }
        catch (Exception e)
        {
            await transaction.RollbackAsync(cancellationToken);
            var errorText = $"ERROR: BorrarRegistroDeTablaProgramacionCommand - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }
        return result;
    }
}