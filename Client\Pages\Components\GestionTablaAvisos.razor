﻿@using ProgramadorGeneralBLZ.Shared.ResponseModels
@using ProgramadorGeneralBLZ.Shared.DTO
@using ProgramadorGeneralBLZ.Shared

@inject IToastService toastService
@inject HttpClient http
@inject SpinnerService _spinnerService

<h3>Datos Partes</h3>
<DxGrid Data="@datosAvisos" SizeMode="SizeMode.Medium" 
        CssClass="ch-320 smallFont progGrid" PageSize="22"
        Context="ContextGridPartes" @ref="GridTablaAvisos"
        AllowSort="true" ShowFilterRow="true">
    <Columns>
        <DxGridDataColumn FieldName="Fecha" Width="100px" SortIndex="0" SortOrder="GridColumnSortOrder.Descending"/>
        <DxGridDataColumn FieldName="Aviso" />
    </Columns>
</DxGrid>
@code {
    [Parameter]
    public int? IdPedido { get; set; }

    DxGrid? GridTablaAvisos;
    ListResult<TablaAvisosDTO> resultDatosAvisos { get; set; }
    List<TablaAvisosDTO> datosAvisos { get; set; }
    
    protected override async Task OnInitializedAsync()
    {
        _spinnerService.Show();
        resultDatosAvisos = await http.GetFromJsonAsync<ListResult<TablaAvisosDTO>>($"DatosGenerales/avisos");
        if (resultDatosAvisos.Errors.Any())
        {
            toastService.ShowInfo($"{resultDatosAvisos.Errors.First()}");
        }
        else
        {
            datosAvisos = resultDatosAvisos.Data;
        }
        StateHasChanged();
        _spinnerService.Hide();
    }
}
