﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace ProgramadorGeneralBLZ.Server.Models.InterfaseBD;

public partial class Reservas
{
    public int Id { get; set; }

    public string NumOrden { get; set; }

    public string NumOperacion { get; set; }

    public string NumReservaSec { get; set; }

    public string NumPosReservaSec { get; set; }

    public string ClaseNecesidad { get; set; }

    public string FechaNecesidad { get; set; }

    public string NumeroLote { get; set; }

    public decimal CantidadNecesaria { get; set; }

    public string Unidad { get; set; }

    public string Almacen { get; set; }

    public string Material { get; set; }

    public string Centro { get; set; }

    public string ClaseMovimientoGestionStocks { get; set; }

    public string NumeroPosicionListaMateriales { get; set; }

    public string TipoPosicionListaMateriales { get; set; }

    public bool Borrado { get; set; }

    public DateTime? FechaRegistro { get; set; }

    public DateTime? FechaModificacion { get; set; }

    public DateTime? FechaBorrado { get; set; }

    public virtual ClaseMovimiento ClaseMovimientoGestionStocksNavigation { get; set; }

    public virtual Matmas MaterialNavigation { get; set; }

    public virtual Orden NumOrdenNavigation { get; set; }

    public virtual TipoPosicion TipoPosicionListaMaterialesNavigation { get; set; }

    public virtual UnidadMedida UnidadNavigation { get; set; }
}