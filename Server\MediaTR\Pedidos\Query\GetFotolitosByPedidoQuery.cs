﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.DatoLita01;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Pedidos.Query;

public class GetFotolitosByPedidoQuery : IRequest<List<string>>   
{
    public GetFotolitosByPedidoQuery(string idPedido)
    {
        IdPedido = idPedido;
    }
    public string IdPedido { get; set; }
}

internal class GetFotolitosByPedidoQueryHandler : IRequestHandler<GetFotolitosByPedidoQuery, List<string>>
{
    private readonly DatoLita01Context _contextLita01;
    public GetFotolitosByPedidoQueryHandler(DatoLita01Context contextLita01)
    {
        _contextLita01 = contextLita01;
    }

    public async Task<List<string>> Handle(GetFotolitosByPedidoQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var result = new List<string>();

            var motivos = await _contextLita01.Motivos.Where(o => o.Codigo == request.IdPedido)
                .Select(o=>o.Modelo)
                .ToListAsync(cancellationToken);

            var fotolitos = motivos.Any()
                ? await _contextLita01.MotivosDetallados.Where(o => motivos.Contains(o.Codigo))
                    .Select(o => o.Fotolitos)
                    .ToListAsync(cancellationToken)
                : null;

            if (fotolitos == null) 
                return result;

            result.AddRange(fotolitos.Select(f => $"https://litalsabackup01.blob.core.windows.net/imagenes-fotolitos/{f}.jpg"));
            
            return result;
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: GetFotolitosByPedidoQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            throw new Exception(errorText, e);
        }
    }
}