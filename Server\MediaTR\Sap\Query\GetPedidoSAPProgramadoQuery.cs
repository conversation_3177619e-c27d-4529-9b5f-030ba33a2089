﻿using System.Diagnostics;
using System.Reflection;
using System.Text.RegularExpressions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Nelibur.ObjectMapper;
using ProgramadorGeneralBLZ.Server.Data.DatoLita01;
using ProgramadorGeneralBLZ.Server.Data.InterfaseBD;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.MediaTR.Pedidos.Query;
using ProgramadorGeneralBLZ.Server.Models.InterfaseBD;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Services.DatabaseManipulation;
using ProgramadorGeneralBLZ.Server.Services.SAP;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Sap.Query
{
    public class GetPedidoSapProgramadoQuery : IRequest<SingleResult<ListadoPedidosProcesadosFiltradosDTO>>
    {
        /// <summary>
        /// Query para obtener el pedido, con distintas combinaciones:
        /// El último
        /// Por ID de Litalsa
        /// Por ID del Cliente y/o IDCliente ya que dos clientes pueden tener el mismo código de pedido.
        /// </summary>
        /// <param name="idPedido"></param>
        /// <param name="idPedidoCliente"></param>
        /// <param name="idCliente"></param>
        /// <param name="ultimo"></param>
        public GetPedidoSapProgramadoQuery(int? idPedido, string idPedidoCliente = null, int? idCliente = null, bool ultimo = false)
        {
            IdCliente = idCliente;
            IdPedidoCliente = idPedidoCliente;
            IdPedido = idPedido;
            Ultimo = ultimo;
        }

        public int? IdPedido { get; set; }
        public bool Ultimo { get; set; }
        public int? IdCliente { get; set; }
        public string IdPedidoCliente { get; set; }

    }
    public class GetPedidoSapProgramadoQueryHandler : IRequestHandler<GetPedidoSapProgramadoQuery, SingleResult<ListadoPedidosProcesadosFiltradosDTO>>
    {
        private readonly ProgramadorLitalsaContext _contextProg;
        private readonly DatoLita01Context _contextLita01;
        private readonly InterfaseBDContext _contextInterfases;
        private readonly IDataManipulationService _dataManipulationService;
        private readonly ISapService _sapService;


        public GetPedidoSapProgramadoQueryHandler(ProgramadorLitalsaContext contextProg,
            DatoLita01Context contextLita01, IDataManipulationService dataManipulationService, InterfaseBDContext contextInterfases, ISapService sapService)
        {
            _contextProg = contextProg;
            _contextLita01 = contextLita01;
            _dataManipulationService = dataManipulationService;
            _contextInterfases = contextInterfases;
            _sapService = sapService;
        }

        /// <summary>
        /// Recuperamos de la BD los datos del pedido indicado.
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<SingleResult<ListadoPedidosProcesadosFiltradosDTO>> Handle(GetPedidoSapProgramadoQuery request, CancellationToken cancellationToken)
        {
            var result = new SingleResult<ListadoPedidosProcesadosFiltradosDTO>
            {
                Data = new ListadoPedidosProcesadosFiltradosDTO
                {
                    ListadoIdPedidos = new List<int?>(),
                    Pedidos = new List<PedidoProcesadoDTO>()
                },
                Errors = new List<string>()
            };
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            try
            {
                int? pedidoEsteAnyo = 0;
                int? pedidoAnyoPasado = 0;
                var listPedProcesados = new List<PedidoProcesado>();
                var ordenesSap = new List<Orden>();
                var operacionesSap = new List<Operaciones>();

                Debug.WriteLine($"Llamada identificar pedido - INICIO: {stopwatch.ElapsedMilliseconds}");
                if (request.Ultimo)
                {
                    //Obtener el ultimo pedido que no es 3, 6 ni 9.
                    var pattern = "^2.[0|3|6]....";
                    Regex rg = new Regex(pattern);
                    //var f = from pedidoProcesado in _contextProg.PedidoProcesado
                    //        let matches = !rg.IsMatch(pedidoProcesado.IdPedido.ToString())
                    //        where matches.Count > 0
                    //            orderby Id
                    listPedProcesados.Add(await _contextProg.PedidoProcesado.Where(o =>
                        o.IdPedido.ToString().Substring(2, 1) != "3" &&
                        o.IdPedido.ToString().Substring(2, 1) != "6" &&
                        o.IdPedido.ToString().Substring(2, 1) != "9").OrderByDescending(o => o.Id).FirstOrDefaultAsync(cancellationToken));
                }
                else if (request.IdPedido != null)
                {
                    if (request.IdPedido.ToString().Length < 7)
                    {
                        pedidoEsteAnyo = int.Parse(request.IdPedido.Value.ToString($"{DateTime.Now:yy}00000"));
                        pedidoAnyoPasado = int.Parse(request.IdPedido.Value.ToString($"{DateTime.Now.AddYears(-1):yy}00000"));
                        var pedidoPorAnyo = _contextProg.PedidoProcesado.Where(o =>
                            o.IdPedido == pedidoEsteAnyo || o.IdPedido == pedidoAnyoPasado).OrderByDescending(o => o.IdPedido).FirstOrDefault();
                        if (pedidoPorAnyo != null)
                        {
                            listPedProcesados.Add(pedidoPorAnyo);

                        }

                    }
                    else
                    {
                        listPedProcesados.Add(_contextProg.PedidoProcesado.FirstOrDefault(o => o.IdPedido == request.IdPedido));
                    }
                }
                else if (request.IdPedidoCliente != null)
                {
                    var queryIdPedidoCliente = _contextProg.PedidoProcesado.Where(o => o.Supedido == request.IdPedidoCliente).AsQueryable();
                    listPedProcesados.AddRange(request.IdCliente != null
                        ? queryIdPedidoCliente.Where(o => o.IdCliente == request.IdCliente)
                        : queryIdPedidoCliente);
                }
                else
                {
                    listPedProcesados = null;
                }

                //if (request.IdPedido.ToString().Length < 7 && !request.Ultimo)
                //{
                //    pedidoEsteAnyo = int.Parse(request.IdPedido.Value.ToString($"{DateTime.Now:yy}00000"));
                //    pedidoAnyoPasado = int.Parse(request.IdPedido.Value.ToString($"{DateTime.Now.AddYears(-1):yy}00000"));
                //    datos =  _contextProg.PedidoProcesado.FirstOrDefault(o => o.IdPedido == pedidoEsteAnyo || o.IdPedido == pedidoAnyoPasado);
                //}
                //else
                //{
                //    datos = request.Ultimo
                //        ? _contextProg.PedidoProcesado.OrderByDescending(o => o.Id).First()
                //        :  _contextProg.PedidoProcesado.FirstOrDefault(o => o.IdPedido == request.IdPedido);
                //}
                if (listPedProcesados == null || listPedProcesados.Count == 0 || listPedProcesados.Any(o => o == null))
                {
                    result.Errors.Add("Código de pedido no encontrado.");
                    return result;
                }

                foreach (var pp in listPedProcesados)
                {
                    var datosCliente = _contextProg.Clientes.FirstOrDefault(o => o.CodigoCliente == pp.IdCliente);
                    if (datosCliente == null)
                    {
                        result.Errors.Add("Datos de cliente para el último pedido no encontrados.");
                        return result;
                    }

                    List<FasePedidoDTO> objFasesDto = null;
                    var datosFasesPedido = _contextProg.TablaCodigosPedido.Where(o => o.Idpedido == pp.IdPedido).OrderBy(o => o.Fase);

                    if (datosFasesPedido.Any())
                    {
                        //Mapear a DTO
                        objFasesDto = TinyMapper.Map<List<FasePedidoDTO>>(datosFasesPedido.ToList());
                        foreach (var fase in objFasesDto)
                        {
                            fase.TextoIdCodigoAplicacion = _contextProg.CodApliAll
                                .Where(o => o.Codbaz == fase.Idcodigoaplicacion).Select(s => new { texto = $"{s.Codbaz},{s.Nombaz}" }).FirstOrDefault()?.texto ?? "N/A";
                            fase.TextoIdbarniz = _contextProg.TablaProductos.Where(o => o.Idproducto == fase.Idbarniz)
                                .Select(s => new { texto = $"{s.Idproducto},{s.Denominacion}" }).FirstOrDefault()?.texto ?? "N/A";
                        }
                    }

                    //Mapear a DTO
                    var objDto = TinyMapper.Map<PedidoProcesadoDTO>(pp);
                    //Rellenar campos restantes del DTO
                    objDto.FasesPedido = objFasesDto;
                    objDto.NombreCliente = _dataManipulationService.GetNombreCliente(pp.IdCliente.Value);
                    objDto.PesoHoja = _dataManipulationService.CalcularPesoPorHoja(pp.IdPedido.Value, pp.AnchoHjlta.Value, pp.LargoHjlta.Value, pp.EspesorHjlta.Value);
                   
                    objDto.PesoTotalHojas = _dataManipulationService.CalcularPesoTotal(objDto.HojasPedido.Value, objDto.PesoHoja.Value);
                    var maquinas = _contextProg.Maquinas.ToList();
                    var programaciones = _contextProg.TablaProgramacion
                        .Where(o => o.Idpedido == objDto.IdPedido)
                        .ToList();
                    objDto.TxtEstado = _dataManipulationService.GetTextoEstadoCodigosAplicacion(objDto.IdPedido.Value, false, false, maquinas, programaciones);
                  
                    objDto.Mermas = objDto.HojasTerminadas > 0
                        ? 1 - objDto.HojasTerminadas / objDto.HojasPedido
                        : null;
                    //Los campos de fechas se obtienen de DATO01LITA, en el access se obtienen de una tabla vinculada directamente a dato01Lita
                    //pero con c# puedo conectar directamente de forma más cómoda.
                    var datosFechas = _contextLita01.Fechas.FirstOrDefault(o => o.Codigo == objDto.IdPedido);
                    if (datosFechas != null)
                    {
                        objDto.FechaFtp = string.IsNullOrWhiteSpace(datosFechas.Fechaftp) ? null : Convert.ToDateTime(datosFechas.Fechaftp);
                        objDto.FechaHojalata = string.IsNullOrWhiteSpace(datosFechas.Fechahojalata) ? null : Convert.ToDateTime(datosFechas.Fechahojalata);
                        objDto.FechaLanzamiento = string.IsNullOrWhiteSpace(datosFechas.Fechamaxima) ? null : Convert.ToDateTime(datosFechas.Fechamaxima);
                        objDto.FechaContrato = string.IsNullOrWhiteSpace(datosFechas.Fcontrato) ? null : Convert.ToDateTime(datosFechas.Fcontrato);
                        objDto.FechaAcordada = string.IsNullOrWhiteSpace(datosFechas.Facordada) ? null : Convert.ToDateTime(datosFechas.Facordada);
                    }

                    //objDto.FechaPedido = !string.IsNullOrEmpty(datosFechas.Fechapedido) ? Convert.ToDateTime(datosFechas.Fechapedido) : null;
                    //objDto.FechaFin = datosFechas.Fechaftp;
                    //objDto.FechaEntregaSolicitada = datosFechas.Fechaftp;

                    //Buscamos si tiene reprocesos, ya sean 3 o 6.
                    objDto.IdPedidoReproceso = _dataManipulationService.GetPedidoReproceso(pp.IdPedido.Value);
                    objDto.CaracteristicasHojalata = _dataManipulationService.GetCaracteristicasHojalata(pp.IdPedido.Value);
                    objDto.Esc = _dataManipulationService.GetDatosPlano(datosCliente.Id, objDto.Plano, "Es");
                    var ppdto = new PedidoProcesadoDTO();
                    var type = ppdto.GetType();

                    var campoC0_PEDNames = Enumerable.Range(1, 11).Select(i => i < 10 ? $"C0{i}ped" : $"Co{i}ped").ToList();

                    //TINTAS
                    foreach (var campoC0_PEDName in campoC0_PEDNames)
                    {
                        var campoC0_PEDValue = type.GetProperty(campoC0_PEDName)?.GetValue(objDto);
                        var codTintas = campoC0_PEDValue != null ? int.Parse(campoC0_PEDValue.ToString()) : 0;

                        if (codTintas == 0) continue;

                        var aarticuTinta = _contextLita01.AArticu
                            .FirstOrDefault(o => o.Estadis.Contains("TI") && o.Codigo2 != null && o.Codigo2 == codTintas.ToString());
                        var valorTinta = $"{aarticuTinta?.Codigo2 ?? string.Empty},{aarticuTinta?.Descrip ?? string.Empty}";
                        var campoTexto = $"{campoC0_PEDName}Text";
                        var propertyInfo = objDto.GetType().GetProperty(campoTexto);

                        if (propertyInfo != null)
                        {
                            propertyInfo.SetValue(objDto, Convert.ChangeType(valorTinta, propertyInfo.PropertyType), null);
                        }
                    }

                    for (var i = 1; i <= 4; i++)
                    {
                        // hay que localizar el codigo de las tintas que estén en la posicion i
                        // y ver si es distinto de 0 o no nulo
                        var campoCD_PEDName = $"Cd{i}ped";

                        var campoCD_PEDValue = type.GetProperty(campoCD_PEDName)?.GetValue(objDto);
                        var codTintas = campoCD_PEDValue != null ? int.Parse(campoCD_PEDValue.ToString()) : 0;
                        if (codTintas == 0) continue;
                        //Codtintas bebe de a_articu y no se está actualizando en los jobs de actualizaciones así que la 
                        //sustituimos por su verdadero origen que es A_ARTICU
                        var aarticuTinta = _contextLita01.AArticu
                            .FirstOrDefault(o => o.Estadis.Contains("TI") && o.Codigo2 != null && o.Codigo2 == codTintas.ToString());
                        var valorTinta = $"{aarticuTinta?.Codigo2 ?? string.Empty},{aarticuTinta?.Descrip ?? string.Empty}";
                        var campoTexto = $"{campoCD_PEDName}Text";
                        PropertyInfo propertyInfo = objDto.GetType().GetProperty(campoTexto);
                        propertyInfo.SetValue(objDto, Convert.ChangeType(valorTinta, propertyInfo.PropertyType), null);
                    }
                    objDto.ObsAlmacen = _dataManipulationService.GetObservacionesAlmacen(pp.IdPedido.Value, true, false, null);
                    //Prueba temporal para obtener el texto (YP)
                    if (objDto.HojasTerminadas > 0 && !objDto.ObsAlmacen.Contains("(YP)"))
                    {
                        objDto.ObsAlmacen += " (YP) ";
                    }
                    objDto.ObsCalidad = await _dataManipulationService.GetObservacionesCalidadLite(pp.IdPedido.Value, null);
                    //objDto.ExisteFicheroFotomecanica = Directory.EnumerateFiles(@"\\dc1\Company\COPIA APOGEEX\", $"{objDto.IdPedido}*.pdf").Any();

                    result.Data.Pedidos.Add(objDto);
                    result.Data.ListadoIdPedidos.Add(objDto.IdPedido.Value);
                }

            }
            catch (Exception e)
            {
                result.Errors.Add($"{e.Message}--{(!string.IsNullOrWhiteSpace(e.InnerException?.Message) ? e.InnerException : string.Empty)}");
                return result;
            }
            stopwatch.Stop();
            return result;
        }
    }
}
