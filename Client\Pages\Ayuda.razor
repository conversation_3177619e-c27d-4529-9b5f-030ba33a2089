﻿@page "/Ayuda"
@using System.Net
@using System.Text.Json
@using Microsoft.AspNetCore.Authorization
@using ProgramadorGeneralBLZ.Shared
@using ProgramadorGeneralBLZ.Shared.ResponseModels

@inject IConfiguration Config
@inject SpinnerService SpinnerService
@inject IToastService ToastService
@inject HttpClient Http
@inject IHttpClientFactory ClientFactory

@attribute [Authorize(Roles = $"{Roles.Admin}, {Roles.Programador}")]

<PageTitle>Ayuda</PageTitle>

<AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}")">
	<Authorized Context="authContext">
		<DxLayoutBreakpoint DeviceSize="DeviceSize.Large" @bind-IsActive="@_isSmallScreen" />
		<div class="h-100 overflow-auto px-2 py-1">
			<h3>Resultados inválidos para la conversión de cadenas de texto a números enteros:</h3>
			<DxGrid Data="@_gruposDatosInvalidos"
					CustomizeCellDisplayText="GridCabecera_CustomizeCellDisplayText"
					VirtualScrollingEnabled="false"
					TextWrapEnabled="true"
					FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always"
					ColumnResizeMode="GridColumnResizeMode.NextColumn"
					PageSize="10">
				<Columns>
					<DxGridDataColumn Caption="Tabla" />
				</Columns>
				<DetailRowTemplate>
					<DxGrid AutoExpandAllGroupRows="true"
							ColumnResizeMode="GridColumnResizeMode.NextColumn"
							TextWrapEnabled="false"
							UnboundColumnData="GridRegistrosInvalidos_CustomUnboundColumnData"
							Data="GetDatosInvalidosPorTabla(context)"
							CssClass="tabla_scroll">
						<Columns>
							@BuildColumns_RegistrosInvalidos(context)
						</Columns>
					</DxGrid>
				</DetailRowTemplate>
			</DxGrid>
		</div>
	</Authorized>
	<NotAuthorized>
		<NoPuedesPasar />
	</NotAuthorized>
</AuthorizeView>

@code {
	private bool _isSmallScreen;
	private List<IGrouping<string, Dictionary<string, object>>> _gruposDatosInvalidos;

	protected override async Task OnInitializedAsync()
	{
		SpinnerService.Show();
		_gruposDatosInvalidos = [];
		Console.WriteLine($"url= {Config["API_ActualizarDB"]}");
		var client = ClientFactory.CreateClient();
		client.Timeout = TimeSpan.FromSeconds(400);

		var response = await client.GetAsync($"{Config["API_ActualizarDB"]}/validaciondatosvarchartoint", CancellationToken.None);

		if (response.StatusCode != HttpStatusCode.OK)
		{
			var msj = await response.Content.ReadFromJsonAsync<ApiProblem>();
			ToastService.ShowError($"ERROR: Status {msj.Status} - {msj.Detail}");
		}
		else
		{
			var strJson = await response.Content.ReadAsStringAsync();
			var listaOriginal = JsonSerializer
				.Deserialize<List<List<Dictionary<string, object>>>>(strJson, new JsonSerializerOptions
				{
					PropertyNameCaseInsensitive = true
				});
			if (listaOriginal != null && listaOriginal.Any())
			{
				var listaResultados = listaOriginal
					.SelectMany(x => x)
					.ToList();

				// agrupamos por NombreTabla, que es un campo que en principio siempre debería devolver el sp
				_gruposDatosInvalidos = listaResultados
					.Where(d => d.ContainsKey("NombreTabla") && d.Values.Any())
					.GroupBy(d =>
						$"{d["NombreTabla"]} (Columnas Comprobadas: {(d["ColumnasComprobadas"])})")
					.ToList();

			}
		}
		SpinnerService.Hide();
	}

	private void GridCabecera_CustomizeCellDisplayText(GridCustomizeCellDisplayTextEventArgs e)
	{
		// solo deifnimos una columna, por tanto a la que entra le ponemos el nombre de la tabla
		var kv = (IGrouping<string, Dictionary<string, object>>)e.DataItem;
		e.DisplayText = $"{kv.Key} ({kv.Count()})";
	}

	private List<Dictionary<string, object>> GetDatosInvalidosPorTabla(GridDetailRowTemplateContext context)
	{
		var kv = (IGrouping<string, Dictionary<string, object>>)context.DataItem;
		var listaSrc = kv.ToList();

		return listaSrc;
	}

	private void GridRegistrosInvalidos_CustomUnboundColumnData(GridUnboundColumnDataEventArgs e)
	{
		if (e.DataItem is Dictionary<string, object> dict)
		{
			// Verificar si el diccionario contiene la clave
			if (dict.ContainsKey(e.FieldName))
			{
				e.Value = dict[e.FieldName];
			}
			else
			{
				e.Value = string.Empty; // O null, según prefieras
			}
		}
	}

	private RenderFragment BuildColumns_RegistrosInvalidos(GridDetailRowTemplateContext context)
	{
		var kv = ((IGrouping<string, Dictionary<string, object>>)context.DataItem)
			.SelectMany(d => d.Keys)
			.Distinct()
			.Where(i => !i.Equals("NombreTabla") && !i.Equals("ColumnasComprobadas"))
			.ToList();

		RenderFragment columnas = b =>
		{
			int contador = 0;
			foreach (var col in kv)
			{
				b.OpenComponent(contador, typeof(DxGridDataColumn));
				b.AddAttribute(0, "Caption", col);
				b.AddAttribute(1, "FieldName", col); // Usamos el nombre de la columna directamente
				b.AddAttribute(2, "UnboundType", GridUnboundColumnType.String);
				b.CloseComponent();
				contador++;
			}
		};
		return columnas;
	}
}
