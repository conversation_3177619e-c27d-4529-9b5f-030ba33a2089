﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace ProgramadorGeneralBLZ.Server.Models.InterfaseBD;

public partial class Motivos
{
    public string IdMotivo { get; set; }

    public string IdTroquel { get; set; }

    public string IdCliente { get; set; }

    public string RefMotivoCliente { get; set; }

    public string Marca { get; set; }

    public string DescripcionComercial { get; set; }

    public string TipoProducto { get; set; }

    public bool Embuticion { get; set; }

    public string IdTratamiento { get; set; }

    public string CodigoArchivo { get; set; }

    public string ReferenciaColor { get; set; }

    public string MuestraFisica { get; set; }

    public string IdMotivoPrecedente { get; set; }

    public bool Gama { get; set; }

    public bool Activo { get; set; }

    public DateTime FechaCreacion { get; set; }

    public TimeSpan HoraCreacion { get; set; }

    public string UsuarioCreacion { get; set; }

    public DateTime FechaMod { get; set; }

    public TimeSpan HoraMod { get; set; }

    public string UsuarioMod { get; set; }

    public DateTime FechaObsoleto { get; set; }

    public string UsuarioObsoleto { get; set; }

    public virtual Debmas ClienteNavigation { get; set; }

    public virtual TratamientosCabecera TratamientoNavigation { get; set; }

    public virtual Troqueles TroquelNavigation { get; set; }

    public virtual ICollection<MotivosColores> MotivosColores { get; set; } = new List<MotivosColores>();

    public virtual ICollection<Orden> Orden { get; set; } = new List<Orden>();
}