﻿{
   "CodeGenerationMode": 3,
   "ContextClassName": "InterfaseBDContext",
   "ContextNamespace": null,
   "DefaultDacpacSchema": null,
   "FilterSchemas": false,
   "IncludeConnectionString": true,
   "ModelNamespace": null,
   "OutputContextPath": "Data\\InterfaseBD",
   "OutputPath": "Models\\InterfaseBD",
   "PreserveCasingWithRegex": true,
   "ProjectRootNamespace": "ProgramadorGeneralBLZ.Server",
   "Schemas": null,
   "SelectedHandlebarsLanguage": 0,
   "SelectedToBeGenerated": 0,
   "T4TemplatePath": null,
   "Tables": [
      {
         "Name": "[dbo].[BATMAS]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[CARA_APLICAR]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[CLASE_MOVIMIENTO]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[CODIGOS_APLICACION]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[COLORES]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[CONSUMO_ALTA]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[DEBMAS]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[FAMILIA]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[GRUPO_ARTICULOS]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[HORAS]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MATMAS]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MENSAJE_OPERACIONES]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MOTIVOS]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MOTIVOS_COLORES]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[OPERACIONES]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[ORDEN]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[PLANOS]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[POSICION_ESCUADRA]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[RAMO]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[RESERVAS]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[RESPONSABLE]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[SENTIDO_LECTURA]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[STOCK]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[SUBFAMILIA]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[TIPO_ELEMENTO]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[TIPO_MATERIAL]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[TIPO_POSICION]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[TIPO_TRATAMIENTO]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[TRATAMIENTOS_CABECERA]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[TRATAMIENTOS_DETALLE]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[TROQUELES]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[UNIDAD_MEDIDA]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[USO_TINTA_MOTIVO]",
         "ObjectType": 0
      }
   ],
   "UiHint": "litaent.Prueba.dbo",
   "UncountableWords": null,
   "UseBoolPropertiesWithoutDefaultSql": false,
   "UseDatabaseNames": false,
   "UseDateOnlyTimeOnly": false,
   "UseDbContextSplitting": false,
   "UseDecimalDataAnnotationForSprocResult": true,
   "UseFluentApiOnly": true,
   "UseHandleBars": false,
   "UseHierarchyId": false,
   "UseInflector": false,
   "UseLegacyPluralizer": false,
   "UseManyToManyEntity": false,
   "UseNoDefaultConstructor": false,
   "UseNoNavigations": false,
   "UseNoObjectFilter": false,
   "UseNodaTime": false,
   "UseNullableReferences": false,
   "UseSchemaFolders": false,
   "UseSchemaNamespaces": false,
   "UseSpatial": false,
   "UseT4": false
}