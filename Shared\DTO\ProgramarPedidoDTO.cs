﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProgramadorGeneralBLZ.Shared.DTO
{
    public class ProgramarPedidoDTO
    {
        public List<DatosGeneralesPedidoDTO>? Pedidos { get; set; }
        public List<DatosGeneralesPedidoFotomecanicaLitoDTO>? PedidosLito { get; set; }
        [NotMapped]
        public MaquinaDTO Maquina { get; set; }
        [NotMapped]
        public CodigoAplicacionDTO? CodigoAplicacion { get; set; }
        [NotMapped]
        public ProductoDTO? Barniz { get; set; }
        [NotMapped]
        public bool WetOnWet { get; set; }

    }

}
