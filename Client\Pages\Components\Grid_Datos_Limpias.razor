﻿@using ProgramadorGeneralBLZ.Shared.ResponseModels
@using ProgramadorGeneralBLZ.Shared.DTO
@using ProgramadorGeneralBLZ.Shared

@inject IToastService toastService
@inject HttpClient http
@inject SpinnerService _spinnerService

<h3>Datos <PERSON>zas</h3>
<DxGrid Data="@datosLimpias" SizeMode="SizeMode.Medium"
        Context="ContextGridDatosLimpias" @ref="GridLimpias"
        PageSize="20" AllowSort="true" ShowFilterRow="true"
        CssClass="ch-700">
    <Columns>
        <DxGridDataColumn FieldName="DeProductoxparatirar" Caption="Del Producto" />
        <DxGridDataColumn FieldName="DeIdProducto" Caption="ID" DisplayFormat="F0" Width="120px" />
        <DxGridDataColumn FieldName="Aproductoxparatirar" Caption="Al Producto" />
        <DxGridDataColumn FieldName="AidProducto" Caption="ID" DisplayFormat="F0" Width="120px" />
        <DxGridDataColumn FieldName="TipoLimpieza" Width="150px" TextAlignment="GridTextAlignment.Center"/>
    </Columns>
</DxGrid>
@code {
    [Parameter]
    public int? CodBarniz { get; set; }

    DxGrid? GridLimpias;
    ListResult<TblLimpiezasDTO> resultDatosLimpias { get; set; }
    List<TblLimpiezasDTO> datosLimpias { get; set; }

    protected override async Task OnInitializedAsync()
    {
        resultDatosLimpias = await http.GetFromJsonAsync<ListResult<TblLimpiezasDTO>>($"DatosGenerales/limpias/{CodBarniz.Value}");
        if (resultDatosLimpias.Errors.Any())
        {
            toastService.ShowInfo($"{resultDatosLimpias.Errors.First()}");
        }
        else
        {
            datosLimpias = resultDatosLimpias.Data;
        }
        StateHasChanged();
    }
}
