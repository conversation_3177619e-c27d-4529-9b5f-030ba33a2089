﻿<?xml version="1.0" encoding="utf-8"?>
<XtraReportsLayoutSerializer SerializerVersion="********" Ref="1" ControlType="DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v22.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Name="BarnizadoReportV2" Landscape="true" Margins="43, 57, 32.29167, 27.83807" PaperKind="A4" PageWidth="1169" PageHeight="827" Version="22.2" DataMember="PedidoProgramadoEnviadoImprimir" DataSource="#Ref-0" Font="Arial, 9.75pt">
  <Parameters>
    <Item1 Ref="3" Visible="false" Description="Parameter1" Name="MaquinaNombreParameter" />
    <Item2 Ref="4" Visible="false" Description="Parameter1" Name="TipoLavadoParameter" />
    <Item3 Ref="6" Visible="false" Description="VerCambiosParameter" ValueInfo="False" Name="VerCambiosParameter" Type="#Ref-5" />
  </Parameters>
  <Bands>
    <Item1 Ref="7" ControlType="TopMarginBand" Name="TopMargin" HeightF="32.29167" />
    <Item2 Ref="8" ControlType="BottomMarginBand" Name="BottomMargin" HeightF="27.83807" />
    <Item3 Ref="9" ControlType="GroupHeaderBand" Name="GroupHeader1" GroupUnion="WithFirstDetail" Level="1" HeightF="27.00001">
      <GroupFields>
        <Item1 Ref="10" FieldName="ApliProducto" />
        <Item2 Ref="11" FieldName="Orden" />
      </GroupFields>
      <SortingSummary Ref="12" Enabled="true" Function="Min" FieldName="Posicion" />
      <Controls>
        <Item1 Ref="13" ControlType="XRTable" Name="table1" SizeF="1069,27.00001" LocationFloat="0,0">
          <Rows>
            <Item1 Ref="14" ControlType="XRTableRow" Name="tableRow1" Weight="1">
              <Cells>
                <Item1 Ref="15" ControlType="XRTableCell" Name="tableCell2" Weight="1.63285971758865" StyleName="GroupData1" Font="Arial, 12pt, style=Bold">
                  <ExpressionBindings>
                    <Item1 Ref="16" EventName="BeforePrint" PropertyName="Text" Expression="[ApliProducto]" />
                  </ExpressionBindings>
                  <StylePriority Ref="17" UseFont="false" />
                </Item1>
                <Item2 Ref="18" ControlType="XRTableCell" Name="xrTableCell4" Weight="0.21926948147314415" Multiline="true" Text="xrTableCell4" TextAlignment="MiddleCenter" StyleName="GroupData1" Font="Arial, 14pt, style=Bold" ForeColor="Red" BackColor="White">
                  <ExpressionBindings>
                    <Item1 Ref="19" EventName="BeforePrint" PropertyName="Text" Expression="Iif([Tuberia]==true,'(TUBERIA)' ,'' )" />
                  </ExpressionBindings>
                  <StylePriority Ref="20" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                </Item2>
                <Item3 Ref="21" ControlType="XRTableCell" Name="xrTableCell3" Weight="0.27671140771592029" Multiline="true" Text="xrTableCell3" TextAlignment="MiddleLeft" StyleName="GroupData1" Font="Arial, 14pt, style=Bold" ForeColor="Red" BackColor="White">
                  <ExpressionBindings>
                    <Item1 Ref="22" EventName="BeforePrint" PropertyName="Text" Expression="[TipoLavada]" />
                  </ExpressionBindings>
                  <StylePriority Ref="23" UseFont="false" UseForeColor="false" UseBackColor="false" UseTextAlignment="false" />
                </Item3>
              </Cells>
            </Item1>
          </Rows>
        </Item1>
      </Controls>
    </Item3>
    <Item4 Ref="24" ControlType="GroupHeaderBand" Name="GroupHeader2" GroupUnion="WithFirstDetail" HeightF="19.66663">
      <Controls>
        <Item1 Ref="25" ControlType="XRTable" Name="table2" SizeF="1069,19.66663" LocationFloat="0,0">
          <Rows>
            <Item1 Ref="26" ControlType="XRTableRow" Name="tableRow2" Weight="1">
              <Cells>
                <Item1 Ref="27" ControlType="XRTableCell" Name="tableCell8" Weight="0.11369220843724703" Text="Cliente" TextAlignment="MiddleLeft" StyleName="DetailCaption1" Font="Arial, 12pt, style=Italic">
                  <StylePriority Ref="28" UseFont="false" UseTextAlignment="false" />
                </Item1>
                <Item2 Ref="29" ControlType="XRTableCell" Name="tableCell9" Weight="0.058183287115597844" Text="Hojas" TextAlignment="MiddleCenter" StyleName="DetailCaption1" Font="Arial, 12pt, style=Italic" Padding="2,2,0,0,100">
                  <StylePriority Ref="30" UseFont="false" UsePadding="false" UseTextAlignment="false" />
                </Item2>
                <Item3 Ref="31" ControlType="XRTableCell" Name="tableCell12" Weight="0.23145065683517957" Text="Hojalata" TextAlignment="MiddleCenter" StyleName="DetailCaption1" Font="Arial, 12pt, style=Italic">
                  <StylePriority Ref="32" UseFont="false" UseTextAlignment="false" />
                </Item3>
                <Item4 Ref="33" ControlType="XRTableCell" Name="xrTableCell1" Weight="0.074836277591899092" Multiline="true" Text="Pedido" TextAlignment="MiddleCenter" StyleName="DetailCaption1" Font="Arial, 12pt, style=Italic">
                  <StylePriority Ref="34" UseFont="false" UseTextAlignment="false" />
                </Item4>
                <Item5 Ref="35" ControlType="XRTableCell" Name="xrTableCell2" Weight="0.082343632975902276" Multiline="true" Text="Diametro" TextAlignment="MiddleLeft" StyleName="DetailCaption1" Font="Arial, 12pt, style=Italic" Padding="0,6,0,0,100">
                  <StylePriority Ref="36" UseFont="false" UsePadding="false" UseTextAlignment="false" />
                </Item5>
                <Item6 Ref="37" ControlType="XRTableCell" Name="tableCell21" Weight="0.54269306509471393" Text="Observaciones" TextAlignment="MiddleLeft" StyleName="DetailCaption1" Font="Arial, 12pt, style=Italic">
                  <StylePriority Ref="38" UseFont="false" UseTextAlignment="false" />
                </Item6>
              </Cells>
            </Item1>
          </Rows>
        </Item1>
      </Controls>
    </Item4>
    <Item5 Ref="39" ControlType="DetailBand" Name="Detail" HeightF="127.0557" KeepTogether="true" Borders="Bottom" BorderWidth="3" BorderDashStyle="Solid">
      <SortFields>
        <Item1 Ref="40" FieldName="Posicion" />
      </SortFields>
      <Controls>
        <Item1 Ref="41" ControlType="XRLabel" Name="xrLabel16" TextFormatString="{0:dd / HH:mm}" CanShrink="true" Text="FormatString('{0:dd HH:mm}', [HoraFinEstimada])&#xA;" TextAlignment="TopLeft" SizeF="55.26044,18.74997" LocationFloat="1013.74,53.75328" Font="Arial, 8pt" Padding="6,0,0,0,100" Borders="None" BorderDashStyle="Solid">
          <ExpressionBindings>
            <Item1 Ref="42" EventName="BeforePrint" PropertyName="Text" Expression="[HoraComienzoEstimada]" />
            <Item2 Ref="43" EventName="BeforePrint" PropertyName="Visible" Expression="?VerCambiosParameter=true" />
          </ExpressionBindings>
          <StylePriority Ref="44" UseFont="false" UsePadding="false" UseBorders="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="45" ControlType="XRLabel" Name="xrLabel14" Multiline="true" CanShrink="true" Text="xrLabel14" SizeF="543.1304,18.74997" LocationFloat="0,93.75003" Font="Arial, 8pt" Padding="0,0,0,0,100" Borders="None">
          <ExpressionBindings>
            <Item1 Ref="46" EventName="BeforePrint" PropertyName="Text" Expression="[TiposCambio]" />
            <Item2 Ref="47" EventName="BeforePrint" PropertyName="Visible" Expression="?VerCambiosParameter=true&#xA;" />
          </ExpressionBindings>
          <StylePriority Ref="48" UseFont="false" UsePadding="false" UseBorders="false" />
        </Item2>
        <Item3 Ref="49" ControlType="XRLabel" Name="xrLabel10" CanGrow="false" Text="PROGRAMACIÓN BARNIZADO" TextAlignment="MiddleCenter" WordWrap="false" SizeF="1069,14.55568" LocationFloat="0,112.5" StyleName="Title" Font="Arial, 10pt" ForeColor="Red" Padding="0,0,0,0,100">
          <ExpressionBindings>
            <Item1 Ref="50" EventName="BeforePrint" PropertyName="Text" Expression="'------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------'" />
          </ExpressionBindings>
          <StylePriority Ref="51" UseFont="false" UseForeColor="false" UsePadding="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="52" ControlType="XRLabel" Name="xrLabel13" TextFormatString="{0}" Multiline="true" CanGrow="false" CanShrink="true" TextAlignment="TopLeft" SizeF="264.845,47.9167" LocationFloat="0,45.83333" Font="Arial, 8pt" BackColor="Transparent" Padding="0,0,0,0,100" BorderColor="Black" Borders="None" BorderWidth="1">
          <Summary Ref="53" FormatString="{0}" />
          <ExpressionBindings>
            <Item1 Ref="54" EventName="BeforePrint" PropertyName="Text" Expression="[ObsCalidad]" />
          </ExpressionBindings>
          <StylePriority Ref="55" UseFont="false" UseBackColor="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="56" ControlType="XRLabel" Name="xrLabel3" TextFormatString="{0}" Multiline="true" CanGrow="false" CanShrink="true" TextAlignment="TopLeft" SizeF="278.2854,47.9167" LocationFloat="264.845,45.83333" Font="Arial, 7pt" BackColor="Transparent" Padding="0,0,0,0,100" BorderColor="Black" Borders="None" BorderWidth="1">
          <Summary Ref="57" FormatString="{0}" />
          <ExpressionBindings>
            <Item1 Ref="58" EventName="BeforePrint" PropertyName="Text" Expression="[TextoEstadoCodApli]" />
          </ExpressionBindings>
          <StylePriority Ref="59" UseFont="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="60" ControlType="XRLabel" Name="Texto164" CanGrow="false" TextAlignment="TopLeft" SizeF="83.65752,18.99996" LocationFloat="0,25.00003" Font="Arial, 10pt, style=Bold" BackColor="Transparent" Padding="0,0,0,0,100" BorderColor="Black" Borders="None" BorderWidth="1">
          <Summary Ref="61" FormatString="{0}" />
          <ExpressionBindings>
            <Item1 Ref="62" EventName="BeforePrint" PropertyName="Text" Expression="Iif([Urgente], 'URGENTE:', '')" />
          </ExpressionBindings>
          <StylePriority Ref="63" UseTextAlignment="false" />
        </Item6>
        <Item7 Ref="64" ControlType="XRLabel" Name="RequeridoEnFecha" TextFormatString="{0:dd/MM}" CanGrow="false" TextAlignment="TopLeft" SizeF="80.76093,19" LocationFloat="85.78642,25.00003" Font="Arial, 10pt, style=Bold" BackColor="Transparent" Padding="0,0,0,0,100" BorderColor="Black" Borders="None" BorderWidth="1">
          <ExpressionBindings>
            <Item1 Ref="65" EventName="BeforePrint" PropertyName="Text" Expression="[RequeridoEnFecha]" />
          </ExpressionBindings>
          <StylePriority Ref="66" UseTextAlignment="false" />
        </Item7>
        <Item8 Ref="67" ControlType="XRPanel" Name="Cuadro116" SizeF="47.43985,19" LocationFloat="437.4987,25" BackColor="Transparent" BorderColor="Black" Borders="All" BorderWidth="1" BorderDashStyle="Solid">
          <StylePriority Ref="68" UseBackColor="false" UseBorderDashStyle="false" />
        </Item8>
        <Item9 Ref="69" ControlType="XRLabel" Name="Plano" TextFormatString="{0}" CanGrow="false" TextAlignment="TopLeft" SizeF="181.812,20.83333" LocationFloat="232.1208,25" Font="Arial, 9pt" BackColor="Transparent" Padding="0,0,0,0,100" BorderColor="Black" Borders="None" BorderWidth="1">
          <Summary Ref="70" FormatString="{0}" />
          <ExpressionBindings>
            <Item1 Ref="71" EventName="BeforePrint" PropertyName="Text" Expression="[Plano]" />
          </ExpressionBindings>
          <StylePriority Ref="72" UseFont="false" UseTextAlignment="false" />
        </Item9>
        <Item10 Ref="73" ControlType="XRLabel" Name="Etiqueta144" CanGrow="false" Text="Plano:" TextAlignment="TopLeft" SizeF="44.74005,20.83335" LocationFloat="187.3807,25" Font="Times New Roman, 9pt, style=Italic" ForeColor="Navy" BackColor="Transparent" Padding="0,0,0,0,100" BorderColor="Black" Borders="None" BorderWidth="1">
          <StylePriority Ref="74" UseFont="false" />
        </Item10>
        <Item11 Ref="75" ControlType="XRLabel" Name="txt_barniz_por_pedido" TextFormatString="{0:#.00}" CanGrow="false" CanShrink="true" TextAlignment="TopLeft" SizeF="55.26007,18.74996" LocationFloat="1013.74,35.00335" Font="Arial, 6pt" BackColor="Transparent" Padding="6,0,0,0,100" BorderColor="Black" Borders="None" BorderWidth="1">
          <ExpressionBindings>
            <Item1 Ref="76" EventName="BeforePrint" PropertyName="Text" Expression="[BarnizNecesario]" />
          </ExpressionBindings>
          <StylePriority Ref="77" UsePadding="false" UseTextAlignment="false" />
        </Item11>
        <Item12 Ref="78" ControlType="XRLabel" Name="xrLabel2" TextFormatString="{0:dd / HH:mm}" CanShrink="true" Text="FormatString('{0:dd HH:mm}', [HoraFinEstimada])&#xA;" TextAlignment="MiddleLeft" SizeF="55.26031,18.74997" LocationFloat="1013.74,72.50328" Font="Arial, 8pt" Padding="0,0,0,0,100" Borders="None" BorderDashStyle="Solid">
          <ExpressionBindings>
            <Item1 Ref="79" EventName="BeforePrint" PropertyName="Text" Expression="[HoraFinEstimada]" />
          </ExpressionBindings>
          <StylePriority Ref="80" UseFont="false" UsePadding="false" UseBorders="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item12>
        <Item13 Ref="81" ControlType="XRLabel" Name="xrLabel1" CanShrink="true" Text="[Observaciones] + Char(13) + Char(10) + [Obspaseposterior]&#xA;" TextAlignment="TopLeft" SizeF="55.26044,20.42" LocationFloat="1013.74,14.58333" Font="Arial, 9pt, style=Bold" Padding="6,0,0,0,100" Borders="None">
          <ExpressionBindings>
            <Item1 Ref="82" EventName="BeforePrint" PropertyName="Text" Expression="IIf([flejar]=1,'SI','NO')" />
          </ExpressionBindings>
          <StylePriority Ref="83" UseFont="false" UsePadding="false" UseBorders="false" UseTextAlignment="false" />
        </Item13>
        <Item14 Ref="84" ControlType="XRLabel" Name="xrLabel4" TextAlignment="MiddleLeft" SizeF="110.1678,25" LocationFloat="0,0" StyleName="DetailData1" Font="Arial, 9pt" ForeColor="Black" Padding="6,6,0,0,100" BorderColor="Transparent" Borders="None" BorderWidth="2" BorderDashStyle="Dash">
          <ExpressionBindings>
            <Item1 Ref="85" EventName="BeforePrint" PropertyName="Text" Expression="[IdCliente]+', '+[NombreCliente]" />
          </ExpressionBindings>
          <StylePriority Ref="86" UseFont="false" UseForeColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item14>
        <Item15 Ref="87" ControlType="XRLabel" Name="xrLabel5" TextAlignment="MiddleLeft" SizeF="56.37953,25" LocationFloat="110.1678,0" StyleName="DetailData1" Font="Arial, 9pt" ForeColor="Black" Padding="6,6,0,0,100" BorderColor="Transparent" Borders="Left" BorderWidth="2" BorderDashStyle="Dash">
          <ExpressionBindings>
            <Item1 Ref="88" EventName="BeforePrint" PropertyName="Text" Expression="FormatString('{0:#,##0}', [HojasPedido])" />
          </ExpressionBindings>
          <StylePriority Ref="89" UseFont="false" UseForeColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item15>
        <Item16 Ref="90" ControlType="XRLabel" Name="xrLabel6" TextAlignment="MiddleLeft" SizeF="227.9514,25" LocationFloat="166.5474,0" StyleName="DetailData1" Font="Arial, 9pt" ForeColor="Black" Padding="6,6,0,0,100" BorderColor="Transparent" Borders="Left" BorderWidth="2" BorderDashStyle="Dash">
          <ExpressionBindings>
            <Item1 Ref="91" EventName="BeforePrint" PropertyName="Text" Expression="[CaractHjlta]" />
          </ExpressionBindings>
          <StylePriority Ref="92" UseFont="false" UseForeColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item16>
        <Item17 Ref="93" ControlType="XRLabel" Name="xrLabel7" TextAlignment="MiddleLeft" SizeF="80.43982,25" LocationFloat="394.4987,0" StyleName="DetailData1" Font="Arial, 9pt" ForeColor="Black" Padding="6,6,0,0,100" BorderColor="Transparent" Borders="Left" BorderWidth="2" BorderDashStyle="Dash">
          <ExpressionBindings>
            <Item1 Ref="94" EventName="BeforePrint" PropertyName="Text" Expression="[Idpedido]" />
          </ExpressionBindings>
          <StylePriority Ref="95" UseFont="false" UseForeColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item17>
        <Item18 Ref="96" ControlType="XRLabel" Name="xrLabel8" TextFormatString="{0:#.0}" TextAlignment="MiddleLeft" SizeF="68.19189,25" LocationFloat="474.9385,0" StyleName="DetailData1" Font="Arial, 9pt" ForeColor="Black" Padding="6,6,0,0,100" BorderColor="Transparent" Borders="Left" BorderWidth="2" BorderDashStyle="Dash">
          <ExpressionBindings>
            <Item1 Ref="97" EventName="BeforePrint" PropertyName="Text" Expression="[Formato]" />
          </ExpressionBindings>
          <StylePriority Ref="98" UseFont="false" UseForeColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item18>
        <Item19 Ref="99" ControlType="XRLabel" Name="xrLabel9" Multiline="true" CanShrink="true" TextAlignment="TopLeft" SizeF="307.0204,112.5" LocationFloat="543.1304,0" StyleName="DetailData1" Font="Arial, 8pt" ForeColor="Black" Padding="6,6,0,0,100" BorderColor="Transparent" Borders="Left" BorderWidth="2" BorderDashStyle="Dash">
          <ExpressionBindings>
            <Item1 Ref="100" EventName="BeforePrint" PropertyName="Text" Expression="[Observaciones] + Char(13) + Char(10) + [Obspaseposterior]" />
          </ExpressionBindings>
          <StylePriority Ref="101" UseFont="false" UseForeColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item19>
        <Item20 Ref="102" ControlType="XRLabel" Name="xrLabel11" Multiline="true" CanShrink="true" TextAlignment="TopLeft" SizeF="163.5892,112.5" LocationFloat="850.1508,0" StyleName="DetailData1" Font="Arial, 8pt" ForeColor="Black" BackColor="Transparent" Padding="6,6,0,0,100" BorderColor="Transparent" Borders="Left" BorderWidth="2" BorderDashStyle="Dash">
          <ExpressionBindings>
            <Item1 Ref="103" EventName="BeforePrint" PropertyName="Text" Expression="[ObsAlmacen]" />
          </ExpressionBindings>
          <StylePriority Ref="104" UseFont="false" UseForeColor="false" UseBackColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item20>
        <Item21 Ref="105" ControlType="XRLabel" Name="xrLabel12" TextAlignment="TopLeft" SizeF="55.26007,14.58333" LocationFloat="1013.74,0" StyleName="DetailData1" Font="Arial, 8pt" ForeColor="Black" Padding="6,6,0,0,100" BorderColor="Transparent" Borders="Left" BorderWidth="2" BorderDashStyle="Dash">
          <ExpressionBindings>
            <Item1 Ref="106" EventName="BeforePrint" PropertyName="Text" Expression="[Posicion]" />
          </ExpressionBindings>
          <StylePriority Ref="107" UseFont="false" UseForeColor="false" UsePadding="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item21>
      </Controls>
      <StylePriority Ref="108" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" />
    </Item5>
    <Item6 Ref="109" ControlType="GroupFooterBand" Name="GroupFooter2" HeightF="34.3867">
      <Controls>
        <Item1 Ref="110" ControlType="XRPanel" Name="panel1" SizeF="1069,34.3867" LocationFloat="0,0" StyleName="TotalBackground1">
          <Controls>
            <Item1 Ref="111" ControlType="XRLabel" Name="label4" CanGrow="false" TextAlignment="MiddleLeft" WordWrap="false" SizeF="54.25066,14.88444" LocationFloat="110.1678,3.400008" StyleName="TotalData1">
              <Summary Ref="112" Running="Group" />
              <ExpressionBindings>
                <Item1 Ref="113" EventName="BeforePrint" PropertyName="Text" Expression="sumMax([Posicion])" />
              </ExpressionBindings>
              <StylePriority Ref="114" UseTextAlignment="false" />
            </Item1>
            <Item2 Ref="115" ControlType="XRLabel" Name="label6" CanGrow="false" TextAlignment="MiddleLeft" WordWrap="false" SizeF="54.01229,14.88444" LocationFloat="56.15546,3.400007" StyleName="TotalData1">
              <Summary Ref="116" Running="Group" />
              <ExpressionBindings>
                <Item1 Ref="117" EventName="BeforePrint" PropertyName="Text" Expression="sumMin([Posicion])" />
              </ExpressionBindings>
              <StylePriority Ref="118" UseTextAlignment="false" />
            </Item2>
            <Item3 Ref="119" ControlType="XRLabel" Name="label7" Text="Hojas" TextAlignment="MiddleLeft" SizeF="60.64334,14.88444" LocationFloat="222.8841,3.400008" StyleName="TotalCaption1">
              <StylePriority Ref="120" UseTextAlignment="false" />
            </Item3>
            <Item4 Ref="121" ControlType="XRLabel" Name="label8" CanGrow="false" TextAlignment="MiddleLeft" WordWrap="false" SizeF="79.36353,14.88444" LocationFloat="283.5275,3.400008" StyleName="TotalData1">
              <Summary Ref="122" Running="Group" />
              <ExpressionBindings>
                <Item1 Ref="123" EventName="BeforePrint" PropertyName="Text" Expression="sumSum([HojasPedido])+' hojas'" />
              </ExpressionBindings>
              <StylePriority Ref="124" UseTextAlignment="false" />
            </Item4>
            <Item5 Ref="125" ControlType="XRLabel" Name="label9" Text="Superficie" TextAlignment="MiddleLeft" SizeF="72.51636,14.88444" LocationFloat="390.823,3.400008" StyleName="TotalCaption1">
              <StylePriority Ref="126" UseTextAlignment="false" />
            </Item5>
            <Item6 Ref="127" ControlType="XRLabel" Name="label10" TextFormatString="{0:#,#}" CanGrow="false" TextAlignment="MiddleLeft" WordWrap="false" SizeF="69.02512,14.88444" LocationFloat="463.3394,3.400008" StyleName="TotalData1">
              <Summary Ref="128" Running="Group" />
              <ExpressionBindings>
                <Item1 Ref="129" EventName="BeforePrint" PropertyName="Text" Expression="sumSum([Sup]) + ' m2'" />
              </ExpressionBindings>
              <StylePriority Ref="130" UseTextAlignment="false" />
            </Item6>
            <Item7 Ref="131" ControlType="XRLabel" Name="label11" Text="Fin" TextAlignment="MiddleLeft" SizeF="49.52524,14.88444" LocationFloat="563.9637,3.400008" StyleName="TotalCaption1">
              <StylePriority Ref="132" UseTextAlignment="false" />
            </Item7>
            <Item8 Ref="133" ControlType="XRLabel" Name="label12" CanGrow="false" TextAlignment="MiddleLeft" WordWrap="false" SizeF="115.0387,14.88444" LocationFloat="613.4889,3.400008" StyleName="TotalData1">
              <Summary Ref="134" Running="Group" />
              <ExpressionBindings>
                <Item1 Ref="135" EventName="BeforePrint" PropertyName="Text" Expression="sumMax([HoraFinEstimada])" />
              </ExpressionBindings>
              <StylePriority Ref="136" UseTextAlignment="false" />
            </Item8>
            <Item9 Ref="137" ControlType="XRLabel" Name="label13" Text="Barniz" TextAlignment="MiddleLeft" SizeF="63.44904,14.88444" LocationFloat="799.2018,3.400008" StyleName="TotalCaption1">
              <StylePriority Ref="138" UseTextAlignment="false" />
            </Item9>
            <Item10 Ref="139" ControlType="XRLabel" Name="label14" CanGrow="false" TextAlignment="MiddleLeft" WordWrap="false" SizeF="55.63361,14.88444" LocationFloat="862.651,3.400008" StyleName="TotalData1">
              <Summary Ref="140" Running="Group" />
              <ExpressionBindings>
                <Item1 Ref="141" EventName="BeforePrint" PropertyName="Text" Expression="sumSum([BarnizNecesario])" />
              </ExpressionBindings>
              <StylePriority Ref="142" UseTextAlignment="false" />
            </Item10>
          </Controls>
        </Item1>
      </Controls>
    </Item6>
    <Item7 Ref="143" ControlType="PageHeaderBand" Name="PageHeader" HeightF="29.16667" Borders="All">
      <Controls>
        <Item1 Ref="144" ControlType="XRLabel" Name="xrLabel19" Multiline="true" Text="xrLabel17" TextAlignment="TopLeft" SizeF="80.707,25.52083" LocationFloat="313.7917,0" Font="Arial, 12pt, style=Bold, Italic" Padding="2,2,0,0,100" BorderColor="Blue" Borders="Bottom" BorderWidth="2" BorderDashStyle="Double">
          <ExpressionBindings>
            <Item1 Ref="145" EventName="BeforePrint" PropertyName="Text" Expression="'Máquina: '" />
          </ExpressionBindings>
          <StylePriority Ref="146" UseFont="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item1>
        <Item2 Ref="147" ControlType="XRLabel" Name="xrLabel18" Multiline="true" Text="xrLabel17" TextAlignment="TopLeft" SizeF="313.7917,25.52083" LocationFloat="0,0" Font="Arial, 12pt, style=Bold, Italic" Padding="2,2,0,0,100" BorderColor="Blue" Borders="Bottom" BorderWidth="2" BorderDashStyle="Double">
          <ExpressionBindings>
            <Item1 Ref="148" EventName="BeforePrint" PropertyName="Text" Expression="'PROGRAMACIÓN BARNIZADO'" />
          </ExpressionBindings>
          <StylePriority Ref="149" UseFont="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item2>
        <Item3 Ref="150" ControlType="XRLabel" Name="xrLabel17" Multiline="true" Text="xrLabel17" TextAlignment="MiddleCenter" SizeF="57.8125,25.52083" LocationFloat="788.806,0" Padding="2,2,0,0,100" BorderColor="Blue" Borders="Bottom" BorderWidth="2" BorderDashStyle="Double">
          <ExpressionBindings>
            <Item1 Ref="151" EventName="BeforePrint" PropertyName="Text" Expression="'Página: '" />
          </ExpressionBindings>
          <StylePriority Ref="152" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item3>
        <Item4 Ref="153" ControlType="XRPageInfo" Name="xrPageInfo2" PageInfo="DateTime" TextFormatString="{0:dd/MM/yyyy HH:mm}" TextAlignment="MiddleCenter" SizeF="150.7158,25.52083" LocationFloat="918.2846,0" Padding="2,2,0,0,100" BorderColor="Blue" Borders="Bottom" BorderWidth="2" BorderDashStyle="Double">
          <StylePriority Ref="154" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item4>
        <Item5 Ref="155" ControlType="XRPageInfo" Name="xrPageInfo1" TextAlignment="MiddleLeft" SizeF="71.66608,25.52083" LocationFloat="846.6185,0" Padding="2,2,0,0,100" BorderColor="Blue" Borders="Bottom" BorderWidth="2" BorderDashStyle="Double">
          <StylePriority Ref="156" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" UseTextAlignment="false" />
        </Item5>
        <Item6 Ref="157" ControlType="XRLabel" Name="xrLabel15" Multiline="true" Text="xrLabel15" SizeF="394.3073,25.52083" LocationFloat="394.4987,0" Font="Arial, 12pt, style=Bold, Italic" Padding="2,2,0,0,100" BorderColor="Blue" Borders="Bottom" BorderWidth="2" BorderDashStyle="Double">
          <ExpressionBindings>
            <Item1 Ref="158" EventName="BeforePrint" PropertyName="Text" Expression="?MaquinaNombreParameter" />
          </ExpressionBindings>
          <StylePriority Ref="159" UseFont="false" UseBorderColor="false" UseBorders="false" UseBorderWidth="false" UseBorderDashStyle="false" />
        </Item6>
      </Controls>
      <StylePriority Ref="160" UseBorders="false" />
    </Item7>
  </Bands>
  <StyleSheet>
    <Item1 Ref="161" Name="Title" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Arial, 14.25pt" ForeColor="255,64,70,80" BackColor="Transparent" BorderColor="Black" Sides="None" StringFormat="Near;Near;0;None;Character;GenericDefault" BorderWidthSerializable="1" />
    <Item2 Ref="162" Name="GroupCaption1" BorderStyle="Inset" Padding="6,2,0,0,100" Font="Arial, 8.25pt, style=Bold" ForeColor="255,228,228,228" BackColor="255,177,29,46" BorderColor="White" Sides="Bottom" StringFormat="Near;Center;0;None;Character;GenericDefault" TextAlignment="MiddleLeft" BorderWidthSerializable="2" />
    <Item3 Ref="163" Name="GroupData1" BorderStyle="Inset" Padding="6,2,0,0,100" Font="Arial, 8.25pt, style=Bold" ForeColor="White" BackColor="255,177,29,46" BorderColor="White" Sides="Bottom" StringFormat="Near;Center;0;None;Character;GenericDefault" TextAlignment="MiddleLeft" BorderWidthSerializable="2" />
    <Item4 Ref="164" Name="DetailCaption1" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Arial, 14pt, style=Bold" ForeColor="White" BackColor="255,177,29,46" BorderColor="White" Sides="Left" StringFormat="Near;Near;0;None;Character;GenericDefault" TextAlignment="TopLeft" BorderWidthSerializable="2" />
    <Item5 Ref="165" Name="DetailData1" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Arial, 8.25pt" ForeColor="Black" BorderColor="Transparent" Sides="Left" StringFormat="Near;Center;0;None;Character;GenericDefault" TextAlignment="MiddleLeft" BorderWidthSerializable="2" />
    <Item6 Ref="166" Name="GroupFooterBackground3" BorderStyle="Inset" Padding="6,2,0,0,100" Font="Arial, 8.25pt, style=Bold" ForeColor="255,228,228,228" BackColor="255,109,117,129" BorderColor="White" Sides="Bottom" StringFormat="Near;Center;0;None;Character;GenericDefault" TextAlignment="MiddleLeft" BorderWidthSerializable="2" />
    <Item7 Ref="167" Name="DetailData3_Odd" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Arial, 8.25pt" ForeColor="Black" BackColor="255,243,245,248" BorderColor="Transparent" Sides="None" StringFormat="Near;Center;0;None;Character;GenericDefault" TextAlignment="MiddleLeft" BorderWidthSerializable="1" />
    <Item8 Ref="168" Name="TotalCaption1" BorderStyle="Inset" Padding="6,2,0,0,100" Font="Arial, 8.25pt, style=Bold" ForeColor="255,182,186,192" Sides="None" StringFormat="Near;Center;0;None;Character;GenericDefault" TextAlignment="MiddleLeft" />
    <Item9 Ref="169" Name="TotalData1" BorderStyle="Inset" Padding="2,6,0,0,100" Font="Arial, 8.25pt, style=Bold" ForeColor="255,75,75,75" Sides="None" StringFormat="Near;Center;0;None;Character;GenericDefault" TextAlignment="MiddleLeft" />
    <Item10 Ref="170" Name="TotalBackground1" BorderStyle="Inset" BackColor="255,243,245,248" BorderColor="White" Sides="Bottom" StringFormat="Near;Near;0;None;Character;GenericDefault" BorderWidthSerializable="2" />
    <Item11 Ref="171" Name="GrandTotalCaption1" BorderStyle="Inset" Padding="6,2,0,0,100" Font="Arial, 8.25pt, style=Bold" ForeColor="255,147,147,147" Sides="None" StringFormat="Near;Center;0;None;Character;GenericDefault" TextAlignment="MiddleLeft" />
    <Item12 Ref="172" Name="GrandTotalData1" BorderStyle="Inset" Padding="2,6,0,0,100" Font="Arial, 8.25pt, style=Bold" ForeColor="255,75,75,75" Sides="None" StringFormat="Near;Center;0;None;Character;GenericDefault" TextAlignment="MiddleLeft" />
    <Item13 Ref="173" Name="GrandTotalBackground1" BorderStyle="Inset" BackColor="White" BorderColor="255,75,75,75" Sides="Bottom" StringFormat="Near;Near;0;None;Character;GenericDefault" BorderWidthSerializable="2" />
    <Item14 Ref="174" Name="PageInfo" BorderStyle="Inset" Padding="6,6,0,0,100" Font="Arial, 8.25pt, style=Bold" ForeColor="255,64,70,80" StringFormat="Near;Near;0;None;Character;GenericDefault" />
  </StyleSheet>
  <ComponentStorage>
    <Item1 Ref="0" ObjectType="DevExpress.DataAccess.Sql.SqlDataSource,DevExpress.DataAccess.v22.2" Name="sqlDataSource1" Base64="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" />
  </ComponentStorage>
  <ObjectStorage>
    <Item1 ObjectType="DevExpress.XtraReports.Serialization.ObjectStorageInfo, DevExpress.XtraReports.v22.2" Ref="5" Content="System.Boolean" Type="System.Type" />
  </ObjectStorage>
</XtraReportsLayoutSerializer>