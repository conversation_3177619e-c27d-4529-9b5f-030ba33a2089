var inactivityTimeout;
var resetInactivityTimerFunc;
var dotNetReferenceGlobal;

function startInactivityTimer(activar, dotNetReference) {
    dotNetReferenceGlobal = dotNetReference;
    resetInactivityTimerFunc = function () {
        clearTimeout(inactivityTimeout);
        inactivityTimeout = setTimeout(function () {
            dotNetReferenceGlobal.invokeMethodAsync('SetInactivo', true);
        }, 300000); // 5 minutos
    };

    if (activar) {
        resetInactivityTimerFunc();
        document.addEventListener("mousemove", resetInactivityTimerFunc, false);
        document.addEventListener("keypress", resetInactivityTimerFunc, false);
    }
}

function stopInactivityTimer() {
    clearTimeout(inactivityTimeout);
    if (resetInactivityTimerFunc) {
        document.removeEventListener("mousemove", resetInactivityTimerFunc, false);
        document.removeEventListener("keypress", resetInactivityTimerFunc, false);
    }
}



document.addEventListener("keydown", function (event) {
    // Check if the popup is visible
    var popupVisible = document.querySelector(".dxbl-modal-footer");
    if (popupVisible) {
        if (event.key === "Enter") {
            // Simulate click on the Accept button
            var acceptButton = popupVisible.querySelector(".dxbl-btn-success");
            if (acceptButton) {
                acceptButton.click();
            }
        } else if (event.key === "Escape") {
            // Simulate click on the Cancel button
            var cancelButton = popupVisible.querySelector(".dxbl-btn-danger");
            if (cancelButton) {
                cancelButton.click();
            }
        }
    }
    var visorVisible = document.querySelector("#VisorReportes");
    if (visorVisible) {
        if (event.key === "ArrowRight")//Flecha derecha 
        {
            if (document.querySelector(".dxrd-image-preview-next")) {
                document.querySelector(".dxrd-image-preview-next").click()
            }
        }
        if (event.key === "ArrowLeft")//Flecha izquierda 
        {
            if (document.querySelector(".dxrd-image-preview-prev")) {
                document.querySelector(".dxrd-image-preview-prev").click()
            }
        }
    }
});
