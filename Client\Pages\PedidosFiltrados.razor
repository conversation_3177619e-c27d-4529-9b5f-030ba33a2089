﻿@page "/PedidosFiltrados"

@using System.Text
@using ProgramadorGeneralBLZ.Shared
@using Microsoft.AspNetCore.Authorization
@using ProgramadorGeneralBLZ.Shared.DTO
@using ProgramadorGeneralBLZ.Shared.ResponseModels
@using System.Text.Json
@using DevExpress.Blazor.Internal

@inject Blazored.LocalStorage.ILocalStorageService LocalStorage
@inject IJSRuntime Js
@inject IToastService ToastService
@inject HttpClient Http
@inject SpinnerService SpinnerService
@inject NavigationManager NavManager
@inject FiltroService FiltroService

@attribute [Authorize(Roles = $"{Roles.Admin}, {Roles.Programador}, {Roles.Consulta}")]

<PageTitle>Consulta Fechas por Cliente</PageTitle>

<AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}, {Roles.Consulta}")">
    <Authorized Context="authContext">
        <DxLayoutBreakpoint DeviceSize="DeviceSize.Large" />
        <div class="h-100 px-2 py-1">
            <DxFormLayout SizeMode="SizeMode.Small" ReadOnly="true">
                <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.Card" Caption="Filtros"
                                   BeginRow="true" CssClass="py-3" Visible="@(VisiblePedidos)">
                    <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanLg="4">
                        <DxFormLayoutItem Caption="Datos Cliente" CaptionCssClass="negrita">
                            <DxComboBox Data="@DatosClientes" AllowUserInput="true"
                                        SearchMode="ListSearchMode.AutoSearch"
                                        SearchFilterCondition="ListSearchFilterCondition.Contains"
                                        DropDownWidthMode="DropDownWidthMode.EditorWidth"
                                        ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                        TextFieldName="Combinado"
                                        ValueFieldName="CodigoCliente"
                                        ListRenderMode="ListRenderMode.Virtual"
                                        @bind-Value="Filtro.IdCliente"
                                        NullText="Cliente...">
                            </DxComboBox>
                        </DxFormLayoutItem>
                        <DxFormLayoutItem Caption="Línea Tintas" CaptionCssClass="negrita">
                            <DxComboBox Data="@DatosMaquinas"
                                        SearchMode="ListSearchMode.AutoSearch"
                                        SearchFilterCondition="ListSearchFilterCondition.Contains"
                                        TextFieldName="@nameof(MaquinaDTO.IdmaquinaG21)"
                                        ValueFieldName="Idmaquina"
                                        ListRenderMode="ListRenderMode.Entire"
                                        DropDownWidthMode="DropDownWidthMode.EditorWidth"
                                        ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                        @bind-Value="Filtro.LineaTintas"
                                        NullText="Máquina...">
                            </DxComboBox>
                        </DxFormLayoutItem>
                        <DxFormLayoutItem Caption="Tipo Elemento">
                            <DxTextBox @bind-Text="Filtro.TipoElemento" ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" />
                        </DxFormLayoutItem>
                        <DxFormLayoutItem Caption="Formato">
                            <DxSpinEdit @bind-Value="Filtro.Formato" ShowSpinButtons="false" ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" />
                        </DxFormLayoutItem>
                        <DxFormLayoutItem Caption="Plano">
                            <DxTextBox @bind-Text="Filtro.Plano" ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" />
                        </DxFormLayoutItem>
                        <DxFormLayoutItem Caption="Motivos">
                            <DxTextBox @bind-Text="Filtro.Motivos" ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" />
                        </DxFormLayoutItem>
                        <DxFormLayoutItem Caption="Pedido" CaptionCssClass="negrita">
                            <DxSpinEdit @bind-Value="Filtro.IdPedido" ShowSpinButtons="false" ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" />
                        </DxFormLayoutItem>
                        <DxFormLayoutItem Caption="Tipo Pedido">
                            <DxTextBox @bind-Text="Filtro.TipoPedido" ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" />
                        </DxFormLayoutItem>
                        <DxFormLayoutItem Caption="SU Pedido">
                            <DxTextBox @bind-Text="Filtro.Supedido" ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" />
                        </DxFormLayoutItem>
                        <DxFormLayoutItem Caption="WO">
	                        <DxTextBox @bind-Text="Filtro.Wo" ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" />
                        </DxFormLayoutItem>
                        <DxFormLayoutItem Caption="Fecha Pedido">
                            <DxDateEdit @bind-Date="Filtro.FechaPedido" ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" />
                        </DxFormLayoutItem>
                        <DxFormLayoutItem Caption="Rango Fechas">
                            <DxDateRangePicker @bind-StartDate="@Filtro.FechaMinPedido"
                                               @bind-EndDate="@Filtro.FechaMaxPedido"
                                               ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                               FirstDayOfWeek="DayOfWeek.Monday" />
                        </DxFormLayoutItem>
                        <DxFormLayoutItem Caption="Hora Pedido" Visible="false">
                            <DxTimeEdit @bind-Time="Filtro.HoraPedido" ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" />
                        </DxFormLayoutItem>
                        <DxFormLayoutItem Caption="Cod. Apli" CaptionCssClass="negrita">
                            <DxComboBox Data="@DatosCodsApliBarniz"
                                        SearchMode="ListSearchMode.AutoSearch"
                                        SearchFilterCondition="ListSearchFilterCondition.Contains"
                                        TextFieldName="Combinado"
                                        ValueFieldName="Idcodigoaplicacion"
                                        ListRenderMode="ListRenderMode.Virtual"
                                        @bind-Value="Filtro.CodApli">
                            </DxComboBox>
                        </DxFormLayoutItem>
                        <DxFormLayoutItem Caption="Barniz" CaptionCssClass="negrita">
	                        <DxComboBox Data="@DatosCodsBarniz"
	                                    SearchMode="ListSearchMode.AutoSearch"
	                                    SearchFilterCondition="ListSearchFilterCondition.Contains"
	                                    ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
	                                    TextFieldName="CombinadoSimple"
	                                    ValueFieldName="Idcodigoaplicacion"
	                                    ListRenderMode="ListRenderMode.Virtual"
	                                    @bind-Value="Filtro.CodBarniz">
	                        </DxComboBox>
                        </DxFormLayoutItem>

                        <DxFormLayoutItem Caption="Ocultar Reprocesos" BeginRow="true" CaptionCssClass="negrita">
                            <DxCheckBox Checked="@Filtro.OcultarReprocesos"
                                        CheckedExpression="@(() => Filtro.OcultarReprocesos)"
                                        CheckedChanged="@(async (bool nuevoValor) => await OnOcultarReprocesosChanged(nuevoValor))" AllowIndeterminateState="false" ValueUnchecked="false" ValueChecked="true" />
                        </DxFormLayoutItem>
                        <DxFormLayoutItem Caption="Incluir Lote" BeginRow="true" CaptionCssClass="negrita" Visible="!MostrarAgrupado">
                            <DxCheckBox @bind-Checked="Filtro.IncluirLote" AllowIndeterminateState="false" ValueUnchecked="false" ValueChecked="true" />
                        </DxFormLayoutItem>
                        <DxFormLayoutItem Caption="Lote" CaptionCssClass="negrita" Visible="Filtro.IncluirLote">
                            <DxTextBox @bind-Text="Filtro.LoteBarniz" ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" />
                        </DxFormLayoutItem>
                    </DxFormLayoutGroup>
                    <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanLg="3">
                        <DxFormLayoutItem Caption="Pi1ped">
                            <DxSpinEdit @bind-Value="Filtro.Pi1ped" ShowSpinButtons="false" ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" />
                        </DxFormLayoutItem>
                        <DxFormLayoutItem Caption="Pe1ped">
                            <DxSpinEdit @bind-Value="Filtro.Pe1ped" ShowSpinButtons="false" ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" />
                        </DxFormLayoutItem>
                        <DxFormLayoutItem Caption="Pi2ped">
                            <DxSpinEdit @bind-Value="Filtro.Pi2ped" ShowSpinButtons="false" ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" />
                        </DxFormLayoutItem>
                        <DxFormLayoutItem Caption="Pe2ped">
                            <DxSpinEdit @bind-Value="Filtro.Pe2ped" ShowSpinButtons="false" ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" />
                        </DxFormLayoutItem>
                        <DxFormLayoutItem Caption="Pi3ped">
                            <DxSpinEdit @bind-Value="Filtro.Pi3ped" ShowSpinButtons="false" ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" />
                        </DxFormLayoutItem>
                        <DxFormLayoutItem Caption="Pe3ped">
                            <DxSpinEdit @bind-Value="Filtro.Pe3ped" ShowSpinButtons="false" ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" />
                        </DxFormLayoutItem>
                        @* <DxFormLayoutItem Caption="Lista Barniz 1" CaptionCssClass="negrita" ColSpanLg="12">
                            <DxTextBox NullText="Type search text..."
                                       InputDelay="500"
                                       @bind-Text="@SearchText"
                                       BindValueMode="BindValueMode.OnInput"
                                       ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto">
                            </DxTextBox>
                            <DxListBox Data="@DatosCodsBarniz" ListRenderMode="ListRenderMode.Virtual"
                                       TextFieldName="CombinadoSimple" SearchText="@SearchText"
                                       ValueFieldName="Idcodigoaplicacion"
                                       SelectionMode="ListBoxSelectionMode.Multiple"
                                       ShowCheckboxes="true"
                                       CssClass=""
                                       @bind-Values="Filtro.ListCodBarniz1">
                            </DxListBox>
                            <p class="demo-text cw-480 mt-3">
                                Selección: [ @GetSelectedValuesDescription() ]
                            </p>
                        </DxFormLayoutItem> *@
                        <DxFormLayoutItem Caption="Lista Barniz" CaptionCssClass="negrita" BeginRow="true" ColSpanLg="12">
                            <DxTagBox Data="@DatosCodsBarniz" NullText="Busca barnices..."
                                      ListRenderMode="ListRenderMode.Virtual"
                                      SearchMode="ListSearchMode.AutoSearch"
                                      SearchFilterCondition="ListSearchFilterCondition.Contains"
                                      ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                      TextFieldName="CombinadoSimple"
                                      ValueFieldName="Idcodigoaplicacion"
                                      @bind-Values="Filtro.ListCodBarniz2" />
                        </DxFormLayoutItem>
                    </DxFormLayoutGroup>
                    <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanLg="2">
                        <DxFormLayoutItem Caption="Ancho Hojalata" ColSpanLg="12">
                            <DxSpinEdit @bind-Value="Filtro.AnchoHjlta" ShowSpinButtons="false" ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" />
                        </DxFormLayoutItem>
                        <DxFormLayoutItem Caption="Largo Hojalata" BeginRow="true" ColSpanLg="12">
                            <DxSpinEdit @bind-Value="Filtro.LargoHjlta" ShowSpinButtons="false" ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" />
                        </DxFormLayoutItem>
                        <DxFormLayoutItem Caption="Espesor Hojalata" BeginRow="true" ColSpanLg="12">
                            <DxSpinEdit @bind-Value="Filtro.EspesorHjlta" ShowSpinButtons="false" ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" />
                        </DxFormLayoutItem>
                        <DxFormLayoutItem Caption="Tipo Hojalata" BeginRow="true" ColSpanLg="12">
                            <DxSpinEdit @bind-Value="Filtro.TipoHjlta" ShowSpinButtons="false" ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" />
                        </DxFormLayoutItem>
                        <DxFormLayoutItem Caption="Estado Tintas" BeginRow="true" ColSpanLg="12">
                            <DxTextBox @bind-Text="Filtro.EstadoTintas" ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" />
                        </DxFormLayoutItem>
                        <DxFormLayoutItem Caption="Obs. Artículo" BeginRow="true" ColSpanLg="12">
                            <DxTextBox @bind-Text="Filtro.ObsArt" ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" />
                        </DxFormLayoutItem>
                    </DxFormLayoutGroup>
                    <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanLg="3">
                        @*TINTAS *@
                        <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanLg="6">
                            <DxFormLayoutItem Caption="Tinta 1" ColSpanLg="12">
                                <DxComboBox Data="@DatosTintas" AllowUserInput="true"
                                            SearchMode="ListSearchMode.AutoSearch"
                                            SearchFilterCondition="ListSearchFilterCondition.Contains"
                                            DropDownWidthMode="DropDownWidthMode.EditorWidth"
                                            ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                            TextFieldName="Combinado"
                                            ValueFieldName="Codtin"
                                            ListRenderMode="ListRenderMode.Virtual"
                                            @bind-Value="Filtro.Tinta1"
                                            NullText="Tinta 1...">
                                </DxComboBox>
                            </DxFormLayoutItem>
                            <DxFormLayoutItem Caption="Tinta 2" ColSpanLg="12">
                                <DxComboBox Data="@DatosTintas" AllowUserInput="true"
                                            SearchMode="ListSearchMode.AutoSearch"
                                            SearchFilterCondition="ListSearchFilterCondition.Contains"
                                            DropDownWidthMode="DropDownWidthMode.EditorWidth"
                                            ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                            TextFieldName="Combinado"
                                            ValueFieldName="Codtin"
                                            ListRenderMode="ListRenderMode.Virtual"
                                            @bind-Value="Filtro.Tinta2"
                                            NullText="Tinta 2...">
                                </DxComboBox>
                            </DxFormLayoutItem>
                            <DxFormLayoutItem Caption="Tinta 3" ColSpanLg="12">
                                <DxComboBox Data="@DatosTintas" AllowUserInput="true"
                                            SearchMode="ListSearchMode.AutoSearch"
                                            SearchFilterCondition="ListSearchFilterCondition.Contains"
                                            DropDownWidthMode="DropDownWidthMode.EditorWidth"
                                            ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                            TextFieldName="Combinado"
                                            ValueFieldName="Codtin"
                                            ListRenderMode="ListRenderMode.Virtual"
                                            @bind-Value="Filtro.Tinta3"
                                            NullText="Tinta 3...">
                                </DxComboBox>
                            </DxFormLayoutItem>
                            <DxFormLayoutItem Caption="Tinta 4" ColSpanLg="12">
                                <DxComboBox Data="@DatosTintas" AllowUserInput="true"
                                            SearchMode="ListSearchMode.AutoSearch"
                                            SearchFilterCondition="ListSearchFilterCondition.Contains"
                                            DropDownWidthMode="DropDownWidthMode.EditorWidth"
                                            ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                            TextFieldName="Combinado"
                                            ValueFieldName="Codtin"
                                            ListRenderMode="ListRenderMode.Virtual"
                                            @bind-Value="Filtro.Tinta4"
                                            NullText="Tinta 4...">
                                </DxComboBox>
                            </DxFormLayoutItem>
                            <DxFormLayoutItem Caption="Tinta 5" ColSpanLg="12">
                                <DxComboBox Data="@DatosTintas" AllowUserInput="true"
                                            SearchMode="ListSearchMode.AutoSearch"
                                            SearchFilterCondition="ListSearchFilterCondition.Contains"
                                            DropDownWidthMode="DropDownWidthMode.EditorWidth"
                                            ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                            TextFieldName="Combinado"
                                            ValueFieldName="Codtin"
                                            ListRenderMode="ListRenderMode.Virtual"
                                            @bind-Value="Filtro.Tinta5"
                                            NullText="Tinta 5...">
                                </DxComboBox>
                            </DxFormLayoutItem>
                            <DxFormLayoutItem Caption="Tinta 6" ColSpanLg="12">
                                <DxComboBox Data="@DatosTintas" AllowUserInput="true"
                                            SearchMode="ListSearchMode.AutoSearch"
                                            SearchFilterCondition="ListSearchFilterCondition.Contains"
                                            DropDownWidthMode="DropDownWidthMode.EditorWidth"
                                            ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                            TextFieldName="Combinado"
                                            ValueFieldName="Codtin"
                                            ListRenderMode="ListRenderMode.Virtual"
                                            @bind-Value="Filtro.Tinta6"
                                            NullText="Tinta 6...">
                                </DxComboBox>
                            </DxFormLayoutItem>
                            <DxFormLayoutItem Caption="Tinta 7" ColSpanLg="12">
                                <DxComboBox Data="@DatosTintas" AllowUserInput="true"
                                            SearchMode="ListSearchMode.AutoSearch"
                                            SearchFilterCondition="ListSearchFilterCondition.Contains"
                                            DropDownWidthMode="DropDownWidthMode.EditorWidth"
                                            ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                            TextFieldName="Combinado"
                                            ValueFieldName="Codtin"
                                            ListRenderMode="ListRenderMode.Virtual"
                                            @bind-Value="Filtro.Tinta7"
                                            NullText="Tinta 7...">
                                </DxComboBox>
                            </DxFormLayoutItem>
                        </DxFormLayoutGroup>
                        @*TINTAS EXTERNAS*@
                        <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanLg="6">
                            <DxFormLayoutItem Caption="Tinta Int 1" ColSpanLg="12">
                                <DxComboBox Data="@DatosTintas" AllowUserInput="true"
                                            SearchMode="ListSearchMode.AutoSearch"
                                            SearchFilterCondition="ListSearchFilterCondition.Contains"
                                            DropDownWidthMode="DropDownWidthMode.EditorWidth"
                                            ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                            TextFieldName="Combinado"
                                            ValueFieldName="Codtin"
                                            ListRenderMode="ListRenderMode.Virtual"
                                            @bind-Value="Filtro.Cd1ped"
                                            NullText="Tinta Int 1...">
                                </DxComboBox>
                            </DxFormLayoutItem>
                            <DxFormLayoutItem Caption="Tinta Int 2" ColSpanLg="12">
                                <DxComboBox Data="@DatosTintas" AllowUserInput="true"
                                            SearchMode="ListSearchMode.AutoSearch"
                                            SearchFilterCondition="ListSearchFilterCondition.Contains"
                                            DropDownWidthMode="DropDownWidthMode.EditorWidth"
                                            ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                            TextFieldName="Combinado"
                                            ValueFieldName="Codtin"
                                            ListRenderMode="ListRenderMode.Virtual"
                                            @bind-Value="Filtro.Cd2ped"
                                            NullText="Tinta Int 2...">
                                </DxComboBox>
                            </DxFormLayoutItem>
                            <DxFormLayoutItem Caption="Tinta Int 3" ColSpanLg="12">
                                <DxComboBox Data="@DatosTintas" AllowUserInput="true"
                                            SearchMode="ListSearchMode.AutoSearch"
                                            SearchFilterCondition="ListSearchFilterCondition.Contains"
                                            DropDownWidthMode="DropDownWidthMode.EditorWidth"
                                            ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                            TextFieldName="Combinado"
                                            ValueFieldName="Codtin"
                                            ListRenderMode="ListRenderMode.Virtual"
                                            @bind-Value="Filtro.Cd3ped"
                                            NullText="Tinta Int 3...">
                                </DxComboBox>
                            </DxFormLayoutItem>
                            <DxFormLayoutItem Caption="Tinta Int 4" ColSpanLg="12">
                                <DxComboBox Data="@DatosTintas" AllowUserInput="true"
                                            SearchMode="ListSearchMode.AutoSearch"
                                            SearchFilterCondition="ListSearchFilterCondition.Contains"
                                            DropDownWidthMode="DropDownWidthMode.EditorWidth"
                                            ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                            TextFieldName="Combinado"
                                            ValueFieldName="Codtin"
                                            ListRenderMode="ListRenderMode.Virtual"
                                            @bind-Value="Filtro.Cd4ped"
                                            NullText="Tinta Int 4...">
                                </DxComboBox>
                            </DxFormLayoutItem>
                        </DxFormLayoutGroup>
                    </DxFormLayoutGroup>
                    <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" BeginRow="true">
                        <DxFormLayoutItem ColSpanLg="2">
                            <DxButton RenderStyle="ButtonRenderStyle.Success" Text="FILTRAR" Click="@Filtrar" />
                            <DxButton RenderStyle="ButtonRenderStyle.Success" Text="@TxtAgrupar" Enabled="@AgruparActivo" Click="@Agrupar" />
                        </DxFormLayoutItem>
                        <DxFormLayoutItem ColSpanLg="1">
                            <DxButton RenderStyle="ButtonRenderStyle.Warning" Text="Limpiar Filtro" Click="@LimpiarFiltro" />
                        </DxFormLayoutItem>
                        <DxFormLayoutItem ColSpanLg="1">
                            <DxButton RenderStyle="ButtonRenderStyle.Info" Text="@(_textoBotonTraza)" Click="@MarcarTrazabilidad" Visible="@Filtro.General" />
                        </DxFormLayoutItem>
                        <DxFormLayoutItem ColSpanLg="1">
                            <DxButton RenderStyle="ButtonRenderStyle.Info" Text="Selector Columnas" Click="@ColumnChooser" />
                        </DxFormLayoutItem>
                        <DxFormLayoutItem ColSpanLg="1">
                            <DxButton Click="ResetLayoutButton_Click" RenderStyle="ButtonRenderStyle.Dark" Text="Resetear Columnas" />
                        </DxFormLayoutItem>
                    </DxFormLayoutGroup>
                </DxFormLayoutGroup>
                <DxLoadingPanel @bind-Visible="PanelVisible"
                                IsContentBlocked="true"
                                ApplyBackgroundShading="true"
                                IndicatorAreaVisible="false"
                                IndicatorAnimationType="WaitIndicatorAnimationType.Spin"
                                Text="Cargando..." ZIndex="100"
                                TextAlignment="LoadingPanelTextAlignment.Right">
                    <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" BeginRow="true" CssClass="" Visible="@(VisiblePedidos)">
                        <DxGrid Data="@PedidosProcesados" SizeMode="SizeMode.Small" id="idProg"
                                CssClass="smallFont progGrid" PageSize="12"
                                KeyFieldName="IdPedido" Context="GridPedidos" @ref="_gridPedidos"
                                AllowSort="true" FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always"
                                SelectionMode="GridSelectionMode.Multiple"
                                SelectAllCheckboxMode="GridSelectAllCheckboxMode.Page"
                                CustomizeElement="GridPedidos_CustomizeColumnChooserItems"
                                LayoutAutoLoading=Grid_LayoutAutoLoading
                                LayoutAutoSaving=Grid_LayoutAutoSaving>
                            <Columns>
                                <DxGridDataColumn FieldName="IdPedido" Width="90px" DisplayFormat="F0" TextAlignment="GridTextAlignment.Center"
                                                  SortIndex="0" SortOrder="GridColumnSortOrder.Descending" Name="AuditBarniz"
                                                  FixedPosition="GridColumnFixedPosition.Left">
                                    <CellDisplayTemplate Context="cellText">
                                        <span class="p-1 d-block text-left" style="cursor: pointer"
                                              @ondblclick="() => VerDatosPedido(cellText.DisplayText)">
                                            @cellText.DisplayText
                                        </span>
                                    </CellDisplayTemplate>
                                </DxGridDataColumn>
                                <DxGridDataColumn FieldName="Supedido" TextAlignment="GridTextAlignment.Center" Width="100px" Name="AuditBarniz">
                                    <CellDisplayTemplate Context="cellText">
                                        @{
                                            string summary = cellText.Value?.ToString() ?? string.Empty;
                                            <div class="d-block text-truncate" title="@summary" style="max-width: 100px">
                                                @summary
                                            </div>
                                        }
                                    </CellDisplayTemplate>
                                </DxGridDataColumn>
                                <DxGridDataColumn FieldName="DatosCliente" Caption="Cliente" Width="100px" Name="AuditBarniz">
                                    @*FilterRowValue="@Filtro.IdCliente"
                                    FilterRowOperatorType="GridFilterRowOperatorType.Contains">*@
                                    <CellDisplayTemplate Context="cellText">
                                        @{
                                            string summary = cellText.Value?.ToString() ?? string.Empty;
                                            <div class="d-block text-truncate" title="@summary" style="max-width: 100px">
                                                @summary
                                            </div>
                                        }
                                    </CellDisplayTemplate>
                                </DxGridDataColumn>
                                <DxGridDataColumn FieldName="Wo" TextAlignment="GridTextAlignment.Center" Width="90px" Name="AuditBarniz" />
                                <DxGridDataColumn FieldName="FechaPedido" TextAlignment="GridTextAlignment.Center" Width="90px" Name="AuditBarniz" />
                                <DxGridDataColumn FieldName="TipoPedido" TextAlignment="GridTextAlignment.Center" Width="90px" Visible="!_esTraza" />
                                <DxGridDataColumn FieldName="HojasPedido" DisplayFormat="F0" TextAlignment="GridTextAlignment.Center" Width="90px" Name="AuditBarniz" />
                                <DxGridDataColumn FieldName="HojasTerminadas" DisplayFormat="F0" TextAlignment="GridTextAlignment.Center" Width="90px" Visible="!_esTraza" />
                                <DxGridDataColumn FieldName="Motivos" Width="205px" Visible="!_esTraza">
                                    <CellDisplayTemplate Context="cellText">
                                        @{
                                            string summary = cellText.Value?.ToString() ?? string.Empty;
                                            <div class="d-block text-truncate" title="@summary" style="max-width: 200px">
                                                @summary
                                            </div>
                                        }
                                    </CellDisplayTemplate>
                                </DxGridDataColumn>
                                <DxGridDataColumn FieldName="Formato" DisplayFormat="N2" Width="55px" Visible="!_esTraza" />
                                <DxGridDataColumn FieldName="TipoElemento" TextAlignment="GridTextAlignment.Center" Visible="!_esTraza" />
                                <DxGridDataColumn FieldName="TxtEstado" Width="300px" Name="Texto Estado" Visible="@MostrarAgrupado" />
                                <DxGridDataColumn FieldName="CodApli" DisplayFormat="F0" TextAlignment="GridTextAlignment.Center"
                                                  Width="90px" Name="AuditBarniz" Visible="@(!MostrarAgrupado)" />
                                <DxGridDataColumn FieldName="Barniz" DisplayFormat="F0" TextAlignment="GridTextAlignment.Center"
                                                  Width="90px" Name="AuditBarniz" Visible="@(!MostrarAgrupado)" />
                                <DxGridDataColumn FieldName="Tuberia" DisplayFormat="F0" TextAlignment="GridTextAlignment.Center"
                                                  Width="90px" Name="AuditBarniz" Visible="@(!MostrarAgrupado)" />
                                <DxGridDataColumn FieldName="Lote" TextAlignment="GridTextAlignment.Center" Width="90px" Name="AuditBarniz" Visible="@(!MostrarAgrupado)" />
                                <DxGridDataColumn FieldName="NombreLineaTintas" Caption="Línea Tintas" Width="90px" Visible="!_esTraza && !MostrarAgrupado" />
                                <DxGridDataColumn FieldName="RequeridoEnFecha" Width="90px" Visible="!_esTraza" />
                                <DxGridDataColumn FieldName="FechaPlanchas" Width="90px" Visible="!_esTraza" />
                                <DxGridDataColumn FieldName="Obsarticulo" Width="90px" Visible="!_esTraza">
                                    <CellDisplayTemplate Context="cellText">
                                        @{
                                            string summary = cellText.Value?.ToString() ?? string.Empty;
                                            <div class="d-block text-truncate" title="@summary" style="max-width: 200px">
                                                @summary
                                            </div>
                                        }
                                    </CellDisplayTemplate>
                                </DxGridDataColumn>
                                <DxGridDataColumn FieldName="LargoHjlta" DisplayFormat="N2" TextAlignment="GridTextAlignment.Center"
                                                  Width="90px" Visible="!_esTraza" />
                                <DxGridDataColumn FieldName="AnchoHjlta" DisplayFormat="N2" TextAlignment="GridTextAlignment.Center"
                                                  Width="90px" Visible="!_esTraza" />
                                <DxGridDataColumn FieldName="EspesorHjlta" DisplayFormat="N2" TextAlignment="GridTextAlignment.Center"
                                                  Width="90px" Visible="!_esTraza" />
                                <DxGridDataColumn FieldName="TipoHjlta" TextAlignment="GridTextAlignment.Center" Visible="!_esTraza" />
                                <DxGridDataColumn FieldName="Pi1ped" DisplayFormat="F0" Width="100px" Visible="!_esTraza" />
                                <DxGridDataColumn FieldName="Pi2ped" DisplayFormat="F0" Width="100px" Visible="!_esTraza" />
                                <DxGridDataColumn FieldName="Pi3ped" DisplayFormat="F0" Width="100px" Visible="!_esTraza" />
                                <DxGridDataColumn FieldName="Pe1ped" DisplayFormat="F0" Width="100px" Visible="!_esTraza" />
                                <DxGridDataColumn FieldName="Pe2ped" DisplayFormat="F0" Width="100px" Visible="!_esTraza" />
                                <DxGridDataColumn FieldName="Pe3ped" DisplayFormat="F0" Width="100px" Visible="!_esTraza" />

                                <DxGridDataColumn FieldName="Tinta1Desc" DisplayFormat="F0" Width="150px" Visible="!_esTraza" />
                                <DxGridDataColumn FieldName="Tinta2Desc" DisplayFormat="F0" Width="150px" Visible="!_esTraza" />
                                <DxGridDataColumn FieldName="Tinta3Desc" DisplayFormat="F0" Width="150px" Visible="!_esTraza" />
                                <DxGridDataColumn FieldName="Tinta4Desc" DisplayFormat="F0" Width="150px" Visible="!_esTraza" />
                                <DxGridDataColumn FieldName="Tinta5Desc" DisplayFormat="F0" Width="150px" Visible="!_esTraza" />
                                <DxGridDataColumn FieldName="Tinta6Desc" DisplayFormat="F0" Width="150px" Visible="!_esTraza" />
                                <DxGridDataColumn FieldName="Tinta7Desc" DisplayFormat="F0" Width="150px" Visible="!_esTraza" />

                                <DxGridDataColumn FieldName="TintaExt1Desc" DisplayFormat="F0" Width="150px" Visible="!_esTraza" />
                                <DxGridDataColumn FieldName="TintaExt2Desc" DisplayFormat="F0" Width="150px" Visible="!_esTraza" />
                                <DxGridDataColumn FieldName="TintaExt3Desc" DisplayFormat="F0" Width="150px" Visible="!_esTraza" />
                                <DxGridDataColumn FieldName="TintaExt4Desc" DisplayFormat="F0" Width="150px" Visible="!_esTraza" />
                                <DxGridDataColumn FieldName="Estado" Visible="!_esTraza" />
                            </Columns>

                        </DxGrid>
                    </DxFormLayoutGroup>

                    <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" BeginRow="true" CssClass="" Visible="@(VisibleHojalata)" Context="campo2">
                        <DxGrid Data="@Hjlt" SizeMode="SizeMode.Small"
                                CssClass="ch-390 smallFont progGrid" PageSize="22"
                                KeyFieldName="refmtc" Context="g2" @ref="_gridHjlt"
                                AllowSort="true" FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always"
                                SelectionMode="GridSelectionMode.Multiple"
                                SelectAllCheckboxMode="GridSelectAllCheckboxMode.Page">
                            <Columns>
                                <DxGridDataColumn FieldName="Refmtc" Caption="Referencia" TextAlignment="GridTextAlignment.Center" />
                                <DxGridDataColumn FieldName="Climtc" Caption="Cliente" DisplayFormat="F0" TextAlignment="GridTextAlignment.Center" />
                                <DxGridDataColumn FieldName="Tmamtc" Caption="Recubrimiento" DisplayFormat="F0" TextAlignment="GridTextAlignment.Center" />
                                <DxGridDataColumn FieldName="Larmtc" Caption="Largo" DisplayFormat="F0" Width="100px" TextAlignment="GridTextAlignment.Center" />
                                <DxGridDataColumn FieldName="Ancmtc" Caption="Ancho" DisplayFormat="F0" Width="100px" TextAlignment="GridTextAlignment.Center" />
                                <DxGridDataColumn FieldName="Espmtc" Caption="Espesor" DisplayFormat="F0" Width="100px" TextAlignment="GridTextAlignment.Center" />
                                <DxGridDataColumn FieldName="Tprmtc" Caption="Procesos" DisplayFormat="F0" Width="100px" TextAlignment="GridTextAlignment.Center" />
                                <DxGridDataColumn FieldName="Promtc" Caption="Procedencia" DisplayFormat="F0" Width="100px" TextAlignment="GridTextAlignment.Center" />
                                <DxGridDataColumn FieldName="SumaDestomtc" Caption="Referencia" DisplayFormat="F0" Width="100px" TextAlignment="GridTextAlignment.Center" />
                                <DxGridDataColumn FieldName="Expr1" Caption="Reservado" DisplayFormat="F0" Width="100px" TextAlignment="GridTextAlignment.Center" />
                                <DxGridDataColumn FieldName="Disponible" DisplayFormat="F0" Width="100px" TextAlignment="GridTextAlignment.Center" />
                            </Columns>

                        </DxGrid>
                    </DxFormLayoutGroup>

                </DxLoadingPanel>
                <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" BeginRow="true" CssClass="py-3">
                    <DxFormLayoutItem Context="campoDropdown" ColSpanLg="2">
                        <DxButton Click="@(Grid_ExportXlsx)" RenderStyle="ButtonRenderStyle.Info" CssClass="float-right me-2">Exportar</DxButton>
                    </DxFormLayoutItem>
                </DxFormLayoutGroup>
            </DxFormLayout>
        </div>
    </Authorized>
    <NotAuthorized>
        <NoPuedesPasar />
    </NotAuthorized>
</AuthorizeView>

@code {
    string SearchText { get; set; }
    private bool PanelVisible { get; set; }
    private bool _esTraza = false;
    private string _textoBotonTraza = "Mostrar Columnas Trazabilidad";
    private FiltroDTO Filtro { get; set; }
    private List<PedidoProcesadoLiteDTO> PedidosProcesados { get; set; }
    private List<PedidoProcesadoLiteDTO> PedidosProcesados_DesAgrupados_SinReprocesos { get; set; }
    private List<PedidoProcesadoLiteDTO> PedidosProcesados_DesAgrupados_ConReprocesos { get; set; }
    private List<PedidoProcesadoLiteDTO> PedidosProcesados_Agrupados_SinReprocesos { get; set; }
    private List<PedidoProcesadoLiteDTO> PedidosProcesados_Agrupados_ConReprocesos { get; set; }
    private List<ConsultaHojalataDTO> Hjlt { get; set; }
    List<ClienteDropdownDTO> DatosClientes { get; set; }
    List<MaquinaDTO> DatosMaquinas { get; set; }
    List<CodtintasDTO> DatosTintas { get; set; }
    List<CodigoAplicacionDTO> DatosCodsApliBarniz { get; set; }
    List<CodigoAplicacionDTO> DatosCodsBarniz { get; set; }
    DxGrid? _gridPedidos;
    DxGrid? _gridHjlt;
    string TxtAgrupar { get; set; }
    bool VisiblePedidos { get; set; }
    bool VisibleHojalata { get; set; }
    bool MostrarAgrupado { get; set; }
    bool AgruparActivo => PedidosProcesados != null && PedidosProcesados.Any() && !Filtro.IncluirLote;

    protected override Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            _esTraza = false;
            TxtAgrupar = "AGRUPAR";
        }
        return base.OnAfterRenderAsync(firstRender);
    }

    protected override async Task OnInitializedAsync()
    {
        SpinnerService.Show();
        _esTraza = false;
        Filtro = await FiltroService.GetFiltroAsync();
        var resultMaquinas = await Http.GetFromJsonAsync<ListResult<MaquinaDTO>>("Maquinas/dropdownbytipo/Todas");
        var resultDatosGenerales = await Http.GetFromJsonAsync<SingleResult<DatosGeneralesDTO>>("DatosGenerales");
        var resultTintas = await Http.GetFromJsonAsync<ListResult<CodtintasDTO>>("DatosGenerales/getTintas");

        DatosMaquinas = resultMaquinas.Data;
        DatosClientes = resultDatosGenerales.Data.Clientes;
        DatosCodsBarniz = resultDatosGenerales.Data.Barnices;
        DatosCodsApliBarniz = resultDatosGenerales.Data.CodsApli;
        DatosTintas = resultTintas.Data;
        if (Filtro.General)
        {
            VisiblePedidos = true;
            VisibleHojalata = false;
        }
        else if (Filtro.VerHojalata)
        {
            var result = await Http.GetFromJsonAsync<ListResult<ConsultaHojalataDTO>>($"DatosGenerales/consultaHojalata?idcliente={Filtro.IdCliente}&largo={Filtro.LargoHjlta}&ancho={Filtro.AnchoHjlta}&espesor={Filtro.EspesorHjlta}");
            if (result.Errors.Any())
            {
                ToastService.ShowError($"{result.Errors.First()}");
            }
            else
            {
                Hjlt = result.Data;
                VisiblePedidos = false;
                VisibleHojalata = true;
            }
        }
        else
        {
            ReiniciarPedidosProcesados();
            await GetDatosListadoHojalata(true);
            ActualizarPedidosProcesados();
            StateHasChanged();
        }
        SpinnerService.Hide();
    }

    private async Task GetDatosListadoHojalata(bool usarLocalStorageFilter)
    {
        if (usarLocalStorageFilter)
        {
            Filtro = await FiltroService.GetFiltroAsync();
        }
        var response = await Http.PostAsJsonAsync("Pedido/getall", Filtro);
        //var resTest = await response.Content.ReadAsStringAsync();
        var result = await response.Content.ReadFromJsonAsync<ListResult<PedidoProcesadoLiteDTO>>();
        if (result.Errors.Any())
        {
            ToastService.ShowError($"{result.Errors.First()}");
        }
        else
        {
            if (Filtro.OcultarReprocesos)
                PedidosProcesados_DesAgrupados_SinReprocesos = result.Data ?? new List<PedidoProcesadoLiteDTO>();
            else
                PedidosProcesados_DesAgrupados_ConReprocesos = result.Data ?? new List<PedidoProcesadoLiteDTO>();

            VisiblePedidos = true;
            VisibleHojalata = false;
        }
    }

    async Task Grid_ExportXlsx()
    {
        if (Hjlt != null)
        {
            await _gridHjlt.ExportToXlsxAsync($"{DateTime.Now:yyyyMMdd}_PedidosHojalata_Filtrados", new GridXlExportOptions()
            {
                //ExportSelectedRowsOnly = ExportSelectedRowsOnly,
                //CustomizeCell = OnCustomizeCell
            });
        }
        else
        {
            await _gridPedidos.ExportToXlsxAsync($"{DateTime.Now:yyyyMMdd}_Pedidos_Filtrados", new GridXlExportOptions()
            {
                //ExportSelectedRowsOnly = ExportSelectedRowsOnly,
                //CustomizeCell = OnCustomizeCell
            });
        }
    }

    async Task VerDatosPedido(string context)
    {
        await Js.InvokeVoidAsync("open", $"{NavManager.BaseUri}ProgramacionPedidos/{context}", "_blank");
    }

    async Task Filtrar()
    {
        PanelVisible = true;
        ReiniciarPedidosProcesados();
        MostrarAgrupado = false;
        TxtAgrupar = "AGRUPAR";
        await GetDatosListadoHojalata(false);
        ActualizarPedidosProcesados();
        PanelVisible = false;
    }

    async Task Agrupar()
    {
        PanelVisible = true;
        MostrarAgrupado = !MostrarAgrupado;
        TxtAgrupar = MostrarAgrupado ? "DESAGRUPAR" : "AGRUPAR";

        if (MostrarAgrupado)
            await AgruparPedidos(PedidosProcesados);

        ActualizarPedidosProcesados();
        PanelVisible = false;
    }


    private async Task AgruparPedidos(List<PedidoProcesadoLiteDTO> listaPedidos)
    {
        if (Filtro.OcultarReprocesos && PedidosProcesados_Agrupados_SinReprocesos != null)
            return;
        if (!Filtro.OcultarReprocesos && PedidosProcesados_Agrupados_ConReprocesos != null)
            return;

        var result = await ObtenerPedidosAgrupados(listaPedidos);
        if (result.Errors.Any())
        {
            ToastService.ShowError($"{result.Errors.First()}");
            return;
        }

        if (Filtro.OcultarReprocesos)
            PedidosProcesados_Agrupados_SinReprocesos = result.Data ?? new List<PedidoProcesadoLiteDTO>();
        else
            PedidosProcesados_Agrupados_ConReprocesos = result.Data ?? new List<PedidoProcesadoLiteDTO>();
    }

    private void ActualizarPedidosProcesados()
    {
        if (MostrarAgrupado)
            PedidosProcesados = Filtro.OcultarReprocesos ? PedidosProcesados_Agrupados_SinReprocesos : PedidosProcesados_Agrupados_ConReprocesos;
        else
            PedidosProcesados = Filtro.OcultarReprocesos ? PedidosProcesados_DesAgrupados_SinReprocesos : PedidosProcesados_DesAgrupados_ConReprocesos;
    }

    private async Task<ListResult<PedidoProcesadoLiteDTO>> ObtenerPedidosAgrupados(List<PedidoProcesadoLiteDTO> listaPedidos)
    {
        var pedidosAgrupados = listaPedidos.DistinctBy(g => g.IdPedido).ToList();
        var response = await Http.PostAsJsonAsync("Pedido/getCodApliAgrupado", pedidosAgrupados);
        return await response.Content.ReadFromJsonAsync<ListResult<PedidoProcesadoLiteDTO>>();
    }

    private async Task OnOcultarReprocesosChanged(bool nuevoValor)
    {
        PanelVisible = true;
        Filtro.OcultarReprocesos = nuevoValor;

        if (PedidosProcesados != null)
        {
            if (Filtro.OcultarReprocesos)
            {
                if (MostrarAgrupado)
                {
                    if (PedidosProcesados_Agrupados_SinReprocesos == null)
                    {
                        if (PedidosProcesados_DesAgrupados_SinReprocesos == null)
                            await GetDatosListadoHojalata(false);
                        await AgruparPedidos(PedidosProcesados_DesAgrupados_SinReprocesos);
                    }
                }
                else
                {
                    if (PedidosProcesados_DesAgrupados_SinReprocesos == null)
                        await GetDatosListadoHojalata(false);
                }
            }
            else
            {
                if (MostrarAgrupado)
                {
                    if (PedidosProcesados_Agrupados_ConReprocesos == null)
                    {
                        if (PedidosProcesados_DesAgrupados_ConReprocesos == null)
                            await GetDatosListadoHojalata(false);
                        await AgruparPedidos(PedidosProcesados_DesAgrupados_ConReprocesos);
                    }
                }
                else
                {
                    if (PedidosProcesados_DesAgrupados_ConReprocesos == null)
                        await GetDatosListadoHojalata(false);
                }
            }
        }

        ActualizarPedidosProcesados();
        PanelVisible = false;
    }

    void ColumnChooser()
    {
        _gridPedidos.ShowColumnChooser();
    }
    async Task Grid_LayoutAutoLoading(GridPersistentLayoutEventArgs e)
    {
        var json = await LocalStorage.GetItemAsync<string?>("_gridPedidosLayout");
        if (!string.IsNullOrEmpty(json))
            e.Layout = JsonSerializer.Deserialize<GridPersistentLayout>(json);
    }
    async Task Grid_LayoutAutoSaving(GridPersistentLayoutEventArgs e)
    {
        await LocalStorage.SetItemAsync("_gridPedidosLayout", JsonSerializer.Serialize(e.Layout));
    }
    async Task ResetLayoutButton_Click()
    {
        _esTraza = false;
        _textoBotonTraza = "Mostrar Columnas Trazabilidad";
        await LocalStorage.RemoveItemAsync("_gridPedidosLayout");
        StateHasChanged();
        // await RefreshPageAsync();
    }
    void GridPedidos_CustomizeColumnChooserItems(GridCustomizeElementEventArgs e)
    {
        if (e.ElementType == GridElementType.ColumnChooserItem && e.Column.Name == "AuditBarniz")
        {
            e.CssClass = "highlighted-item";
        }
    }
    void MarcarTrazabilidad()
    {
        _esTraza = !_esTraza;
        _textoBotonTraza = _esTraza ? "Resetear Columnas Trazabilidad"
            : "Mostrar Columnas Trazabilidad";
    }
    void LimpiarFiltro()
    {
        Filtro = new FiltroDTO
            {
                General = true
            };
        VisiblePedidos = true;
        VisibleHojalata = false;
        MostrarAgrupado = false;
        TxtAgrupar = "AGRUPAR";
        ReiniciarPedidosProcesados();
    }

    void ReiniciarPedidosProcesados()
    {
        PedidosProcesados = null;
        PedidosProcesados_Agrupados_ConReprocesos = null;
        PedidosProcesados_Agrupados_SinReprocesos = null;
        PedidosProcesados_DesAgrupados_ConReprocesos = null;
        PedidosProcesados_DesAgrupados_SinReprocesos = null;
    }
    string GetSelectedValuesDescription()
    {
        return Filtro?.ListCodBarniz1 == null || !Filtro.ListCodBarniz1.Any()
            ? string.Empty
            : string.Join(", ", Filtro.ListCodBarniz1);
    }
}
