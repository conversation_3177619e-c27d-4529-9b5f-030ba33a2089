﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Pedidos.Command;

public class AsignarFechaMultiplesPedidosCommand : IRequest<SingleResult<int>>
{
    public AsignarFechaMultiplesPedidosCommand(PedidosFechaDTO pf)
    {
        PedidosFecha = pf;
    }
    
    public PedidosFechaDTO PedidosFecha { get; set; }
}

public class AsignarFechaMultiplesPedidosCommandHandler : IRequestHandler<AsignarFechaMultiplesPedidosCommand, SingleResult<int>>
{
    private readonly ProgramadorLitalsaContext _programadorLitalsaContext ;
    public AsignarFechaMultiplesPedidosCommandHandler(ProgramadorLitalsaContext programadorLitalsaContext)
    {
        _programadorLitalsaContext = programadorLitalsaContext;
    }

    public async Task<SingleResult<int>> Handle(AsignarFechaMultiplesPedidosCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<int>{ Errors = new List<string>(), Data = 0 };
        try
        {
            foreach (var p in request.PedidosFecha.Pedidos)
            {
                var pedido = _programadorLitalsaContext.PedidoProcesado.FirstOrDefault(o => o.IdPedido == p);
                if (!request.PedidosFecha.LimpiarObs)
                {
                    pedido.RequeridoEnFecha = request.PedidosFecha.Fecha;
                    pedido.ObsACliente += $"FECHA ACTUALIZADA EL {DateTime.Today:dd/MM} A {request.PedidosFecha.Fecha.Value.Date:dd/MM} ";
                }
                else
                {
                    pedido.ObsACliente = string.Empty;
                }
                //20240712 - Añadido el guardar los datos en tabla auxiliar para combatir el pisado de datos cuando se actualizan los datos
                //en IN2 mientras se está haciendo un update del programador
                var dato = await _programadorLitalsaContext.ControlDatosActualizables
                    .FirstOrDefaultAsync(o => o.IdPedido == pedido.IdPedido, cancellationToken);

                dato ??= new ControlDatosActualizables { IdPedido = pedido.IdPedido.Value };

                dato.FechaEntrega = pedido.RequeridoEnFecha;
                dato.Urgente = pedido.Urgente;
                dato.MantenerAbierto = pedido.MantenerAbierto;
                dato.Cerrado = pedido.Anulado;
                dato.NoIncluirListado = pedido.NoIncluirEnListado;
                dato.ObsProg = pedido.ObsProgramacion;
                dato.ObsCliente = pedido.ObsACliente;
                dato.FechaCierrePedido = pedido.FechaFin;
                dato.Maquina = pedido.LineaTintas;

                if (dato.Id == 0)
                {
                    dato.FechaCreacion = DateTime.Now;
                    await _programadorLitalsaContext.ControlDatosActualizables.AddAsync(dato, cancellationToken);
                }
                else
                {
                    dato.FechaModificacion = DateTime.Now;
                    _programadorLitalsaContext.ControlDatosActualizables.Update(dato);
                }
            }
            var dbResult = await _programadorLitalsaContext.SaveChangesAsync(cancellationToken);
            result.Data = dbResult;
        }
        catch (Exception e)
        {
            var error = $"ERROR: AsignarFechaMultiplesPedidosCommand - {e.Message}--{(!string.IsNullOrWhiteSpace(e.InnerException?.Message) ? e.InnerException : string.Empty)}";
            result.Errors.Add(error);
        }
        return result;
    }
}