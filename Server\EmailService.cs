﻿using MailKit.Net.Smtp;
using MailKit.Security;
using Microsoft.Extensions.Options;
using MimeKit;
using ProgramadorGeneralBLZ.Server.Data;

namespace ProgramadorGeneralBLZ.Server
{
    public interface IEmailService
    {
        Task SendAsync(string to, string subject, string html, MessagePriority prioridad, string from = null);
        void Send(string to, string subject, string html, MessagePriority prioridad, string from = null);
    }

    public class EmailService : IEmailService
    {
        private readonly ConfigEmail _configEmail;

        public EmailService(IOptions<ConfigEmail> configEmail)
        {
            _configEmail = configEmail.Value;
        }

        public async Task SendAsync(string to, string subject, string html, MessagePriority prioridad, string from = null)
        {
            // create message
            var email = new MimeMessage();
            email.From.Add(MailboxAddress.Parse(from ?? _configEmail.EmailFrom));
            email.To.Add(MailboxAddress.Parse(to));
            email.Subject = subject;
            email.Priority = prioridad;
            var builder = new BodyBuilder
            {
                TextBody = html,
                HtmlBody = html,
            };
            email.Body = builder.ToMessageBody();

            // send email
            using (var smtp = new SmtpClient())
            {
                smtp.Connect(_configEmail.SmtpServer, _configEmail.SmtpPort, SecureSocketOptions.SslOnConnect);
                smtp.Authenticate(_configEmail.SmtpUsername, _configEmail.SmtpPassword);
                await smtp.SendAsync(email);
                smtp.Disconnect(true);
            }
        }
        public void Send(string to, string subject, string html, MessagePriority prioridad, string from = null)
        {
            // create message
            var email = new MimeMessage();
            email.From.Add(MailboxAddress.Parse(from ?? _configEmail.EmailFrom));
            email.To.Add(MailboxAddress.Parse(to));
            email.Subject = subject;
            email.Priority = prioridad;
            var builder = new BodyBuilder
            {
                TextBody = html,
                HtmlBody = html,
            };
            email.Body = builder.ToMessageBody();

            // send email
            using (var smtp = new SmtpClient())
            {
                smtp.Connect(_configEmail.SmtpServer, _configEmail.SmtpPort, SecureSocketOptions.SslOnConnect);
                smtp.Authenticate(_configEmail.SmtpUsername, _configEmail.SmtpPassword);
                smtp.Send(email);
                smtp.Disconnect(true);
            }
        }
    }
}