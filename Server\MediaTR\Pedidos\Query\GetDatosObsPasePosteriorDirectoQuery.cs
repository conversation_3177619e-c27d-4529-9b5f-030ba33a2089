﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Services.DatabaseManipulation;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Pedidos.Query;

public class GetDatosObsPasePosteriorParaReprocesoQuery : IRequest<SingleResult<string>>
{
    public GetDatosObsPasePosteriorParaReprocesoQuery(int idPedido, int idAplicacion)
    {
        IdAplicacion = idAplicacion;
        IdPedido = idPedido;
    }
    public int IdPedido { get; set; }
    public int IdAplicacion { get; set; }
}

public class GetDatosObsPasePosteriorParaReprocesoQueryHandler : IRequestHandler<GetDatosObsPasePosteriorParaReprocesoQuery, SingleResult<string>>
{
    private readonly ProgramadorLitalsaContext _contextProg;
    private readonly IDataManipulationService _dataManipulationService;

    public GetDatosObsPasePosteriorParaReprocesoQueryHandler(ProgramadorLitalsaContext contextProg, IDataManipulationService dataManipulationService)
    {
        _contextProg = contextProg;
        _dataManipulationService = dataManipulationService;
    }

    public async Task<SingleResult<string>> Handle(GetDatosObsPasePosteriorParaReprocesoQuery request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<string> { Errors = new List<string>(), Data = "" };
        try
        {
            var idPedidoOriginal = int.Parse(request.IdPedido.ToString()[..2] + "0" + request.IdPedido.ToString()[3..]);
            var idAplicacion = request.IdAplicacion;
            var datosProcesos =
                await _dataManipulationService.DevuelveOrdenProcesos(idPedidoOriginal, idAplicacion, 0);

            var procesoTexto = _dataManipulationService.DevuelveDatosAplicacion(datosProcesos.CodApli, "NombreApliProgramacion");

            var txtApliPosterior = "LUEGO " + procesoTexto;
            

            txtApliPosterior += datosProcesos.Cara == "ep"
                ? " POR CARA EXTERIOR"
                : " POR CARA " + (datosProcesos.Cara[0] == 'i' ? "INTERIOR" : "EXTERIOR");

            result.Data = txtApliPosterior + ".";
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: GetDatosObsPasePosteriorParaReprocesoQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            //throw new Exception(errorText, e);
            result.Errors.Add(errorText);
        }
        return result;
    }
}