﻿.navbar.header-navbar {
    flex-grow: 0;
    flex-wrap: nowrap;
    border: none;
    background-color: inherit;
    border-radius: 0;
    height: 3.5rem;
    min-height: 3.5rem;
    box-shadow: 8px 2px 6px 0px rgba(0, 0, 0, 0.35);
    justify-content: flex-start;
}

.header-navbar .navbar-toggler {
    width: 60px;
    outline: none;
    border-radius: 0;
    padding-left: .75rem;
    padding-right: .75rem;
    box-shadow: none;
    align-self: stretch;
}

.header-navbar .navbar-toggler .navbar-toggler-icon {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(255,255,255, 1)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 8h24M4 16h24M4 24h24'/%3E%3C/svg%3E");
    background-color: transparent !important;
    height: 2rem;
    width: 2rem;
}

.title {
    font-size: 1.1rem;
    text-overflow: ellipsis;
    overflow: hidden;
}
::deep .dxbl-menu .dx-menu-title {
    padding: 0.3rem 0.3rem;
}
@media (max-width: 350px) {
    .title {
        font-size: inherit;
    }
}

/*menu LOGIN*/

.menu-icon {
    width: 2rem;
    height: 2rem;
    min-width: 1rem;
    min-height: 1rem;
    background-size: contain;
    mask-repeat: no-repeat;
    -webkit-mask-repeat: no-repeat;
    background-position: center center;
    /*background-color: currentColor;*/
}

.dx-menu-item.notoggle > .dropdown-menu {
    transform: matrix(1, 0, 0, 1, -112, -1) !important;
}

.dx-menu-item.notoggle a > span.dropdown-toggle {
    display: none;
}

.dxbl-menu > .nav .dx-menu-item.notoggle {
    display: none;
}

.dxbl-menu > .nav .dx-menu-item.search-menu-item {
    display: none;
}

.dxbl-menu > .nav .separator {
    display: none;
}

.dx-menu-item .search {
    min-width: 123px
}

.dxbl-menu-item-tmpl > .search {
    padding: 0.25rem;
    position: relative;
}

.user-profile .logo-container {
    margin-top: 1.25rem;
    margin-bottom: 1.25rem;
}

.user-profile .log-off-btn {
    padding-top: 0.815rem;
    padding-bottom: 0.815rem;
    border-top: 1px solid #e5e5e5;
}
.menu-icon-user-profile {
    /*background-image: url('images/userprofile.svg');*/
    background-image: url('https://api.iconify.design/oi/account-logout.svg?color=white');
}
.user-profile .menu-icon-user-profile {
    opacity: 0.25;
    margin-top: 1.25rem;
    margin-bottom: 1.25rem;
    background-image: url('images/userprofile.svg');
}

.menu-icon-large {
    width: 5rem;
    height: 5rem;
}
.ajusteBtn span {
    margin-top: 5px !important;
}
::deep .iconInButton {
    padding: 0 0.75rem 0 0 !important;
    top: -2px !important;
}