﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa
{
    public partial class ConnectionLog
    {
        public ConnectionLog()
        {
            BlockedMachine = new HashSet<BlockedMachine>();
        }

        public int Id { get; set; }
        public int UserId { get; set; }
        public DateTime DateLogin { get; set; }
        public DateTime DateLastSeen { get; set; }
        public DateTime? DateOut { get; set; }
        public bool Active { get; set; }

        public virtual UserLite User { get; set; }
        public virtual ICollection<BlockedMachine> BlockedMachine { get; set; }
    }
}