﻿using System.Data;
using System.Diagnostics;
using System.Reactive.Concurrency;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.DatoLita01;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Services.DatabaseManipulation;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Pedidos.Query;

public class GetPedidosFotoLitoProgramarQuery : IRequest<ListResult<DatosGeneralesPedidoFotomecanicaLitoDTO>>
{
    /// <summary>
    /// Obtiene el listado de pedidos programados para barnizado según los parámetros del filtro
    /// </summary>
    public GetPedidosFotoLitoProgramarQuery()
    {
    }
}

internal class GetPedidosFotoLitoProgramarQueryHandler : IRequestHandler<GetPedidosFotoLitoProgramarQuery, ListResult<DatosGeneralesPedidoFotomecanicaLitoDTO>>
{
    private readonly ProgramadorLitalsaContext _programadorLitalsaContext;
    private readonly DatoLita01Context _datoLita01Context;
    private readonly IDbContextFactory<ProgramadorLitalsaContext> _programadorLitalsaContextFactory;
    private readonly IDataManipulationService _dataManipulationService;
    public GetPedidosFotoLitoProgramarQueryHandler(ProgramadorLitalsaContext programadorLitalsaContext, IDbContextFactory<ProgramadorLitalsaContext> programadorLitalsaContextFactory, IDataManipulationService dataManipulationService, DatoLita01Context datoLita01Context)
    {
        _programadorLitalsaContext = programadorLitalsaContext;
        _programadorLitalsaContextFactory = programadorLitalsaContextFactory;
        _dataManipulationService = dataManipulationService;
        _datoLita01Context = datoLita01Context;
    }

    public async Task<ListResult<DatosGeneralesPedidoFotomecanicaLitoDTO>> Handle(GetPedidosFotoLitoProgramarQuery request, CancellationToken cancellationToken)
    {
        //ATENTO A LAS OBS1!!!!! Igual hay que construirlas a mano, porque se ha dejado fuera de los pasos de actualizaciones la ñapa que hace el access
        //de leer y traer la información, concatenar y re-actualizar el campo en la tabla de los pedidos.
        Stopwatch stopwatch = new Stopwatch();
        stopwatch.Start();
        var result = new ListResult<DatosGeneralesPedidoFotomecanicaLitoDTO>()
        {
            Data = new List<DatosGeneralesPedidoFotomecanicaLitoDTO>(),
            Errors = new List<string>()
        };
        try
        {
            var programador = await _programadorLitalsaContextFactory.CreateDbContextAsync(cancellationToken);
            Debug.WriteLine($"sp_PedidosLitoFotomecanicaParaProgramar I: {stopwatch.ElapsedMilliseconds}");
            var pedidos = await programador.Set<DatosGeneralesPedidoFotomecanicaLitoDTO>()
                .FromSqlRaw("[dbo].[sp_PedidosLitoFotomecanicaParaProgramar]")
                .AsNoTracking()
                .ToListAsync(cancellationToken);
            var listaPedidos = pedidos.Select(o => o.IdPedido).ToList();
            // Crear la cadena con los Ids para la consulta SQL
            string idList = string.Join(", ", listaPedidos);
            Debug.WriteLine($"sp_PedidosLitoFotomecanicaParaProgramar F: {stopwatch.ElapsedMilliseconds}");

            Debug.WriteLine($"PedidoProcesado I: {stopwatch.ElapsedMilliseconds}");
            var datosPedido = _programadorLitalsaContext.PedidoProcesado
                .FromSqlRaw($"SELECT * FROM PedidoProcesado WHERE IdPedido IN ({idList})")
                .ToList();
            Debug.WriteLine($"PedidoProcesado F: {stopwatch.ElapsedMilliseconds}");

            Debug.WriteLine($"Matped I: {stopwatch.ElapsedMilliseconds}");
            var datosMatPed = _programadorLitalsaContext.Matped
                .FromSqlRaw($"SELECT * FROM Matped WHERE Nummpe IN ({idList})")
                .ToList();
            Debug.WriteLine($"Matped F: {stopwatch.ElapsedMilliseconds}");

            Debug.WriteLine($"foreach I: {stopwatch.ElapsedMilliseconds}");

            var ppdto = new DatosGeneralesPedidoFotomecanicaLitoDTO();
            var type = ppdto.GetType();
            var campoC0_PEDNames = Enumerable.Range(1, 11).Select(i => i < 10 ? $"C0{i}ped" : $"Co{i}ped").ToList();
            foreach (var p in pedidos)
            {
                p.PasesTotales = _programadorLitalsaContext.Database.SqlQuery<int>($"SELECT dbo.DevuelveNumeroColores({p.IdPedido})").AsEnumerable().First() * p.HojasPedido;

                //TINTAS
                var textoTintas = string.Empty;
                var tintasPrueba = string.Empty;
                foreach (var campoC0_PEDName in campoC0_PEDNames)
                {
                    var campoC0_PEDValue = type.GetProperty(campoC0_PEDName)?.GetValue(p);
                    var codTintas = campoC0_PEDValue != null ? int.Parse(campoC0_PEDValue.ToString()) : 0;
                    tintasPrueba +=
                        $"{_programadorLitalsaContext.Database.SqlQuery<string>($"SELECT dbo.dcolor({codTintas})").AsEnumerable().First()}";

                    if (codTintas <= 4 || codTintas == 900 || codTintas == 9000) continue;

                    textoTintas +=
                        $"{_programadorLitalsaContext.Database.SqlQuery<string>($"SELECT dbo.busca_color_especial_v2({codTintas},1)").AsEnumerable().First()} - ";
                }

                p.TextoTintas = textoTintas;
                p.Tintas = tintasPrueba;

                //p.Tintas = $"{_programadorLitalsaContext.Database.SqlQuery<string>($"SELECT dbo.dcolor({p.C01ped ?? 0})").AsEnumerable().First()}" +
                //           $"{_programadorLitalsaContext.Database.SqlQuery<string>($"SELECT dbo.dcolor({p.C02ped ?? 0})").AsEnumerable().First()}" +
                //           $"{_programadorLitalsaContext.Database.SqlQuery<string>($"SELECT dbo.dcolor({p.C03ped ?? 0})").AsEnumerable().First()}" +
                //           $"{_programadorLitalsaContext.Database.SqlQuery<string>($"SELECT dbo.dcolor({p.C04ped ?? 0})").AsEnumerable().First()}" +
                //           $"{_programadorLitalsaContext.Database.SqlQuery<string>($"SELECT dbo.dcolor({p.C05ped ?? 0})").AsEnumerable().First()}" +
                //           $"{_programadorLitalsaContext.Database.SqlQuery<string>($"SELECT dbo.dcolor({p.C06ped ?? 0})").AsEnumerable().First()}" +
                //           $"{_programadorLitalsaContext.Database.SqlQuery<string>($"SELECT dbo.dcolor({p.C07ped ?? 0})").AsEnumerable().First()}" +
                //           $"{_programadorLitalsaContext.Database.SqlQuery<string>($"SELECT dbo.dcolor({p.C08ped ?? 0})").AsEnumerable().First()}" +
                //           $"{_programadorLitalsaContext.Database.SqlQuery<string>($"SELECT dbo.dcolor({p.C09ped ?? 0})").AsEnumerable().First()}" +
                //           $"{_programadorLitalsaContext.Database.SqlQuery<string>($"SELECT dbo.dcolor({p.Co10ped ?? 0})").AsEnumerable().First()}";

                p.TextEstado = _dataManipulationService.GetTextoEstadoCodigosAplicacion(p.IdPedido.Value);
                p.DescHojalata = _dataManipulationService.GetCaracteristicasHojalata(p.IdPedido.Value,
                    datosPedido.Single(o => o.IdPedido == p.IdPedido),
                    datosMatPed.FirstOrDefault(o => o.Codigo == p.IdPedido.Value.ToString()));
            }
            Debug.WriteLine($"foreach F: {stopwatch.ElapsedMilliseconds}");
            result.Data = pedidos;
            await programador.DisposeAsync();
        }
        catch (Exception e)
        {
                var errorText = $"ERROR: GetPedidosFotoLitoProgramarQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }
        stopwatch.Stop();
        
        return result;
    }
}