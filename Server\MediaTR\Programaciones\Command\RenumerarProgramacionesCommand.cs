﻿using MediatR;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Services.DatabaseManipulation;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Programaciones.Command;

public class RenumerarProgramacionesCommand : IRequest<SingleResult<int>>
{
    public int Linea { get; set; }
    public int Desde { get; set; }

    public RenumerarProgramacionesCommand(int linea, int desde)
    {
        Linea = linea;
        Desde = desde;
    }
    
}

public class RenumerarProgramacionesCommandHandler : IRequestHandler<RenumerarProgramacionesCommand, SingleResult<int>>
{
    private readonly IDataManipulationService _dataManipulationService;
    public RenumerarProgramacionesCommandHandler(IDataManipulationService dataManipulationService)
    {
        _dataManipulationService = dataManipulationService;
    }

    public async Task<SingleResult<int>> Handle(RenumerarProgramacionesCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<int>{ Errors = new List<string>(), Data = 0 };
        try
        {
            await _dataManipulationService.RenumerarProgramacionAsync(request.Desde, request.Linea);
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: RenumerarProgramacionesCommand - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }
        return result;
    }
}