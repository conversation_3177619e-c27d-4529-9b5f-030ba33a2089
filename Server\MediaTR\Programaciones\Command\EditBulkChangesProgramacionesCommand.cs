﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Services.DatabaseManipulation;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Programaciones.Command;

public class EditBulkChangesProgramacionesCommand : IRequest<SingleResult<int>>
{

    public EditBulkChangesProgramacionesCommand(BulkChangesDTO bulkChanges)
    {
        BulkChangesDTO = bulkChanges;
    }

    public BulkChangesDTO BulkChangesDTO { get; set; }
}

public class EditBulkChangesProgramacionesCommanddHandler : IRequestHandler<EditBulkChangesProgramacionesCommand, SingleResult<int>>
{
    private readonly ProgramadorLitalsaContext _contextProg;
    private readonly IDataManipulationService _dataManipulationService;
    public EditBulkChangesProgramacionesCommanddHandler(ProgramadorLitalsaContext contextProg, IDataManipulationService dataManipulationService)
    {
        _contextProg = contextProg;
        _dataManipulationService = dataManipulationService;
    }

    public async Task<SingleResult<int>> Handle(EditBulkChangesProgramacionesCommand request, CancellationToken ct)
    {
        var result = new SingleResult<int> { Errors = new List<string>(), Data = 0 };
        try
        {
            var bulk = request.BulkChangesDTO;
            var progExistente = await
                _contextProg.TablaProgramacion.Where(o =>
                    bulk.Posiciones.Contains(o.Posicion.Value) &&
                    o.Idlinea == bulk.CurrentMaquina.Idmaquina).ToListAsync(ct);

            foreach (var p in progExistente)
            {
                //Limpiamos campos de fechas, horas y cambios.
                p.HoraFinEstimada = null;
                p.HoraComienzoEstimada = null;
                p.DiaReal = null;
                p.HoraReal = null;
                //p.VarCambios = null; // 15/07/25 Carlos indica no reiniciar la variable de cambios
                p.TiempoEstimadoCambio = null;
                p.TiposCambio = null;

                //Para segurar que siempre aparece al final.
                if (bulk.MaquinaDto is not null)
                {
                    p.Posicion += 9000000;
                    p.Idlinea = bulk.MaquinaDto.Idmaquina;
                }
                if (bulk.CodigoAplicacionDto is not null || bulk.ProductoDto is not null)
                {
                    if (!p.Observaciones.Contains('*'))
                        p.Observaciones = $"*{p.Observaciones}";

                    // Si 'Idaplicacion' ha cambiado, obtener el nuevo 'Codapli' y el nuevo 'Producto'
                    int? barnizAConsultar = null;
                    if (bulk.CodigoAplicacionDto is not null)
                    {
                        barnizAConsultar = _contextProg.Codapli.FirstOrDefault(o => o.Codbaz == bulk.CodigoAplicacionDto.Idcodigoaplicacion.Value).Codbarniz;
                    }
                    // Si 'Idproducto' ha cambiado, obtener el nuevo 'Producto'
                    else if (bulk.ProductoDto is not null)
                    {
                        barnizAConsultar = bulk.ProductoDto.Idproducto;
                    }
                    var nuevoProducto = _contextProg.TablaProductos.FirstOrDefault(o => o.Idproducto == barnizAConsultar.Value);
                    p.Producto = $"{nuevoProducto?.Denominacion ?? string.Empty}";
                    p.Idproducto = bulk.ProductoDto.Idproducto;
                }
                //Si las capas se han modificado.
                if (bulk.MinCapa is not null || bulk.MaxCapa is not null)
                {
                    p.Observaciones = $"*{p.Observaciones}";
                    p.PesoMin = bulk.MinCapa ?? p.PesoMin;
                    p.Peso = bulk.MaxCapa ?? p.Peso;
                    p.Idproducto = bulk.ProductoDto.Idproducto;
                    p.Producto =
                        $"{_contextProg.TablaProductos.FirstOrDefault(o => o.Idproducto == p.Idproducto).Denominacion} " +
                        $"Peso: {bulk.MinCapa} - {bulk.MaxCapa} g/m2";
                }
            }

            var dbResult = await _contextProg.SaveChangesAsync(ct);
            result.Data = dbResult;
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: EditBulkChangesProgramacionesCommand - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }
        return result;
    }
}