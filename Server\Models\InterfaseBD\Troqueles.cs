﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace ProgramadorGeneralBLZ.Server.Models.InterfaseBD;

public partial class Troqueles
{
    public string IdTroquel { get; set; }

    public string IdCliente { get; set; }

    public string TipoElemento { get; set; }

    public string Familia { get; set; }

    public string SubFamilia { get; set; }

    public string FormatoEnvase { get; set; }

    public string DiametroEnvase { get; set; }

    public string AlturaEnvase { get; set; }

    public decimal Desarrollo { get; set; }

    public decimal DiametroReal { get; set; }

    public decimal RlIzq { get; set; }

    public decimal RlDer { get; set; }

    public decimal EjeMayor { get; set; }

    public decimal EjeMenor { get; set; }

    public decimal RtSuperior { get; set; }

    public decimal RtInferior { get; set; }

    public decimal AlturaElemento { get; set; }

    public bool Embuticion { get; set; }

    public string Descripcion { get; set; }

    public string FormatoIn2 { get; set; }

    public string Observaciones { get; set; }

    public decimal Superficie { get; set; }

    public bool Activo { get; set; }

    public DateTime FechaCreacion { get; set; }

    public TimeSpan HoraCreacion { get; set; }

    public string UsuarioCreacion { get; set; }

    public DateTime? FechaMod { get; set; }

    public TimeSpan? HoraMod { get; set; }

    public string UsuarioMod { get; set; }

    public DateTime? FechaObsoleto { get; set; }

    public string UsuarioObsoleto { get; set; }

    public virtual Familia FamiliaNavigation { get; set; }

    public virtual Debmas ClienteNavigation { get; set; }

    public virtual ICollection<Motivos> Motivos { get; set; } = new List<Motivos>();

    public virtual ICollection<Planos> Planos { get; set; } = new List<Planos>();

    public virtual Subfamilia SubFamiliaNavigation { get; set; }

    public virtual TipoElemento TipoElementoNavigation { get; set; }
}