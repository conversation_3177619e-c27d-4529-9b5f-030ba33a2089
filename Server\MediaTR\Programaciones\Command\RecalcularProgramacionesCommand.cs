﻿using System;
using MediatR;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Services.DatabaseManipulation;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Programaciones.Command;

public class RecalcularProgramacionesCommand(int linea, int desde, List<PedidoIndexCodigoDTO> listado)
    : IRequest<SingleResult<int>>
{
    public List<PedidoIndexCodigoDTO> Listado { get; set; } = listado;
    public int Linea { get; set; } = linea;
    public int Desde { get; set; } = desde;
}

public class RecalcularProgramacionesCommandHandler(ProgramadorLitalsaContext contextProg)
    : IRequestHandler<RecalcularProgramacionesCommand, SingleResult<int>>
{
    public async Task<SingleResult<int>> Handle(RecalcularProgramacionesCommand request,
        CancellationToken cancellationToken)
    {
        var result = new SingleResult<int> { Errors = new List<string>(), Data = 0 };

        try
        {
            var maquina = await contextProg.Maquinas.FindAsync([request.Linea], cancellationToken);
            if (maquina != null && maquina.PosicionDesde != request.Desde)
            {
                result.Data = 0;
                result.Errors.Add($"Atención, la posición DESDE de la máquina no coincide con la mostrada");
                return result;
            }

            // CÓDIGO ANTERIOR COMENTADO - Lógica para posiciones intermedias
            //var posicion =
            //    request.Desde + 10; //Añadimos 10 para empezar a ajustar posición desde justo la siguiente al mínimo.
            //var posicionAnterior = request.Desde;

            //foreach (var item in request.Listado)
            //{
            //    //04/09/2023: Se oculta la columna de IDPROGRAMACION y por tanto hay que ajustar aqui y en el js de ProgramacionLitografia.razor.js
            //    //El ajuste se realiza en el handler, ya que sirve con cambiar los parámetros de búsqueda ya que ahora se está enviando posición|idpedido.
            //    var idPedido = int.Parse(item.Codigo.Split("|")[1]);
            //    var posicionPedido = int.Parse(item.Codigo.Split("|")[0]);
            //    var pedido = await contextProg.TablaProgramacion
            //        .FirstOrDefaultAsync(x =>
            //                x.Idlinea == request.Linea &&
            //                x.Idpedido == idPedido &&
            //                x.Posicion == posicionPedido,
            //            cancellationToken: cancellationToken);
            //    //Solo ajusto/sobreescribo las posiciones que son enteras, para respetar las que sean de tipo 11, 15, etc que meten a mano entre medias.
            //    //11/02/2025 - Con carlos sentado al lado se elimina la restricción de no modificar posiciones intermedias. Ahora se ajusta su posición
            //    //de las decenas pero se mantiene el valor de las unidades, así seguiran quedando entre medias de sus nuevas posiciones
            //    if (pedido.Posicion is not 0)
            //    {
            //        var esPosicionIntermedia = pedido.Posicion % 10 > 0;
            //        if (esPosicionIntermedia)
            //        {
            //            //pedido.Posicion = posicion + pedido.Posicion % 10;
            //            if (pedido.Posicion > posicionAnterior && (pedido.Posicion - posicionAnterior) < 10)
            //            {
            //                posicionAnterior = posicion;
            //                posicion = posicionAnterior + 10;
            //                continue;
            //            }

            //            if (pedido.Posicion > posicionAnterior && (pedido.Posicion - posicionAnterior) > 10)
            //                pedido.Posicion = posicionAnterior + pedido.Posicion % 10;
            //            else if (pedido.Posicion < posicionAnterior)
            //                pedido.Posicion = posicionAnterior + pedido.Posicion % 10;
            //            posicionAnterior = posicion;
            //            //posicion = posicionAnterior + 10;
            //        }
            //        else
            //        {
            //            pedido.Posicion = posicion;
            //            posicionAnterior = posicion;
            //            posicion += 10;
            //        }
            //    }
            //}

            // NUEVA LÓGICA - Asignar posiciones múltiplos de 10 secuencialmente
            // Calcular la siguiente posición disponible múltiplo de 10 a partir de request.Desde
            var siguientePosicionMultiploDe10 = ((request.Desde / 10) + 1) * 10;
            var posicionActual = siguientePosicionMultiploDe10;

            foreach (var item in request.Listado)
            {
                //04/09/2023: Se oculta la columna de IDPROGRAMACION y por tanto hay que ajustar aqui y en el js de ProgramacionLitografia.razor.js
                //El ajuste se realiza en el handler, ya que sirve con cambiar los parámetros de búsqueda ya que ahora se está enviando posición|idpedido.
                var idPedido = int.Parse(item.Codigo.Split("|")[1]);
                var posicionPedido = int.Parse(item.Codigo.Split("|")[0]);
                var pedido = await contextProg.TablaProgramacion
                    .FirstOrDefaultAsync(x =>
                            x.Idlinea == request.Linea &&
                            x.Idpedido == idPedido &&
                            x.Posicion == posicionPedido,
                        cancellationToken: cancellationToken);

                // Asignar la posición actual (múltiplo de 10) y avanzar al siguiente múltiplo de 10
                if (pedido.Posicion is not 0)
                {
                    pedido.Posicion = posicionActual;
                    posicionActual += 10;
                }
            }

            // Guardar cambios en TablaProgramacion
            await contextProg.SaveChangesAsync(cancellationToken);
            result.Data = 1;
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: RecalcularProgramacionesCommand - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }
}