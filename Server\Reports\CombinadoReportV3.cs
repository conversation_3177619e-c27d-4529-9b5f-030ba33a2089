﻿using System;
using System.Collections;
using System.ComponentModel;
using System.Drawing;
using DevExpress.XtraReports.Expressions;
using DevExpress.XtraReports.Parameters;
using DevExpress.XtraReports.UI;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using static MailKit.Net.Imap.ImapMailboxFilter;

namespace ProgramadorGeneralBLZ.Server.Reports
{
    public partial class CombinadoReportV3 : XtraReport
    {
        public CombinadoReportV3()
        {
            InitializeComponent();
        }
        private void xrSubreport1_BeforePrint(object sender, CancelEventArgs e)
        {
            var idPedido = GetCurrentColumnValue<int>("Idpedido");
            var idLinea = GetCurrentColumnValue<int>("Idlinea");
            var posicion = GetCurrentColumnValue<int>("Posicion");
            var tipoPedido = GetCurrentColumnValue<string>("TipoPedido");
            var report = (XtraReport)((XRControl)sender).Parent.Parent;

            var subreport = ((XRSubreport)sender);
            var maquinaValue = report.Parameters["MaquinaNombreParameter"].Value;
            var verCambioValue = report.Parameters["VerCambiosParameter"].Value;
            var wowValue = report.Parameters["WoWParameter"].Value;
            var idMaquinaValue = report.Parameters["IdMaquinaParameter"].Value;
            //subreport.ParameterBindings.Add(new ParameterBinding("MaquinaNombreParameter", null, paramMaquina.ToString()));
            //subreport.ParameterBindings.Add(new ParameterBinding("OcultarCabeceraParameter", null, "true"));
            //subreport.ParameterBindings.Add(new ParameterBinding("VerCambiosParameter", null, "VerCambiosParameter"));
            subreport.ParameterBindings.Add(
                new ParameterBinding("PosicionParameter", null, "PedidoProgramadoEnviadoImprimirCombinado.Posicion"));

            switch (tipoPedido)
            {
                case "Barnizado":
                    ////subreport.DataMember = "PedidoProgramadoEnviadoImprimir";
                    //subreport.ReportSource = new BarnizadoReportV2();
                    //subreport.Visible = true;
                    ////subreport.BeginInit();
                    ////var dsB = subreport.DataSource as PedidoProgramadoEnviadoImprimir;
                    ((XRSubreport)sender).ReportSource = new BarnizadoReportV2();
                    BarnizadoReportV2 subRepB = subreport.ReportSource as BarnizadoReportV2;
                    subRepB.Parameters["MaquinaNombreParameter"].Value = maquinaValue.ToString();
                    subRepB.Parameters["OcultarCabeceraParameter"].Value = true.ToString();
                    subRepB.Parameters["VerCambiosParameter"].Value = verCambioValue.ToString();
                    subRepB.Parameters["IdMaquinaParameter"].Value = idMaquinaValue.ToString();
                    //subRepB.Parameters.Clear();
                    //foreach (var p in report.Parameters)
                    //{
                    //    subRepB.Parameters.Add(p);
                    //}
                    break;
                case "Litografia":
                    //subreport.ReportSource = new LitografiaReport();
                    //xrSubreport1.ParameterBindings.Add(new ParameterBinding("WoWParameter", null, null));
                    //subreport.Visible = true;
                    ((XRSubreport)sender).ReportSource = new LitografiaReport();
                    LitografiaReport subRepL = subreport.ReportSource as LitografiaReport;
                    subRepL.Parameters["WoWParameter"].Value = wowValue.ToString();
                    subRepL.Parameters["MaquinaNombreParameter"].Value = maquinaValue.ToString();
                    subRepL.Parameters["OcultarCabeceraParameter"].Value = true.ToString();
                    subRepL.Parameters["VerCambiosParameter"].Value = verCambioValue.ToString();
                    subRepL.Parameters["IdMaquinaParameter"].Value = idMaquinaValue.ToString();
                    break;
            }

            //((XRSubreport)sender).ReportSource = subreport;
        }
        //private void xrSubreport1_BeforePrint(object sender, CancelEventArgs e)
        //{
        //    var idPedido = GetCurrentColumnValue<int>("Idpedido");
        //    var idLinea = GetCurrentColumnValue<int>("Idlinea");
        //    var posicion = GetCurrentColumnValue<int>("Posicion");
        //    var tipoPedido = GetCurrentColumnValue<string>("TipoPedido");
        //    var report = (XtraReport)((XRControl)sender).Parent.Parent;

        //    var subreport = new XtraReport();

        //    foreach (var p in report.Parameters)
        //    {
        //        subreport.Parameters.Add(p);
        //    }

        //    subreport.DataSource = this.DataSource;

        //    int pageWidth = (int)(subreport.PageWidth - (subreport.Margins.Left + subreport.Margins.Right));
        //    switch (tipoPedido)
        //    {
        //        case "Barnizado":
        //            subreport.DataMember = "PedidoProgramadoEnviadoImprimir";
        //            subreport.Visible = true;
        //            subreport.BeginInit();
        //            //var dsB = subreport.DataSource as PedidoProgramadoEnviadoImprimir;

        //            break;
        //        case "Litografia":
        //            subreport.DataMember = "PedidoLitoProgramadoEnviadoImprimir";
        //            subreport.Visible = true;
        //            subreport.BeginInit();
        //            //var dsL = subreport.DataSource as PedidoLitoProgramadoEnviadoImprimir;
        //            break;
        //    }

        //}
    }
}
