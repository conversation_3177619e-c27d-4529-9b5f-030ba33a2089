﻿using System;
using System.Collections;
using System.ComponentModel;
using System.Drawing;
using DevExpress.XtraReports.UI;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;

namespace ProgramadorGeneralBLZ.Server.Reports
{
    public partial class LitografiaReport : DevExpress.XtraReports.UI.XtraReport
    {
        public LitografiaReport()
        {
            InitializeComponent();
            //this.DataSourceDemanded += XtraReport_DataSourceDemanded;
            XRRichText richText = this.FindControl("xrRichText1", true) as XRRichText;
            if (richText != null)
            {
                // Asigna el manejador del evento BeforePrint
                richText.BeforePrint += RichText_BeforePrint;
            }

        }
        private void RichText_BeforePrint(object sender, CancelEventArgs e)
        {
            var richText = (XRRichText)sender;
            var fieldValue = GetCurrentColumnValue("Observaciones").ToString();
            var fieldValue2 = GetCurrentColumnValue("ObsCalidad").ToString();
            var fullValue = $"{fieldValue} {fieldValue2}";

            // Reemplaza los saltos de línea con la secuencia RTF correspondiente
            fullValue = fullValue/*.Replace(Environment.NewLine, @"\par ")*/.Replace("\n", @"\par ");

            // Define el formato RTF básico con fuente Arial y tamaño 9
            const string rtfHeader = @"{\rtf1\ansi\deff0{\fonttbl{\f0 Arial;}}\uc1\pard\fs16"; // \fs16 corresponde a tamaño 8 en puntos
            const string rtfColorTable = @"{\colortbl ;\red255\green0\blue0;}";

            var rtfFormattedText =
                // Condicionalmente reemplaza "REVISAR REGISTROS DE VISIÓN ARTIFICIAL EN PREPRINT" con versión en RTF con color rojo
                fullValue?.Contains("REVISAR REGISTROS DE VISIÓN ARTIFICIAL EN PREPRINT") == true 
                ? fullValue.Replace("REVISAR REGISTROS DE VISIÓN ARTIFICIAL EN PREPRINT", @"{\cf1. REVISAR REGISTROS DE VISIÓN ARTIFICIAL EN PREPRINT}") 
                : fullValue;

            // Envolver el texto en un formato RTF completo
            var rtfText = rtfHeader + rtfColorTable + rtfFormattedText + @"}";

            // Asigna el texto formateado al richText
            richText.Rtf = rtfText;
        }
        private void XtraReport_DataSourceDemanded(object sender, EventArgs e)
        {
            // Obteniendo el objeto reporte
            var report = sender as DevExpress.XtraReports.UI.XtraReport;

            // Obteniendo los datos del reporte
            var data = report.DataSource as PedidoLitoProgramadoEnviadoImprimir; // Reemplaza "TuTipoDeDatos" con el tipo de datos correcto

            // Suponiendo que Idaplicacion es una propiedad de tu fuente de datos
            bool retira = (data?.Idaplicacion == 998);
            bool tintamate = (data?.Idaplicacion == 997);

            // Ajustando la visibilidad de los controles en el reporte
            // Suponiendo que c01ped, c02ped, etc., son los nombres de los controles en tu reporte
            report.FindControl("T01Ext", false).Visible = !retira && !tintamate;
            report.FindControl("T02Ext", false).Visible = !retira && !tintamate;
            report.FindControl("T03Ext", false).Visible = !retira && !tintamate;
            report.FindControl("T04Ext", false).Visible = !retira && !tintamate;
            report.FindControl("T05Ext", false).Visible = !retira && !tintamate;
            report.FindControl("T06Ext", false).Visible = !retira && !tintamate;
            report.FindControl("T07Ext", false).Visible = !retira && !tintamate;
            report.FindControl("T08Ext", false).Visible = !retira && !tintamate;
            report.FindControl("T09Ext", false).Visible = !retira && !tintamate;
            report.FindControl("T10Ext", false).Visible = !retira && !tintamate;
            report.FindControl("T11Ext", false).Visible = !retira && !tintamate;

            report.FindControl("T01Int", false).Visible = retira && !tintamate;
            report.FindControl("T02Int", false).Visible = retira && !tintamate;
            report.FindControl("T03Int", false).Visible = retira && !tintamate;
            report.FindControl("T04Int", false).Visible = retira && !tintamate;
        }

    }
}
