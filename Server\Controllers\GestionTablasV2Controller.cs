﻿// Mantengo los mismos using ...
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Identity.Web.Resource;
using Nelibur.ObjectMapper;
using ProgramadorGeneralBLZ.Server.CustomFilterAttributes;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Repositorios;
using ProgramadorGeneralBLZ.Shared;
using ProgramadorGeneralBLZ.Shared.DTO;

namespace ProgramadorGeneralBLZ.Server.Controllers
{
    [Authorize]
    [ApiController]
    [Route("[controller]")]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAdB2C:Scopes")]
    [ClaimRequirementContainRole("extension_Roles", $"{Roles.Programador},{Roles.Admin}")]
    public class GestionTablasV2Controller : ControllerBase
    {
        private readonly IRepositorioGestionTablas _repo;
        private readonly ILogger<GestionTablasV2Controller> _logger;
        private readonly ProgramadorLitalsaContext _context;

        public GestionTablasV2Controller(IRepositorioGestionTablas repo, ILogger<GestionTablasV2Controller> logger, ProgramadorLitalsaContext programadorLitalsaContext)
        {
            _repo = repo;
            _logger = logger;
            _context = programadorLitalsaContext;
        }
        [HttpGet("GrupoNotificaciones")]
        public async Task<ActionResult<List<GrupoNotificacionesDTO>>> GetGruposNotificaciones()
        {
            return await _repo.GetAllEntities<GrupoNotificaciones, GrupoNotificacionesDTO>();
        }

        [HttpPost("GrupoNotificaciones")]
        public async Task<ActionResult<GrupoNotificacionesDTO>> PostGrupoNotificaciones(GrupoNotificacionesDTO grupo)
        {
            return await _repo.AddEntity<GrupoNotificaciones, GrupoNotificacionesDTO>(grupo, nameof(GetGruposNotificaciones), "Id");
        }

        [HttpPut("GrupoNotificaciones/{id}")]
        public async Task<IActionResult> PutGrupoNotificaciones(int id, GrupoNotificacionesDTO grupo)
        {
            return await _repo.UpdateEntity<GrupoNotificaciones, GrupoNotificacionesDTO>(id, grupo, "Id");
        }

        [HttpDelete("GrupoNotificaciones/{id}")]
        public async Task<IActionResult> DeleteGrupoNotificaciones(int id)
        {
            return await _repo.DeleteEntity<GrupoNotificaciones>(id, "Id");
        }

        // ...

        [HttpGet("DestinatarioProgramaciones")]
        public async Task<ActionResult<List<DestinatarioProgramacionesDTO>>> GetDestinatarioProgramaciones()
        {
            return await _repo.GetAllEntities<DestinatarioProgramaciones, DestinatarioProgramacionesDTO>();
        }

        [HttpPost("DestinatarioProgramaciones")]
        public async Task<ActionResult<DestinatarioProgramacionesDTO>> PostDestinatarioProgramaciones(DestinatarioProgramacionesDTO d)
        {
            return await _repo.AddEntity<DestinatarioProgramaciones, DestinatarioProgramacionesDTO>(d, nameof(GetDestinatarioProgramaciones), "Id");
        }

        [HttpPut("DestinatarioProgramaciones/{id}")]
        public async Task<IActionResult> PutDestinatarioProgramaciones(int id, DestinatarioProgramacionesDTO d)
        {
            return await _repo.UpdateEntity<DestinatarioProgramaciones, DestinatarioProgramacionesDTO>(id, d, "Id");
        }

        [HttpDelete("DestinatarioProgramaciones/{id}")]
        public async Task<IActionResult> DeleteDestinatarioProgramaciones(int id)
        {
            return await _repo.DeleteEntity<DestinatarioProgramaciones>(id, "Id");
        }

        [HttpGet("Comentarios")]
        public async Task<ActionResult<List<ComentariosDTO>>> GetComentarios()
        {
            return await _repo.GetAllEntities<TablaComentarios, ComentariosDTO>();
        }

        [HttpPost("Comentarios")]
        public async Task<ActionResult<ComentariosDTO>> PostComentarios(ComentariosDTO c)
        {
            return await _repo.AddEntity<TablaComentarios, ComentariosDTO>(c, nameof(GetComentarios), "Id");
        }

        [HttpPut("Comentarios/{id}")]
        public async Task<IActionResult> PutComentarios(int id, ComentariosDTO c)
        {
            return await _repo.UpdateEntity<TablaComentarios, ComentariosDTO>(id, c, "Id");
        }

        [HttpDelete("Comentarios/{id}")]
        public async Task<IActionResult> DeleteComentarios(int id)
        {
            return await _repo.DeleteEntity<TablaComentarios>(id, "Id");
        }

        [HttpGet("TablaCfg")]
        public async Task<ActionResult<List<TablaCfgDTO>>> GetTablaCfg()
        {
            return await _repo.GetAllEntities<TablaCfg, TablaCfgDTO>();
        }

        [HttpPost("TablaCfg")]
        public async Task<ActionResult<TablaCfgDTO>> PostTablaCfg(TablaCfgDTO tcfg)
        {
            return await _repo.AddEntity<TablaCfg, TablaCfgDTO>(tcfg, nameof(GetTablaCfg), "Id");
        }

        [HttpPut("TablaCfg/{id}")]
        public async Task<IActionResult> PutTablaCfg(int id, TablaCfgDTO tcfg)
        {
            return await _repo.UpdateEntity<TablaCfg, TablaCfgDTO>(id, tcfg, "Id");
        }

        [HttpDelete("TablaCfg/{id}")]
        public async Task<IActionResult> DeleteTablaCfg(int id)
        {
            return await _repo.DeleteEntity<TablaCfg>(id, "Id");
        }


        [HttpGet("Plano")]
        public async Task<ActionResult<List<PlanoDTO>>> GetPlano()
        {
            return await _repo.GetAllEntities<Plano, PlanoDTO>();
            //return await GetAllEntities<Plano, PlanoDTO>();
        }
        [HttpPost("Plano")]
        public async Task<ActionResult<PlanoDTO>> PostPlano(PlanoDTO p)
        {
            return await _repo.AddEntity<Plano, PlanoDTO>(p, nameof(GetPlano), "Idplano");
            //return await AddEntity<Plano, PlanoDTO>(p, nameof(GetPlano), "Idplano");
        }
        [HttpPut("Plano/{id}")]
        public async Task<IActionResult> PutPlano(int id, PlanoDTO p)
        {
            return await _repo.UpdateEntity<Plano, PlanoDTO>(id, p, "Idplano");
            //return await UpdateEntity<Plano, PlanoDTO>(id, p, "Idplano");
        }
        [HttpDelete("Plano/{id}")]
        public async Task<IActionResult> DeletePlano(int id)
        {
            return await _repo.DeleteEntity<Plano>(id, "Idplano");
            //return await DeleteEntity<Plano>(id, "Idplano");
        }


        [HttpGet("TblLimpiezas")]
        public async Task<ActionResult<List<TblLimpiezasDTO>>> GetTblLimpiezas()
        {
            return await _repo.GetAllEntities<TblLimpiezas, TblLimpiezasDTO>();
        }
        [HttpPost("TblLimpiezas")]
        public async Task<ActionResult<TblLimpiezasDTO>> PostTblLimpiezas(TblLimpiezasDTO l)
        {
            return await _repo.AddEntity<TblLimpiezas, TblLimpiezasDTO>(l, nameof(GetTblLimpiezas), "Idlavada");
        }
        [HttpPut("TblLimpiezas/{id}")]
        public async Task<IActionResult> PutTblLimpiezas(int id, TblLimpiezasDTO l)
        {
            return await _repo.UpdateEntity<TblLimpiezas, TblLimpiezasDTO>(id, l, "Idlavada");
        }
        [HttpDelete("TblLimpiezas/{id}")]
        public async Task<IActionResult> DeleteTblLimpiezas(int id)
        {
            return await _repo.DeleteEntity<TblLimpiezas>(id, "Idlavada");
        }


        [HttpGet("Maquina")]
        public async Task<ActionResult<List<MaquinaDTO>>> GetMaquinas()
        {
            return await _repo.GetAllEntities<Maquinas, MaquinaDTO>();
        }
        [HttpPost("Maquina")]
        public async Task<ActionResult<MaquinaDTO>> PostMaquina(MaquinaDTO m)
        {
            return await _repo.AddEntity<Maquinas, MaquinaDTO>(m, nameof(GetMaquinas), "Idmaquina");
        }
        [HttpPut("Maquina/{id}")]
        public async Task<IActionResult> PutMaquina(int id, MaquinaDTO m)
        {
            return await _repo.UpdateEntity<Maquinas, MaquinaDTO>(id, m, "Idmaquina");
        }
        [HttpDelete("Maquina/{id}")]
        public async Task<IActionResult> DeleteMaquina(int id)
        {
            return await _repo.DeleteEntity<Maquinas>(id, "Idmaquina");
        }
        


        [HttpGet("Cliente")]
        public async Task<ActionResult<List<ClienteDTO>>> GetClientes()
        {
            return await _repo.GetAllEntities<Clientes, ClienteDTO>();
        }
        [HttpPost("Cliente")]
        public async Task<ActionResult<ClienteDTO>> PostCliente(ClienteDTO c)
        {
            return await _repo.AddEntity<Clientes, ClienteDTO>(c, nameof(GetClientes), "Id");
        }
        [HttpPut("Cliente/{id}")]
        public async Task<IActionResult> PutCliente(int id, ClienteDTO c)
        {
            return await _repo.UpdateEntity<Clientes, ClienteDTO>(id, c, "Id");
        }
        [HttpDelete("Cliente/{id}")]
        public async Task<IActionResult> DeleteCliente(int id)
        {
            return await _repo.DeleteEntity<Clientes>(id, "Id");
        }



        [HttpGet("Producto")]
        public async Task<ActionResult<List<ProductoDTO>>> GetProductos()
        {
            return await _repo.GetAllEntities<TablaProductos, ProductoDTO>();
        }
        [HttpPost("Producto")]
        public async Task<ActionResult<ProductoDTO>> PostProducto(ProductoDTO c)
        {
            return await _repo.AddEntity<TablaProductos, ProductoDTO>(c, nameof(GetProductos), "Idproducto");
        }
        [HttpPut("Producto/{id}")]
        public async Task<IActionResult> PutProducto(int id, ProductoDTO c)
        {
            return await _repo.UpdateEntity<TablaProductos, ProductoDTO>(id, c, "Idproducto");
        }
        [HttpDelete("Producto/{id}")]
        public async Task<IActionResult> DeleteProducto(int id)
        {
            return await _repo.DeleteEntity<TablaProductos>(id, "Idproducto");
        }
    }
}
