﻿using System.Security.Claims;
using System.Text.RegularExpressions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.VisualBasic;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.DatoLita01;
using ProgramadorGeneralBLZ.Server.Models.InterfaseBD;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Services.DatabaseManipulation;
using ProgramadorGeneralBLZ.Shared;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Pedidos.Command;

public class ProgramarPedidosLitografiaCommand : IRequest<SingleResult<int>>
{
    public ProgramarPedidosLitografiaCommand(ProgramarPedidoDTO datosLitografia, ClaimsPrincipal user)
    {
        DatosLitografia = datosLitografia;
        User = user;
    }
    public ProgramarPedidoDTO DatosLitografia { get; set; }
    public ClaimsPrincipal User { get; set; }
}

public class ProgramarPedidosLitografiaCommandHandler : IRequestHandler<ProgramarPedidosLitografiaCommand, SingleResult<int>>
{
    private readonly ProgramadorLitalsaContext _programadorLitalsaContext;
    private readonly IDataManipulationService _dataManipulationService;
    public ProgramarPedidosLitografiaCommandHandler(ProgramadorLitalsaContext programadorLitalsaContext, IDataManipulationService dataManipulationService)
    {
        _programadorLitalsaContext = programadorLitalsaContext;
        _dataManipulationService = dataManipulationService;
    }

    public async Task<SingleResult<int>> Handle(ProgramarPedidosLitografiaCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<int> { Errors = new List<string>(), Info = new List<string>(), Data = 0 };
        var pedidoEnCurso = 0;
        try
        {
            var listadoIdPedidos = request.DatosLitografia.PedidosLito.Select(s => s.IdPedido);

            var lastPosicion = _programadorLitalsaContext.TablaProgramacion
                .Where(o => o.Idlinea == request.DatosLitografia.Maquina.Idmaquina)
                .Max(o => o.Posicion);
            foreach (var pedido in request.DatosLitografia.PedidosLito)
            {
                var codSimultaneo = 0;
                var codPosterior = 0;
                var codAnterior = 0;
                var ordenProcesos =
                    await _dataManipulationService.DevuelveOrdenProcesos(pedido.IdPedido.Value, pedido.Idcodigoaplicacion.Value, 0);

                pedidoEnCurso = pedido.IdPedido.Value;

                codAnterior = ordenProcesos.CodApliAnterior;
                var tp = new TablaProgramacion();

                if (request.DatosLitografia.Maquina.TipoMaquina == Enums.TipoMaquina.Impresora.ToString())
                {
                    tp.ImpresoraComoBarnizadora = false;
                }

                tp.Idpedido = pedido.IdPedido;
                tp.Idaplicacion = pedido.Idcodigoaplicacion;
                tp.Idlinea = request.DatosLitografia.Maquina.Idmaquina;
                tp.Posicion = (int)(Math.Ceiling((double)lastPosicion / 10) * 10) + 10;


                if (request.DatosLitografia.WetOnWet)
                {
                    codSimultaneo = ordenProcesos.CodApliPosterior;

                    // Inicio 12/06/25. Javi
                    // Carlos me comenta que si es reproceso con wetonwet y no tiene programada la siguiente fase, salga un mensaje informando del error
                    if (codSimultaneo == -1)
                    {
                        var esReproceso = new[] { "3", "6" }.Contains(pedidoEnCurso.ToString()[2].ToString());
                        if (esReproceso)
                        {
                            result.Errors.Add($"Has marcado el pedido {pedidoEnCurso}, que es un reproceso, a programar con Wet on Wet y sin la fase siguiente.");
                            return result;
                        }
                    }
                    // Fin 12/06/25
                    var ordenProcesos2 =
                        await _dataManipulationService.DevuelveOrdenProcesos(pedido.IdPedido.Value, codSimultaneo, 0);
                    codPosterior = ordenProcesos2.CodApliPosterior;
                    tp.AplicacionSimultanea = codSimultaneo;
                    var regTabla = _programadorLitalsaContext.CodApliAll.FirstOrDefault(o => o.Codbaz == codSimultaneo);
                    tp.Idproducto = regTabla.Codbarniz;
                    tp.Peso = regTabla.Grmbaz == 4.5 ? 5 : (float?)regTabla.Grmbaz;
                    tp.PesoMin = regTabla.Grmbazmin == 4 ? (float?)4.5 : (float?)regTabla.Grmbazmin;
                    tp.Producto =
                        $"{_programadorLitalsaContext.TablaProductos.FirstOrDefault(o => o.Idproducto == tp.Idproducto).Denominacion} Peso: {tp.PesoMin} - {tp.Peso} g/m2";
                    tp.TemperaturaSecado = await _dataManipulationService.GetTemperaturaSecado_LITO(tp.Idproducto, tp.Idlinea.Value, cancellationToken);
                    tp.VelocidadMaxima = (int?)await _dataManipulationService.GetVelocidadMaxima(tp.Idproducto, tp.Idlinea.Value, cancellationToken);

                }
                else
                {
                    codSimultaneo = 0;
                    codPosterior = ordenProcesos.CodApliPosterior;
                }

                //obs

                var cadena = string.Empty;
                //CREO QUE YA NO SE NECESITA
                //'8/04/2020 se incluyen la realización de los trípticos
                //    Dim hacertriptico As Boolean
                //    Dim ObsTriptico As String

                //    hacertriptico = Form_Frm_programacion_pedido_LITO!Tríptico
                //    ObsTriptico = Nz(Form_Frm_programacion_pedido_LITO!ObsTriptico, "")

                //If hacertriptico Then cadena = cadena & "HACER TRIPTICO. " & ObsTriptico
                //'***********+FIN TRIPTICOS

                if (pedido.Obs1.Contains("NUEVO DISEÑO", StringComparison.OrdinalIgnoreCase))
                {
                    cadena += $"NUEVO DISEÑO {Environment.NewLine}";
                }

                //30/01/2021 EN LOS CASOS QUE SEAN LITOGRAFÍAS DISTINTAS DE 999 LO INDICAMOS EN LAS OBSERVACIONES.
                if (tp.Idaplicacion.Value < 999)
                {
                    cadena += $"{_dataManipulationService.DevuelveDatosAplicacion(tp.Idaplicacion.Value, "NombreAplicacion")}. ";
                }

                var esultimoproceso = ordenProcesos.FasePosterior <= 0;
                if (request.DatosLitografia.WetOnWet)
                {
                    ////VERSION V2 - SIMPLIFICADA.
                    ////Cuando es WoW quiere decir que hay dos fases que se dan a la vez, por lo que si aplicamos 20 en WoW
                    ////quiere decir que a la vez se da la siguiente, 30, por lo que la verdadera siguiente es la 40 

                    //var datosFases = await _programadorLitalsaContext.TablaCodigosPedido
                    //    .Where(o => o.Idpedido == pedido.IdPedido).OrderBy(o => o.Fase).ToListAsync(cancellationToken);

                    //var faseActual = datosFases.FirstOrDefault(o => o.Posicion == pedido.Posicion);
                    //var faseWoWSimultanea = datosFases.FirstOrDefault(o => o.Fase > faseActual.Fase);
                    //var faseSiguienteAWoW = datosFases.FirstOrDefault(o => o.Fase > faseWoWSimultanea.Fase);
                    ////Si la fase siguiente a la fase WoW (la simultanea a la fase actual) es null, quiere decir que es la ultima fase.
                    //if (faseSiguienteAWoW==null)
                    //{
                    //    esultimoproceso = true;
                    //}

                    //if (esultimoproceso)
                    //{
                    //    cadena += $"{_dataManipulationService.DevuelveObservacionesFlejado(tp.Idpedido.Value)} {pedido.Obsflejado}. ";
                    //    tp.Flejar = true;
                    //}
                    //else
                    //{
                    //    var siguienteProcesoTexto = _dataManipulationService.DevuelveDatosAplicacion(faseSiguienteAWoW.Idcodigoaplicacion.Value, "NombreApliProgramacion");
                    //    cadena += $" NO FLEJAR. ";
                    //    //**08/03/22, AÑADIR SIGUIENTE PROCESO.
                    //    var txtApliPosterior = "LUEGO " + siguienteProcesoTexto;
                    //    var caraPosterior = ordenProcesos.CaraPosterior;

                    //    txtApliPosterior += caraPosterior == "ep"
                    //        ? " POR CARA EXTERIOR"
                    //        : " POR CARA " + (caraPosterior[0] == 'i' ? "INTERIOR" : "EXTERIOR");

                    //    cadena += $"{txtApliPosterior}. ";
                    //}
                    //cadena += _dataManipulationService.AñadeObservacionesCodigosYBarnices(
                    //    Strings.Left(tp.Idaplicacion.ToString(), 4), "0", pedido.IdCliente.ToString().Trim());
                    //cadena += _dataManipulationService.AñadeObservacionesCodigosYBarnices(
                    //    Strings.Left(faseSiguienteAWoW.Idcodigoaplicacion.Value.ToString(), 4),
                    //    _dataManipulationService.DevuelveDatosAplicacion(tp.Idaplicacion.Value, "codigobarnizasociado"), "0");


                    DatosFasesDTO ordenProcesosParaSiguienteProceso = null;
                    var sigProceso = 0;
                    if (ordenProcesos.CodApliPosterior != 0 && ordenProcesos.CodApliPosterior != -1)
                    {
                        var posiciones = new List<string> { "e1", "e2", "e3" };
                        foreach (var pos in posiciones)
                        {
                            var item = _programadorLitalsaContext.TablaCodigosPedido
                                .FirstOrDefault(o => o.Idpedido == tp.Idpedido.Value && o.Posicion == pos);

                            if (item?.Idcodigoaplicacion.ToString().StartsWith(ordenProcesos.CodApliPosterior.ToString()) ?? false)
                            {
                                sigProceso = item.Idcodigoaplicacion ?? 0;
                            }
                        }
                        ordenProcesosParaSiguienteProceso = await _dataManipulationService.DevuelveOrdenProcesos(pedido.IdPedido.Value, sigProceso, 0);
                        esultimoproceso = ordenProcesosParaSiguienteProceso.FasePosterior <= 0;
                    }
                    else
                    {
                        cadena += $"****** COMPROBAR APLICACION DE ACABADO Y NOMBRE *****. {Environment.NewLine}";
                    }

                    //Creo que solo afecta a BARNIZADO
                    //DESDE AQUI --------
                    if (esultimoproceso)
                    {
                        cadena += $"{_dataManipulationService.DevuelveObservacionesFlejado(tp.Idpedido.Value)} {pedido.Obsflejado}. ";
                        tp.Flejar = true;
                    }
                    else
                    {
                        var siguienteProcesoTexto = _dataManipulationService.DevuelveDatosAplicacion(ordenProcesosParaSiguienteProceso.CodApliPosterior, "NombreApliProgramacion");
                        cadena += $" NO FLEJAR. ";
                        //**08/03/22, AÑADIR SIGUIENTE PROCESO.
                        var txtApliPosterior = "LUEGO " + siguienteProcesoTexto;
                        var caraPosterior = ordenProcesosParaSiguienteProceso.CaraPosterior;

                        txtApliPosterior += caraPosterior == "ep"
                            ? " POR CARA EXTERIOR"
                            : " POR CARA " + (caraPosterior[0] == 'i' ? "INTERIOR" : "EXTERIOR");

                        cadena += $"{txtApliPosterior}. ";
                    }
                    //Hay un caso extra en el ACCESS para esto pero en teoría no cae por ahi nunca, en la BD TablaProgramacion, no hay ningún registro
                    //con ese texto en ninguna de las columnas de observaciones
                    //Case 2
                    //cadena = cadena & "****** COMPROBAR FLEJADO ******"
                    //'Form_Frm_programacion_programacion_LITO.Revisar = -1

                    //09/02/21, se incluye búsqueda en la cadena de codigo_aplicacion de Lito y del cliente
                    cadena += _dataManipulationService.AñadeObservacionesCodigosYBarnices(
                        Strings.Left(tp.Idaplicacion.ToString(), 4), "0", pedido.IdCliente.ToString().Trim());
                    cadena += _dataManipulationService.AñadeObservacionesCodigosYBarnices(
                        Strings.Left(sigProceso.ToString(), 4),
                        _dataManipulationService.DevuelveDatosAplicacion(tp.Idaplicacion.Value, "codigobarnizasociado"), "0");
                    // HASTA AQUI----------
                }
                else
                {
                    if (esultimoproceso)
                    {
                        cadena += $"{_dataManipulationService.DevuelveObservacionesFlejado(tp.Idpedido.Value)} {pedido.Obsflejado}. ";
                        tp.Flejar = true;
                    }
                    else
                    {
                        var siguienteProcesoTexto =
                            _dataManipulationService.DevuelveDatosAplicacion(ordenProcesos.CodApliPosterior,
                                "NombreApliProgramacion");

                        var caraPosterior = ordenProcesos.CaraPosterior;
                        if (!string.IsNullOrEmpty(caraPosterior))
                        {
                            var txtApliPosterior = "LUEGO " + siguienteProcesoTexto;
                            txtApliPosterior += caraPosterior.ToLower() == "ep"
                                ? " POR CARA EXTERIOR"
                                : " POR CARA " + (caraPosterior[0] == 'i' ? "INTERIOR" : "EXTERIOR");
                            cadena += $"{txtApliPosterior}. ";
                        }
                    }

                }

                //27/08/2019 CLIENTES PARA LOS QUE NO SE PUEDEN JUNTAR PAQUETES.
                //***************************************

                if (pedido.IdCliente == 6)
                {
                    cadena = cadena.TrimEnd('\r', '\n') + $"{Environment.NewLine}NO JUNTAR PAQUETES.";
                }

                if (pedido.IdCliente == 74 && pedido.Plano.Equals("CA220x4-T225L1-04", StringComparison.InvariantCultureIgnoreCase))
                {
                    cadena += $"{Environment.NewLine}RESPETAR ARRANQUE EN PINZA Y ESCUADRA";
                }

                //19/04/2024 Añadido para Litografía
                var listaPlanos1 = new List<string> { "TRR125L8-02", "TRR125L8-03", "TRR125L8-06", "TRR125L8-07" };
                var listaPlanos2 = new List<string> { "TRR125L8-04", "TRR125L8-05" };
                var listaPlanos3 = new List<string> { "TRR125L8-08", "TRR125L8-09" };
                if (pedido.IdCliente == 6)
                {
                    if (listaPlanos1.Contains(pedido.Plano.ToUpperInvariant()))
                    {
                        cadena += $"{Environment.NewLine}TIRAR DE PUNTA & TACOS DEL PALLET DE PUNTA (PARALELOS AL 968)";
                        if (pedido.Idcodigoaplicacion.ToString().StartsWith("620"))
                        {
                            cadena += $"{Environment.NewLine}IMPORTANTE!! VIGILAR TRANSVERSAL EN PINZA (QUE NO ENTRE EN EL TRABAJO)";
                        }
                    }
                    else if (listaPlanos2.Contains(pedido.Plano.ToUpperInvariant()))
                    {
                        cadena += $"{Environment.NewLine}TIRAR DE PUNTA & TACOS DEL PALLET DE PUNTA (PARALELOS AL 972)";
                        if (pedido.Idcodigoaplicacion.ToString().StartsWith("620"))
                        {
                            cadena += $"{Environment.NewLine}IMPORTANTE!! VIGILAR TRANSVERSAL EN PINZA (QUE NO ENTRE EN EL TRABAJO)";
                        }
                    }
                    else if (listaPlanos3.Contains(pedido.Plano.ToUpperInvariant()))
                    {
                        cadena += $"{Environment.NewLine}TIRAR DE PUNTA & TACOS DEL PALLET DE PUNTA (PARALELOS AL 970)";
                    }
                }


                //12/07/2024 Se unifica para Boue, Suiza y Alemania (12, 14, 18)
                if (pedido.IdCliente is 12 or 14 or 18)
                {
                    if (pedido.Idcodigoaplicacion.ToString().StartsWith("6201"))
                    {
                        cadena += $"{Environment.NewLine}RESERVAS LONGITUDINALES NO PUEDEN SER MÁS ESTRECHAS DE 5,4mm.";
                    }
                    else
                    {
                        cadena += $"{Environment.NewLine}RESERVAS LONGITUDINALES NO PUEDEN SER MÁS ESTRECHAS DE 5mm.";
                    }
                }


                if (pedido.Plano.Contains("216L1-01", StringComparison.OrdinalIgnoreCase) && pedido.IdCliente == 3)
                {
                    cadena += $"{Environment.NewLine}PALLETS DE PUNTA, LOS TACOS VAN PARALELOS AL 800.";
                }

                if (pedido.Plano.Contains("TAPON29-02", StringComparison.OrdinalIgnoreCase) && pedido.IdCliente == 100)
                {
                    cadena += $"{Environment.NewLine}¡IMPORTANTE! RESPETAR AJUSTE EN PINZA Y ESCUADRA";
                }
                if (pedido.Plano.Contains("TAPON29-04", StringComparison.OrdinalIgnoreCase) && pedido.IdCliente == 100)
                {
                    cadena += $"{Environment.NewLine}TIRAR DE PUNTA";
                }

                // 22/02/2016 SE INTRODUCE UNA OBSERVACIÓN EN LOS PEDIDOS DE LECHES DE MIVISA PARA QUE SE COMPLETEN LAS HOJAS HASTA UN MÍNIMO DEL 95% RESPECTO DE LAS HOJAS MARCADAS EN EL PEDIDO
                // HAY QUE COGER DE PEDIDOS DE BARNIZADO CON HOJALATA PREPARADA.

                if (pedido.IdCliente == 84)
                {
                    string pedidoCogerDe = string.Empty;

                    // ****************GESTION HOJAS ABBOTT****************************************
                    // 07/04/17: COGER DE ABBOTT.
                    // 25/03/2022: SE INCLUYE LAS HOJAS MAX Y MIN PARA PEDIDOS DE ABBOTT.
                    // 13/03/2025: CUIDADO!! Revisar siempre los planos en las funciones DevuelveHojasMin y DevuelveHojasMax

                    if (pedido.Plano.Contains("100L3-11") || pedido.Plano.Contains("100L3-12")
                        || pedido.Plano.Contains("127L2-11") || pedido.Plano.Contains("127L2-20")
                        || pedido.Plano.Contains("100L3-14") || pedido.Plano.Contains("100L3-15")
                        || pedido.Plano.Contains("127L2-20") || pedido.Plano.Contains("127L2-21")
                        || pedido.Plano.Contains("100L3-14;15") || pedido.Plano.Contains("127L2-20;21")
                        )
                    {
                        var procesosOrigen = _dataManipulationService.GetDatosTipoHojalata(pedido.IdPedido.Value, "Procesos").Text;
                        if ((!string.IsNullOrEmpty(pedido.Pe1ped.ToString()) && pedido.Pe1ped.ToString().Substring(0, 4) == "5201" ||
                             procesosOrigen.Contains("5201"))
                            ||
                            (!string.IsNullOrEmpty(pedido.Pe1ped.ToString()) && pedido.Pe1ped.ToString().Substring(0, 4) == "6201" ||
                             procesosOrigen.Contains("6201")))
                        {
                            var devuelveCaracteristicasHojalata = _dataManipulationService.GetDatosPedido(pedido.IdPedido.Value, "DevuelveCaracteristicasHojalata").Text;
                            devuelveCaracteristicasHojalata = devuelveCaracteristicasHojalata.Replace("  ", " ");
                            var nombreAplicacion = codAnterior != -1
                            ? _dataManipulationService.DevuelveDatosAplicacion(codAnterior, "NombreApliProgramacion")
                            : procesosOrigen.Contains("5201")
                                ? "enganche"
                                : "esmalte";
                            var esEsmalte = nombreAplicacion.ToLower().Contains("esmalt");
                            var esEnganche = nombreAplicacion.ToLower().Contains("engan");
                            if (devuelveCaracteristicasHojalata.ToUpperInvariant().Contains("23x950x833 2.8/2.8 HJ".ToUpperInvariant()))
                            {
                                pedidoCogerDe = esEsmalte
                                ? _programadorLitalsaContext.TablaCfg.FirstOrDefault(o => o.Iddato == "PedidosEsmalteAbbott100").Dato
                                : esEnganche
                                    ? _programadorLitalsaContext.TablaCfg.FirstOrDefault(o => o.Iddato == "PedidosEngancheAbbott100").Dato
                                    : "N/A";
                            }
                            if (devuelveCaracteristicasHojalata.ToUpperInvariant().Contains("23x839x803 2.8/2.8 HJ".ToUpperInvariant()))
                            {
                                pedidoCogerDe = esEsmalte
                                    ? _programadorLitalsaContext.TablaCfg.FirstOrDefault(o => o.Iddato == "PedidosEsmalteAbbott127").Dato
                                    : esEnganche
                                        ? _programadorLitalsaContext.TablaCfg.FirstOrDefault(o => o.Iddato == "PedidosEngancheAbbott127").Dato
                                        : "N/A";
                            }
                            if (pedido.Formato.ToString() == "100,1")
                            {
                                pedidoCogerDe = esEsmalte
                                    ? _programadorLitalsaContext.TablaCfg.FirstOrDefault(o => o.Iddato == "PedidosEsmalteAbbott100,1")?.Dato ?? string.Empty
                                    : esEnganche
                                        ? _programadorLitalsaContext.TablaCfg.FirstOrDefault(o => o.Iddato == "PedidosEngancheAbbott100,1")?.Dato ?? string.Empty
                                        : "N/A";
                            }
                        }

                        var yaProgramado = await _programadorLitalsaContext.TablaProgramacion
                            .AnyAsync(o => o.Idaplicacion == pedido.Idcodigoaplicacion &&
                                           o.Idpedido == pedido.IdPedido, cancellationToken);
                        var numColores = _dataManipulationService.GetDatosPedido(pedido.IdPedido.Value, "num_tintas").Num.Value;
                        var numColoresMaquina = (int)request.DatosLitografia.Maquina.NumeroCuerpos;

                        var irAMaximo = !yaProgramado && (numColores / numColoresMaquina) > 1;

                        var hojasMin = _dataManipulationService.DevuelveHojasMin(pedido.IdPedido.Value);
                        if (hojasMin == 0)
                            result.Info.Add($"Pedido: {tp.Idpedido} - Hojas mínimas a entregar no definidas correctamente, revisar en pedido.");

                        var hojasMax = _dataManipulationService.DevuelveHojasMax(pedido.IdPedido.Value);

                        // Número de hojas a entregar para Abbott
                        if (hojasMin != 0)
                        {
                            if (irAMaximo)
                            {
                                // QUIERE DECIR QUE ESTAMOS EN LA PRIMERA PASADA DE UN PEDIDO DE DOBLE PASADA
                                // DEFINIMOS 40 HOJAS COMO LAS MERMAS MÍNIMAS EN LA 2ª PASADA DE LITO
                                cadena += " IMPRIMIR AL MENOS " + Math.Max(hojasMax, hojasMin + 40) + " HOJAS.";

                                if (!string.IsNullOrEmpty(pedidoCogerDe))
                                {
                                    cadena += " SI FUERA NECESARIO, COGER HOJALATA DE CONSIGNA CON PROCEDENCIA: " + pedidoCogerDe + ".";
                                }
                                else
                                {
                                    result.Info.Add($"Pedido: {tp.Idpedido} - NO HAY STOCK DE BASES PREPARADAS PARA COMPLETAR LOS PEDIDOS DE LECHES.");
                                }
                            }
                            else
                            {
                                //31/10/2024 Nuevo cálculo de hojas min y max para abbot
                                long nuevoMinimo = 0;
                                nuevoMinimo = Convert.ToInt64(_dataManipulationService.DevuelveString(_dataManipulationService.GetDatosPedido(tp.Idpedido.Value, "Observaciones_Pedido").Text, "MINIMO DE", "HOJAS"));
                                if (nuevoMinimo == 0)
                                    nuevoMinimo = Convert.ToInt64(_dataManipulationService.DevuelveString(_dataManipulationService.GetDatosPedido(tp.Idpedido.Value, "Observaciones_Pedido").Text, "MINIMO DE", "HJS"));

                                double nuevoMaximo = 0;
                                if (nuevoMinimo > 0)
                                    nuevoMaximo = 1.035 * nuevoMinimo;

                                cadena += " AJUSTAR EL NÚMERO DE HOJAS A ENVIAR ENTRE MÍNIMO DE " + Math.Round((decimal)nuevoMinimo, 0) + " Y UN MÁXIMO DE " + Math.Round((decimal)nuevoMaximo, 0) + " HOJAS.";
                                if (!string.IsNullOrEmpty(pedidoCogerDe))
                                {
                                    cadena += " SINO SE LLEGA AL MÍNIMO COGER HOJALATA DE CONSIGNA CON PROCEDENCIA: " + pedidoCogerDe.Replace("\n", "") + ".";
                                }
                                else
                                {
                                    result.Info.Add($"Pedido: {tp.Idpedido} - NO HAY STOCK DE BASES PREPARADAS PARA COMPLETAR LOS PEDIDOS DE LECHES.");
                                }
                            }
                        }
                    }
                    //28/02/2025 - Comentado por Carlos. Sustituido con otro comentario
                    if ((pedido.Obs1.ToUpper().Contains("SEVILLA") || pedido.Obs1.ToUpper().Contains("OSUNA") )&&
                        (pedido.Motivos.Contains("ABBOTT") || pedido.Formato == 127 || pedido.Formato == 126))
                    {
                        cadena += $"{Environment.NewLine}REVISAR REGISTROS DE VISIÓN ARTIFICIAL EN PREPRINT";
                    }
                    //if (pedido.Obs1.ToUpper().Contains("SEVILLA") && esultimoproceso &&
                    //    (pedido.Motivos.Contains("ABBOTT") || pedido.Formato == 127 || pedido.Formato == 126))
                    //{
                    //    cadena += $"{Environment.NewLine}AL TERMINAR EL PEDIDO DEJAR PARA SELECCIONAR AMBAS CARAS";
                    //}
                }

                // **********************SI YA SE HA TIRADO ALGUNA HOJA DE ESE PROCESO LO INDICAMOS*************
                // **********************SI ES TINTA MATE LO IGNORAMOS*************
                if (pedido.Idcodigoaplicacion != (int)Enums.TipoTintas.TintaMate)
                {
                    var datosPedido = _programadorLitalsaContext.PedidoProcesado.FirstOrDefault(o => o.IdPedido == pedido.IdPedido);
                    var listaTintas = new List<string>();
                    for (var i = 1; i <= 8; i++)
                    {
                        // Usamos TryParse en lugar de Parse para manejar posibles errores.
                        if (!int.TryParse(_dataManipulationService.GetPropertyValueByName(datosPedido, $"C0{i}ped"), out int codTintas))
                            continue;

                        if (codTintas == 0)
                            continue;

                        if (!int.TryParse(_dataManipulationService.GetPropertyValueByName(datosPedido, $"Hojasco{i}ped"), out int hojasAux))
                            continue;

                        if (hojasAux > 0)
                        {
                            listaTintas.Add(codTintas.ToString());
                        }
                    }
                    var hojasMinimasPorTinta = _programadorLitalsaContext.HojasTrabajoG21
                        .Where(o => o.Orden == pedido.IdPedido && listaTintas.Contains(o.Dato) && o.Operacion == "10")
                        .GroupBy(o => o.Dato)
                        .Select(g => new
                        {
                            Tinta = g.Key,
                            HojasMinimas = g.Sum(o => o.Cantidad)
                        })
                        .Min(o => o.HojasMinimas) ?? 0;

                    if (hojasMinimasPorTinta != 0)
                        cadena = "YA TIRADAS #" + Strings.Format(hojasMinimasPorTinta, "0,0") + "# HOJAS. " + cadena;
                }
                // ********************************************************************************
                //08/01/2025 - Indicado por Carlos
                if (pedido.IdCliente == 26 && pedido.Plano == "0286L1-02")
                {
                    cadena += $"{Environment.NewLine}DE PUNTA.";
                }

                var formatoEspecial = _dataManipulationService.GetDatosPedido(pedido.IdPedido.Value, "DevuelveCaracteristicasHojalata").Text;
                if (pedido.IdCliente.Value == 109 && formatoEspecial.ToUpperInvariant().Contains("786 x 738 x 22 HJ".ToUpperInvariant()))
                {
                    cadena += "IMPRIMIR DE PUNTA.";
                }
                //17/10/16: SEGÚN 2016-RC-229, hubo manchados de tinta por la cara interior de la scroll de 990*975

                if (pedido.IdCliente.Value == 4 && pedido.Plano.ToUpperInvariant().Contains("RR125B4-".ToUpperInvariant()) &&
                    request.DatosLitografia.Maquina.Idmaquina == 7)
                    cadena += "DESCONECTAR LOS AIRES DE DENTRO DE LA MÁQUINA PARA EVITAR MANCHADOS DE TINTA POR LA CARA INTERIOR.";

                var cadenaPlanchas = string.Empty;
                string[] observations = { pedido.Obs1, pedido.Obs2, pedido.Obs3 };

                foreach (var obs in observations)
                {
                    if (obs.ToUpperInvariant().Contains("MISMAS"))
                    {
                        cadenaPlanchas = obs + ".";
                        cadenaPlanchas = cadenaPlanchas.Substring(cadenaPlanchas.ToUpperInvariant().IndexOf("MISMAS", StringComparison.OrdinalIgnoreCase)).ToUpper() + " ";
                        break;
                    }
                }
                cadena += cadenaPlanchas;

                tp.Observaciones = !string.IsNullOrEmpty(pedido.Estado) && pedido.Estado.Contains("PDTE HJLTA")
                    ? $"PEDIDO SIN HOJALATA ASIGNADA{Environment.NewLine}{cadena}"
                    : cadena;

                //flejado
                tp.Flejar = esultimoproceso;
                if (pedido.SINCARPETA.Value)
                {
                    tp.Observaciones += " CARPETA";
                }

                tp.Idaplicacionposterior = ordenProcesos.CodApliPosterior;
                tp.Posicionaplicacionposterior = ordenProcesos.CaraPosterior;
                tp.Idaplicacionanterior = ordenProcesos.CodApliAnterior;
                tp.Posicionaplicacionanterior = ordenProcesos.CaraAnterior;
                tp.CaraAaplicar = pedido.Posicion;// REVISAR!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
                tp.TipoLavada = null;
                tp.HojasAprocesar = pedido.HojasPedido;
                tp.HoraComienzoEstimada = null;
                tp.HoraFinEstimada = null;
                tp.DuracionEstimada = null;

                tp.Orden = 666;//Es un campo para el access para hacer que el formulario impreso saliese en orden

                if (tp.Idaplicacionposterior == -1)
                {
                    tp.Obspaseposterior = $"{_dataManipulationService.DevuelveObservacionesFlejado(tp.Idpedido.Value)}{Environment.NewLine}{pedido.Obsflejado}";
                }

                var cadenaCalidad = "";
                if (request.DatosLitografia.Maquina.WetonWet)
                    cadenaCalidad +=
                        await _dataManipulationService.GetObservacionesCalidadLite(tp.Idpedido.Value, codSimultaneo);

                cadenaCalidad +=
                    await _dataManipulationService.GetObservacionesCalidadLite(tp.Idpedido.Value,
                        tp.Idaplicacion.Value);
                if (!string.IsNullOrEmpty(cadenaCalidad))
                {
                    tp.ObsCalidad = cadenaCalidad.Length > 255 ? cadenaCalidad.Substring(0, 255) : cadenaCalidad;
                }

                _programadorLitalsaContext.TablaProgramacion.Add(tp);
                _programadorLitalsaContext.TablaCodigosPedido
                    .Where(p => p.Idpedido == tp.Idpedido && p.Idcodigoaplicacion == tp.Idaplicacion)
                    .ToList()
                    .ForEach(p => p.EstadoTintas = "Programado");

                lastPosicion += 10;
                result.Data++;
            }
            //Nos aseguramos que siempre, tras añadir un pedido, se muestre la máquina hasta la última posición, simulando que HASTA = 999999999
            var maquina =
                await _programadorLitalsaContext.Maquinas.SingleAsync(o =>
                    o.Idmaquina == request.DatosLitografia.Maquina.Idmaquina, cancellationToken);
            maquina.PosicionHasta = 9999999;
            await _programadorLitalsaContext.SaveChangesAsync(cancellationToken);
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: Pedido: {pedidoEnCurso} - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
            return result;
        }
        return result;
    }
}