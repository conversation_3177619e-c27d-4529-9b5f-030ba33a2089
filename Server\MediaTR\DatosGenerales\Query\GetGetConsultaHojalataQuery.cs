﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;
using static DevExpress.Xpo.Helpers.AssociatedCollectionCriteriaHelper;

namespace ProgramadorGeneralBLZ.Server.MediaTR.DatosGenerales.Query
{
    public class GetGetConsultaHojalataQuery : IRequest<ListResult<ConsultaHojalataDTO>>
    {

        public GetGetConsultaHojalataQuery(int idCliente, int largo, int ancho, int espesor)
        {
            IdCliente = idCliente;
            Largo = largo;
            Ancho = ancho;
            Espesor = espesor;
        }

        public int IdCliente;
        public int Largo;
        public int Ancho;
        public int Espesor;
    }
    public class GetGetConsultaHojalataQueryHandler : IRequestHandler<GetGetConsultaHojalataQuery, ListResult<ConsultaHojalataDTO>>
    {
        private readonly ProgramadorLitalsaContext _contextProg;
        public GetGetConsultaHojalataQueryHandler(ProgramadorLitalsaContext contextProg)
        {
            _contextProg = contextProg;
        }
        public async Task<ListResult<ConsultaHojalataDTO>> Handle(GetGetConsultaHojalataQuery request, CancellationToken cancellationToken)
        {
            var result = new ListResult<ConsultaHojalataDTO>()
            {
                Data = new List<ConsultaHojalataDTO>(),
                Errors = new List<string>()
            };
            try
            {

                var query = await _contextProg.Matcli
                    .Where(mc => mc.Climtc == request.IdCliente  && mc.Larmtc == request.Largo && mc.Ancmtc == request.Ancho && mc.Espmtc == request.Espesor)
                    .GroupBy(mc => new { mc.Refmtc, mc.Empmtc, mc.Climtc, mc.Tmamtc, mc.Larmtc, mc.Ancmtc, mc.Espmtc, mc.Tprmtc, mc.Promtc, mc.Stimtc })
                    .Select(g => new ConsultaHojalataDTO
                    {
                        Refmtc = g.Key.Refmtc,
                        Empmtc = g.Key.Empmtc,
                        Climtc = g.Key.Climtc.Value,
                        Tmamtc = g.Key.Tmamtc,
                        Larmtc = g.Key.Larmtc,
                        Ancmtc = g.Key.Ancmtc,
                        Espmtc = g.Key.Espmtc,
                        Tprmtc = g.Key.Tprmtc,
                        Promtc = g.Key.Promtc,
                        Stimtc = g.Key.Stimtc,
                        SumaDestomtc = g.Sum(mc => mc.Stomtc),
                        Expr1 = g.Sum(mc => mc.Resmtc ?? 0),
                        Disponible = g.Sum(mc => mc.Stomtc - (mc.Resmtc ?? 0))
                    })
                    .Where(rc => rc.Disponible > 0)
                    .OrderBy(rc => rc.Climtc)
                    .ThenBy(rc => rc.Larmtc)
                    .ThenBy(rc => rc.Ancmtc)
                    .ThenBy(rc => rc.Espmtc)
                    .ThenBy(rc => rc.Tprmtc)
                    .ToListAsync(cancellationToken);

                result.Data = query;
            }
            catch (Exception e)
            {
                var errorText = $"ERROR: GetGetConsultaHojalataQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";

                result.Errors.Add(errorText);
            }
            return result;
        }
    }
}
