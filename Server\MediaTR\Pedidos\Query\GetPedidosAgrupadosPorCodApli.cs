﻿using Microsoft.Data.SqlClient;
using System.Reflection;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Nelibur.ObjectMapper;
using ProgramadorGeneralBLZ.Server.Data.DatoLita01;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Services;
using ProgramadorGeneralBLZ.Shared;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;
using ProgramadorGeneralBLZ.Server.Models.DatoLita01;
using System.Data;
using System.Data.Odbc;
using System.Data.OleDb;
using ProgramadorGeneralBLZ.Server.Controllers;
using DevExpress.CodeParser;
using ProgramadorGeneralBLZ.Server.Services.DatabaseManipulation;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Pedidos.Query
{
    public class GetPedidosAgrupadosPorCodApli : IRequest<ListResult<PedidoProcesadoLiteDTO>>
    {
        public List<PedidoProcesadoLiteDTO> Lista;

        public GetPedidosAgrupadosPorCodApli(List<PedidoProcesadoLiteDTO> lista)
        {
            Lista = lista;
        }
    }

    public class GetPedidosAgrupadosPorCodApliHandler : IRequestHandler<GetPedidosAgrupadosPorCodApli, ListResult<PedidoProcesadoLiteDTO>>
    {
        private readonly ProgramadorLitalsaContext _contextProg;
        private readonly IConfiguration _configuration;
        private readonly ILogger<GestionTablasController> _logger;
        private readonly IDataManipulationService _dataManipulationService;

        public GetPedidosAgrupadosPorCodApliHandler(ProgramadorLitalsaContext contextProg, IConfiguration configuration,
            ILogger<GestionTablasController> logger, IDataManipulationService dataManipulationService)
        {
            _contextProg = contextProg;
            _configuration = configuration;
            _logger = logger;
            _dataManipulationService = dataManipulationService;
        }

        /// <summary>
        /// Recuperamos de la BD los datos del pedido indicado.
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<ListResult<PedidoProcesadoLiteDTO>> Handle(GetPedidosAgrupadosPorCodApli request, CancellationToken cancellationToken)
        {
            var result = new ListResult<PedidoProcesadoLiteDTO>
            {
                Data = new List<PedidoProcesadoLiteDTO>(),
                Errors = new List<string>()
            };
            try
            {
                var pedidos = request.Lista;
                var listaIdPedidos = pedidos.Select(o => o.IdPedido);
                var programaciones = await _contextProg.TablaProgramacion
                    .Where(o => listaIdPedidos.Contains(o.Idpedido)).ToListAsync(cancellationToken);
                var maquinas = await _contextProg.Maquinas.ToListAsync(cancellationToken);
                foreach (var pa in pedidos.Where(o=>string.IsNullOrEmpty(o.TxtEstado)))
                {
                    pa.TxtEstado = _dataManipulationService.GetTextoEstadoCodigosAplicacion(pa.IdPedido.Value, false,
                        false, maquinas, programaciones);
                }
                result.Data = pedidos;
                return result;
            }
            catch (Exception e)
            {
                result.Errors.Add($"GetPedidosAgrupadosPorCodApliHandler: {e.Message}--{(!string.IsNullOrWhiteSpace(e.InnerException?.Message) ? e.InnerException : string.Empty)}");
                return result;
            }
        }
    }
}
