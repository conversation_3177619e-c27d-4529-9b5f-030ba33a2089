﻿namespace ProgramadorGeneralBLZ.Shared;

public class DatabaseService
{
    private readonly HttpClient _httpClient;

    public DatabaseService(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public async Task<string> BackupDatabaseAsync()
    {
        var response = await _httpClient.PostAsync("Database/backup", null);
        response.EnsureSuccessStatusCode();
        return await response.Content.ReadAsStringAsync();
    }

    public async Task<string> RestoreDatabaseAsync()
    {
        var response = await _httpClient.PostAsync("database/restore", null);
        response.EnsureSuccessStatusCode();
        return await response.Content.ReadAsStringAsync();
    }
}