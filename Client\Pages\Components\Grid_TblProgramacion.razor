﻿@using ProgramadorGeneralBLZ.Shared.ResponseModels
@using ProgramadorGeneralBLZ.Shared.DTO
@using ProgramadorGeneralBLZ.Shared

@inject IToastService toastService
@inject HttpClient http
@inject SpinnerService spinnerService

<h3>Datos Tabla Programación</h3>
<DxGrid Data="@datosProgramacion" SizeMode="SizeMode.Small" 
        Context="ContextGridProgramacion" @ref="GridProgramacion"
        PageSize="10" AllowSort="true"
        PagerPosition="GridPagerPosition.TopAndBottom"
        PageSizeSelectorVisible="true" 
        EditMode="GridEditMode.EditRow"
        EditorRenderMode="GridEditorRenderMode.Integrated"
        PageSizeSelectorItems="@(new int[] { 10, 20, 100 })"
        PageSizeSelectorAllRowsItemVisible="true"
        PagerSwitchToInputBoxButtonCount="10"
        PagerVisibleNumericButtonCount="10" sho>
    <Columns>
        <DxGridCommandColumn Width="80px" NewButtonVisible="false" EditButtonVisible="false">
            <CellDisplayTemplate>
                <a class="oi oi-x" @onclick="@(() => Delete((TablaProgramacionDTO)context.DataItem))" style="text-decoration: none; color: red;" href="javascript:void(0);"></a>
            </CellDisplayTemplate>
        </DxGridCommandColumn>
        <DxGridDataColumn FieldName="Idprogramacion" Width="90px" DisplayFormat="F0"
                          SortOrder="GridColumnSortOrder.Ascending" SortIndex="0"
                          TextAlignment="GridTextAlignment.Center"/>
        <DxGridDataColumn FieldName="Idpedido" Width="90px" DisplayFormat="F0"
                          TextAlignment="GridTextAlignment.Center"/>
        <DxGridDataColumn FieldName="Posicion" Width="90px" DisplayFormat="F0"
                          TextAlignment="GridTextAlignment.Center"/>
        <DxGridDataColumn FieldName="DatosPedido" Width="125px"/>
        <DxGridDataColumn FieldName="Idaplicacion" Context="combo" Caption="Id Aplicación">
            <CellDisplayTemplate>
                @{
                    var item = (TablaProgramacionDTO)combo.DataItem;
                }
                <CellDisplay Value="@($"{item.Idaplicacion}, {item.TextoIdAplicacion}")"/>
            </CellDisplayTemplate>
        </DxGridDataColumn>
        <DxGridDataColumn FieldName="Producto" Width="205px">
            <CellDisplayTemplate Context="cellText">
                <CellDisplay Value="@(cellText.Value?.ToString() ?? string.Empty)"/>
            </CellDisplayTemplate>
        </DxGridDataColumn>
        <DxGridDataColumn FieldName="Idproducto" Context="combo" Caption="Id Producto" Width="200px">
            <CellDisplayTemplate>
                @{
                    var item = (TablaProgramacionDTO)combo.DataItem;
                }
                <CellDisplay Value="@($"{item.Idproducto}, {item.TextoIdProducto}")"/>
            </CellDisplayTemplate>
        </DxGridDataColumn>
        <DxGridDataColumn FieldName="Idlinea" Caption="Máquina" Context="combo" Width="80px"
                          TextAlignment="GridTextAlignment.Center">
            <CellDisplayTemplate>
                @{
                    var item = (TablaProgramacionDTO)combo.DataItem;
                }
                <CellDisplay Value="@($"{item.NombreG21}")"/>
                @*<p>@($"{item.Idproducto}, {item.TextoIdProducto}")</p>*@
            </CellDisplayTemplate>
        </DxGridDataColumn>
        <DxGridDataColumn FieldName="HoraComienzoEstimada" Caption="Hora Ini Est" DisplayFormat="dd/MM HH:mm"
                          Width="80px" TextAlignment="GridTextAlignment.Center"/>
        <DxGridDataColumn FieldName="HoraFinEstimada" Caption="Hora Fin Est" DisplayFormat="dd/MM HH:mm"
                          Width="80px" TextAlignment="GridTextAlignment.Center"/>
        <DxGridDataColumn FieldName="DiaReal" Width="80px" TextAlignment="GridTextAlignment.Center"/>
        <DxGridDataColumn FieldName="HoraReal" Width="80px" TextAlignment="GridTextAlignment.Center"/>
        <DxGridDataColumn FieldName="Observaciones" >
            <CellDisplayTemplate Context="cellText">
                <CellDisplay Value="@(cellText.Value?.ToString() ?? string.Empty)"/>
            </CellDisplayTemplate>
        </DxGridDataColumn>
        <DxGridDataColumn FieldName="Flejar" Width="80px">
            <CellDisplayTemplate Context="checkbox">
                <DxCheckBox Checked="@((bool)checkbox.Value)" CssClass="noElegir"/>
            </CellDisplayTemplate>
        </DxGridDataColumn>
    </Columns>
</DxGrid>
@code {
    [Parameter]
    public int? IdPedido { get; set; }

    DxGrid? GridProgramacion;
    ListResult<TablaProgramacionDTO> resultDatosProgramacion { get; set; }
    List<TablaProgramacionDTO> datosProgramacion { get; set; }
    
    protected override async Task OnInitializedAsync()
    {
        spinnerService.Show();
        await LoadData();
        spinnerService.Hide();

    }

    async Task LoadData()
    {
        resultDatosProgramacion = await http.GetFromJsonAsync<ListResult<TablaProgramacionDTO>>($"Pedido/getprogresumenbypedido?idPedido={IdPedido}");
        if (resultDatosProgramacion.Errors.Any())
        {
            toastService.ShowInfo($"{resultDatosProgramacion.Errors.First()}");
        }
        else
        {
            datosProgramacion = resultDatosProgramacion.Data;
        }
        StateHasChanged();
    }

    async Task Delete(TablaProgramacionDTO item)
    {
        spinnerService.Show();

        var response = await http.DeleteAsync($"Programacion/borrarsolotabla/{item.Idprogramacion}");
        if (response.IsSuccessStatusCode)
            await LoadData();
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            toastService.ShowError(error);
        }
        spinnerService.Hide();
    }
}
