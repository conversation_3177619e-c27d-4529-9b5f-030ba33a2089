﻿using DevExpress.CodeParser;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Nelibur.ObjectMapper;
using ProgramadorGeneralBLZ.Server.Data.DatoLita01;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Pedidos.Query;

public class GetProgramacionByMaquinaQuery : IRequest<ListResult<TablaProgramacionDTO>>
{
    public GetProgramacionByMaquinaQuery(int idMaquina, bool ultimo)
    {
        IdMaquina = idMaquina;
        Ultimo = ultimo;
    }

    public int IdMaquina { get; set; }
    public bool Ultimo { get; set; }

    internal class
        GetProgramacionByMaquinaQueryHandler : IRequestHandler<GetProgramacionByMaquinaQuery,
            ListResult<TablaProgramacionDTO>>
    {
        private readonly ProgramadorLitalsaContext _programadorLitalsaContext;
        private readonly DatoLita01Context _datoLita01Context;

        public GetProgramacionByMaquinaQueryHandler(ProgramadorLitalsaContext programadorLitalsaContext, DatoLita01Context datoLita01Context)
        {
            _programadorLitalsaContext = programadorLitalsaContext;
            _datoLita01Context = datoLita01Context;
        }

        public async Task<ListResult<TablaProgramacionDTO>> Handle(GetProgramacionByMaquinaQuery request,
            CancellationToken cancellationToken)
        {
            var result = new ListResult<TablaProgramacionDTO>
            {
                Data = new List<TablaProgramacionDTO>(),
                Errors = new List<string>()
            };
            try
            {
                var lista = new List<TablaProgramacionDTO>();
                var maquina =
                    await _programadorLitalsaContext.Maquinas.FirstOrDefaultAsync(o =>
                        o.Idmaquina == request.IdMaquina, cancellationToken: cancellationToken);


                List<TablaProgramacion> prog = new();
                if (request.Ultimo)
                {
                    var maxPosicionObj = _programadorLitalsaContext.TablaProgramacion
                        .Include(t => t.IdlineaNavigation)
                        .Include(t => t.IdaplicacionNavigation)
                        .Include(t => t.IdproductoNavigation)
                        .Where(o => o.Idlinea == maquina.Idmaquina)
                        .OrderByDescending(o => o.Posicion)
                        .FirstOrDefault();

                    if (maxPosicionObj != null)
                    {
                        prog.Add(maxPosicionObj);
                    }
                }
                else
                {
                    prog.AddRange(await _programadorLitalsaContext.TablaProgramacion
                        .Include(t => t.IdlineaNavigation)
                        .Include(t => t.IdaplicacionNavigation)
                        .Include(t => t.IdproductoNavigation)
                        .Where(o =>
                        o.Idlinea == maquina.Idmaquina && o.Posicion > maquina.PosicionDesde &&
                        o.Posicion <= maquina.PosicionHasta).ToListAsync(cancellationToken));
                }
                //Para ahorrarnos crear otro DTO para LITO, hay que apañar que idProducto sea null en los pedidos de lito, 
                //por lo que lo preasignamos como 0 y tiramos por ahi...
                //if (maquina.TipoMaquina==Enums.TipoMaquina.Impresora.ToString())
                //{
                //    prog.ForEach(o => o.Idproducto = 0);
                //}

                //result.Data = prog.Select(TinyMapper.Map<TablaProgramacionDTO>).ToList();


                foreach (var item in prog)
                {
                    var dto = new TablaProgramacionDTO()
                    {
                        Idprogramacion = item.Idprogramacion,
                        Idpedido = item.Idpedido,
                        Idaplicacion = item.Idaplicacion,
                        Idlinea = item.Idlinea,
                        Posicion = item.Posicion,
                        HojasAprocesar = item.HojasAprocesar,
                        Producto = item.Producto,
                        Flejar = item.Flejar,
                        HoraComienzoEstimada = item.HoraComienzoEstimada,
                        HoraFinEstimada = item.HoraFinEstimada,
                        DuracionEstimada = item.DuracionEstimada,
                        HoraReal = item.HoraReal,
                        DiaReal = item.DiaReal,
                        VarCambios = item.VarCambios,
                        TiposCambio = item.TiposCambio,
                        Orden = item.Orden,
                        Revisar = item.Revisar,
                        Idproducto = item.Idproducto,
                        Peso = item.Peso,
                        PesoMin = item.PesoMin,
                        BarnizNecesario = item.BarnizNecesario,
                        Archivado = item.Archivado,
                        TipoLavada = item.TipoLavada,
                        CaraAaplicar = item.CaraAaplicar,
                        Idaplicacionposterior = item.Idaplicacionposterior,
                        Posicionaplicacionposterior = item.Posicionaplicacionposterior,
                        Idaplicacionanterior = item.Idaplicacionanterior,
                        Posicionaplicacionanterior = item.Posicionaplicacionanterior,
                        Volteado = item.Volteado,
                        AplicacionSimultanea = item.AplicacionSimultanea,
                        Observaciones = item.Observaciones,
                        Obspaseposterior = item.Obspaseposterior,
                        ObsCalidad = item.ObsCalidad,
                        ObsAlmacen = item.ObsAlmacen,
                        TemperaturaSecado = item.TemperaturaSecado,
                        VelocidadMaxima = item.VelocidadMaxima,
                        OrdenProcesoAplicacion = item.OrdenProcesoAplicacion,
                        PasadasAdicionales = item.PasadasAdicionales,
                        Programador = item.Programador,
                        TiempoEstimadoCambio = item.TiempoEstimadoCambio,
                        TiempoEstimadoTirada = item.TiempoEstimadoTirada,
                        DatosPedido = item.DatosPedido,
                        NombreG21 = item.IdlineaNavigation?.IdmaquinaG21,
                        TextoIdAplicacion = item.IdaplicacionNavigation?.Nombreprogramacion,
                        TextoIdProducto = item.IdproductoNavigation?.Denominacion,
                    };

                    lista.Add(dto);
                }




                //TEMPORAL ya que los pedidos programados viejos no lo tienen y así lo mostramos al vuelo
                var datosPedidos = await _programadorLitalsaContext.PedidoProcesado
                    .Where(o => prog.Select(s => s.Idpedido).Contains(o.IdPedido)).ToListAsync(cancellationToken);
                foreach (var p in lista)
                {
                    var pedido = datosPedidos.FirstOrDefault(o => o.IdPedido == p.Idpedido);
                    var cliente =
                        _programadorLitalsaContext.Clientes.FirstOrDefault(o => o.CodigoCliente == pedido.IdCliente);
                    var motivo = maquina.TipoMaquina == Enums.TipoMaquina.Impresora.ToString()
                        ? pedido.Motivos
                        : string.Empty;
                    p.DatosPedido = cliente == null
                        ? "N/A"
                        : $"{cliente.NombreCliente[..3]} {pedido.Formato} {pedido.EspesorHjlta}x{pedido.AnchoHjlta}x{pedido.LargoHjlta} {motivo}";
                    p.NombreG21 = maquina.IdmaquinaG21;

                    if (maquina.TipoMaquina == Enums.TipoMaquina.Barnizadora.ToString())
                    {
                        //Datos extra de PEDPROCESO. Se añaden aquí porque el access los recogía al vuelo en el grid inferior de prog barnizado.
                        //Pero en C# no se puede hacer de esa forma.
                        var pedProcesoLite = await _programadorLitalsaContext.DboPedproceso.Where(o => o.Pedido == p.Idpedido)
                            .ToListAsync(cancellationToken);
                        if (pedProcesoLite.Any())
                        {
                            var pedProcesoDTO = TinyMapper.Map<List<DboPedprocesoDTO>>(pedProcesoLite);
                            foreach (var pp in pedProcesoDTO)
                            {
                                //var sigProcesoTexto = _programadorLitalsaContext.CodApliAll.FirstOrDefault(o => o.Codbaz.Value == pp.Proceso).Nombaz;
                                var sigProcesoTexto = _programadorLitalsaContext.Codapli.FirstOrDefault(o => o.Codbaz == pp.Proceso)?.Nombaz ?? "NO DEFINIDO";
                                var texto = $"LUEGO {sigProcesoTexto}";
                                if (pp.Lugar.Contains("i"))
                                {
                                    texto += " POR CARA INTERIOR";
                                }
                                else if (pp.Lugar.Contains("ep"))
                                {
                                    texto += " POR CARA EXTERIOR";
                                }
                                pp.TextoCaraSiguiente = texto;
                            }

                            p.DatosPedProceso = pedProcesoDTO;
                        }
                    }
                }
                //FIN TEMPORAL
                result.Data = lista.OrderBy(o => o.Posicion.Value).ToList();

            }
            catch (Exception e)
            {
                var errorText =
                    $"ERROR: GetProgramacionByMaquinaQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
                result.Errors.Add(errorText);
            }
            return result;
        }
    }
}