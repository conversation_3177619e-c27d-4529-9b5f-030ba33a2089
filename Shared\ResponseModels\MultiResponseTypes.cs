﻿using System.Text.Json.Serialization;

namespace ProgramadorGeneralBLZ.Shared.ResponseModels
{
    [Serializable]
    public class MultiResponseTypes : PrimitiveResult
    {
        public int? Num { get; set; }
        public double? Double { get; set; }
        public float? Float { get; set; }
        public bool? Bool { get; set; }
        public string? Text { get; set; }
        public DateTime? Fecha { get; set; }
    }
}
