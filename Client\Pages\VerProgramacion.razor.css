﻿body {
    font-family: 'Arial', sans-serif;
    background-color: #fff;
    margin: 0;
    padding: 0;
}

::deep .contenedor {
    margin: 0;
    padding: 20px;
}

/* Cabecera del grupo se quedará fija en la parte superior del grupo */
::deep .cabeceraGrupo {
    position: sticky;
    border-bottom: 2px solid royalblue !important;
    top: 0;
    z-index: 20;
    background-color: #46444a; /* Fondo para evitar transparencia de contenido inferior */
    /*padding: 5px 0 0 10px;
    margin-bottom: 10px;*/
}

::deep .subcabeceraGrupo {
    border-bottom: 1px solid royalblue !important;
}

::deep .cabecera-reporte h1 {
    color: #000;
    font-size: 18px;
    font-weight: bold;
    text-transform: uppercase;
}

/*.cabecera-reporte span {
    color: #000;
    font-weight: bold;
}*/
::deep .cabecera-grupo h2 {
    /*background-color: #0044cc;  Azul oscuro para el título de la cabecera del grupo */
    color: royalblue;
    padding: 5px;
}

::deep .cabecera-grupo .marcadoVerde {
    color: green;
}

::deep .cabecera-grupo h3 {
    /*background-color: #0044cc;  Azul oscuro para el título de la cabecera del grupo */
    color: royalblue;
    padding: 5px;
}

::deep .cabecera-grupo-lavada h2 {
    color: red;
    padding: 5px;
}

::deep .borde {
    border: 1px solid white !important;
}

::deep .linea-separacion {
    border-top: 2px dashed blue; /* Línea de guiones */
}

::deep .sumario {
    width: 100%;
    font-weight: bold;
    font-size: 18px;
    margin-bottom: 20px;
    background-color: grey;
    margin-bottom: 0 !important;
}

    ::deep .sumario span {
        padding-left: 9%;
    }

::deep .dxbl-group-body-content {
    padding-bottom: 0 !important;
}

::deep .textoAzul {
    color: royalblue;
    font-style: italic;
}

::deep .pequeño {
    font-size: 14px;
}

::deep .grande {
    font-size: 18px;
}

::deep .negrita {
    font-weight: bold;
}

::deep .ajusteParaVacio {
    display: inline-block;
    min-width: 100px;
}

::deep btnGroup{
    padding: 5px 15px;
}

::deep .sinempezar {
    background-color: transparent;
    margin: 0;
}
::deep .detenido {
    background-color: transparent;
    margin: 0;
}
::deep .incluido {
    background-color: transparent;
    margin: 0;
}

::deep .empezado {
    background-color: darkseagreen;
    margin: 0;
    color: black;
}

::deep .terminado {
    position: relative; /* Necesario para posicionar el pseudo-elemento */
    margin: 0;
}

    ::deep .terminado::before {
        content: "✖"; /* Símbolo para la X */
        position: absolute;
        top: 40%;
        left: 50%;
        /* Centra el pseudo-elemento y estíralo horizontalmente X veces manteniendo la altura normal */
        transform: translate(-50%, -50%) scaleX(4.5) scaleY(1.25);
        font-size: 12em; /* Ajusta el tamaño según necesites */
        color: rgba(144, 238, 144, 0.4); /* Tono rojo suave y transparente */
        pointer-events: none; /* Para no interferir con las interacciones */
        z-index: 2; /* Asegura que la X quede por encima del contenido */
    }

::deep .retirado {
    background-color: darkred;
    margin: 0;
}

::deep .noOkRojo {
    color: red;
}

::deep .okVerde {
    color: darkgreen;
}


.header-container {
    position: sticky;
    top: 0;
    z-index: 10;
    width: 100%;
    margin-bottom: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1); /* Opcional, para separar visualmente */
}

/* Contenedor para el contenido desplazable */
.content-scroll {
    /* Calcula la altura disponible restando la altura del header (por ejemplo, 150px) */
    height: calc(100vh - 150px);
    overflow-y: auto;
    /* Para navegadores Firefox */
    scrollbar-width: thin; /* Hace la barra más delgada */
    scrollbar-color: #888 transparent; /* Color del “thumb” y del track en Firefox */
    /* (Opcional) Para Chrome experimental:
       hace que la scrollbar sea "overlay" en lugar de "scroll", superponiéndose al contenido
       *No estándar*, no todos los navegadores lo soportan
    */
    overflow: overlay;
}

/* Cuando no hay contenido, reducir la altura para no interferir con elementos flotantes */
.content-scroll.no-content {
    height: auto;
    min-height: 50px;
    pointer-events: none;
}

    /* Scrollbar personalizada para navegadores basados en WebKit (Chrome, Safari, Edge) */
    .content-scroll::-webkit-scrollbar {
        width: 8px; /* Barra más delgada */
        background: transparent;
    }

    .content-scroll::-webkit-scrollbar-track {
        background: transparent;
        margin: 4px; /* Un pequeño margen para que la barra no ocupe espacio adicional */
    }

    .content-scroll::-webkit-scrollbar-thumb {
        background: #888; /* Color del “thumb” */
        border-radius: 4px;
    }

        .content-scroll::-webkit-scrollbar-thumb:hover {
            background: #666;
        }



/* Contenedor general para cada grupo */
::deep .group-container {
    position: relative;
    margin-bottom: 20px; /* Espacio entre grupos */
    --dxbl-fl-group-border-radius: 0px; /*Quitamos el borde redondeado*/
}

::deep .ancho100{
    width: 100%;
}
.background {
    filter: blur(4px);
    position: absolute;
    width: 100%;
    height: 100%;
}
.foreground {
    backdrop-filter: blur(10px);
}