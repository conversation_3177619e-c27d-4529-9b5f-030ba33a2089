﻿using MediatR;
using Nelibur.ObjectMapper;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Pedidos.Command
{
    public class DeleteFasePedidoCommand : IRequest<SingleResult<int>>
    {
        public DeleteFasePedidoCommand(int idProceso)
        {
            IdProceso = idProceso;
        }

        public int IdProceso { get; set; }
    }
    public class DeleteFasePedidoCommandHandler : IRequestHandler<DeleteFasePedidoCommand, SingleResult<int>>
    {
        private readonly ProgramadorLitalsaContext _contextProg;
        public DeleteFasePedidoCommandHandler(ProgramadorLitalsaContext contextProg)
        {
            _contextProg = contextProg;
        }


        public async Task<SingleResult<int>> Handle(DeleteFasePedidoCommand request, CancellationToken cancellationToken)
        {
            var result = new SingleResult<int>{ Errors = new List<string>(), Data = 0 };
            try
            {
                var faseABorrar = _contextProg.TablaCodigosPedido
                    .FirstOrDefault(o => o.Idproceso == request.IdProceso);
                _contextProg.TablaCodigosPedido.Remove(faseABorrar);
                var dbResult = await _contextProg.SaveChangesAsync(cancellationToken);
                result.Data = dbResult;
            }
            catch (Exception e)
            {
                result.Errors.Add($"Se ha producido un error en la eliminación de la fase seleccionada");
            }
            return result;

        }
    }
}
