﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;
using ProgramadorGeneralBLZ.Shared;

namespace ProgramadorGeneralBLZ.Server.Models.ProteoLitalsa
{
    public class DatosProteoDB
    {
        //DE WORKORDERS
        [Column("WORK_ORDER")] public double WorkOrder { get; set; }
        [Column("START_PLANNED")] public DateTime? StartPlanned { get; set; }
        [Column("DATE_TIME_DOWNLOAD")] public DateTime? DateTimeDownload { get; set; }
        [Column("DATE_TIME_UPDATE")] public DateTime? DateTimeUpdate { get; set; }
        [Column("ESTIMATED_DURATION")] public TimeSpan? EstimatedDuration { get; set; }
        [Column("CLIENT")] public string Client { get; set; }
        [Column("METALWORK")] public string Metalwork { get; set; }

        //DE PAHSES
        [Column("MACHINE_PLANNED")] public string MachinePlanned { get; set; }
        [Column("FREE4")] public string Free4 { get; set; }// <PERSON>ina
        [Column("FREE3")] public string Free3 { get; set; }// Bobina Siderurgia
        [Column("VELOCIDAD_MAXIMA")] public int? VelocidadMaxima { get; set; }
        [Column("EMPEZADA_PROTEO")] public bool? EmpezadaProteo { get; set; }
        [Column("FECHA_EMPEZADA_PROTEO")] public DateTime? FechaEmpezadaProteo { get; set; }
        
    }
}
