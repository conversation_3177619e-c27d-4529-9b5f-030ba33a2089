﻿@page "/ProgramacionLitografia"
@using Microsoft.AspNetCore.SignalR.Client
@using ProgramadorGeneralBLZ.Shared
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Authorization
@using ProgramadorGeneralBLZ.Shared.DTO
@using ProgramadorGeneralBLZ.Shared.ResponseModels
@using ProgramadorGeneralBLZ.Client.Pages.Components
@using System.Text.Json;
@using System.Text;
@using System.Text.Json.Serialization
@using System.Collections.ObjectModel
@using System.Security.Claims

@implements IAsyncDisposable

@inject Blazored.LocalStorage.ILocalStorageService localStorage
@inject IJSRuntime Js
@inject IToastService ToastService
@inject HttpClient Http
@inject SpinnerService SpinnerService
@inject AuthenticationStateProvider GetAuthenticationStateAsync
@inject NavigationManager Navigation
@inject IHttpClientFactory HttpClientFactory
@inject AuthenticationStateProvider AuthState

@attribute [Authorize(Roles = Roles.Programador)]

<PageTitle>Litografia</PageTitle>

<AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}")">
    <Authorized Context="authContext">
        <DxLayoutBreakpoint DeviceSize="DeviceSize.Large" @bind-IsActive="@_isSmallScreen" />
        <div class="h-100 overflow-auto px-2 py-1">
            <DxFormLayout SizeMode="SizeMode.Small">
                <DxGridLayout CssClass="h-100" ColumnSpacing="1px" RowSpacing="1px">
                    <Rows>
                        <DxGridLayoutRow Areas="CintaSuperior" Height="auto" />
                        <DxGridLayoutRow Areas="GridSuperior" Height="auto" />
                        <DxGridLayoutRow Areas="CintaIntermedia" Height="auto" />
                        <DxGridLayoutRow Areas="GridIntermedio" Height="auto" />
                        <DxGridLayoutRow Areas="CintaInferior" Height="auto" />
                        <DxGridLayoutRow Areas="GridInferior" Height="auto" />
                        <DxGridLayoutRow Areas="CintaBotones" Height="auto" />
                    </Rows>
                    <Items>
                        <DxGridLayoutItem Area="CintaSuperior">
                            <Template>
                                <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanLg="3">
                                    <DxFormLayoutItem Context="ddMaquina" ColSpanLg="12" ColSpanXs="6">
                                        <span @ref="@_popupTarget"></span>
                                        <DxComboBox Data="@DatosMaquinas" @ref="@_componentMaquina" CssClass="ajusteExtra"
                                                    TextFieldName="@nameof(MaquinaDTO.IdmaquinaG21)"
                                                    Value="@CurrentMaquina"
                                                    SelectedItemChanged="@((MaquinaDTO m) => OnUpdateMaquina(m))"
                                                    NullText="Máquina...">
                                        </DxComboBox>
                                        <DxButtonGroup RenderStyle="ButtonRenderStyle.Info">
                                            <Items>
                                                <DxButtonGroupItem CssClass="movMaq" IconCssClass="oi oi-arrow-thick-left" Click="@(() => OnButtonGroupClick("PrevMaquina"))" />
                                                <DxButtonGroupItem CssClass="movMaq" IconCssClass="oi oi-arrow-thick-right" Click="@(() => OnButtonGroupClick("NextMaquina"))" />
                                            </Items>
                                        </DxButtonGroup>
                                        @* <DxToolbar ItemClick="OnItemClick" CssClass="ajusteToolbar" AdaptivityMinRootItemCount="2">
                                            <Items>
                                                <DxToolbarItem CssClass="toolbarItem1" Alignment="ToolbarItemAlignment.Right"
                                                               IconCssClass="oi oi-arrow-thick-left" Name="PrevMaquina"
                                                               RenderStyle="ButtonRenderStyle.Info" />
                                                <DxToolbarItem CssClass="toolbarItem2" Alignment="ToolbarItemAlignment.Right"
                                                               IconCssClass="oi oi-arrow-thick-right" Name="NextMaquina"
                                                               RenderStyle="ButtonRenderStyle.Info" />
                                            </Items>
                                        </DxToolbar> *@
                                    </DxFormLayoutItem>
                                    <DxFormLayoutItem Context="botones" ColSpanLg="12" ColSpanXs="6">

                                    </DxFormLayoutItem>
                                </DxFormLayoutGroup>
                            </Template>
                        </DxGridLayoutItem>
                        <DxGridLayoutItem Area="GridSuperior">
                            <Template>
                                <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="12">
                                    <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="11">
                                        <DxLoadingPanel @bind-Visible="PanelVisibleFoto"
                                                        IsContentBlocked="true"
                                                        ApplyBackgroundShading="true"
                                                        IndicatorAreaVisible="false"
                                                        IndicatorAnimationType="WaitIndicatorAnimationType.Spin"
                                                        Text="Cargando..."
                                                        TextAlignment="LoadingPanelTextAlignment.Right">
                                            <DxGrid Data="@DatosPedidosFotoLito" SizeMode="SizeMode.Small" CssClass="ch-185 smallFont"
                                                    ShowFilterRow="true" ColumnResizeMode="GridColumnResizeMode.ColumnsContainer"
                                                    FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always"
                                                    FilterCriteriaChanged="GridDatosPedidosFotoLito_FilterCriteriaChanged"
                                                    SelectionMode="GridSelectionMode.Multiple" PageSize="100" @ref="GridLitoFoto">
                                                <Columns>
                                                    <DxGridDataColumn FieldName="IdPedido" Caption="Pedido" TextAlignment="GridTextAlignment.Center"
                                                                      Width="70px" DisplayFormat="F0" SortIndex="2" SortOrder="GridColumnSortOrder.Descending"
                                                                      FilterRowOperatorType="GridFilterRowOperatorType.Contains">
                                                        <CellDisplayTemplate Context="cellText">
                                                            <span class="p-1 d-block text-left" style="cursor: pointer"
                                                                  @ondblclick="() => VerDatosPedido(cellText.DisplayText)">
                                                                @cellText.DisplayText
                                                            </span>
                                                        </CellDisplayTemplate>
                                                    </DxGridDataColumn>
                                                    <DxGridDataColumn FieldName="IdCliente" SortIndex="0" SortOrder="GridColumnSortOrder.Ascending" Visible="false" />
                                                    <DxGridDataColumn FieldName="DatosCliente" Caption="Cliente" Width="120px"
                                                                      FilterRowOperatorType="GridFilterRowOperatorType.Contains">
                                                        <CellDisplayTemplate Context="cellText">
                                                            @{
                                                                string summary = cellText.Value?.ToString() ?? string.Empty;
                                                                <span class="d-block text-truncate" title="@summary" style="max-width: 100px">
                                                                    @summary
                                                                </span>
                                                            }
                                                        </CellDisplayTemplate>
                                                    </DxGridDataColumn>
                                                    <DxGridDataColumn FieldName="HojasPedido" Caption="Nº Hojas" Width="70px" TextAlignment="GridTextAlignment.Center" />
                                                    <DxGridDataColumn FieldName="Motivos" Width="205px">
                                                        <CellDisplayTemplate Context="cellText">
                                                            @{
                                                                string summary = cellText.Value?.ToString() ?? string.Empty;
                                                                <span class="d-block text-truncate" title="@summary" style="max-width: 200px">
                                                                    @summary
                                                                </span>
                                                            }
                                                        </CellDisplayTemplate>
                                                    </DxGridDataColumn>
                                                    <DxGridDataColumn FieldName="TipoElemento" Caption="Tipo" Width="35px" TextAlignment="GridTextAlignment.Center"
                                                                      FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
                                                    <DxGridDataColumn FieldName="DescHojalata" Width="200px"
                                                                      FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
                                                    <DxGridDataColumn FieldName="Escuadra" Width="40px" TextAlignment="GridTextAlignment.Center"
                                                                      FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
                                                    <DxGridDataColumn FieldName="Formato" DisplayFormat="N2" Width="55px" TextAlignment="GridTextAlignment.Center"
                                                                      SortIndex="1" SortOrder="GridColumnSortOrder.Ascending"
                                                                      FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
                                                    <DxGridDataColumn FieldName="Plano" Width="85px" TextAlignment="GridTextAlignment.Center"
                                                                      FilterRowOperatorType="GridFilterRowOperatorType.Contains">
                                                        <CellDisplayTemplate Context="cellText">
                                                            @{
                                                                string summary = cellText.Value?.ToString() ?? string.Empty;
                                                                <div class="d-block text-truncate" title="@summary" style="max-width: 85px">
                                                                    @summary
                                                                </div>
                                                            }
                                                        </CellDisplayTemplate>
                                                    </DxGridDataColumn>
                                                    <DxGridDataColumn FieldName="TextEstado" Caption=" TextoEstado" Width="100%"
                                                                      FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
                                                    <DxGridDataColumn FieldName="Tintas" Width="100px" Context="txtTintas"
                                                                      FilterRowOperatorType="GridFilterRowOperatorType.Contains">
                                                        <CellDisplayTemplate>
                                                            @{
                                                                var item = (DatosGeneralesPedidoFotomecanicaLitoDTO)txtTintas.DataItem;
                                                                //string summary = cellText.Value?.ToString() ?? string.Empty;
                                                                <span class="d-block text-truncate" title="@item.TextoTintas">
                                                                    @item.Tintas
                                                                </span>
                                                            }
                                                        </CellDisplayTemplate>
                                                    </DxGridDataColumn>
                                                    <DxGridDataColumn FieldName="RequeridoEnFecha" Caption="Req. Fecha" Width="80px"
                                                                      FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
                                                    <DxGridDataColumn FieldName="URGENTE">
                                                        <CellDisplayTemplate Context="checkbox">
                                                            <DxCheckBox Checked="@((bool)checkbox.Value)" />
                                                        </CellDisplayTemplate>
                                                    </DxGridDataColumn>
                                                    <DxGridDataColumn FieldName="Idcodigoaplicacion" Context="combo" Caption="Id Aplicación" Width="180px"
                                                                      FilterRowOperatorType="GridFilterRowOperatorType.Contains">
                                                        <CellDisplayTemplate>
                                                            @{
                                                                var item = (DatosGeneralesPedidoFotomecanicaLitoDTO)combo.DataItem;
                                                            }
                                                            <CellDisplay Value="@($"{item.Idcodigoaplicacion}, {item.TextoIdAplicacion}")" />
                                                        </CellDisplayTemplate>
                                                    </DxGridDataColumn>
                                                    <DxGridDataColumn FieldName="FechaPedido" Caption="F. Pedido" Width="70px" />
                                                </Columns>
                                            </DxGrid>
                                        </DxLoadingPanel>
                                    </DxFormLayoutGroup>
                                    <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="1">
                                        <DxFormLayoutItem CssClass="textoBlanco" Caption="Hojas Totales" Context="textosAzules"
                                                          ColSpanLg="12" ColSpanXs="12" CaptionPosition="CaptionPosition.Vertical">
                                            <DxSpinEdit @bind-Value="@_currentTextoAzulFotoLito.HTotales" ShowSpinButtons="false"
                                                        DisplayFormat="{0:N0} [hojas]" CssClass="textoAzul" ReadOnly="true" />
                                        </DxFormLayoutItem>
                                        <DxFormLayoutItem ColSpanLg="12" ColSpanXs="12" Context="btnUpdate">
                                            <DxButton Click="@(ActualizarGridLitoSuperior)" RenderStyle="ButtonRenderStyle.Info"
                                                      IconCssClass="oi oi-arrow-thick-left" Text="Actualizar" />
                                        </DxFormLayoutItem>
                                        @* <DxFormLayoutItem CssClass="textoBlanco" Caption="Pases Totales" Context="textosAzules"
                                        ColSpanLg="12" ColSpanXs="12" CaptionPosition="CaptionPosition.Vertical">
                                        <DxSpinEdit @bind-Value="@_currentTextoAzulFotoLito.PasesTotales" ShowSpinButtons="false"
                                        DisplayFormat="{0:N0} [pases]" CssClass="textoAzul" ReadOnly="true" />
                                        </DxFormLayoutItem> *@

                                    </DxFormLayoutGroup>
                                </DxFormLayoutGroup>
                            </Template>
                        </DxGridLayoutItem>
                        <DxGridLayoutItem Area="CintaIntermedia">
                            <Template>
                            </Template>
                        </DxGridLayoutItem>
                        <DxGridLayoutItem Area="GridIntermedio">
                            <Template>
                                <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="12">
                                    <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="11">
                                        <DxLoadingPanel @bind-Visible="PanelVisibleLito"
                                                        IsContentBlocked="true"
                                                        ApplyBackgroundShading="true"
                                                        IndicatorAreaVisible="false"
                                                        IndicatorAnimationType="WaitIndicatorAnimationType.Spin"
                                                        Text="Cargando..."
                                                        TextAlignment="LoadingPanelTextAlignment.Right">
                                            <DxGrid Data="@DatosPedidosLito" SizeMode="SizeMode.Small" CssClass="ch-315 smallFont progGrid2"
                                                    ShowFilterRow="true" ColumnResizeMode="GridColumnResizeMode.ColumnsContainer"
                                                    SelectionMode="GridSelectionMode.Multiple" PageSize="150" @ref="GridLitoAsignada"
                                                    SelectAllCheckboxMode="GridSelectAllCheckboxMode.Page"
                                                    FilterCriteriaChanged="GridDatosPedidosLito_FilterCriteriaChanged"
                                                    FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always"
                                                    @bind-SelectedDataItems="_selectedPedidosLitoDataItems">
                                                <Columns>
                                                    <DxGridSelectionColumn Width="50">
                                                    </DxGridSelectionColumn>
                                                    <DxGridDataColumn FieldName="IdPedido" Caption="Pedido" TextAlignment="GridTextAlignment.Center"
                                                                      Width="70px" DisplayFormat="F0" SortIndex="3" SortOrder="GridColumnSortOrder.Descending"
                                                                      FilterRowOperatorType="GridFilterRowOperatorType.Contains">
                                                        <CellDisplayTemplate Context="cellText">
                                                            <span class="p-1 d-block text-left" style="cursor: pointer"
                                                                  @ondblclick="() => VerDatosPedido(cellText.DisplayText)">
                                                                @cellText.DisplayText
                                                            </span>
                                                        </CellDisplayTemplate>
                                                    </DxGridDataColumn>
                                                    <DxGridDataColumn FieldName="DatosCliente" Caption="Cliente" Width="120px"
                                                                      FilterRowOperatorType="GridFilterRowOperatorType.Contains"
                                                                      SortIndex="0" SortOrder="GridColumnSortOrder.Ascending">
                                                        <CellDisplayTemplate Context="cellText">
                                                            @{
                                                                string summary = cellText.Value?.ToString() ?? string.Empty;
                                                                <span class="d-block text-truncate" title="@summary" style="max-width: 100px">
                                                                    @summary
                                                                </span>
                                                            }
                                                        </CellDisplayTemplate>
                                                    </DxGridDataColumn>
                                                    <DxGridDataColumn FieldName="HojasPedido" Caption="Nº Hojas" Width="70px"
                                                                      TextAlignment="GridTextAlignment.Center"
                                                                      FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
                                                    <DxGridDataColumn FieldName="HojasTerminadas" Caption="Hojas Term" Width="70px"
                                                                      TextAlignment="GridTextAlignment.Center"
                                                                      FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
                                                    <DxGridDataColumn FieldName="Motivos" Width="205px" FilterRowOperatorType="GridFilterRowOperatorType.Contains"
                                                                      SortIndex="2" SortOrder="GridColumnSortOrder.Ascending">
                                                        <CellDisplayTemplate Context="cellText">
                                                            @{
                                                                string summary = cellText.Value?.ToString() ?? string.Empty;
                                                                <span class="d-block text-truncate" title="@summary" style="max-width: 200px">
                                                                    @summary
                                                                </span>
                                                            }
                                                        </CellDisplayTemplate>
                                                    </DxGridDataColumn>
                                                    <DxGridDataColumn FieldName="DescHojalata" Width="200px" />
                                                    <DxGridDataColumn FieldName="TipoElemento" Width="40px" Caption="Tipo Ele"
                                                                      TextAlignment="GridTextAlignment.Center"
                                                                      FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
                                                    <DxGridDataColumn FieldName="Formato" DisplayFormat="N2" Width="55px"
                                                                      TextAlignment="GridTextAlignment.Center"
                                                                      FilterRowOperatorType="GridFilterRowOperatorType.Contains"
                                                                      SortIndex="1" SortOrder="GridColumnSortOrder.Ascending" />
                                                    <DxGridDataColumn FieldName="Plano" Width="85px" TextAlignment="GridTextAlignment.Center"
                                                                      FilterRowOperatorType="GridFilterRowOperatorType.Contains">
                                                        <CellDisplayTemplate Context="cellText">
                                                            @{
                                                                string summary = cellText.Value?.ToString() ?? string.Empty;
                                                                <div class="d-block text-truncate" title="@summary" style="max-width: 85px">
                                                                    @summary
                                                                </div>
                                                            }
                                                        </CellDisplayTemplate>
                                                    </DxGridDataColumn>
                                                    <DxGridDataColumn FieldName="TextEstado" Caption=" TextoEstado" Width="300px"
                                                                      FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
                                                    <DxGridDataColumn FieldName="Tintas" Width="100px" Context="txtTintas"
                                                                      FilterRowOperatorType="GridFilterRowOperatorType.Contains">
                                                        <CellDisplayTemplate>
                                                            @{
                                                                var item = (DatosGeneralesPedidoFotomecanicaLitoDTO)txtTintas.DataItem;
                                                                //string summary = cellText.Value?.ToString() ?? string.Empty;
                                                                <span class="d-block text-truncate" title="@item.TextoTintas">
                                                                    @item.Tintas
                                                                </span>
                                                            }
                                                        </CellDisplayTemplate>
                                                    </DxGridDataColumn>
                                                    <DxGridDataColumn FieldName="FechaEntregaSolicitada" Caption="F.Entrega" Width="60px"
                                                                      FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
                                                    <DxGridDataColumn FieldName="RequeridoEnFecha" Caption="Req. Fecha" Width="80px"
                                                                      FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
                                                    <DxGridDataColumn FieldName="URGENTE">
                                                        <CellDisplayTemplate Context="checkbox">
                                                            <DxCheckBox Checked="@((bool)checkbox.Value)" />
                                                        </CellDisplayTemplate>
                                                    </DxGridDataColumn>
                                                    <DxGridDataColumn FieldName="Idcodigoaplicacion" Context="combo" Caption="Id Aplicación"
                                                                      Width="200px" FilterRowOperatorType="GridFilterRowOperatorType.Contains"
                                                                      TextAlignment="GridTextAlignment.Left">
                                                        <CellDisplayTemplate>
                                                            @{
                                                                var item = (DatosGeneralesPedidoFotomecanicaLitoDTO)combo.DataItem;
                                                            }
                                                            <CellDisplay Value="@($"{item.Idcodigoaplicacion}, {item.TextoIdAplicacion}")" />
                                                        </CellDisplayTemplate>
                                                    </DxGridDataColumn>
                                                    <DxGridDataColumn FieldName="Tipo_hjlta" Width="40px" Caption="Tipo Hoj" TextAlignment="GridTextAlignment.Center" FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
                                                    <DxGridDataColumn FieldName="Escuadra" Width="40px" Caption="Esc" TextAlignment="GridTextAlignment.Center" FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
                                                </Columns>
                                            </DxGrid>
                                        </DxLoadingPanel>
                                    </DxFormLayoutGroup>
                                    <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="1">
                                        <DxFormLayoutItem CssClass="textoBlanco" Caption="Hojas Tot" Context="textosAzules"
                                                          ColSpanLg="12" ColSpanXs="12" CaptionPosition="CaptionPosition.Vertical">
                                            <DxSpinEdit @bind-Value="@_currentTextoAzul.HTotales" ShowSpinButtons="false"
                                                        DisplayFormat="{0:N0} [hojas]" CssClass="textoAzul" ReadOnly="true" />
                                        </DxFormLayoutItem>
                                        @* <DxFormLayoutItem CssClass="textoBlanco" Caption="Pases tot:" Context="textosAzules"
                                        ColSpanLg="12" ColSpanXs="12" CaptionPosition="CaptionPosition.Vertical">
                                        <DxSpinEdit @bind-Value="@_currentTextoAzul.PasesTotales" ShowSpinButtons="false"
                                        DisplayFormat="{0:N0} [pases]" CssClass="textoAzul" ReadOnly="true" />
                                        </DxFormLayoutItem> *@
                                        <DxFormLayoutItem CssClass="textoBlanco" Caption="Hojas efect: " Context="textosAzules"
                                                          ColSpanLg="12" ColSpanXs="12" CaptionPosition="CaptionPosition.Vertical">
                                            <DxSpinEdit @bind-Value="@_currentTextoAzul.HojasEfectivas" ShowSpinButtons="false"
                                                        DisplayFormat="{0:N0} [hojas]" CssClass="textoAzul" ReadOnly="true" />
                                        </DxFormLayoutItem>
                                        <DxFormLayoutItem CssClass="textoBlanco" Caption="Dias Trabajo" Context="textosAzules"
                                                          ColSpanLg="12" ColSpanXs="12" CaptionPosition="CaptionPosition.Vertical">
                                            <DxSpinEdit @bind-Value="@_currentTextoAzul.DiasTrabajo" ShowSpinButtons="false"
                                                        DisplayFormat="{0:N2} [días]" CssClass="textoAzul" ReadOnly="true" />
                                        </DxFormLayoutItem>
                                        <DxFormLayoutItem CssClass="textoBlanco" Caption="Previsión final:" Context="textosAzules"
                                                          ColSpanLg="12" ColSpanXs="12" CaptionPosition="CaptionPosition.Vertical">
                                            <DxDateEdit @bind-Date="@_currentTextoAzul.PrevisionFinal" TimeSectionVisible="true"
                                                        CssClass="textoAzul" ReadOnly="true" InputCssClass="smallSize" />
                                        </DxFormLayoutItem>
                                    </DxFormLayoutGroup>
                                </DxFormLayoutGroup>
                            </Template>
                        </DxGridLayoutItem>
                        <DxGridLayoutItem Area="CintaInferior">
                            <Template>
                                <div class="gridlayout-cm gridlayout-item mt-0">
                                    <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="12" CssClass="mt-0">
                                        <DxFormLayoutItem Context="botones" ColSpanLg="2" ColSpanXs="6">
                                            <DxButton RenderStyle="ButtonRenderStyle.Primary" CssClass="btnWide"
                                                      Enabled="@(_selectedPedidosLitoDataItems.Any())"
                                                      IconCssClass="oi oi-arrow-thick-bottom"
                                                      Click="@((e) => MoverEntreGrids(true))" />
                                            <DxButton RenderStyle="ButtonRenderStyle.Primary" CssClass="btnWide"
                                                      Enabled="@(_selectedAImprimirDataItems.Any())"
                                                      IconCssClass="oi oi-arrow-thick-top"
                                                      Click="@((e) => MoverEntreGrids(false))" />
                                            <DxButton RenderStyle="ButtonRenderStyle.Warning" CssClass="btnWide"
                                                      Enabled="@(_selectedAImprimirDataItems.Any())"
                                                      IconCssClass="oi oi-aperture"
                                                      @onclick="@(() => _isCambiosMasivosPopUpVisible = true)" />
                                        </DxFormLayoutItem>
                                        <DxFormLayoutItem Context="botones" ColSpanLg="2" ColSpanXs="6">
                                            <DxCheckBox @bind-Checked="@_wetOnWet" Enabled="_wetOnWetReadOnly">Wet On Wet</DxCheckBox>
                                        </DxFormLayoutItem>
                                    </DxFormLayoutGroup>
                                </div>
                            </Template>
                        </DxGridLayoutItem>
                        <DxGridLayoutItem Area="GridInferior">
                            <Template>
                                <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="12">
                                    <DxLoadingPanel @bind-Visible="PanelVisible"
                                                    IsContentBlocked="true"
                                                    ApplyBackgroundShading="true"
                                                    IndicatorAreaVisible="false"
                                                    IndicatorAnimationType="WaitIndicatorAnimationType.Spin"
                                                    Text="Cargando..."
                                                    TextAlignment="LoadingPanelTextAlignment.Right">
                                        <DxGrid Data="@DatosProgramacion" SizeMode="SizeMode.Small" @key="@MyKey" id="idProgLito"
                                                CssClass="ch-330 smallFont progGrid" PageSize="80" EditMode="GridEditMode.PopupEditForm"
                                                PopupEditFormCssClass="popupEditor"
                                                KeyFieldName="Idprogramacion" Context="GridProgramacion" @ref="GridProg"
                                                EditNewRowPosition="GridEditNewRowPosition.Top"
                                                ShowFilterRow="true" ColumnResizeMode="GridColumnResizeMode.ColumnsContainer"
                                                SelectionMode="GridSelectionMode.Multiple" ValidationEnabled="false"
                                                SelectAllCheckboxMode="GridSelectAllCheckboxMode.Page"
                                                EditModelSaving="Grid_EditModelSaving"
                                                CustomizeElement="Grid_CustomizeElement"
                                                @bind-SelectedDataItems="_selectedAImprimirDataItems"
                                                AllowSort="false"
                                                AllowSelectRowByClick="true"
                                                AllowDragRows="true"
                                                ItemsDropped="Grid_ItemsDropped">
                                            <Columns>
                                                <DxGridSelectionColumn Width="50">
                                                </DxGridSelectionColumn>
                                                <DxGridCommandColumn NewButtonVisible="false" DeleteButtonVisible="false" Width="50px">
                                                    <CellDisplayTemplate>
                                                        <a class="oi oi-pencil" @onclick="@(() => StartEditing(context.VisibleIndex))" style="text-decoration: none; color: #c75fff;" href="javascript:void(0);"></a>
                                                    </CellDisplayTemplate>
                                                    <CellEditTemplate>
                                                        <a class="oi oi-arrow-thick-bottom" @onclick="@(() => GridProg.SaveChangesAsync())" style="text-decoration: none; color: greenyellow; margin-right: 6px; margin-top: 3px;" href="javascript:void(0);"></a>
                                                        <a class="oi oi-x" @onclick="@(() => GridProg.CancelEditAsync())" style="text-decoration: none; color: red;" href="javascript:void(0);"></a>
                                                    </CellEditTemplate>
                                                </DxGridCommandColumn>
                                                <DxGridDataColumn FieldName="Id" Visible="false" />
                                                <DxGridDataColumn FieldName="Idprogramacion" Caption="ID.Prog" Width="60px" DisplayFormat="F0"
                                                                  FilterRowOperatorType="GridFilterRowOperatorType.Contains" Visible="false" />
                                                <DxGridDataColumn FieldName="Idpedido" Caption="Pedido" TextAlignment="GridTextAlignment.Center"
                                                                  Width="70px" DisplayFormat="F0" FilterRowOperatorType="GridFilterRowOperatorType.Contains">
                                                    <CellDisplayTemplate Context="cellText">
                                                        <span class="p-1 d-block text-left" style="cursor: pointer" @ondblclick="() => VerDatosPedido(cellText.DisplayText)">
                                                            @cellText.DisplayText
                                                        </span>
                                                    </CellDisplayTemplate>
                                                </DxGridDataColumn>
                                                <DxGridDataColumn FieldName="Posicion" DisplayFormat="F0" Width="70px" FilterRowOperatorType="GridFilterRowOperatorType.Contains"
                                                                  TextAlignment="GridTextAlignment.Center">
                                                    <CellDisplayTemplate Context="cellText">
                                                        <span class="p-1 d-block text-left posi" style="cursor: pointer">
                                                            @cellText.DisplayText
                                                        </span>
                                                    </CellDisplayTemplate>
                                                </DxGridDataColumn>
                                                <DxGridDataColumn FieldName="DatosPedido" Width="205px" FilterRowOperatorType="GridFilterRowOperatorType.Contains">
                                                    <CellDisplayTemplate Context="cellText">
                                                        @{
                                                            string summary = cellText.Value?.ToString() ?? string.Empty;
                                                            <span class="d-block text-truncate" title="@summary" style="max-width: 200px">
                                                                @summary
                                                            </span>
                                                        }
                                                        @*<CellDisplay Value="@(cellText.Value?.ToString() ?? string.Empty)" />*@
                                                    </CellDisplayTemplate>
                                                </DxGridDataColumn>
                                                <DxGridDataColumn FieldName="Idlinea" Caption="Máquina" Width="60px" FilterRowOperatorType="GridFilterRowOperatorType.Contains"
                                                                  Context="combo" TextAlignment="GridTextAlignment.Center">
                                                    <CellDisplayTemplate>
                                                        @{
                                                            var item = (TablaProgramacionDTO)combo.DataItem;
                                                        }
                                                        <CellDisplay Value="@($"{item.NombreG21}")" />
                                                    </CellDisplayTemplate>
                                                </DxGridDataColumn>
                                                <DxGridDataColumn FieldName="HoraComienzoEstimada" Caption="Hora Ini Est"
                                                                  TextAlignment="GridTextAlignment.Center" DisplayFormat="dd/MM HH:mm" Width="70px" />
                                                <DxGridDataColumn FieldName="HoraFinEstimada" Caption="Hora Fin Est"
                                                                  TextAlignment="GridTextAlignment.Center" DisplayFormat="dd/MM HH:mm" Width="70px" />
                                                <DxGridDataColumn FieldName="VarCambios" Caption="Cambios" Width="50px"
                                                                  TextAlignment="GridTextAlignment.Center" />
                                                <DxGridDataColumn FieldName="DiaReal" Width="80px" TextAlignment="GridTextAlignment.Center" />
                                                <DxGridDataColumn FieldName="HoraReal" DisplayFormat="t" Width="80px" TextAlignment="GridTextAlignment.Center">
                                                    <CellDisplayTemplate Context="cellText">
                                                        <span class="p-1 d-block text-left">
                                                            @(string.IsNullOrEmpty(cellText.DisplayText) ? "--:--" : cellText.DisplayText)
                                                        </span>
                                                    </CellDisplayTemplate>
                                                </DxGridDataColumn>
                                                <DxGridDataColumn FieldName="Observaciones" Width="205px" FilterRowOperatorType="GridFilterRowOperatorType.Contains">
                                                    <CellDisplayTemplate Context="cellText">
                                                        @{
                                                            string summary = cellText.Value?.ToString() ?? string.Empty;
                                                            <span class="d-block text-truncate" title="@summary" style="max-width: 200px">
                                                                @summary
                                                            </span>
                                                        }
                                                        @*<CellDisplay Value="@(cellText.Value?.ToString() ?? string.Empty)" />*@
                                                    </CellDisplayTemplate>
                                                </DxGridDataColumn>
                                                <DxGridDataColumn FieldName="ObsAlmacen" Width="140px" Context="columnaAlmacen">
                                                    <CellDisplayTemplate>
                                                        @{
                                                            var item = (TablaProgramacionDTO)columnaAlmacen.DataItem;
                                                        }
                                                        <span class="p-1 d-block text-left" style="cursor: pointer; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"
                                                              @ondblclick="() => GetObsAlmacenByDblClick(item)">
                                                            @item.ObsAlmacen
                                                        </span>
                                                    </CellDisplayTemplate>
                                                </DxGridDataColumn>
                                                <DxGridDataColumn FieldName="Flejar" Width="40px">
                                                    <CellDisplayTemplate Context="checkbox">
                                                        <DxCheckBox Checked="@((bool)checkbox.Value)" CssClass="noElegir" />
                                                    </CellDisplayTemplate>
                                                </DxGridDataColumn>
                                                <DxGridDataColumn FieldName="Idaplicacion" Context="combo" Caption="Id Aplicación" Width="180px"
                                                                  FilterRowOperatorType="GridFilterRowOperatorType.Contains">
                                                    <CellDisplayTemplate>
                                                        @{
                                                            var item = (TablaProgramacionDTO)combo.DataItem;
                                                        }
                                                        <CellDisplay Value="@($"{item.Idaplicacion}, {item.TextoIdAplicacion}")" />
                                                        @*<p>@($"{item.Idaplicacion}, {item.TextoIdAplicacion}")</p>*@
                                                    </CellDisplayTemplate>
                                                </DxGridDataColumn>
                                                <DxGridDataColumn FieldName="Producto" Width="205px" FilterRowOperatorType="GridFilterRowOperatorType.Contains">
                                                    <CellDisplayTemplate Context="cellText">
                                                        <CellDisplay Value="@(cellText.Value?.ToString() ?? string.Empty)" />
                                                    </CellDisplayTemplate>
                                                </DxGridDataColumn>
                                                <DxGridDataColumn FieldName="Idproducto" Context="combo" Caption="Id Producto" Width="150px"
                                                                  FilterRowOperatorType="GridFilterRowOperatorType.Contains">
                                                    <CellDisplayTemplate>
                                                        @{
                                                            var item = (TablaProgramacionDTO)combo.DataItem;
                                                        }
                                                        <CellDisplay Value="@($"{item.Idproducto}, {item.TextoIdProducto}")" />
                                                        @*<p>@($"{item.Idproducto}, {item.TextoIdProducto}")</p>*@
                                                    </CellDisplayTemplate>
                                                </DxGridDataColumn>
                                                <DxGridDataColumn FieldName="PesoMin" Caption="P.M" Width="40px" TextAlignment="GridTextAlignment.Center"
                                                                  FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
                                                <DxGridDataColumn FieldName="Peso" Caption="P" Width="40px" TextAlignment="GridTextAlignment.Center"
                                                                  FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
                                            </Columns>
                                            <EditFormTemplate>
                                                @{
                                                    var prog = (TablaProgramacionDTO)GridProgramacion.EditModel;
                                                }
                                                <DxFormLayout CssClass="w-100">
                                                    <DxFormLayoutGroup Context="grupo" ColSpanLg="6" Decoration="FormLayoutGroupDecoration.None">
                                                        <DxFormLayoutItem Caption="Id pedido:" ColSpanLg="12">
                                                            <DxSpinEdit @bind-Value="@prog.Idpedido" ShowSpinButtons="false" ShowValidationIcon="true" ReadOnly="true" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="Posicion:" ColSpanLg="12">
                                                            <DxSpinEdit @bind-Value="@prog.Posicion" ShowSpinButtons="false" ShowValidationIcon="true" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="Dia Real:" ColSpanLg="12">
                                                            <DxDateEdit @bind-Date="@prog.DiaReal" ShowValidationIcon="true"
                                                                        ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                                                        @ondblclick="() => FechaAutomatica(prog)" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="Hora Real:" ColSpanLg="12">
                                                            <DxTimeEdit @bind-Time="@prog.HoraReal" ShowValidationIcon="true" Format="t"
                                                                        ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                                                        Mask="@DateTimeMask.ShortTime" @ondblclick="() => HoraAutomatica(prog)" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="Cambios:" ColSpanLg="12">
                                                            <DxSpinEdit ShowSpinButtons="false" @bind-Value="@prog.VarCambios" ShowValidationIcon="true" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="Observaciones:" CaptionPosition="CaptionPosition.Vertical" ColSpanLg="12">
                                                            <DxMemo @bind-Text="@prog.Observaciones" ShowValidationIcon="true" Rows="5" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="Obs. Almacén:" CaptionPosition="CaptionPosition.Vertical" ColSpanLg="12">
                                                            <DxMemo @bind-Text="@prog.ObsAlmacen" ShowValidationIcon="true" Rows="5" />
                                                        </DxFormLayoutItem>
                                                    </DxFormLayoutGroup>
                                                    <DxFormLayoutGroup Context="grupo2" ColSpanLg="6" Decoration="FormLayoutGroupDecoration.None">
                                                        <DxFormLayoutItem Caption="Datos Pedido:">
                                                            <DxTextBox @bind-Text="@prog.DatosPedido" ShowValidationIcon="true" ReadOnly="true" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="Id Maquina:" ColSpanLg="12">
                                                            <DxComboBox Data="@DatosMaquinas"
                                                                        FilteringMode="DataGridFilteringMode.Contains"
                                                                        TextFieldName="IdmaquinaG21"
                                                                        ValueFieldName="Idmaquina"
                                                                        ListRenderMode="ListRenderMode.Virtual"
                                                                        @bind-Value="prog.Idlinea">
                                                            </DxComboBox>
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="Hora Comienzo Estimada:" ColSpanLg="12">
                                                            <DxTimeEdit @bind-Time="@prog.HoraComienzoEstimada" ReadOnly="true" Format="t"
                                                                        ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                                                        ShowValidationIcon="true" Mask="@DateTimeMask.ShortTime" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="Hora Fin Estimada:" ColSpanLg="12">
                                                            <DxTimeEdit @bind-Time="@prog.HoraFinEstimada" ReadOnly="true" Format="t"
                                                                        ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                                                        ShowValidationIcon="true" Mask="@DateTimeMask.ShortTime" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="Id aplicacion:" ColSpanLg="12">
                                                            <DxComboBox Data="@DatosCodsApliLito"
                                                                        FilteringMode="DataGridFilteringMode.Contains"
                                                                        ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                                                        TextFieldName="Combinado"
                                                                        ValueFieldName="Idcodigoaplicacion"
                                                                        ListRenderMode="ListRenderMode.Virtual"
                                                                        @bind-Value="prog.Idaplicacion">
                                                            </DxComboBox>
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="Id producto:" ColSpanLg="12">
                                                            <DxComboBox Data="@DatosBarniz"
                                                                        FilteringMode="DataGridFilteringMode.Contains"
                                                                        ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                                                        TextFieldName="Idproducto"
                                                                        ValueFieldName="Idproducto"
                                                                        ListRenderMode="ListRenderMode.Virtual"
                                                                        @bind-Value="prog.Idproducto">
                                                            </DxComboBox>
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="Producto:" ColSpanLg="12">
                                                            <DxTextBox @bind-Text="@prog.Producto" ShowValidationIcon="true" ReadOnly="true" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="Aplicación Posterior:" ColSpanLg="12">
                                                            <DxComboBox Data="@DatosCodsApliLito" AllowUserInput="true"
                                                                        FilteringMode="DataGridFilteringMode.Contains"
                                                                        TextFieldName="Combinado" ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                                                        ValueFieldName="Idcodigoaplicacion"
                                                                        ValueChanged="@((int? newValue) => OnValueChanged(newValue, prog))"
                                                                        ListRenderMode="ListRenderMode.Virtual"
                                                                        Value="prog.Idaplicacionposterior" ValueExpression="@(() => prog.Idaplicacionposterior)">
                                                            </DxComboBox>
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="Peso Min:" ColSpanLg="3" CaptionPosition="CaptionPosition.Vertical">
                                                            <DxSpinEdit ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" ShowSpinButtons="false"
                                                                        MinValue="0" @bind-Value="@prog.PesoMin" ShowValidationIcon="true" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="Peso:" BeginRow="false" ColSpanLg="3" CaptionPosition="CaptionPosition.Vertical">
                                                            <DxSpinEdit ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" ShowSpinButtons="false"
                                                                        MinValue="0" @bind-Value="@prog.Peso" ShowValidationIcon="true" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="Flejar:" BeginRow="false" ColSpanLg="2" CaptionPosition="CaptionPosition.Vertical">
                                                            <DxCheckBox CssClass="d-inline-block" @bind-Checked="@prog.Flejar"
                                                                        ValueChecked="true" ValueUnchecked="false"
                                                                        @onclick="@(e => OnFlejarChanged(e, prog))" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="Temperatura:" BeginRow="false" ColSpanLg="3" CaptionPosition="CaptionPosition.Vertical">
                                                            <DxSpinEdit CssClass="d-inline-block" @bind-Value="@prog.TemperaturaSecado"
                                                                        ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" ShowSpinButtons="false" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="Obs. Calidad:" CaptionPosition="CaptionPosition.Vertical" ColSpanLg="12">
                                                            <DxMemo @bind-Text="@prog.ObsCalidad" ShowValidationIcon="true" Rows="5" />
                                                        </DxFormLayoutItem>
                                                    </DxFormLayoutGroup>
                                                    @*<DxFormLayoutItem Caption="Id programacion:" ColSpanLg="6">
                                                    <DxSpinEdit @bind-Value="@prog.Idprogramacion" ShowSpinButtons="false" ShowValidationIcon="true" ReadOnly="true" />
                                                    </DxFormLayoutItem>*@
                                                    <DxFormLayoutItem BeginRow="true" ColSpanMd="12">
                                                        <DxButton Context="btn" CssClass="btnSizeM izq" Click="@(() => EditarRegistro("anterior", prog))"
                                                                  IconCssClass="oi oi-arrow-thick-left" RenderStyle="ButtonRenderStyle.Info" />
                                                        <DxButton Context="btn" CssClass="btnSizeM der" Click="@(() => EditarRegistro("siguiente", prog))"
                                                                  IconCssClass="oi oi-arrow-thick-right" RenderStyle="ButtonRenderStyle.Info" />
                                                    </DxFormLayoutItem>
                                                </DxFormLayout>
                                            </EditFormTemplate>
                                            <DragHintTextTemplate>
                                                @{
                                                    if (GridProgramacion.DataItems.Count > 1)
                                                    {
                                                        var texto = string.Empty;
                                                        for (var i = 0; i < GridProgramacion.DataItems.Count; i++)
                                                        {
                                                            var item = GridProgramacion.DataItems[i];
                                                            var dataItem = (TablaProgramacionDTO)item;
                                                            texto += $" {(i != 0 ? " || " : "")}{dataItem.Idpedido}";
                                                        }
                                                        <span class="dxbl-text">@texto</span>
                                                    }
                                                    else
                                                    {
                                                        var dataItem = (TablaProgramacionDTO)GridProgramacion.DataItems[0];
                                                        <span class="dxbl-text">@dataItem.Idpedido</span>
                                                        <span class="dxbl-text">@dataItem.Posicion</span>
                                                        <span class="dxbl-text">@dataItem.DatosPedido</span>
                                                    }
                                                }
                                            </DragHintTextTemplate>
                                        </DxGrid>
                                    </DxLoadingPanel>
                                </DxFormLayoutGroup>
                            </Template>
                        </DxGridLayoutItem>
                        <DxGridLayoutItem Area="CintaBotones">
                            <Template>
                                <div class="gridlayout-ci gridlayout-item">
                                    <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="12">
	                                    <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="4">
		                                    <DxFormLayoutItem Caption="Desde:" Context="ddPos1" ColSpanLg="6" ColSpanXs="6">
			                                    @{
				                                    var value = CurrentMaquina?.PosicionDesde ?? 0;
				                                    var hasta = CurrentMaquina?.PosicionHasta ?? 0;
			                                    }
			                                    <DxSpinEdit Value="@value" Increment="10" MaxValue="@hasta" ValueChanged="@((int value) => OnUpdateDesde(value))" />
		                                    </DxFormLayoutItem>
		                                    <DxFormLayoutItem Context="botones" ColSpanLg="3" ColSpanXs="6">
			                                    <DxButton RenderStyle="ButtonRenderStyle.Light" Click="@SetUltimaPosicion"
			                                              Text="Ultima Posición" CssClass="" />
		                                    </DxFormLayoutItem>
		                                    <DxFormLayoutItem Context="btnImpProg" ColSpanLg="3" ColSpanXs="6" CssClass="px-0">
			                                    <DxButton Text="Imprimir Programación" CssClass=""
			                                              Click="@(() => ProcesoImpresion(false, false))"></DxButton>
		                                    </DxFormLayoutItem>
		                                    <DxFormLayoutItem Caption="Hasta:" Context="ddPos2" BeginRow="true" ColSpanLg="6" ColSpanXs="6">
			                                    @{
				                                    var value = CurrentMaquina?.PosicionHasta ?? 0;
				                                    var desde = CurrentMaquina?.PosicionDesde ?? 0;
			                                    }
			                                    <DxSpinEdit Value="@value" Increment="10" MinValue="@desde"
			                                                @ondblclick="@(SetMaximaPosicion)"
			                                                ValueChanged="@((int value) => OnUpdateHasta(value))" />
		                                    </DxFormLayoutItem>
		                                    <DxFormLayoutItem Context="check" ColSpanLg="6" ColSpanXs="6">
			                                    <DxCheckBox Alignment="CheckBoxContentAlignment.Right" LabelPosition="LabelPosition.Left"
			                                                @bind-Checked="@CheckDesgloseHorarios">Ver Desglose Horarios</DxCheckBox>
		                                    </DxFormLayoutItem>
	                                    </DxFormLayoutGroup>
                                        <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="8" CssClass="">
                                            <DxFormLayoutItem ColSpanXs="12">
                                                <Template Context="layoutContext">
                                                    <div style="display: flex; flex-wrap: wrap; gap: 8px; align-items: flex-start;">
                                                        <div style="display: flex; flex-wrap: wrap; gap: 8px;">
	                                                        <DxButton RenderStyle="ButtonRenderStyle.Primary" Text="Calcular Horarios" CssClass=""
	                                                                  Click="@((e) => CalcularHorarios())" />
	                                                        <DxButton RenderStyle="ButtonRenderStyle.Info" Text="Comprueba Programaciones" CssClass=""
	                                                                  Click="@ComprobarProgramacion" />
	                                                        <DxButton RenderStyle="ButtonRenderStyle.Warning" Text="Renumerar" CssClass=""
	                                                                  Click="@Renumerar" />
                                                        </div>

                                                        <div style="display: flex; flex-direction: column; gap: 8px;">
	                                                        <DxButton RenderStyle="ButtonRenderStyle.Success" Text="Enviar a PROTEO" CssClass=""
	                                                                  Click="@EnviarProteo" Enabled="@(!esDesarrollo)" />
                                                            <DxButton Text="Publicar" CssClass=""
                                                                      Click="@(() => ProcesoImpresion(false, true))" />
                                                        </div>

                                                        <div style="display: flex; flex-wrap: wrap; gap: 8px;">
	                                                        <DxButton RenderStyle="ButtonRenderStyle.Warning" Text="Recalcular Posiciones" CssClass=""
	                                                                  Click="@RecalcularPosiciones" />
                                                        </div>
                                                        <div style="display: flex; flex-direction: column; gap: 8px;">
                                                            <DxButton RenderStyle="ButtonRenderStyle.Danger" Text="Combinado (Imp)" CssClass=""
                                                                      Click="@(() => ProcesoImpresion(true, false))"></DxButton>
                                                            <DxButton RenderStyle="ButtonRenderStyle.Danger" Text="Combinado (Publi)" CssClass=""
                                                                      Click="@(() => ProcesoImpresion(true, true))"></DxButton>
                                                        </div>
                                                        <div style="display: flex; flex-wrap: wrap; gap: 8px;">
	                                                        <DxButton Click="@(GridLitoFoto_ExportXlsx)" RenderStyle="ButtonRenderStyle.Info">Exportar Litografía</DxButton>
	                                                        <DxButton Click="@(GridLitoAsignada_ExportXlsx)" RenderStyle="ButtonRenderStyle.Info">Exportar Lito Asignada</DxButton>
	                                                        <DxButton Click="@(GridProg_ExportXlsx)" RenderStyle="ButtonRenderStyle.Info">Exportar Programación</DxButton>
                                                        </div>
                                                    </div>
                                                </Template>
                                            </DxFormLayoutItem>
                                        </DxFormLayoutGroup>
                                    </DxFormLayoutGroup>
                                </div>
                            </Template>
                        </DxGridLayoutItem>
                    </Items>
                </DxGridLayout>
            </DxFormLayout>
        </div>
    </Authorized>
    <NotAuthorized>
        <NoPuedesPasar />
    </NotAuthorized>
</AuthorizeView>

<BasicPopUp PopUpVisibleEventCallback="PopUpVisibleHandler"
            ListaMensajes="@ListaMensajes"
            WidthSize="1200"
            PopupVisible="PopupBasicoVisible"
            Codigo="2222"
            Contenido="@_contenido">

</BasicPopUp>


<DxPopup HeaderText="Observaciones de Programación" ZIndex="2000" MinWidth="1000"
         @bind-Visible="@_isObsProgPopupVisible" AllowDrag="true"
         CloseOnEscape="false" ShowFooter="true"
         CloseOnOutsideClick="false">
    <BodyContentTemplate>
        <div>
            <p>@((MarkupString)_listadoNotificaciones.ToString())</p>
        </div>
    </BodyContentTemplate>
    <FooterContentTemplate Context="footer">
        <DxButton Click="@(() => NotificacionesRevisadas(true))" RenderStyle="ButtonRenderStyle.Success" CssClass="float-right me-2 accept-button">Aceptar</DxButton>
        <DxButton Click="@(() => NotificacionesRevisadas(false))" RenderStyle="ButtonRenderStyle.Danger" CssClass="float-right cancel-button">Cancelar</DxButton>
    </FooterContentTemplate>
</DxPopup>


<DxWindow @ref="_windowRef"
          AllowResize="true"
          AllowDrag="true"
          ShowCloseButton="true"
          CloseOnEscape="true"
          MinHeight="calc(100vh - 100px)" MinWidth="calc(100vw - 100px)"
          @bind-Visible="_windowVisible">
    <BodyTextTemplate>
        <ReportViewer ParamNombreReport="@ParameterNombreReport" />
    </BodyTextTemplate>
</DxWindow>

<BulkChangesPopUp PopUpBulkVisibleEventCallback="PopUpBulkVisibleHandler"
                  AplicarCambiosEventCallback="AplicarCambiosMasivos"
                  Maquinas="@DatosMaquinas"
                  Barnices="@DatosBarniz"
                  CambiosMasivosPopUpVisible="_isCambiosMasivosPopUpVisible" />
@code
{
    [Parameter]
    public string Maquina { get; set; }
    public int VisibleIndex { get; set; }
    public string ParameterNombreReport { get; set; }
    public int NumPedidosBarnizado { get; set; }
    public int NumPedidosLitografia { get; set; }

    [CascadingParameter]
    private Task<AuthenticationState> authenticationStateTask { get; set; }

    private HubConnection _hubConnection;
    private ClaimsPrincipal User { get; set; }
    private bool _isSmallScreen;
    private bool _wetOnWet;
    private bool _wetOnWetReadOnly;
    private bool PopupBasicoVisible { get; set; }
    private string _contenido;
    private bool _windowVisible;
    private bool _checkUltimo;
    private bool CheckDesgloseHorarios { get; set; }
    private StringBuilder _listadoNotificaciones = new();
    private bool _isObsProgPopupVisible;

    private bool _isCambiosMasivosPopUpVisible = false;
    private bool _notificacionesAceptadas;
    private bool PanelVisible { get; set; }
    private bool PanelVisibleFoto { get; set; }
    private bool PanelVisibleLito { get; set; }

    IReadOnlyList<object> _selectedPedidosLitoDataItems = new List<object>();
    IReadOnlyList<object> _selectedAImprimirDataItems = new List<object>();

    private List<string> ListaMensajes { get; set; }

    Guid MyKey { get; set; } = Guid.NewGuid();
    DxGrid? GridProg;
    DxGrid? GridLitoFoto;
    DxGrid? GridLitoAsignada;
    DxWindow _windowRef;
    DxComboBox<MaquinaDTO, MaquinaDTO> _componentMaquina;

    ElementReference _popupTarget;

    ListResult<MaquinaDTO> ResultMaquinas { get; set; }
    ListResult<CodigoAplicacionDTO> ResultCodsApliLito { get; set; }
    ListResult<ProductoDTO> ResultBarniz { get; set; }
    ListResult<DatosGeneralesPedidoFotomecanicaLitoDTO> ResultPedidosFotoLito { get; set; }
    ListResult<DatosGeneralesPedidoFotomecanicaLitoDTO> ResultPedidosLito { get; set; }
    ListResult<TablaProgramacionDTO> ResultProgramacion { get; set; }

    List<MaquinaDTO> DatosMaquinas { get; set; }
    List<CodigoAplicacionDTO> DatosCodsApliLito { get; set; }
    List<ProductoDTO> DatosBarniz { get; set; }
    List<DatosGeneralesPedidoFotomecanicaLitoDTO> DatosPedidosFotoLito { get; set; }
    List<DatosGeneralesPedidoFotomecanicaLitoDTO> DatosPedidosLito { get; set; }
    List<DatosGeneralesPedidoFotomecanicaLitoDTO> FiltroFotoLito = new();
    List<DatosGeneralesPedidoFotomecanicaLitoDTO> FiltroLito = new();
    ObservableCollection<TablaProgramacionDTO> DatosProgramacion { get; set; }
    MaquinaDTO CurrentMaquina { get; set; }

    DotNetObjectReference<ProgramacionLitografia> _dotNetHelper { get; set; }
    IJSObjectReference _jsModule { get; set; }

    readonly TextoAzulLitografia _currentTextoAzul = new();
    readonly TextoAzulFotoLito _currentTextoAzulFotoLito = new();
    private bool esDesarrollo { get; set; }

    JsonSerializerOptions jsonSerializerOptions = new JsonSerializerOptions
        {
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
        };
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            _jsModule = await Js.InvokeAsync<IJSObjectReference>("import", $"./Pages/ProgramacionLitografia.razor.js?v={DateTime.Now}");
            var client = HttpClientFactory.CreateClient("DatabaseAPI");
            esDesarrollo = Convert.ToBoolean(await client.GetStringAsync("ConsultaDB/current-database"));
        }
    }

    protected override async Task OnInitializedAsync()
    {
        SpinnerService.Show();
        
        var authstate = await AuthState.GetAuthenticationStateAsync();
        User = authstate.User;
        _hubConnection = new HubConnectionBuilder()
	        .WithUrl(Navigation.ToAbsoluteUri("/imprimirHub"))
	        .Build();

        await _hubConnection.StartAsync();
        _hubConnection.On<SingleResultHub<int>>(TiposMensajesImprimirHub.ProgramacionActualizada_Emisor, (resultHub) =>
        {
	        if (resultHub.Errors.Any())
		        ToastService.ShowError(resultHub.Errors.First().Mensaje);
	        else if (resultHub.Info.Any())
		        ToastService.ShowSuccess(resultHub.Info.First().Mensaje);

	        SpinnerService.Hide();
        });

        ResultMaquinas = await Http.GetFromJsonAsync<ListResult<MaquinaDTO>>($"Maquinas/dropdownbytipo/{Enums.TipoMaquina.Impresora}");
        ResultCodsApliLito = await Http.GetFromJsonAsync<ListResult<CodigoAplicacionDTO>>("DatosGenerales/getcodsaplicaciondropdown?tipo=T");
        ResultBarniz = await Http.GetFromJsonAsync<ListResult<ProductoDTO>>("DatosGenerales/getdatosproductos");

        if (ResultMaquinas.Errors.Any() || ResultCodsApliLito.Errors.Any() || ResultBarniz.Errors.Any())
        {
            var errors = ResultMaquinas.Errors.Concat(ResultCodsApliLito.Errors).Concat(ResultBarniz.Errors);
            foreach (var error in errors)
            {
                ToastService.ShowError($"{error}");
            }
        }
        else
        {
            DatosMaquinas = ResultMaquinas.Data;
            DatosCodsApliLito = ResultCodsApliLito.Data;
            DatosBarniz = ResultBarniz.Data;
            var maquinaId = await localStorage.GetItemAsync<int?>("maquinaIdLito");
            CurrentMaquina = DatosMaquinas.FirstOrDefault(o => o.Idmaquina == maquinaId) ?? DatosMaquinas.First();
            _wetOnWet = CurrentMaquina.WetonWet;
            _wetOnWetReadOnly = CurrentMaquina.WetonWet;
            await ActualizarGridLitoYProg();
        }
        SpinnerService.Hide();
    }

    private async Task ActualizarGridLitoSuperior()
    {
        PanelVisibleFoto = true;
        ResultPedidosFotoLito = await Http.GetFromJsonAsync<ListResult<DatosGeneralesPedidoFotomecanicaLitoDTO>>($"Pedido/getpedidosfotolito");
        if (ResultPedidosFotoLito.Errors.Any())
        {
            foreach (var error in ResultPedidosLito.Errors)
            {
                ToastService.ShowError($"{error}");
            }
        }
        else
        {
            DatosPedidosFotoLito = ResultPedidosFotoLito.Data;
            ActualizarTextosAzulGridSup();
        }
        PanelVisibleFoto = false;
    }
    private async Task ActualizarGridLitoYProg(bool limpiarFiltros = true)
    {
        ResultPedidosLito = await Http.GetFromJsonAsync<ListResult<DatosGeneralesPedidoFotomecanicaLitoDTO>>($"Pedido/getpedidoslitobymaquina?idMaquina={CurrentMaquina?.Idmaquina}");
        ResultProgramacion = await Http.GetFromJsonAsync<ListResult<TablaProgramacionDTO>>($"Pedido/getprogramacionbymaquina?maquina={CurrentMaquina?.Idmaquina}&ultimo={_checkUltimo}");

        if (ResultPedidosLito.Errors.Any() || ResultProgramacion.Errors.Any())
        {
            var errors = ResultPedidosLito.Errors.Concat(ResultProgramacion.Errors);
            foreach (var error in errors)
            {
                ToastService.ShowError($"{error}");
            }
        }
        else
        {
            if (limpiarFiltros)
            {
                GridProg.ClearFilter();
                GridLitoAsignada.ClearFilter();
            }
            DatosPedidosLito = ResultPedidosLito.Data;
            DatosProgramacion = new ObservableCollection<TablaProgramacionDTO>(ResultProgramacion.Data);
            ActualizarTextosAzulGridInt();
        }
    }

    private void ActualizarTextosAzulGridSup(bool filtrado = false)
    {
        var datos = filtrado ? FiltroFotoLito : DatosPedidosFotoLito;
        _currentTextoAzulFotoLito.PasesTotales = datos?.Sum(o => o.PasesTotales) ?? 0;
        _currentTextoAzulFotoLito.HTotales = datos?.Sum(o => o.HojasPedido) ?? 0;
    }

    private void ActualizarTextosAzulGridInt(bool filtrado = false)
    {
        PanelVisibleLito = true;
        var datos = filtrado ? FiltroLito : DatosPedidosLito;
        if (datos.Any())
        {
            // NO QUEREMOS PASES TOTALES
            // Se calcularán las veces que tienen que pasar las hojas por la maquina, segun las notas del procedure DevuelveNumeroColores.
            // Aqui tendremos unicamente si es x1 o x2, según las restricciones y notas.
            // Pases totales pasaran a ser HojasTotales que seran las hojas del pedido por ese x1 o x2
            // Los dias de trabajo serán las hojastotales (nuevas) / velocidad de maquina
            // (según sea la columna MD, Scroll o Velocidad 2 Pasasdas o Velocidad Alu) Estos valores se scana del pedido.
            // MD es velocidad media, por defecto, srcoll es scroll si las hojas han sido x2, pues por esa columna y si es Alu por esa columna y luego todo *24
            //Quedaran los campos llamados Hojas Totales; Hojas Efectivas (que son las multiplicadas); Dias Trabajo; Prevision Final
            // _currentTextoAzul.PasesTotales = datos?.Sum(o => o.PasesTotales) ?? 0;
            _currentTextoAzul.HTotales = datos?.Sum(o => o.HojasPedido - o.HojasTerminadas) ?? 0;
            _currentTextoAzul.HojasEfectivas = datos?.Sum(o => o.HojasPedido * o.MultiplicadorHojas) ?? 0;
            _currentTextoAzul.DiasTrabajo = datos?.Sum(o => (o.HojasPedido * o.MultiplicadorHojas) /
                                                           ((CurrentMaquina.PorcentajeTiempoTirada / 100) * o.VelocidadParaPedido * 24)) ?? 0;
            // _currentTextoAzul.DiasTrabajo = (datos?.Sum(o => o.PasesTotales) /
            //                                  ((CurrentMaquina.PorcentajeTiempoTirada / 100) * CurrentMaquina.PasesHora * 24)) ?? 0;
            _currentTextoAzul.PrevisionFinal = DatosProgramacion?.Where(tp => tp.Idlinea == CurrentMaquina.Idmaquina)
                .Max(tp => tp.HoraFinEstimada)?.AddDays(_currentTextoAzul.DiasTrabajo.Value) ?? null;
        }
        else
        {
            _currentTextoAzul.PasesTotales = 0;
            _currentTextoAzul.HTotales = 0;
            _currentTextoAzul.HojasEfectivas = 0;
            _currentTextoAzul.DiasTrabajo = 0;
            _currentTextoAzul.PrevisionFinal = null;
        }
        PanelVisibleLito = false;
    }

    private void PopUpVisibleHandler(bool cerrar)
    {
        PopupBasicoVisible = false;
    }

    private async Task OnButtonGroupClick(string texto)
    {
        var newIndex = DatosMaquinas.IndexOf(CurrentMaquina) + (texto == "PrevMaquina" ? -1 : 1);
        if (newIndex >= 0 && newIndex < DatosMaquinas.Count)
        {
            var newMaquina = DatosMaquinas[newIndex];
            await OnUpdateMaquina(newMaquina);
        }
    }
    private async Task OnItemClick(ToolbarItemClickEventArgs e)
    {
        var clicked = e.ItemName;
        var newIndex = DatosMaquinas.IndexOf(CurrentMaquina) + (clicked == "PrevMaquina" ? -1 : 1);
        if (newIndex >= 0 && newIndex < DatosMaquinas.Count)
        {
            var newMaquina = DatosMaquinas[newIndex];
            await OnUpdateMaquina(newMaquina);
        }
    }

    private async Task OnUpdateMaquina(MaquinaDTO m)
    {
        SpinnerService.Show();
        if (m != CurrentMaquina)
        {
            CurrentMaquina = m;
            CurrentMaquina.PosicionHasta = 9999999;

        }
        await ActualizarGridLitoYProg();
        _wetOnWet = CurrentMaquina.WetonWet;
        _wetOnWetReadOnly = CurrentMaquina.WetonWet;
        _selectedPedidosLitoDataItems = new List<object>();
        _selectedAImprimirDataItems = new List<object>();
        await localStorage.SetItemAsync("maquinaIdLito", CurrentMaquina.Idmaquina);
        SpinnerService.Hide();
    }
    private StringBuilder ConstruirMensaje(string texto)
    {
        var sb = new StringBuilder();
        sb.Append("&nbsp;&nbsp;&nbsp;").Append(texto).Append("<br>");
        return sb;
    }

    private async Task MoverEntreGrids(bool programar)
    {
        var error = false;
        SpinnerService.Show();
        if (programar)
        {
            if (CurrentMaquina == null)
            {
                ToastService.ShowWarning("Se debe seleccionar una Máquina.");
                await _componentMaquina.FocusAsync();
                return;
            }
            if (!_selectedPedidosLitoDataItems.Any())
            {
                ToastService.ShowWarning("Debes seleccionar como mínimo un pedido.");
                return;
            }

            var pedidosSeleccionados = _selectedPedidosLitoDataItems.Cast<DatosGeneralesPedidoFotomecanicaLitoDTO>().ToList();

            _listadoNotificaciones = new StringBuilder();

            foreach (var p in pedidosSeleccionados)
            {
                var texto = new StringBuilder();
                var obs = $"{p.Obs1}, {p.Obs2}, {p.Obs3}";

                if (obs.Contains("USAR") || obs.Contains("PARAD"))
                {
                    texto.AppendLine();
                    texto.Append(ConstruirMensaje($"Observaciones: {obs}"));
                }
                if (!string.IsNullOrWhiteSpace(p.ObsProgramacion))
                {
                    texto.AppendLine();
                    texto.Append(ConstruirMensaje($"Observaciones Programación: {p.ObsProgramacion}"));
                }
                if (!string.IsNullOrWhiteSpace(p.ObservacionesAplicacion))
                {
                    texto.AppendLine();
                    texto.Append(ConstruirMensaje($"Observaciones Aplicación: {p.ObservacionesAplicacion}"));
                }
                if (!string.IsNullOrEmpty(p.Estado) && p.Estado.Contains("PDTE HJLTA"))
                {
                    texto.AppendLine();
                    texto.Append(ConstruirMensaje($"<strong>ATENCIÓN: El pedido {p.IdPedido} NO TIENE HOJALATA ASIGNADA.</strong>"));
                }
                if (!string.IsNullOrEmpty(p.Estado) && p.Estado.Contains("PARADO"))
                {
                    texto.AppendLine();
                    texto.Append(ConstruirMensaje($"<strong>ATENCIÓN: El pedido {p.IdPedido} está PARADO.</strong>"));
                }

                if (texto.Length <= 0)
                    continue;
                _listadoNotificaciones.Append($"<strong>Pedido {p.IdPedido}</strong>:").Append("<br>");
                _listadoNotificaciones.Append(texto).Append("<br>");
            }
            if (_listadoNotificaciones.Length > 0)
            {
                _notificacionesAceptadas = false;
                _isObsProgPopupVisible = true;
                SpinnerService.Hide();
                await Task.Run(async () =>
                {
                    while (_isObsProgPopupVisible && !_notificacionesAceptadas)
                    {
                        await Task.Delay(250); // Espera un poco antes de verificar nuevamente si el popup está visible
                    }
                });
            }
            else
            {
                _isObsProgPopupVisible = false;
                _notificacionesAceptadas = true;
                _listadoNotificaciones = new StringBuilder();
            }

            if (!string.IsNullOrEmpty(_listadoNotificaciones.ToString()))
            {
                ToastService.ShowInfo($"Las notificaciones han sido: {(_notificacionesAceptadas ? "Aceptadas" : "Rechazadas")}");
            }
            if (!_notificacionesAceptadas)
            {
                return;
            }
            _listadoNotificaciones = new StringBuilder();
            // LLAMADA_API: Realiza la última llamada al API para finalizar
            SpinnerService.Show();
            var datosProgramacion = new ProgramarPedidoDTO
                {
                    Barniz = null,
                    CodigoAplicacion = null,
                    Maquina = CurrentMaquina,
                    PedidosLito = pedidosSeleccionados,
                    WetOnWet = _wetOnWet
                };
            var jsonString = JsonSerializer.Serialize(datosProgramacion, jsonSerializerOptions);
            HttpContent content = new StringContent(jsonString, Encoding.UTF8, "application/json");

            Console.WriteLine(jsonString);
            var response = await Http.PostAsync($"Pedido/programarpedidosbytipo?tipo={Enums.TipoPedido.Litografia}", content);
            var result = await response.Content.ReadFromJsonAsync<SingleResult<int>>();

            if (result.HasErrors)
            {
                ToastService.ShowError($"{result.Errors.First()}");
            }
            else
            {
                if (result.HasInfo)
                    ToastService.ShowInfo($"{string.Join(" -- ", result.Info)}");
                else
                    ToastService.ShowSuccess($"Programación actualizada: {result.Data} pedidos nuevos.");
                _selectedPedidosLitoDataItems = new List<object>();
                _selectedAImprimirDataItems = new List<object>();
            }
        }
        else
        {
            var dict = _selectedAImprimirDataItems.Cast<TablaProgramacionDTO>().ToDictionary(item => item.Idprogramacion, item => item.Idpedido.Value);
            if (dict.Count > 10)
            {
                var confirmed = await Js.InvokeAsync<bool>("confirm", "Se van a subir mas de 10 registros. ¿Estás seguro?");
                if (!confirmed)
                {
                    return;
                }
            }
            var jsonString = JsonSerializer.Serialize(dict);
            HttpContent content = new StringContent(jsonString, Encoding.UTF8, "application/json");
            var response = await Http.PostAsync($"Programacion/borrarprogramaciones/{Enums.TipoPedido.Litografia}", content);
            var result = await response.Content.ReadFromJsonAsync<SingleResult<int>>();

            if (result.Errors.Any())
            {
                ToastService.ShowError($"{result.Errors.First()}");
            }
            else
            {
                _selectedPedidosLitoDataItems = new List<object>();
                _selectedAImprimirDataItems = new List<object>();
                ToastService.ShowSuccess($"Programación eliminada y PROTEO actualizado.");
            }
        }

        await ActualizarGridLitoYProg(false);
        SpinnerService.Hide();
    }

    private async Task RecalcularPosiciones()
    {
        PanelVisible = true;
        //SpinnerService.Show();
        if (DatosProgramacion.Count > 10)
        {
            var confirmed = await Js.InvokeAsync<bool>("confirm", "Se van a procesar mas de 10 registros. ¿Estás seguro?");
            if (!confirmed)
            {
                return;
            }
        }
        if (DatosProgramacion.Any(o => o.Posicion % 10 != 0))
        {
            var confirmed = await Js.InvokeAsync<bool>("confirm", "ATENCION Hay pedidos con posiciones intermedias para renumerar. ¿Fistro pecador, estás seguro?");
            if (!confirmed)
            {
                return;
            }

        }
        var pedidoIndexCodigo = await Js.InvokeAsync<Dictionary<int, string>>("getIndexYCodigo");
        var pedidoIndexCodigoList = pedidoIndexCodigo.Select(kv => new PedidoIndexCodigoDTO { Index = kv.Key, Codigo = kv.Value }).ToList();

        var jsonString = JsonSerializer.Serialize(pedidoIndexCodigoList/*, jsonSerializerOptions*/);

        HttpContent content = new StringContent(jsonString, Encoding.UTF8, "application/json");
        Console.WriteLine(jsonString);
        var response = await Http.PostAsync($"Programacion/recalcular?maquina={CurrentMaquina.Idmaquina}&desde={CurrentMaquina.PosicionDesde}", content);
        var resultEdit = await response.Content.ReadFromJsonAsync<SingleResult<int>>();
        var testCheck = response.Content.ReadAsStringAsync();

        if (resultEdit.Errors.Any())
        {
            foreach (var error in resultEdit.Errors)
            {
                ToastService.ShowError($"{error}");
            }
        }
        else
        {
            DatosProgramacion = null;
            _selectedAImprimirDataItems = new List<object>();
            DatosProgramacion = new ObservableCollection<TablaProgramacionDTO>();
            StateHasChanged();
            await ActualizarGridProgramacion();
        }

        PanelVisible = false;
        //SpinnerService.Hide();
    }
    private async Task ActualizarGridProgramacion()
    {
        ResultProgramacion = await Http.GetFromJsonAsync<ListResult<TablaProgramacionDTO>>($"Pedido/getprogramacionbymaquina?maquina={CurrentMaquina?.Idmaquina}&ultimo={_checkUltimo}");
        DatosProgramacion = new ObservableCollection<TablaProgramacionDTO>(ResultProgramacion.Data);
    }
    private async Task ComprobarProgramacion()
    {
        PanelVisible = true;
        ListaMensajes = new List<string>();

        var response = await Http.GetFromJsonAsync<ListResult<string>>($"Programacion/comprobarprogramacion?maquina={CurrentMaquina.Idmaquina}");

        if (response.Errors.Any())
        {
            foreach (var error in response.Errors)
            {
                ToastService.ShowError($"{error}");
            }
        }
        else
        {
            ListaMensajes = response.Data;
            _contenido = "Mensajes";
            PopupBasicoVisible = true;
        }
        //SpinnerService.Hide();
        PanelVisible = false;
    }
    private async Task Renumerar()
    {

        PanelVisible = true;
        //SpinnerService.Show();
        if (DatosProgramacion.Count > 10)
        {
            var confirmed = await Js.InvokeAsync<bool>("confirm", "Se van a procesar mas de 10 registros. ¿Estás seguro?");
            if (!confirmed)
            {
                return;
            }
        }
        var response = await Http.GetFromJsonAsync<SingleResult<int>>($"Programacion/renumerar?maquina={CurrentMaquina.Idmaquina}&desde={CurrentMaquina.PosicionDesde.Value}");


        if (response.Errors.Any())
        {
            foreach (var error in response.Errors)
            {
                ToastService.ShowError($"{error}");
            }
        }
        else
        {
            await ActualizarGridProgramacion();
        }
        PanelVisible = false;
        //SpinnerService.Hide();
    }
    private async Task EnviarProteo()
    {
        SpinnerService.Show();
        var response = await Http.PostAsJsonAsync($"Programacion/enviarprogramacionproteobymaquina", CurrentMaquina.Idmaquina);
        var result = await response.Content.ReadFromJsonAsync<SingleResult<int>>();
        if (result.HasErrors)
        {
            ToastService.ShowError($"{result.Errors.First()}");
        }
        else
        {
            ToastService.ShowSuccess($"Programación enviada a PROTEO");
        }
        SpinnerService.Hide();
    }

    private async Task CalcularHorarios()
    {
        PanelVisible = true;
        ListaMensajes = new List<string>();
        var responseCalculoHorarios = await Http.GetFromJsonAsync<SingleResult<int>>($"Programacion/calcularhorarios?tipo={Enums.TipoPedido.Litografia}&maquina={CurrentMaquina.Idmaquina}");

        if (responseCalculoHorarios.HasErrors)
        {
            ToastService.ShowError($"{responseCalculoHorarios.Errors.First()}");
        }
        else
        {
            if (responseCalculoHorarios.HasInfo)
            {
                ListaMensajes = (List<string>?)responseCalculoHorarios.Info;
                _contenido = "Mensajes";
                PopupBasicoVisible = true;

            }

            ToastService.ShowSuccess($"Datos de Horarios actualizados.");
            _selectedAImprimirDataItems = new List<object>();
            DatosProgramacion = new ObservableCollection<TablaProgramacionDTO>();
            StateHasChanged();
            await Task.Delay(100);
            await ActualizarGridProgramacion();
            StateHasChanged();
        }
        //SpinnerService.Hide();
        PanelVisible = false;
    }

    private async Task OnUpdateDesde(int value)
    {
        CurrentMaquina.PosicionDesde = value;
        if (!_checkUltimo)
            await ActualizarDatosMaquina(true);
    }

    private async Task OnUpdateHasta(int value)
    {
        CurrentMaquina.PosicionHasta = value;
        if (!_checkUltimo)
            await ActualizarDatosMaquina(true);
    }

    private async Task ActualizarDatosMaquina(bool soloProgramacion = false)
    {
        SpinnerService.Show();
        var newDatosMaquina = CurrentMaquina;
        var response = await Http.PostAsJsonAsync($"DatosGenerales/updatedatosmaquina", newDatosMaquina);
        var resultUpdateDatosMaquinas = await response.Content.ReadFromJsonAsync<SingleResult<int>>();
        if (resultUpdateDatosMaquinas.HasErrors)
        {
            ToastService.ShowError($"{resultUpdateDatosMaquinas.Errors.First()}");
            return;
        }

        //TODO: Estaría bien standarizar bien las respuestas y hacer algo tipo:https://stackoverflow.com/a/55741259
        ToastService.ShowSuccess($"Datos de la máquina {CurrentMaquina.IdmaquinaG21} actualizados.");
        var item = DatosMaquinas.Single(i => i.Idmaquina == CurrentMaquina.Idmaquina);
        int index = DatosMaquinas.IndexOf(item);
        DatosMaquinas[index] = newDatosMaquina;
        if (soloProgramacion)
        {
            await ActualizarGridProgramacion();
        }
        else
        {
            await ActualizarGridLitoYProg();
        }
        SpinnerService.Hide();

    }
    private async Task SetUltimaPosicion(MouseEventArgs obj)
    {
        PanelVisible = true;
        //SpinnerService.Show();
        _checkUltimo = true;

        await ActualizarGridProgramacion();

        if (ResultPedidosLito.Errors.Any())
        {
            foreach (var error in ResultPedidosLito.Errors)
            {
                ToastService.ShowError($"{error}");
            }
        }
        else
        {
            CurrentMaquina.PosicionHasta = 9999999;
            CurrentMaquina.PosicionDesde = DatosProgramacion.Max(o => o.Posicion.Value);
            var response = await Http.PostAsJsonAsync($"DatosGenerales/updatedatosmaquina", CurrentMaquina);
        }
        _checkUltimo = false;
        //SpinnerService.Hide();
        PanelVisible = false;
    }

    private void NotificacionesRevisadas(bool aceptadas)
    {
        _isObsProgPopupVisible = false;
        _notificacionesAceptadas = aceptadas;
        _listadoNotificaciones = new StringBuilder();
    }

    async Task ProcesoImpresion(bool combinado, bool publicar)
    {
        SpinnerService.Show();
        var pedidosSeleccionados = DatosProgramacion.ToList();
        if (!pedidosSeleccionados.Any())
        {
            ToastService.ShowWarning("No hay pedidos seleccionados para imprimir");
            return;
        }
        var obj = new ImprimirPedidosDTO
            {
                Maquina = CurrentMaquina,
                Posiciones = pedidosSeleccionados.Select(o => o.Posicion.Value).ToList()
            };
        var jsonString = JsonSerializer.Serialize(obj, jsonSerializerOptions);
        HttpContent content = new StringContent(jsonString, Encoding.UTF8, "application/json");
        string nombreReporte = combinado ? "CombinadoReportV3" : "LitografiaReport";
        var response = combinado
            ? await Http.PostAsync($"Pedido/generardatosimpresion/{Enums.TipoPedido.Combinado}?esProgramacionPorPantalla={publicar}&nombreReporte={nombreReporte}", content)
            : await Http.PostAsync($"Pedido/generardatosimpresion/{Enums.TipoPedido.Litografia}?esProgramacionPorPantalla={publicar}&nombreReporte={nombreReporte}", content);
        var result = await response.Content.ReadFromJsonAsync<SingleResult<int>>();

        if (result.HasErrors)
        {
            ToastService.ShowError($"{result.Errors.First()}");
            SpinnerService.Hide();
        }
        else
        {
	        if (publicar)
	        {
		        try
		        {
                    // antes de mandar el mensaje de programación actualizada, nos aseguramos de que estamos conectados al hub
			        if (_hubConnection?.State != HubConnectionState.Connected)
			        {
				        ToastService.ShowWarning("Reconectando con el servidor...");
				        await _hubConnection.StartAsync();
			        }
			        await _hubConnection.InvokeAsync("SendProgramacionActualizada", CurrentMaquina.Idmaquina, nombreReporte, User.Identity?.Name);
		        }
		        catch (Exception ex)
		        {
			        // Mostrar el mensaje de error del servidor
			        ToastService.ShowError($"{(!string.IsNullOrEmpty(ex.InnerException?.Message) ? ex.InnerException?.Message : ex.Message)}");
		        }
	        }
	        else
	        {
		        ParameterNombreReport = combinado
			        //? $"ReporteCombinado?MaquinaNombreParameter={CurrentMaquina.IdmaquinaG21}&VerCambiosParameter={CheckDesgloseHorarios}&WoWParameter={CurrentMaquina.WetonWet}"
			        ? $"ReporteCombinadoV3?MaquinaNombreParameter={CurrentMaquina.IdmaquinaG21}" +
			          $"&VerCambiosParameter={CheckDesgloseHorarios}" +
			          $"&WoWParameter={CurrentMaquina.WetonWet}" +
			          $"&OcultarCabeceraParameter={true.ToString()}" +
			          $"&IdMaquinaParameter={CurrentMaquina.Idmaquina}" +
			          $"&EsTipoProgramacionPorPantallaParameter={false}"
			        : $"ReporteLitografia?MaquinaNombreParameter={CurrentMaquina.IdmaquinaG21}" +
			          $"&VerCambiosParameter={CheckDesgloseHorarios}" +
			          $"&WoWParameter={CurrentMaquina.WetonWet}" +
			          $"&IdMaquinaParameter={CurrentMaquina.Idmaquina}" +
			          $"&PosicionParameter=0" +
			          $"&EsTipoProgramacionPorPantallaParameter={false}";
		        if (_windowVisible)
			        await _windowRef.CloseAsync();
		        else
			        await _windowRef.ShowAtAsync(_popupTarget);
		        _selectedAImprimirDataItems = new List<object>();
		        CheckDesgloseHorarios = false;
		        SpinnerService.Hide();
	        }
        }
    }

    async Task Grid_EditModelSaving(GridEditModelSavingEventArgs e)
    {
        SpinnerService.Show();
        var editableProg = (TablaProgramacionDTO)e.EditModel;
        var currentProg = (TablaProgramacionDTO)e.DataItem;

        //Hack
        editableProg.DatosPedProceso = null;

        var jsonString = JsonSerializer.Serialize(editableProg, jsonSerializerOptions);

        HttpContent content = new StringContent(jsonString, Encoding.UTF8, "application/json");
        Console.WriteLine(jsonString);

        var response = await Http.PutAsync($"Programacion/{editableProg.Posicion.Value}/edit", content);
        var resTest = await response.Content.ReadAsStringAsync();
        var resultEdit = await response.Content.ReadFromJsonAsync<SingleResult<int>>();

        if (resultEdit.Errors.Any())
        {
            ToastService.ShowError($"Error al editar la posicion {editableProg.Posicion.Value}: {resultEdit.Errors.First()}");
        }
        else
        {
            var extraTexto = CurrentMaquina.Idmaquina == 17 ? "Verificar temperatura de secado del T4, ¿debe ser la del T3?" : string.Empty;
            ToastService.ShowSuccess($"Posición {editableProg.Posicion.Value} editada correctamente. {extraTexto}");
            _selectedAImprimirDataItems = new List<object>();
            await ActualizarGridProgramacion();
        }
        SpinnerService.Hide();
    }

    private async Task StartEditing(int index)
    {
        VisibleIndex = index;
        await GridProg.StartEditRowAsync(index);
    }
    private async Task EditarRegistro(string posicion, TablaProgramacionDTO datosGrid)
    {
        await GridProg.SaveChangesAsync();

        if (posicion == "anterior")
            VisibleIndex--;
        else
            VisibleIndex++;

        if (VisibleIndex < 0)
            ToastService.ShowWarning($"Estás en el primer registro, no se puede retroceder más");
        else if (VisibleIndex >= DatosProgramacion.Count)
            ToastService.ShowWarning($"Estás en el último registro, no se puede avanzar más");
        else
        {
            await GridProg.StartEditRowAsync(VisibleIndex);
            _selectedAImprimirDataItems = new List<object>();
        }
    }


    void Grid_CustomizeElement(GridCustomizeElementEventArgs e)
    {
        if (e.ElementType == GridElementType.DataRow)
            e.Attributes["data-visible-index"] = e.VisibleIndex;
    }

    public async ValueTask DisposeAsync()
    {
        if (_hubConnection is not null)
        {
            await _hubConnection.DisposeAsync();
        }

        if (_dotNetHelper != null)
        {
            //Now dispose our object reference so our component can be garbage collected
            _dotNetHelper.Dispose();
        }

        GC.SuppressFinalize(this);
    }

    private async Task VerDatosPedido(string context)
    {
        await Js.InvokeVoidAsync("open", $"{Navigation.BaseUri}ProgramacionPedidos/{context}", "_blank");
    }

    private void FechaAutomatica(TablaProgramacionDTO prog)
    {
        prog.DiaReal = DateTime.Today;
    }
    private void HoraAutomatica(TablaProgramacionDTO prog)
    {
        if (prog.HoraReal != null && prog.HoraReal.Value == new TimeSpan(7, 0, 0))
        {
            prog.HoraReal = new TimeSpan(15, 0, 0);
        }
        else if (prog.HoraReal != null && prog.HoraReal.Value == new TimeSpan(15, 0, 0))
        {
            prog.HoraReal = new TimeSpan(23, 0, 0);
        }
        else
        {
            prog.HoraReal = new TimeSpan(7, 0, 0);
        }
    }
    public async void OnValueChanged(int? newValue, TablaProgramacionDTO model)
    {
        if (newValue == null) return;
        model.Idaplicacionposterior = newValue;
        var datosObsPosterior = await Http.GetFromJsonAsync<SingleResult<string>>($"Pedido/{model.Idpedido.Value}/GetDatosObsPasePosteriorParaReproceso/{newValue.Value}");
        if (!datosObsPosterior.HasErrors)
        {
            model.Observaciones += datosObsPosterior.Data;
            StateHasChanged();
        }
    }
    private async Task SetMaximaPosicion()
    {
        CurrentMaquina.PosicionHasta = 9999999;
        await ActualizarDatosMaquina(true);
    }

    private async Task GetObsAlmacenByDblClick(TablaProgramacionDTO item)
    {
        PanelVisible = true;
        //SpinnerService.Show();
        var jsonString = JsonSerializer.Serialize(item);
        HttpContent content = new StringContent(jsonString, Encoding.UTF8, "application/json");
        var response = await Http.PostAsync($"Programacion/forzarcargaobsalmacen", content);
        var result = await response.Content.ReadFromJsonAsync<SingleResult<int>>();

        if (result.Errors.Any())
        {
            ToastService.ShowError($"{result.Errors.First()}");
        }
        else
        {
            await ActualizarGridProgramacion();
            ToastService.ShowSuccess($"Obs Almacén actualizada.");
        }
        PanelVisible = false;
        //SpinnerService.Hide();
    }

    async Task OnFlejarChanged(MouseEventArgs e, TablaProgramacionDTO prog)
    {
        //El valor de Flejar es el que actualmente tiene en pantalla, no el nuevo tras el click que acabas de hacer,
        //por lo que se comprueba de esta forma, si estás en false es que has pulsado para marcar que si tiene flejado y
        //si está a true has marcado para quitar el flejado
        SpinnerService.Show();
        if (!prog.Flejar.Value)
        {
            var datosFlejado = await Http.GetFromJsonAsync<SingleResult<string>>($"Pedido/{prog.Idpedido.Value}/getdatosflejadodirecto");
            if (!datosFlejado.HasErrors)
            {
                prog.Observaciones += datosFlejado.Data;
                prog.Posicionaplicacionposterior = string.Empty;
                //prog.Idaplicacionposterior = -1;
                prog.Idaplicacionposterior = null;
            }
            else
            {
                ToastService.ShowError($"{datosFlejado.Errors.First()}");
            }
        }
        else
        {
            //prog.ObsAlmacen = string.Empty;
            prog.Idaplicacionposterior = null;
        }
        SpinnerService.Hide();
    }


    async Task GridProg_ExportXlsx()
    {
        if (GridProg != null)
            await GridProg.ExportToXlsxAsync($"{DateTime.Now:yyyyMMdd}_Programacion_Litografia", new GridXlExportOptions()
            {
                //ExportSelectedRowsOnly = ExportSelectedRowsOnly,
                //CustomizeCell = OnCustomizeCell
            });
    }
    async Task GridLitoFoto_ExportXlsx()
    {
        if (GridLitoFoto != null)
            await GridLitoFoto.ExportToXlsxAsync($"{DateTime.Now:yyyyMMdd}_Litografia", new GridXlExportOptions()
            {
                //ExportSelectedRowsOnly = ExportSelectedRowsOnly,
                //CustomizeCell = OnCustomizeCell
            });
    }
    async Task GridLitoAsignada_ExportXlsx()
    {
        if (GridLitoAsignada != null)
            await GridLitoAsignada.ExportToXlsxAsync($"{DateTime.Now:yyyyMMdd}_Litografia_Asignada", new GridXlExportOptions()
            {
                //ExportSelectedRowsOnly = ExportSelectedRowsOnly,
                //CustomizeCell = OnCustomizeCell
            });
    }

    private void GridDatosPedidosLito_FilterCriteriaChanged(GridFilterCriteriaChangedEventArgs arg)
    {
        PanelVisibleLito = true;
        FiltroLito = new();
        var numRows = GridLitoAsignada.GetVisibleRowCount();
        for (int i = 0; i < numRows; i++)
        {
            var item = GridLitoAsignada.GetDataItem(i);
            var objCasteado = item as DatosGeneralesPedidoFotomecanicaLitoDTO;
            FiltroLito.Add(objCasteado);
        }
        ActualizarTextosAzulGridInt(true);
        PanelVisibleLito = false;
    }

    private void GridDatosPedidosFotoLito_FilterCriteriaChanged(GridFilterCriteriaChangedEventArgs arg)
    {
        PanelVisibleFoto = true;
        FiltroFotoLito = new();
        var numRows = GridLitoFoto.GetVisibleRowCount();
        for (int i = 0; i < numRows; i++)
        {
            var item = GridLitoFoto.GetDataItem(i);
            var objCasteado = item as DatosGeneralesPedidoFotomecanicaLitoDTO;
            FiltroFotoLito.Add(objCasteado);
        }
        ActualizarTextosAzulGridSup(true);
        PanelVisibleFoto = false;
    }

    async Task Grid_ItemsDropped(GridItemsDroppedEventArgs evt)
    {
        foreach (var item in evt.DroppedItems)
            DatosProgramacion.Remove((TablaProgramacionDTO)item);
        var index = await evt.GetTargetDataSourceIndexAsync();
        foreach (var item in evt.DroppedItems.Reverse())
            DatosProgramacion.Insert(index, (TablaProgramacionDTO)item);
    }
    private async Task AplicarCambiosMasivos(BulkChangesDTO? bulk)
    {
        _isCambiosMasivosPopUpVisible = false;
        if (bulk is not null)
        {
            bulk.Posiciones = _selectedAImprimirDataItems.Cast<TablaProgramacionDTO>().Select(o => o.Posicion.Value).ToList();
            bulk.CurrentMaquina = CurrentMaquina;
            var jsonString = JsonSerializer.Serialize(bulk, jsonSerializerOptions);
            HttpContent content = new StringContent(jsonString, Encoding.UTF8, "application/json");

            var response = await Http.PostAsync($"Programacion/bulkchanges", content);
            var resultInsertLimpias = await response.Content.ReadFromJsonAsync<SingleResult<int>>();
            if (resultInsertLimpias.HasErrors)
            {
                ToastService.ShowError($"{resultInsertLimpias.Errors.First()}");
            }
            else
            {
                ToastService.ShowSuccess("Cambio masivo correcto.");
                await ActualizarGridProgramacion();
            }
        }
        _selectedAImprimirDataItems = new List<object>();
    }
    private void PopUpBulkVisibleHandler(bool cerrar)
    {
        _isCambiosMasivosPopUpVisible = false;
    }
}
