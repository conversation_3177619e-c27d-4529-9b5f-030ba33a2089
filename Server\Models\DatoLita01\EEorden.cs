﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace ProgramadorGeneralBLZ.Server.Models.DatoLita01
{
    public partial class EEorden
    {
        public string Codigo { get; set; }
        public string C1 { get; set; }
        public string C2 { get; set; }
        public string C3 { get; set; }
        public string C4 { get; set; }
        public string C5 { get; set; }
        public string C6 { get; set; }
        public string C7 { get; set; }
        public string C8 { get; set; }
        public string C9 { get; set; }
        public string C10 { get; set; }
        public string C11 { get; set; }
        public string C12 { get; set; }
        public string C13 { get; set; }
        public string C14 { get; set; }
        public string C15 { get; set; }
        public string C16 { get; set; }
        public string C17 { get; set; }
        public string C18 { get; set; }
        public string C19 { get; set; }
        public string C20 { get; set; }
        public string C21 { get; set; }
        public string C22 { get; set; }
        public string C23 { get; set; }
        public string C24 { get; set; }
        public string C25 { get; set; }
        public string C26 { get; set; }
        public string C27 { get; set; }
        public string C28 { get; set; }
        public string C29 { get; set; }
        public string C30 { get; set; }
        public string C31 { get; set; }
        public string C32 { get; set; }
        public string C33 { get; set; }
        public string C34 { get; set; }
        public string C35 { get; set; }
        public string C36 { get; set; }
        public string C37 { get; set; }
        public string C38 { get; set; }
        public string C39 { get; set; }
        public string C40 { get; set; }
        public string C41 { get; set; }
        public string C42 { get; set; }
        public string C43 { get; set; }
        public string C44 { get; set; }
        public string C45 { get; set; }
        public string C46 { get; set; }
        public string C47 { get; set; }
        public string C48 { get; set; }
        public string C49 { get; set; }
        public string C50 { get; set; }
        public string C51 { get; set; }
        public string C52 { get; set; }
        public string C53 { get; set; }
        public string C54 { get; set; }
        public string C55 { get; set; }
        public string C56 { get; set; }
        public string C57 { get; set; }
        public string C58 { get; set; }
        public string C59 { get; set; }
        public string C60 { get; set; }
        public string C61 { get; set; }
        public string C62 { get; set; }
        public string C63 { get; set; }
        public string C64 { get; set; }
        public string C65 { get; set; }
        public string C66 { get; set; }
        public string C67 { get; set; }
        public string C68 { get; set; }
        public string C69 { get; set; }
        public string C70 { get; set; }
        public string C71 { get; set; }
        public string C72 { get; set; }
        public string C73 { get; set; }
        public string C74 { get; set; }
        public string C75 { get; set; }
        public string C76 { get; set; }
        public string C77 { get; set; }
        public string C78 { get; set; }
        public string C79 { get; set; }
        public string C80 { get; set; }
        public string C81 { get; set; }
        public string C82 { get; set; }
        public string C83 { get; set; }
        public string C84 { get; set; }
        public string C85 { get; set; }
        public string C86 { get; set; }
        public string C87 { get; set; }
        public string C88 { get; set; }
        public string C89 { get; set; }
        public string C90 { get; set; }
        public string C91 { get; set; }
        public string C92 { get; set; }
        public string C93 { get; set; }
        public string C94 { get; set; }
        public string C95 { get; set; }
        public string C96 { get; set; }
        public string C97 { get; set; }
        public string C98 { get; set; }
        public string C99 { get; set; }
        public string C100 { get; set; }
        public string C101 { get; set; }
        public string C102 { get; set; }
        public string C103 { get; set; }
        public string C104 { get; set; }
        public string C105 { get; set; }
        public string C106 { get; set; }
        public string C107 { get; set; }
        public string C108 { get; set; }
        public string C109 { get; set; }
        public string C110 { get; set; }
        public string C111 { get; set; }
        public string C112 { get; set; }
        public string C113 { get; set; }
        public string C114 { get; set; }
        public string C115 { get; set; }
        public string C116 { get; set; }
        public string C117 { get; set; }
        public string C118 { get; set; }
        public string C119 { get; set; }
        public string C120 { get; set; }
        public string Pagina { get; set; }
        public double? Cantidad { get; set; }
        public string Memoria { get; set; }
    }
}