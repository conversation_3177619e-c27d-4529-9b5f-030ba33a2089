﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="xrRichText1.SerializableRtfString" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="xrLabel10.ExpressionBindings" xml:space="preserve">
    <value>'------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------'</value>
  </data>
  <data name="sqlDataSource1.ResultSchemaSerializable" xml:space="preserve">
    <value>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</value>
  </data>
</root>