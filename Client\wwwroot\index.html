<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>Programador Litalsa</title>
    <base href="/" />
    <!--<link href="css/bootstrap/bootstrap.min.css" rel="stylesheet" />-->
    <!--<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">-->
    <link rel="stylesheet" href="css/bootstrap-icons-1.13.1/bootstrap-icons.min.css" />
    <link href="_content/DevExpress.Blazor.Themes/blazing-dark.bs5.min.css" rel="stylesheet" asp-append-version="true" />
    <link href="ProgramadorGeneralBLZ.Client.styles.css" rel="stylesheet" />
    <link href="css/app.css" rel="stylesheet" />
    <script type="text/javascript" src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script type="text/javascript" src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
    <link rel="icon" type="image/png" href="favicon.png" />

    <style type="text/css">
        .splash-screen {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-flow: column nowrap;
            height: 90vh;
            margin-top: 5%;
            font-family: "Segoe UI",Roboto,"Helvetica Neue","-apple-system",BlinkMacSystemFont,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol";
            font-size: .88rem;
            font-weight: 400;
            line-height: 1.5;
            text-align: center;
            overflow-y: hidden !important;
        }

            .splash-screen .spinner-border {
                border: .5em solid;
                border-color: #9b17e2 #bfbfbf #bfbfbf;
                width: 120px;
                height: 120px;
            }

        .splash-screen-caption {
            font-size: 1.5rem;
            font-weight: 600;
            margin-top: 1.5rem;
        }

        .splash-screen-text {
            color: #a1a1a1;
            margin-top: .5rem;
        }
    </style>
</head>

<body>
    <div id="app">
        <script type="text/javascript">
            if (/MSIE \d|Trident.*rv:|Edge\//.test(window.navigator.userAgent)) {
                window.location.href = "browserNotSupported.html";
            }

            scrollToEnd = (textarea) => {
                setTimeout(function () {
                    textarea.scrollTop = textarea.scrollHeight;
                    return true;
                }, 0);
            }
        </script>
        <!--<svg class="loading-progress">
            <circle r="40%" cx="50%" cy="50%" />
            <circle r="40%" cx="50%" cy="50%" />
        </svg>
        <div class="loading-progress-text"></div>
        <div class="progress">
            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100" style="width: var(--blazor-load-percentage, 0%)"></div>
        </div>-->

        <div class="splash-screen">
            <div class="spinner-border"></div>
            <div class="splash-screen-caption">Programador Litalsa BLZ</div>
            <div class="splash-screen-text">Cargando...</div>
        </div>
    </div>

    <div id="blazor-error-ui">
        An unhandled error has occurred.
        <a href="" class="reload">Reload</a>
        <a class="dismiss">🗙</a>
    </div>
    <script src="_content/Microsoft.Authentication.WebAssembly.Msal/AuthenticationService.js"></script>
    <script src="_framework/blazor.webassembly.js"></script>
    <!--<script src="~/js/reporting_ViewerCustomization.js"></script>-->
    <script src="js/reporting_ViewerCustomization.js"></script>
    <script src="scripts/reconnectTimer.js"></script>
    <!--<script src="scripts/extraScripts.js"></script>-->
    <script>
    </script>
</body>

</html>
