﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Identity.Web.Resource;
using ProgramadorGeneralBLZ.Shared;
using ProgramadorGeneralBLZ.Server.CustomFilterAttributes;
using ProgramadorGeneralBLZ.Server.MediaTR.Pedidos.Command;
using ProgramadorGeneralBLZ.Server.MediaTR.Pedidos.Query;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;
using ProgramadorGeneralBLZ.Server.MediaTR.Programaciones.Command;
using DevExpress.Blazor.Internal;
using DevExtreme.AspNet.Data;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;

namespace ProgramadorGeneralBLZ.Server.Controllers
{
    [Authorize]
    [ApiController]
    [Route("[controller]")]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAdB2C:Scopes")]
    //[Authorize(Roles = $"{Roles.Programador},{Roles.Admin}")]
    [ClaimRequirementContainRole("extension_Roles", $"{Roles.Programador},{Roles.Admin},{Roles.Consulta}")]
    public class PedidoController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<PedidoController> _logger;
        private readonly IWebHostEnvironment _env;

        public PedidoController(IMediator mediator, ILogger<PedidoController> logger, IWebHostEnvironment env)
        {
            _mediator = mediator;
            _logger = logger;
            _env = env;
        }

        [HttpPost("getall")]
        public async Task<ListResult<PedidoProcesadoLiteDTO>> GetAll(FiltroDTO filtro)
        {
            var model = await _mediator.Send(new GetPedidoProcesadoQuery(filtro));
            return model;
        }

        [HttpPost("getCodApliAgrupado")]
        public async Task<ListResult<PedidoProcesadoLiteDTO>> GetCodApliAgrupado([FromBody]List<PedidoProcesadoLiteDTO> lista)
        {
            var model = await _mediator.Send(new GetPedidosAgrupadosPorCodApli(lista));
            return model;
        }
        
        [HttpGet("getlast")]
        public async Task<SingleResult<ListadoPedidosProcesadosFiltradosDTO>> GetLastPedido()
        {
            var model = await _mediator.Send(new GetPedidoProgramadoQuery(
                null, null, null, true));
            return model;
        }

        [HttpGet("getpedido/{idPedido}")]
        public async Task<SingleResult<ListadoPedidosProcesadosFiltradosDTO>> GetLastPedido(int idPedido)
        {
            var model = await _mediator.Send(new GetPedidoProgramadoQuery(idPedido, null));
            return model;
        }

        //[HttpPost("guardarpedido/{idPedido}")]
        //public async Task<SingleResult<int>> GuardarDatosEditablesPedido(int idPedido, CancellationToken ct = default)
        //{
        //    var model = await _mediator.Send(new CreateFasePedidoCommand(idPedido, fasePedido), ct);
        //    return model;
        //}

        [HttpGet("getpedidocliente/{idPedido}")]
        public async Task<SingleResult<ListadoPedidosProcesadosFiltradosDTO>> GetPedidoDeCliente(string idPedido, string idCliente)
        {
            var model = await _mediator.Send(new GetPedidoProgramadoQuery(
                null, idCliente, Uri.UnescapeDataString(idPedido)));
            return model;
        }

        [HttpGet("{idPedido}/getdatosflejadodirecto")]
        public async Task<SingleResult<string>> GetDatosFlejadoDirecto(int idPedido, CancellationToken ct = default)
        {
            var model = await _mediator.Send(new GetDatosFlejadoDirectoQuery(idPedido), ct);
            return model;
        }
        [HttpGet("{idPedido}/GetDatosObsPasePosteriorParaReproceso/{idAplicacion}")]
        public async Task<SingleResult<string>> GetDatosObsPasePosteriorParaReproceso(int idPedido, int idAplicacion, CancellationToken ct = default)
        {
            var model = await _mediator.Send(new GetDatosObsPasePosteriorParaReprocesoQuery(idPedido, idAplicacion), ct);
            return model;
        }

        [HttpGet("getfasespedido/{idPedido}")]
        public async Task<ListResult<FasePedidoDTO>> GetFasesPedido(int? idPedido, CancellationToken ct = default)
        {
            var model = await _mediator.Send(new GetPedidoFasesQuery(idPedido), ct);
            return model;
        }
        [HttpPost("{idPedido}/createfasepedido")]
        public async Task<SingleResult<int>> CreateFasesPedido(FasePedidoDTO fasePedido, int idPedido, CancellationToken ct = default)
        {
            var model = await _mediator.Send(new CreateFasePedidoCommand(idPedido, fasePedido), ct);
            return model;
        }
        [HttpPut("{idPedido}/editfasepedido/{fase}")]
        public async Task<SingleResult<int>> EditFasesPedido(FasePedidoDTO newFasePedido, int idPedido, int fase, CancellationToken ct = default)
        {
            var model = await _mediator.Send(new EditFasePedidoCommand(idPedido, fase, newFasePedido), ct);
            return model;
        }
        [HttpDelete("{idPedido}/deletefasepedido/{idProceso}")]
        public async Task<SingleResult<int>> DeleteFasePedido(int idProceso, CancellationToken ct = default)
        {
            var model = await _mediator.Send(new DeleteFasePedidoCommand(idProceso), ct);
            return new SingleResult<int>();
        }

        [HttpGet("{idBusqueda}/getFileByType/")]
        public async Task<ListResult<string>> GetFile(string idBusqueda, Enums.TipoFicheros tipo,
            CancellationToken ct = default)
        {
            try
            {
                var result = new ListResult<string>
                {
                    Data = new List<string>()
                };
                string folderPath = Path.Combine(Directory.GetCurrentDirectory(), "Files\\Pdf");
                if (!Directory.Exists(folderPath))
                {
                    Directory.CreateDirectory(folderPath);
                }
                var existingFiles = Directory.GetFiles(folderPath);
                foreach (var f in existingFiles)
                {
                    System.IO.File.Delete(f);
                }
                var filePaths = new List<string>();
                switch (tipo)
                {
                    case Enums.TipoFicheros.Plano:
                        filePaths.Add(await _mediator.Send(new GetPlanoByPedidoQuery(idBusqueda), ct));
                        break;
                    case Enums.TipoFicheros.Motivo:
                        filePaths.AddRange(await _mediator.Send(new GetFotolitosByPedidoQuery(idBusqueda), ct));
                        break;
                    case Enums.TipoFicheros.Fotolito:
                        filePaths.Add(Directory.GetFiles(@"\\dc1\Company\COPIA APOGEEX\", $"{idBusqueda}*.pdf")
                            .FirstOrDefault());
                        break;
                    case Enums.TipoFicheros.FichaTecnica:
                        filePaths.Add(Directory.GetFiles(@"\\*************\in2$\datos\dato01LITA\DOCS\ARTICULOS\", $"{idBusqueda}*00-00-00*.pdf")
                            .FirstOrDefault());
                        break;
                    case Enums.TipoFicheros.Otro:
                        break;
                    default:
                        throw new ArgumentOutOfRangeException(nameof(tipo), tipo, null);
                }

                if (filePaths.All(o => o == null))
                {
                    result.Errors.Add("No existe el documento buscado para este código.");
                    return result;
                }

                if (tipo == Enums.TipoFicheros.Motivo)
                {
                    result.Data.AddRange(filePaths);
                    return result;
                }

                foreach (var filePath in filePaths)
                {
                    var fileName = Path.GetFileName(filePath);
                    // Get the current app path:
                    var currentApplicationPath = Path.Combine(folderPath, fileName);
                    System.IO.File.Copy(filePath, currentApplicationPath, true);
                    result.Data.Add(Path.GetRelativePath(Directory.GetCurrentDirectory(), currentApplicationPath));
                }

                return result;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                Console.WriteLine(e.Message);
                throw;
            }
        }

        [HttpPost("actualizardatos")]
        public async Task<SingleResult<int>> Guardar(PedidoProcesadoDTO datosForm, CancellationToken cancellationToken)
        {
            var result = await _mediator.Send(new UpdateDatosPedidoCommand(datosForm), cancellationToken);
            return result;
        }

        [HttpPost("asignarfechamultiple")]
        public async Task<SingleResult<int>> AsignarFechaMultiplesPedidos(PedidosFechaDTO datos, CancellationToken cancellationToken)
        {
            var result = await _mediator.Send(new AsignarFechaMultiplesPedidosCommand(datos), cancellationToken);
            return result;
        }

        /// <summary>
        /// Obtiene los pedidos de barnizado según el barniz seleccionado.
        /// Se debería refactorizar con el método siguiente ya que hace lo mismo pero para lito
        /// </summary>
        /// <param name="codProductoBarniz"></param>
        /// <returns></returns>
        [HttpGet("getpedidosbyfiltro")]
        public async Task<ListResult<DatosGeneralesPedidoDTO>> GetPedidosByFiltro(int codProductoBarniz)
        {
            var model = await _mediator.Send(new GetPedidosParaProgramarBarnizadoQuery(codProductoBarniz));
            return model;
        }
        /// <summary>
        /// Obtiene los pedidos de litografia que tiene fotomecanica ya asignada y aun no tienen linea de impresora.
        /// </summary>
        /// <returns></returns>
        [HttpGet("getpedidosfotolito")]
        public async Task<ListResult<DatosGeneralesPedidoFotomecanicaLitoDTO>> GetPedidosFotoLito()
        {
            var model = await _mediator.Send(new GetPedidosFotoLitoProgramarQuery());
            return model;
        }
        /// <summary>
        /// Obtiene los pedidos de litografia con linea de impresora.
        /// </summary>
        /// <returns></returns>
        [HttpGet("getpedidoslitobymaquina")]
        public async Task<ListResult<DatosGeneralesPedidoFotomecanicaLitoDTO>> GetPedidosLitografia(int idMaquina)
        {
            var model = await _mediator.Send(new GetPedidosLitoProgramarQuery(idMaquina));
            return model;
        }

        [HttpGet("getprogramacionbymaquina")]
        public async Task<ListResult<TablaProgramacionDTO>> GetProgramacionByMaquina(int maquina, bool ultimo)
        {
            var model = await _mediator.Send(new GetProgramacionByMaquinaQuery(maquina, ultimo));
            return model;
        }
        [HttpGet("getprogresumenbypedido")]
        public async Task<ListResult<TablaProgramacionDTO>> GetProgramacionResumenByPedido(int idPedido)
        {
            var model = await _mediator.Send(new GetProgResumenByPedidoQuery(idPedido));
            return model;
        }

        [HttpPost("programarpedidosbytipo")]
        //public async Task<ListResult<PedidoProgramarPedidoDTO>> GetPedidosByFiltro(int? idMaquina = 0, int? codApli = 0, int? codProductoBarniz = 0)
        public async Task<SingleResult<int>> ProgramarPedidosByTipo([FromQuery] Enums.TipoPedido tipo, [FromBody] ProgramarPedidoDTO datosProgramacion)
        {
            var result = new SingleResult<int>
            {
                Errors = new List<string>(),
                Data = 0
            };
            switch (tipo)
            {
                case Enums.TipoPedido.Barnizado:
                    result = await _mediator.Send(new ProgramarPedidosBarnizadoCommand(datosProgramacion, this.User));
                    break;
                case Enums.TipoPedido.Litografia:
                    result = await _mediator.Send(new ProgramarPedidosLitografiaCommand(datosProgramacion, this.User));
                    break;
                case Enums.TipoPedido.Corte:
                    break;
                case Enums.TipoPedido.Seleccion:
                    break;
                case Enums.TipoPedido.Laminado:
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(tipo), tipo, null);
            }

            return result;
        }

        [HttpPost("generardatosimpresion/{tipoPedido}")]
        public async Task<SingleResult<int>> GenerarDatosImpresion([FromBody] ImprimirPedidosDTO datosImpresion, Enums.TipoPedido tipoPedido, bool esProgramacionPorPantalla, string nombreReporte)
        {
            var result = new SingleResult<int>
            {
                Errors = new List<string>(),
                Data = 0
            };
            switch (tipoPedido)
            {
                case Enums.TipoPedido.Barnizado:
                    result = await _mediator.Send(new GenerarDatosImpresionCommand(datosImpresion, esProgramacionPorPantalla, nombreReporte));
                    break;
                case Enums.TipoPedido.Litografia:
                    result = await _mediator.Send(new GenerarDatosImpresionLitografiaCommand(datosImpresion, esProgramacionPorPantalla, nombreReporte));
                    break;
                case Enums.TipoPedido.Corte:
                    break;
                case Enums.TipoPedido.Seleccion:
                    break;
                case Enums.TipoPedido.Laminado:
                    break;
                case Enums.TipoPedido.Combinado:
                    result = await _mediator.Send(new GenerarDatosImpresionCombinadoCommand(datosImpresion, esProgramacionPorPantalla, nombreReporte));
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(tipoPedido), tipoPedido, null);
            }

            return result;
        }

        [HttpPut("{idPedido}/updatelineatintas")]
        public async Task<SingleResult<int>> ActualizarLineaTintasPorPedido([FromBody] int idMaquina, int idPedido)
        {
            var response = await _mediator.Send(new ActualizarLineaTintasCommand(idPedido, idMaquina));
            return response;
        }
    }
}
