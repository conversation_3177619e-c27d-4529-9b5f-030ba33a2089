
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Identity.Web;
using Nelibur.ObjectMapper;
using ProgramadorGeneralBLZ.Server.Data.DatoLita01;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared.DTO;
using MediatR;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.LitalsaDataWarehouse;
using ProgramadorGeneralBLZ.Server;
using Serilog;
using Serilog.Enrichers;
using Serilog.Events;
using Serilog.Sinks.SystemConsole.Themes;
using ProgramadorGeneralBLZ.Server.Data;
using ProgramadorGeneralBLZ.Server.Services.DatabaseManipulation;
using Microsoft.Extensions.FileProviders;
using DevExpress.AspNetCore;
using DevExpress.AspNetCore.Reporting;
using DevExpress.Web.Internal;
using ProgramadorGeneralBLZ.Server.Data.InterfaseBD;
using ProgramadorGeneralBLZ.Server.Data.ProteoLitalsa;
using ProgramadorGeneralBLZ.Server.Models.InterfaseBD;
using ProgramadorGeneralBLZ.Server.Repositorios;
using DevExpress.XtraCharts;
using ProgramadorGeneralBLZ.Server.Services;
using ProgramadorGeneralBLZ.Shared;
using Blazored.LocalStorage;
using ProgramadorGeneralBLZ.Server.Services.SAP;
using ProgramadorGeneralBLZ.Shared.DTO.SAP;
using Microsoft.AspNetCore.ResponseCompression;
using ProgramadorGeneralBLZ.Server.Models.DatoLita01;

//Origen de arquitectura: https://github.com/carlfranklin/MsalAuthInBlazorWasm

var builder = WebApplication.CreateBuilder(args);

const string loggerTemplate =
    @"{Timestamp:yyyy-MM-dd HH:mm:ss} [{Level:u4}]<{ThreadId}> [{SourceContext:l}] {Message:lj}{NewLine}{Exception}";
var baseDir = AppDomain.CurrentDomain.BaseDirectory;
var logfile = Path.Combine(baseDir, "App_Data", "logs", "log.txt");
Log.Logger = new LoggerConfiguration()
    .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
    .Enrich.With(new ThreadIdEnricher())
    .Enrich.FromLogContext()
    .WriteTo.Console(LogEventLevel.Information, loggerTemplate, theme: AnsiConsoleTheme.Literate)
    .WriteTo.File(logfile, LogEventLevel.Information, loggerTemplate,
        rollingInterval: RollingInterval.Day, retainedFileCountLimit: 90)
    .CreateLogger();


// Add services to the container.

builder.Services.AddDevExpressBlazor();
builder.Services.Configure<DevExpress.Blazor.Configuration.GlobalOptions>(options =>
{
    options.BootstrapVersion = DevExpress.Blazor.BootstrapVersion.v5;
});
builder.WebHost.UseStaticWebAssets();

builder.Services.AddDbContext<DatoLita01Context>(options =>
{
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnectionDatoLita01"));
    //options.EnableSensitiveDataLogging();
});
builder.Services.AddDbContextFactory<ProgramadorLitalsaContext>((sp, options) =>
{
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnectionProgramador"));
});
builder.Services.AddDbContext<ProgramadorLitalsaContext>(options =>
{
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnectionProgramador"));
    //options.EnableSensitiveDataLogging();
});
builder.Services.AddDbContext<LitalsaDataWarehouseContext>(options =>
{
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnectionDataWarehouse"));
    //options.EnableSensitiveDataLogging();
});
builder.Services.AddDbContext<ProteoLitalsaContext>(options =>
{
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnectionProteo"));
    //options.EnableSensitiveDataLogging();
});
builder.Services.AddDbContext<InterfaseBDContext>(options =>
{
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnectionInterfaseBD"));
    //options.EnableSensitiveDataLogging();
});

//builder.Services.AddSqlServer<DatoLita01Context>(builder.Configuration.GetConnectionString("DefaultConnectionDatoLita01"));
//builder.Services.AddSqlServer<ProgramadorLitalsaContext>(builder.Configuration.GetConnectionString("DefaultConnectionProgramador"));
//builder.Services.AddSqlServer<LitalsaDataWarehouseContext>(builder.Configuration.GetConnectionString("DefaultConnectionDataWarehouse"));

//builder.Services.AddDatabaseDeveloperPageExceptionFilter();

builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddMicrosoftIdentityWebApi(builder.Configuration.GetSection("AzureAdB2C"));

builder.Services.AddControllersWithViews();
builder.Services.AddRazorPages();

builder.Services.AddDevExpressControls();
// Register the name resolution service for the Document Viewer.
builder.Services.AddScoped<DevExpress.XtraReports.Services.IReportProvider, ReportProvider>();
// Register the service that works for the Document Viewer (name resolution)
// and Report Designer (loads and saves reports).
//builder.Services.AddScoped<ReportStorageWebExtension, ReportStorage>();

//builder.Services.AddMediatR(Assembly.GetExecutingAssembly());
builder.Services.AddMediatR(AppDomain.CurrentDomain.GetAssemblies());
builder.Services.AddScoped<IDataManipulationService, DataManipulationService>();
builder.Services.AddScoped<ISapService, SapService>();
builder.Services.AddScoped<IRepositorioGestionTablas, RepositorioGestionTablas>();
builder.Services.AddScoped<IEmailService, EmailService>(); 
builder.Services.AddScoped<FiltroService>();
builder.Services.AddScoped<DatabaseService>();

builder.Services.AddBlazoredLocalStorage();

builder.Services.AddSignalR();
builder.Services.AddSingleton<ImprimirBackgroundService>();

builder.Services.AddResponseCompression(opts =>
{
    opts.MimeTypes = ResponseCompressionDefaults.MimeTypes.Concat(
        new[] { "application/octet-stream" });
});

builder.Services.Configure<JwtBearerOptions>(
    JwtBearerDefaults.AuthenticationScheme, options =>
    {
        options.TokenValidationParameters.NameClaimType = "name";
        //options.TokenValidationParameters.RoleClaimType = "role";
        //options.TokenValidationParameters.RoleClaimType = "extension_Roles";
    });


builder.Services.Configure<ConfigEmail>(builder.Configuration.GetSection("EmailConfiguration"));

builder.Host.UseSerilog();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseWebAssemblyDebugging();
}
else
{
    app.UseExceptionHandler("/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();

app.UseReporting(builder =>
{
    builder.UserDesignerOptions.DataBindingMode =
        DevExpress.XtraReports.UI.DataBindingMode.ExpressionsAdvanced;
});
app.UseDevExpressControls();

app.UseBlazorFrameworkFiles();

app.UseStaticFiles();
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(Path.Combine(builder.Environment.ContentRootPath, "Files")),
    RequestPath = "/Files"
});

app.UseRouting();
app.MapHub<ImprimirHub>("/imprimirHub");

app.UseAuthorization();

app.MapRazorPages();
app.MapControllers();
app.UseResponseCompression();
app.MapFallbackToFile("index.html");

// Activar el servicio de fondo
var imprimirService = app.Services.GetRequiredService<ImprimirBackgroundService>();

TinyMapper.Bind<PedidoProcesado, PedidoProcesadoDTO>();
TinyMapper.Bind<List<PedidoProcesado>, List<PedidoProcesadoDTO>>();

TinyMapper.Bind<List<PedidoProgramadoEnviadoImprimirCombinado>, List<PedidoProgramadoEnviadoImprimirCombinadoDTO>>();

TinyMapper.Bind<List<TablaAvisos>, List<TablaAvisosDTO>>();
TinyMapper.Bind<List<TablaCodigosPedido>, List<FasePedidoDTO>>();
TinyMapper.Bind<List<ViewCodApliTablaProductos>, List<ProductoDTO>>();
TinyMapper.Bind<List<Maquinas>, List<MaquinaDTO>>();
TinyMapper.Bind<List<TblLimpiezas>, List<TblLimpiezasDTO>>();
TinyMapper.Bind<List<TblTipoLimpieza>, List<TblTipoLimpiezaDTO>>();
TinyMapper.Bind<List<DboPedproceso>, List<DboPedprocesoDTO>>();
TinyMapper.Bind<TablaCodigosPedido, FasePedidoDTO>();
TinyMapper.Bind<FasePedidoDTO, TablaCodigosPedido>();
TinyMapper.Bind<List<Parteshorariosstr>, List<HojasTrabajoG21>>();

TinyMapper.Bind<DestinatarioProgramaciones, DestinatarioProgramacionesDTO>();
TinyMapper.Bind<DestinatarioProgramacionesDTO, DestinatarioProgramaciones>();
TinyMapper.Bind<List<DestinatarioProgramaciones>, List<DestinatarioProgramacionesDTO>>();
TinyMapper.Bind<List<DestinatarioProgramacionesDTO>, List<DestinatarioProgramaciones>>();

TinyMapper.Bind<GrupoNotificaciones, GrupoNotificacionesDTO>();
TinyMapper.Bind<GrupoNotificacionesDTO, GrupoNotificaciones>();
TinyMapper.Bind<List<GrupoNotificaciones>, List<GrupoNotificacionesDTO>>();
TinyMapper.Bind<List<GrupoNotificacionesDTO>, List<GrupoNotificaciones>>();

TinyMapper.Bind<TablaComentarios, ComentariosDTO>();
TinyMapper.Bind<ComentariosDTO, TablaComentarios>();
TinyMapper.Bind<List<TablaComentarios>, List<ComentariosDTO>>();
TinyMapper.Bind<List<ComentariosDTO>, List<TablaComentarios>>();

TinyMapper.Bind<TablaCfg, TablaCfgDTO>();
TinyMapper.Bind<TablaCfgDTO, TablaCfg>();
TinyMapper.Bind<List<TablaCfg>, List<TablaCfgDTO>>();
TinyMapper.Bind<List<TablaCfgDTO>, List<TablaCfg>>();

TinyMapper.Bind<Plano, PlanoDTO>();
TinyMapper.Bind<PlanoDTO, Plano>();
TinyMapper.Bind<List<Plano>, List<PlanoDTO>>();
TinyMapper.Bind<List<PlanoDTO>, List<Plano>>();

TinyMapper.Bind<Maquinas, MaquinaDTO>();
TinyMapper.Bind<MaquinaDTO, Maquinas>();
TinyMapper.Bind<List<Maquinas>, List<MaquinaDTO>>();
TinyMapper.Bind<List<MaquinaDTO>, List<Maquinas>>();

TinyMapper.Bind<TblLimpiezas, TblLimpiezasDTO>();
TinyMapper.Bind<TblLimpiezasDTO, TblLimpiezas>();
TinyMapper.Bind<List<TblLimpiezas>, List<TblLimpiezasDTO>>();
TinyMapper.Bind<List<TblLimpiezasDTO>, List<TblLimpiezas>>();

TinyMapper.Bind<Clientes, ClienteDTO>();
TinyMapper.Bind<ClienteDTO, Clientes>();
TinyMapper.Bind<List<Clientes>, List<ClienteDTO>>();
TinyMapper.Bind<List<ClienteDTO>, List<Clientes>>();

//SAP
//TinyMapper.Bind<List<Programacion>, List<ProgramacionDTO>>();
//TinyMapper.Bind<List<ProgramacionDTO>, List<Programacion>>();
//TinyMapper.Bind<Programacion, ProgramacionDTO>();
//TinyMapper.Bind<ProgramacionDTO, Programacion>();

TinyMapper.Bind<List<Codtintas>, List<CodtintasDTO>>();

//SAP

TinyMapper.Bind<TablaProductos, ProductoDTO>();
TinyMapper.Bind<ProductoDTO, TablaProductos>();
TinyMapper.Bind<List<TablaProductos>, List<ProductoDTO>>();
TinyMapper.Bind<List<ProductoDTO>, List<TablaProductos>>();


TinyMapper.Bind<ApqLotesnodrizas, ApqLotesnodrizasDTO>();
TinyMapper.Bind<ApqLotesnodrizasDTO, ApqLotesnodrizas>();
TinyMapper.Bind<List<ApqLotesnodrizas>, List<ApqLotesnodrizasDTO>>();
TinyMapper.Bind<List<ApqLotesnodrizasDTO>, List<ApqLotesnodrizas>>();

TinyMapper.Bind<TablaProgramacion, TablaProgramacionDTO>((config) =>
{
    config.Bind(source => source.IdlineaNavigation.IdmaquinaG21, target => target.NombreG21);
    config.Bind(source => source.IdaplicacionNavigation.Nombreprogramacion, target => target.TextoIdAplicacion);
    config.Bind(source => source.IdproductoNavigation.Denominacion, target => target.TextoIdProducto);
});
app.Run();
