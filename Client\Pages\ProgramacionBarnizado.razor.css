﻿::deep .dxbl-row {
    --dxbl-row-item-spacing-y: 0.25rem !important;
}
::deep td {
    padding: 0px !important;
    margin: 0px !important;
}
/*::deep .dxbl-pager-container {
    --dxbl-pager-container-padding-y: 0.1rem !important;
}*/

::deep .gridlayout-cs {
    padding-top: 5px;
    margin-top: 5px;
    /*background-color: var(--bs-orange);*/
}

::deep .gridlayout-gs {
    padding-top: 5px;
    margin-top: 5px;
    /*background-color: var(--bs-red);*/
}

::deep .gridlayout-cm {
    padding-top: 5px;
    margin-top: 5px;
    /*background-color: var(--bs-yellow);*/
}

::deep .gridlayout-gi {
    padding-top: 5px;
    margin-top: 5px;
    /*background-color: var(--bs-green);*/
}

::deep .gridlayout-ci {
    padding-top: 5px;
    margin-top: 5px;
    /*background-color: var(--bs-purple);*/
}

::deep .alTecho {
    margin-top: -30px;
}

::deep .dxbl-checkbox.dxbl-sm .dxbl-checkbox-check-element {
    margin: 0 !important;
}

::deep .btnWide {
    width: 90px !important;
}

::deep .btnSizeS {
    width: 70px !important;
    height: 25px !important;
}


::deep .textoAzul {
    color: deepskyblue !important;
}

::deep .textoBlanco {
    color: white;
}

::deep .ch-320 {
    height: 320px !important;
}

::deep .dxbl-scroll-viewer > .dxbl-scroll-viewer-hor-scroll-bar > .dxbl-scroll-viewer-scroll-thumb {
    background-color: #ba5bed;
    opacity: 1;
    border-radius: 0.1rem;
}

::deep .dxbl-scroll-viewer > .dxbl-scroll-viewer-vert-scroll-bar > .dxbl-scroll-viewer-scroll-thumb {
    background-color: #ba5bed;
    opacity: 1;
    border-radius: 0.1rem;
}

::deep .btnDoble {
    --dxbl-btn-padding-x: 0.65rem !important;
}

    ::deep .btnDoble.btnPrimeroDeDos {
        border-right: 1px whitesmoke solid !important;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        margin-left: -37px;
        width: 61px;
    }

    ::deep .btnDoble.btnSegundoDeDos {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        margin-left: -25px;
        width: 56px;
    }

::deep .smallFont {
    --dxbl-grid-font-size: 0.70rem !important;
    --dxbl-grid-text-cell-padding-x: 0.1rem;
    --dxbl-grid-text-cell-padding-y: 0.1rem;
    --dxbl-grid-editor-cell-padding-x: 0.25rem;
    --dxbl-grid-editor-cell-padding-y: 0.1875rem;
}

@media (max-width: 575.98px) {
    ::deep .gridlayout-item {
        font-size: 0.9em;
    }
}

::deep .dragging {
    background-color: whitesmoke;
    color: black;
}

::deep .multiple-sortable-selected {
    background-color: whitesmoke;
    color: black;
}
