{
  /*
The following identity settings need to be configured
before the project can be successfully executed.
For more info see https://aka.ms/dotnet-template-ms-identity-platform
*/
  "AzureAdB2C": {
    "Instance": "https://Lital**AuthService.b2clogin.com/",
    "ClientId": "f7013c5c-8837-4d9d-8f6f-c72abd38e400",
    "Domain": "Lital**AuthService.onmicrosoft.com",
    "Scopes": "api_access",
    "SignUpSignInPolicyId": "B2C_1_SignupSignin"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "DefaultConnectionProgramador": "Server=QPLANT1;Database=ProgramadorLital**_DES2;  User ID=**;Password=**;MultipleActiveResultSets=True; Encrypt=False; TrustServerCertificate=True;",
    "DefaultConnectionDataWarehouse": "Server=QPLANT1;Database=Lital**DataWarehouse;User ID=**;Password=**;MultipleActiveResultSets=True; Encrypt=False; TrustServerCertificate=True;",
    "DefaultConnectionDatoLita01": "Server=SERVERP\\SQL2019;Database=dato01LITA; User ID=**;Password=**;MultipleActiveResultSets=True; Encrypt=False; TrustServerCertificate=True;",
    "DefaultConnectionProteo": "Server=qplant2\\proteo;Database=proteo_lital**db;User ID=**;Password=*********;MultipleActiveResultSets=True; Encrypt=False",
    "DefaultConnectionInterfaseBD": "Server=LITAENT;Database=Prueba;User ID=**;Password=***********;MultipleActiveResultSets=True; Encrypt=False; TrustServerCertificate=True;"
  },
  "EmailConfiguration": {
    "EmailFrom": "<EMAIL>",
    "SmtpServer": "smtp.ionos.es",
    "SmtpPort": 465,
    "SmtpUsername": "<EMAIL>",
    "SmtpPassword": "LIT2021y.",
    "PopServer": "popserver",
    "PopPort": 995,
    "PopUsername": "popusername",
    "PopPassword": "poppassword"
  }
}
