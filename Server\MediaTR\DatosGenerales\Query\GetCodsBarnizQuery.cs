﻿//using MediatR;
//using Microsoft.EntityFrameworkCore;
//using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
//using ProgramadorGeneralBLZ.Shared.DTO;
//using ProgramadorGeneralBLZ.Shared.ResponseModels;

//namespace ProgramadorGeneralBLZ.Server.MediaTR.DatosGenerales.Query
//{
//    public class GetCodsBarnizQuery : IRequest<ListResult<CodigoApliBarnizDTO>>
//    {
//        public GetCodsBarnizQuery()
//        {
//        }
//    }

//    public class GetCodsBarnizQueryHandler : IRequestHandler<GetCodsBarnizQuery, ListResult<CodigoApliBarnizDTO>>
//    {
//        private readonly ProgramadorLitalsaContext _contextProg;
//        public GetCodsBarnizQueryHandler(ProgramadorLitalsaContext contextProg)
//        {
//            _contextProg = contextProg;
//        }
//        public async Task<ListResult<CodigoApliBarnizDTO>> Handle(GetCodsBarnizQuery request, CancellationToken cancellationToken)
//        {
//            try
//            {
//                var result = new ListResult<CodigoApliBarnizDTO>()
//                {
//                    Data = new List<CodigoApliBarnizDTO>(),
//                    Errors = new List<string>()
//                };

//                var query = await _contextProg.Codapli
//                    .Where(o => o.Codbaz > 1000 || o.Codbaz == 98 || o.Codbaz == 99 || o.Codbaz == 0)
//                    .ToListAsync(cancellationToken);

//                if (!query.Any())
//                {
//                    result.Errors.Add("No se han encontrado los datos de Códigos de Aplicación de Barniz");
//                }
//                else
//                {
//                    result.Data = query.Select(s => new CodigoApliBarnizDTO
//                    {
//                        Idbarniz = s.Codbaz.Value,
//                        Descripcion = s.Nombaz
//                    }).ToList();
//                }

//                return result;
//            }
//            catch (Exception e)
//            {
//                var errorText = $"ERROR: GetCodsBarnizQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
//                throw new Exception(errorText, e);
//            }
//        }
//    }
//}
