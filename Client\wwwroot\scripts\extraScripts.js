
window.jsFunctions = {
    print: function (message) {
        return `from js${message}`;
    },
    addKeyboardListenerEvent: function (dotNetHelper) {
        document.addEventListener("keydown", function (e) {
            var maquina = false;
            var activeElement = document.activeElement;
            var maquinaExiste = document.getElementsByClassName("claseLineaTintas")[0];
            if (maquinaExiste) {
                maquina = document.getElementsByClassName("claseLineaTintas")[0].fieldElement.value;
            }
            //if (activeElement.parentElement.classList.contains("my-text-box") && maquina && e.key === "+") {
            if (document.activeElement.id === "inputPedido" && maquina && e.key === "+") {
                //Limpiamos el campo con el "+"
                activeElement.value = "";
                dotNetHelper.invokeMethodAsync("OnPlusKeyPressed");
            }
            if (e.key === "F10") {
                dotNetHelper.invokeMethodAsync("OnF10KeyPressed");
            }
        });
    },
    openPdfInNewTab: function (pdfUrl) {
        window.open(pdfUrl, "_blank");
    }
};

//function focusEditor(className) {
//    setTimeout(function () { document.getElementsByClassName(className)[0].focus(); }, 100); //100-300 milliseconds
//}
window.focusEditor = (id) => {
    setTimeout(function () { document.getElementById(id).focus(); }, 100);
    document.getElementById(id).focus();

}
window.limpiarCampo = (id) => {
    //var item = document.getElementsByClassName(clase)[0].fieldElement;
    var item = document.getElementById(id);
    item.value = "";
}

window.openPdfInNewTab = (pdfUrl) => {
    window.open(pdfUrl, "_blank");
};
window.downloadPdf = (pdfUrl) => {
    const link = document.createElement("a");
    link.href = pdfUrl;
    link.download = "fichero.pdf";
    link.click();
};
