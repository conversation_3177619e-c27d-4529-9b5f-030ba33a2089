﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa
{
    public partial class PedidoProgramadoEnviadoImprimir
    {
        public int? Id { get; set; }
        public int Idpedido { get; set; }
        public int? Idaplicacion { get; set; }
        public int? Idlinea { get; set; }
        public int? Posicion { get; set; }
        public int? Orden { get; set; }
        public string Producto { get; set; }
        public int? IdCliente { get; set; }
        public string NombreCliente { get; set; }
        public int? HojasPedido { get; set; }
        public string TipoElemento { get; set; }
        public Single? Formato { get; set; }
        public string Plano { get; set; }
        public decimal? AnchoHjlta { get; set; }
        public decimal? LargoHjlta { get; set; }
        public decimal? EspesorHjlta { get; set; }
        public int? TipoHjlta { get; set; }
        public string ApliProducto { get; set; }
        public bool? Flejar { get; set; }
        public decimal? Sup { get; set; }
        public DateTime? HoraComienzoEstimada { get; set; }
        public DateTime? HoraFinEstimada { get; set; }
        public string TiposCambio { get; set; }
        public DateTime? RequeridoEnFecha { get; set; }
        public string PosEscuadra { get; set; }
        public int? Idproducto { get; set; }
        public decimal? Peso { get; set; }
        public decimal? BarnizNecesario { get; set; }
        public string ObsCalidad { get; set; }
        public int? HojasAprocesar { get; set; }
        public string CalleApq { get; set; }
        public string CaractHjlta { get; set; }
        public string MuestraSh { get; set; }
        public string TipoLavada { get; set; }
        public bool? Urgente { get; set; }
        public string Obspaseposterior { get; set; }
        public string Observaciones { get; set; }
        public string ObsAlmacen { get; set; }
        public string TextoEstadoCodApli { get; set; }
        public bool? Tuberia { get; set; }
    }
}