﻿@page "/entregas"
@using Microsoft.AspNetCore.Authorization
@using ProgramadorGeneralBLZ.Shared
@using ProgramadorGeneralBLZ.Shared.DTO
@using ProgramadorGeneralBLZ.Shared.ResponseModels
@inject IJSRuntime Js
@inject IToastService ToastService
@inject HttpClient Http
@inject SpinnerService SpinnerService

@attribute [Authorize(Roles = $"{Roles.Admin}, {Roles.Programador}")]

<PageTitle>Consulta Entregas</PageTitle>

<AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}")">
    <Authorized Context="authContext">
        <DxLayoutBreakpoint DeviceSize="DeviceSize.Large" />
        <div class="h-100 px-2 py-1">
            <DxFormLayout CssClass="">
                <span @ref="@_popupTarget"></span>
            </DxFormLayout>
        </div>
    </Authorized>
    <NotAuthorized>
        <NoPuedesPasar />
    </NotAuthorized>
</AuthorizeView>

<DxWindow @ref="_windowRef"
          AllowResize="false"
          AllowDrag="false"
          ShowCloseButton="true"
          CloseOnEscape="false" PositionX="75"
          MinHeight="calc(100vh - 100px)" MinWidth="calc(100vw - 100px)"
          @bind-Visible="_windowVisible">
    <BodyTextTemplate>
        <ReportViewer ParamNombreReport="@ParameterNombreReport"/>
    </BodyTextTemplate>
</DxWindow>


@code {

    public string ParameterNombreReport { get; set; }
    public bool hayDatos = false;
    DxWindow _windowRef;
    bool _windowVisible;
    ElementReference _popupTarget;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        
        //await base.OnAfterRenderAsync(firstRender);
        //if (firstRender)
        //{
        //    var result = await Http.GetFromJsonAsync<SingleResult<int>>($"DatosGenerales/generardatosentregas");
        //    if (result.Errors.Any())
        //    {
        //        ToastService.ShowError($"{result.Errors.First()}");
        //    }
        //}
    }

    protected override async Task OnInitializedAsync()
    {
        SpinnerService.Show();
        var result = await Http.GetFromJsonAsync<SingleResult<int>>($"DatosGenerales/generarDatosEntregas");
        if (result.Errors.Any())
        {
            ToastService.ShowError($"{result.Errors.First()}");
        }
        else
        {
            ParameterNombreReport = "ReporteEntregas";
            hayDatos = true;
            if (_windowVisible)
                await _windowRef.CloseAsync();
            else
                await _windowRef.ShowAtAsync(_popupTarget);
        }
        //StateHasChanged();
        SpinnerService.Hide();
    }
}