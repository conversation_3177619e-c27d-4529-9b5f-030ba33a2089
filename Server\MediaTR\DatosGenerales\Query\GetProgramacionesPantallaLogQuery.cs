using MediatR;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Programaciones.Query;

public class GetProgramacionesPantallaLogQuery : IRequest<ListResult<ProgramacionesPantallaLogDTO>>
{
    public int IdLinea { get; set; }
    public long? IdMenorQue { get; set; }
    public int TamanoPagina { get; set; }

    public GetProgramacionesPantallaLogQuery(int idLinea, long? idMenorQue, int tamanoPagina)
    {
        IdLinea = idLinea;
        IdMenorQue = idMenorQue;
        TamanoPagina = tamanoPagina;
    }
}

public class GetProgramacionesPantallaLogQueryHandler : IRequestHandler<GetProgramacionesPantallaLogQuery, ListResult<ProgramacionesPantallaLogDTO>>
{
    private readonly ProgramadorLitalsaContext _context;

    public GetProgramacionesPantallaLogQueryHandler(ProgramadorLitalsaContext context)
    {
        _context = context;
    }

    public async Task<ListResult<ProgramacionesPantallaLogDTO>> Handle(GetProgramacionesPantallaLogQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<ProgramacionesPantallaLogDTO>
        {
            Data = new List<ProgramacionesPantallaLogDTO>(),
            Errors = new List<string>()
        };

        try
        {
            // Construir la consulta base
            var query = from log in _context.ProgramacionesPantallaLog
                        join programacion in _context.TablaProgramacion
                            on log.IdProgramacion equals programacion.Idprogramacion into progJoin
                        from prog in progJoin.DefaultIfEmpty()
                        where log.IdLinea == request.IdLinea || (prog != null && prog.Idlinea == request.IdLinea) // la idlinea, actualmente, solo se indica para logs de "Programación actualizada", para así obligarnos a ir a tabla programación a consultar las id lineas correspondientes en las otras operaciones
                        orderby log.Id descending
                        select new ProgramacionesPantallaLogDTO
                        {
                            Id = log.Id,
                            IdProgramacion = log.IdProgramacion,
                            IdTipoLog = log.IdTipoLog,
                            ValorAnterior = log.ValorAnterior,
                            ValorNuevo = log.ValorNuevo,
                            Mensaje = log.Mensaje,
                            Extra = log.Extra,
                            IdUsuario = log.IdUsuario,
                            Fecha = log.Fecha,
                            IdLinea = log.IdLinea ?? (prog != null ? prog.Idlinea : null)
                        };

            // Aplicar filtros según si tiene IdMenorQue o no
            if (request.IdMenorQue.HasValue)
            {
                // Si tiene IdMenorQue, aplicar paginación basada en cursor
                query = query.Where(x => x.Id < request.IdMenorQue.Value);

                // Aplicar límite de tamaño de página
                var mensajes = await query
                    .Take(request.TamanoPagina)
                    .ToListAsync(cancellationToken);

                result.Data = mensajes;
            }
            else
            {
                // Calcular la fecha límite para las últimas 36 horas
                var fechaLimite = DateTime.Now.AddHours(-36);
                query = query.Where(x => x.Fecha >= fechaLimite);
                
                var mensajes = await query
                    .ToListAsync(cancellationToken);

                result.Data = mensajes;
            }
        }
        catch (Exception e)
        {
            result.Errors.Add($"ERROR: GetProgramacionesPantallaLogQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}");
        }

        return result;
    }
}
