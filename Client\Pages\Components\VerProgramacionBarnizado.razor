﻿@using ProgramadorGeneralBLZ.Shared
@using ProgramadorGeneralBLZ.Shared.DTO

<div class="group-container">
	<DxFormLayoutGroup CssClass="mt-0">
		<HeaderTemplate>
		</HeaderTemplate>
		<Items>
			<DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" CssClass="cabeceraGrupo">
				<DxFormLayoutItem ColSpanLg="9" CssClass="px-0">
					<span class="cabecera-grupo @(ListaPedidos.FirstOrDefault()?.MarcadoVerde ?? false ? "" : "marcadoVerde")">
						<h2>@Cabecera</h2>
					</span>
				</DxFormLayoutItem>
				<DxFormLayoutItem ColSpanLg="1">
					<span class="cabecera-grupo-lavada">
						<h2>@(ListaPedidos.FirstOrDefault() is { Tuberia: true } ? "TUBERIA" : string.Empty)</h2>
					</span>
				</DxFormLayoutItem>
				<DxFormLayoutItem ColSpanLg="2">
					<span class="cabecera-grupo-lavada">
						<h2>@(ListaPedidos.First().TipoLavada)</h2>
					</span>
				</DxFormLayoutItem>
			</DxFormLayoutGroup>
			<DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" CssClass="subcabeceraGrupo">
				<DxFormLayoutItem ColSpanLg="2" BeginRow="true">
					<span class="cabecera-grupo">
						<h3>Cliente</h3>
					</span>
				</DxFormLayoutItem>
				<DxFormLayoutItem ColSpanLg="1">
					<span class="cabecera-grupo">
						<h3>Hojas</h3>
					</span>
				</DxFormLayoutItem>
				<DxFormLayoutItem ColSpanLg="2">
					<span class="cabecera-grupo">
						<h3>Hojalata</h3>
					</span>
				</DxFormLayoutItem>
				<DxFormLayoutItem ColSpanLg="1">
					<span class="cabecera-grupo">
						<h3>Pedido</h3>
					</span>
				</DxFormLayoutItem>
				<DxFormLayoutItem ColSpanLg="1">
					<span class="cabecera-grupo">
						<h3>Diametro</h3>
					</span>
				</DxFormLayoutItem>
				<DxFormLayoutItem ColSpanLg="5">
					<span class="cabecera-grupo">
						<h3>Observaciones</h3>
					</span>
				</DxFormLayoutItem>
			</DxFormLayoutGroup>
			@foreach (var item in ListaPedidos)
			{
				<DxFormLayoutGroup CssClass="@(item.NombreEstado?.ToLower().Trim() ?? "sinempezar")"
								   Id="@item.Idpedido.ToString()"
								   Decoration="FormLayoutGroupDecoration.None">
					<DxFormLayoutGroup CssClass="" ColSpanLg="7" Decoration="FormLayoutGroupDecoration.None">
						<HeaderTemplate>
						</HeaderTemplate>
						<Items>
							<DxFormLayoutItem ColSpanLg="3">
								<span class="">
									@item.IdCliente, @item.NombreCliente
								</span>
							</DxFormLayoutItem>
							<DxFormLayoutItem ColSpanLg="2">
								<span class="" style="padding: 0 0 0 55px;">
									@($"{item.HojasPedido:N0}")
								</span>
							</DxFormLayoutItem>
							<DxFormLayoutItem ColSpanLg="3">
								<span class="">
									@item.CaractHjlta
								</span>
							</DxFormLayoutItem>
							<DxFormLayoutItem ColSpanLg="2">
								<span class="grande negrita" style="padding: 0 0 0 55px;">
									@($"{item.Idpedido:00-0-0000}")
								</span>
							</DxFormLayoutItem>
							<DxFormLayoutItem ColSpanLg="2">
								<span class="">
									@($"{item.Formato:N2}")
								</span>
							</DxFormLayoutItem>

							<DxFormLayoutItem ColSpanLg="3" BeginRow="true">
								<span class="negrita">
									@(item.Urgente.HasValue && item.Urgente.Value ? "URGENTE" : string.Empty)
								</span>
							</DxFormLayoutItem>
							<DxFormLayoutItem ColSpanLg="3">
								<span class="negrita">
									@(item.RequeridoEnFecha.HasValue? item.RequeridoEnFecha.Value.ToString("dd/MM") : string.Empty)
								</span>
							</DxFormLayoutItem>
							<DxFormLayoutItem ColSpanLg="3">
								<span class="pequeño">
									<span class="textoAzul negrita">Plano: </span>@item.Plano
								</span>
							</DxFormLayoutItem>
							<DxFormLayoutItem ColSpanLg="3" CssClass="borde p-0">
@* 								<span class="">
									@item.Rodillo
								</span> *@
								<div class="" style="white-space: pre-wrap;">
									@item.Rodillo
								</div>
							</DxFormLayoutItem>
							<DxFormLayoutItem ColSpanLg="6" BeginRow="true">
								<span class="">
									@item.ObsCalidad
								</span>
							</DxFormLayoutItem>
							<DxFormLayoutItem ColSpanLg="6">
								<span class="">
									@item.TextoEstadoCodApli
								</span>
							</DxFormLayoutItem>
							@* <DxFormLayoutItem ColSpanLg="12" BeginRow="true">
                        <span class="">
                            @(item.TiposCambio)
                        </span>
                    </DxFormLayoutItem> *@
						</Items>
					</DxFormLayoutGroup>
					<DxFormLayoutGroup CssClass="" ColSpanLg="3" Decoration="FormLayoutGroupDecoration.None">
						<HeaderTemplate>
						</HeaderTemplate>
						<Items>
							<DxFormLayoutItem ColSpanLg="@(!string.IsNullOrEmpty(item.ObsAlmacen) && item.ObsAlmacen.Trim().Length > 0 ? 7 : 12)">
								<span class="">
									@($"{item.Observaciones} {item.Obspaseposterior}")
								</span>
								@if (!string.IsNullOrEmpty(item.NotaJefeTurno))
								{
									<br />
									<div class="noOkRojo" style="white-space: pre-wrap;">
										<span class="negrita">NOTA: </span>@item.NotaJefeTurno
									</div>
								}
							</DxFormLayoutItem>
							<DxFormLayoutItem ColSpanLg="5" Visible="@(!string.IsNullOrEmpty(item.ObsAlmacen) && item.ObsAlmacen.Trim().Length > 0)">
								<span class="">
									@item.ObsAlmacen
								</span>
							</DxFormLayoutItem>
						</Items>
					</DxFormLayoutGroup>
					<DxFormLayoutGroup CssClass="" ColSpanLg="2" Decoration="FormLayoutGroupDecoration.None">
						<HeaderTemplate>
						</HeaderTemplate>
						<Items>
							<DxFormLayoutItem ColSpanLg="12">
								<span class="" style="font-size: 16px">
									<b>@item.Posicion</b>
								</span>
							</DxFormLayoutItem>
							<DxFormLayoutItem ColSpanLg="12">
								<span class="">
									<b>@(item.Flejar.HasValue && item.Flejar.Value ? "SI" : "NO")</b>
								</span>
							</DxFormLayoutItem>
							<DxFormLayoutItem ColSpanLg="6">
								<span class="">
									@($"{item.BarnizNecesario}")
								</span>
							</DxFormLayoutItem>
							<DxFormLayoutItem CssClass="" ColSpanLg="6">
								<div class="btn-group btn-group-sm" role="group" aria-label="Estados del pedido" style="border-radius: 0.375rem; overflow: hidden;">
									<button type="button"
											class="@GetButtonClass(item, Enums.ProgramacionesPantalla_EstadosPedido.Empezado)"
											title="Empezar orden"
											disabled="@(!EsMaquinista)"
											@onclick="@(() => CambiarEstado(item, Enums.ProgramacionesPantalla_EstadosPedido.Empezado))"
											>
										<i class="oi oi-media-play"></i>
									</button>

									<button type="button"
											class="@GetButtonClass(item, Enums.ProgramacionesPantalla_EstadosPedido.Detenido)"
											title="Detener orden"
											disabled="@(!EsMaquinista)"
											@onclick="@(() => CambiarEstado(item, Enums.ProgramacionesPantalla_EstadosPedido.Detenido))">
										<i class="oi oi-media-stop"></i>
									</button>

									<button type="button"
											class="@GetButtonClass(item, Enums.ProgramacionesPantalla_EstadosPedido.Terminado)"
											title="Finalizar orden"
											disabled="@(!EsMaquinista)"
											@onclick="@(() => CambiarEstado(item, Enums.ProgramacionesPantalla_EstadosPedido.Terminado))">
										<i class="oi oi-circle-check"></i>
									</button>

									<button type="button"
											class="@GetButtonClass(item, Enums.ProgramacionesPantalla_EstadosPedido.Retirado)"
											title="Sacar orden"
											disabled="@(!EsMaquinista)"
											@onclick="@(() => CambiarEstado(item, Enums.ProgramacionesPantalla_EstadosPedido.Retirado))">
										<i class="oi oi-circle-x"></i>
									</button>
								</div>
							</DxFormLayoutItem>
							<DxFormLayoutItem ColSpanLg="6">
								<span class="">
									@(item.HoraComienzoEstimada.HasValue? item.HoraComienzoEstimada.Value.ToString("dd/MM - HH:mm") : string.Empty)
								</span>
							</DxFormLayoutItem>
							<DxFormLayoutItem CssClass="" ColSpanLg="6">
								<DxButton Click="@(async () => { if (item.Posicion.HasValue) await SetDatosPopUp(item, "Rodillo"); })"
										  Context="btn1"
										  Visible=@(EsRodillos) RenderStyle="ButtonRenderStyle.Info" CssClass="btnGroup">Rodillo</DxButton>
								<DxButton Click="@(async () => { if (item.Posicion.HasValue) await SetDatosPopUp(item, "Nota"); })"
										  Context="btn2"
										  Visible=@(EsEncargado||EsJefeTurno) RenderStyle="ButtonRenderStyle.Info" CssClass="btnGroup">Nota</DxButton>
							</DxFormLayoutItem>
							<DxFormLayoutItem ColSpanLg="12">
								<span class="">
									@(item.HoraFinEstimada.HasValue? item.HoraFinEstimada.Value.ToString("dd/MM - HH:mm") : string.Empty)
								</span>
							</DxFormLayoutItem>
						</Items>
					</DxFormLayoutGroup>
					<!-- Línea separadora / Borde inferior -->
					<DxFormLayoutGroup ColSpanLg="12" Decoration="FormLayoutGroupDecoration.None">
						<Items>
							<DxFormLayoutItem ColSpanLg="12" CssClass="borde">
								<span style="width: 100%; display:block;"></span>
							</DxFormLayoutItem>
						</Items>
					</DxFormLayoutGroup>
				</DxFormLayoutGroup>
			}
			@if (ListaPedidos.Any())
			{
				<span class="sumario">
					<!-- Sumatorio del bloque -->
					<span>@($"{ListaPedidos.Min(item => item.Posicion):F0} - {ListaPedidos.Max(item => item.Posicion):F0}")</span>
					<span>Hojas: @($"{ListaPedidos.Sum(item => item.HojasPedido):N0}")</span>
					<span>Superficie: @($"{ListaPedidos.Sum(item => item.Sup):N2}") m2</span>
					<span>
						@{
							var listaAux = ListaPedidos.Where(o => o.HoraFinEstimada != null).ToList();
							var finTexto = listaAux.Any()
								? listaAux.Max(item => item.HoraFinEstimada.Value).ToString("dd/MM/yyyy HH:mm")
								: "";
						}
						Fin: @finTexto
					</span>
				<span>Barniz: @($"{ListaPedidos.Where(o => o.BarnizNecesario.HasValue).Sum(item => item.BarnizNecesario):N2}") kg</span>
			</span>
						}
		</Items>
	</DxFormLayoutGroup>
</div>

@code {
	[Parameter] public List<PedidoProgramacionEnPantallaDTO> ListaPedidos { get; set; }
	[Parameter] public string Cabecera { get; set; }
	// Callbacks para comunicación con el componente padre
	[Parameter] public Func<PedidoProgramacionEnPantallaDTO, Enums.ProgramacionesPantalla_EstadosPedido, Task> OnEstadoPedidoUpdatedViaHub { get; set; }
	[Parameter] public Func<PedidoProgramacionEnPantallaDTO, string, Task> OnShowPopup { get; set; }

	// Propiedades de roles pasadas desde el padre
	[Parameter] public bool EsJefeTurno { get; set; }
	[Parameter] public bool EsEncargado { get; set; }
	[Parameter] public bool EsRodillos { get; set; }
	[Parameter] public bool EsMaquina { get; set; }
	[Parameter] public bool EsMaquinista { get; set; }



	// Método para cambiar el estado de un pedido
	private async Task CambiarEstado(PedidoProgramacionEnPantallaDTO pedido, Enums.ProgramacionesPantalla_EstadosPedido nuevoEstado)
	{
		await OnEstadoPedidoUpdatedViaHub(pedido, nuevoEstado);
	}

	// Método para obtener las clases CSS del botón según el estado
	private string GetButtonClass(PedidoProgramacionEnPantallaDTO item, Enums.ProgramacionesPantalla_EstadosPedido estado)
	{
		var baseClass = "btn btn-sm";
		var isSelected = item.IdEstado == (int)estado;

		if (isSelected)
		{
			return $"{baseClass} btn-info";
		}
		else
		{
			// Fondo blanco para todos los botones no seleccionados
			return $"{baseClass} btn-light";
		}
	}

	private async Task SetDatosPopUp(PedidoProgramacionEnPantallaDTO pedido, string texto)
	{
		await OnShowPopup(pedido, texto);
	}
}