﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa
{
    public partial class Maquinas
    {
        public Maquinas()
        {
            TablaProgramacion = new HashSet<TablaProgramacion>();
            ProgramacionesPantallaLog = new HashSet<ProgramacionesPantallaLog>();
        }

        public int Idmaquina { get; set; }
        public string TipoMaquina { get; set; }
        public string Nombremaquina { get; set; }
        public int? Velocidadmd { get; set; }
        public int? VelocidadScroll { get; set; }
        public int? Velocidad2pasadas { get; set; }
        public int? VelocidadAlu { get; set; }
        public int? CambioRodillo { get; set; }
        public int? CambioPlanchas { get; set; }
        public int? CambioFormato { get; set; }
        public int? CambioEscuadra { get; set; }
        public short? NumeroCuerpos { get; set; }
        public float? PasesHora { get; set; }
        public float? PorcentajeTiempoTirada { get; set; }
        public short? Idmaquinainformix { get; set; }
        public string IdmaquinaG21 { get; set; }
        public int? PosicionDesde { get; set; }
        public int? PosicionHasta { get; set; }
        public int? Idaplicacion { get; set; }
        public int? Idproducto { get; set; }
        public int? CambioGuias { get; set; }
        public int? Numerofotocopias { get; set; }
        public int? AnchoMinimo { get; set; }
        public int? AnchoMaximo { get; set; }
        public int? LargoMinimo { get; set; }
        public int? LargoMaximo { get; set; }
        public string Abreviatura { get; set; }
        public float? RepartoPasesxImpresora { get; set; }
        public bool Retirada { get; set; }
        public string ProductosXsistema { get; set; }
        public int? MaxDisolvente { get; set; }
        public bool WetonWet { get; set; }
        public int? PosicionTandem { get; set; }
        public string PuestoTrabajoSap { get; set; }

        public virtual ICollection<TablaProgramacion> TablaProgramacion { get; set; }
        public virtual ICollection<ProgramacionesPantallaLog> ProgramacionesPantallaLog { get; set; }
    }
}