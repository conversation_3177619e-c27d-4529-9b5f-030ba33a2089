﻿using System.Text;
using MediatR;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared.ResponseModels;
using static DevExpress.Xpo.Helpers.AssociatedCollectionCriteriaHelper;

namespace ProgramadorGeneralBLZ.Server.MediaTR.DatosGenerales.Query;

public class GetConsultaDatosActualizablesQuery : IRequest<SingleResult<bool>>
{
}

internal class GetConsultaDatosActualizablesQueryHandler : IRequestHandler<GetConsultaDatosActualizablesQuery, SingleResult<bool>>
{

    private readonly ProgramadorLitalsaContext _contextProg;
    public GetConsultaDatosActualizablesQueryHandler(ProgramadorLitalsaContext contextProg)
    {
        _contextProg = contextProg;
    }

    public async Task<SingleResult<bool>> Handle(GetConsultaDatosActualizablesQuery request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<bool>
        {
            Data = true,
            Errors = new List<string>()
        };
        try
        {
            var fechaDefaultIN2 = new DateTime(1899, 12, 30);
            var data = await _contextProg.ControlDatosActualizables
                .Where(o => o.FechaCierrePedido == fechaDefaultIN2 || o.MantenerAbierto)
                .ToListAsync(cancellationToken);
            foreach (var item in data)
            {
                var pedido =
                    await _contextProg.PedidoProcesado.FirstOrDefaultAsync(o => o.IdPedido == item.IdPedido,
                        cancellationToken);
                if (pedido == null) continue;
                pedido.IdPedido = item.IdPedido;
                pedido.RequeridoEnFecha = item.FechaEntrega != null && item.FechaEntrega != pedido.RequeridoEnFecha
                    ? item.FechaEntrega
                    : pedido.RequeridoEnFecha;
                pedido.Urgente = item.Urgente;
                pedido.MantenerAbierto = item.MantenerAbierto;
                pedido.Anulado = item.Cerrado;
                pedido.NoIncluirEnListado = item.NoIncluirListado;
                pedido.ObsProgramacion = item.ObsProg;
                pedido.ObsACliente = item.ObsCliente;
                //REVISION 11/03/2025 -- Si la fecha FIN está informada en el pedido y en los datos actualizables es la fecha por defecto de IN2
                //la actualizamos al valor correspondiente
                if ((pedido.FechaFin is not null && pedido.FechaFin != fechaDefaultIN2) && item.FechaCierrePedido == fechaDefaultIN2)
                {
                    item.FechaCierrePedido = pedido.FechaFin;
                }
            }
            await _contextProg.SaveChangesAsync(cancellationToken);
        }
        catch (Exception e)
        {
            result.Data = false;
            var errorText = $"ERROR: GetConsultaDatosActualizablesQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }
        return result;
    }
}