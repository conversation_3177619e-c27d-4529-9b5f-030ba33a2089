﻿using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using MediatR;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Services.DatabaseManipulation;
using ProgramadorGeneralBLZ.Shared;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Programaciones.Query;

public class GetRevisaProgramacionBarnizadoQuery : IRequest<SingleResult<int>>
{
    /// <summary>
    /// Adaptación de Public Sub actualiza_tiempos() para Barnizado, combinando en una unica funcion:
    /// Call recorre_programacion_barnizado(Me.txt_linea, Me.txt_posicion_desde, 9999999)
    /// Call revisa_programacion_barnizado(Me.txt_linea, Me.txt_posicion_desde)
    /// </summary>
    /// <param name="tipo"></param>
    /// <param name="maquina"></param>
    public GetRevisaProgramacionBarnizadoQuery(Enums.TipoPedido tipo, int maquina)
    {
        Tipo = tipo;
        Maquina = maquina;
    }
    public Enums.TipoPedido Tipo;
    public int Maquina;
}

internal class GetRevisaProgramacionBarnizadoQueryHandler : IRequestHandler<GetRevisaProgramacionBarnizadoQuery, SingleResult<int>>
{
    private readonly IDbContextFactory<ProgramadorLitalsaContext> _programadorLitalsaContextFactory;
    private readonly IDataManipulationService _dataManipulationService;
    public GetRevisaProgramacionBarnizadoQueryHandler(IDataManipulationService dataManipulationService, IDbContextFactory<ProgramadorLitalsaContext> contextProg)
    {
        _dataManipulationService = dataManipulationService;
        _programadorLitalsaContextFactory = contextProg;
    }

    public async Task<SingleResult<int>> Handle(GetRevisaProgramacionBarnizadoQuery request, CancellationToken ct)
    {
        var result = new SingleResult<int>
        {
            Data = 0,
            Errors = new List<string>()
        };

        try
        {
            DateTime hora2 = default;
            string descCambio = "",
                tipoBarnizado0 = "";
            int hojasYaTiradas = 0,
                tamaño0 = 0,
                ancho0 = 0,
                idproducto0 = 0,
                idproductoultimo = 0;
            var orden = 0;

            Single formato0 = 0;
            var producto0Orden = "";
            var velocidadst = 0;
            //string result;

            var contextProg = await _programadorLitalsaContextFactory.CreateDbContextAsync(ct);
            //var tiemposMaquina = DevuelveTiemposMaquina(idmaquina, ttamaño, velocidadst, trodillo, tguias);
            var datosMaquina = await contextProg.Maquinas.FirstOrDefaultAsync(o => o.Idmaquina == request.Maquina,
                    ct);
            
            var idmaquina = request.Maquina;
            var posicionHasta = 9999999; // Puedes cambiar este valor si necesitas 

            var desdePosicionTiempo = contextProg.TablaProgramacion
                .Where(x => x.Posicion <= posicionHasta &&
                            x.Idlinea == idmaquina &&
                            x.DiaReal != null &&
                            x.HoraReal != null)
                .Max(x => x.Posicion) ?? 0;

            var tablaProg = await contextProg.TablaProgramacion
                .Where(x => x.Idlinea == idmaquina && x.Posicion >= desdePosicionTiempo)
                .OrderBy(x => x.Posicion)
                .ToListAsync(ct);

            if (tablaProg.Any()) // Comprobamos si el conjunto de registros tiene algún registro
            {
                var firstRecord = tablaProg.First(); // Tomamos el primer registro

                // Verificamos si los campos HoraReal y DiaReal no son nulos
                if (firstRecord.HoraReal.HasValue && firstRecord.DiaReal.HasValue)
                {
                    hora2 = firstRecord.DiaReal.Value.Add(firstRecord.HoraReal.Value);
                }
                else if (firstRecord.HoraComienzoEstimada.HasValue) // Verificamos si el campo HoraComienzoEstimada no es nulo
                {
                    hora2 = firstRecord.HoraComienzoEstimada.Value;
                }
                else // En caso de que todas las comprobaciones sean falsas
                {
                    result.Errors.Add("NO Hay referencia de comienzo");
                    return result; // Salimos de la función/subrutina
                }
            }

            var tablaConsumos = new Dictionary<int, float>();

            foreach (var (itemProg, index) in tablaProg.Select((value, i) => (value, i)))
            {
                //28-08-2023: Venimos de ProgramarPedidosBarnizadoCommandHandler con el campo ImpresoraComoBarnizadora
                //A veces se usan las impresoras como barnizadoras (donde la máquina lo permite) y hay que diferenciar estos pedidos
                //ya que si a continuación, en la misma máquina pero usándola como impresora, se programa un pedido nuevo de sólo LITO
                //hay que vigilar que al dar a calcular tiempos, no se cargue los tiempos que se han calculado desde la vista de barnizado
                //Esto es para cuando se viene de LITO a BARNI, donde se han hecho pedidos como litografía en una impresora (lo normal)
                //Y a continuación se mete un pedido de BARNI pero en la MISMA IMPRESORA, por tanto
                //nos vamos quedando con el tiempo final de cada pedido, pero los saltamos sin hacer nada con ellos.
                if (datosMaquina.TipoMaquina == Enums.TipoMaquina.Impresora.ToString() && (itemProg.ImpresoraComoBarnizadora.HasValue && !itemProg.ImpresoraComoBarnizadora.Value))
                {
                    hora2 = itemProg.HoraFinEstimada.Value;
                    continue;
                }
                
                //añadimos el tiempo de ejecución del pedido al tiempo de comienzo del pedido i-1
                //y obtenemos el tiempo de comienzo del pedido i
                //la hora de fin del pedido anterior es el comienzo del siguiente
                var hora1 = hora2;

                //calculamos el tiempo de ejecución del pedido i, incluyendo el cambio del pedido i-1 al i
                var aux = 0;
                //calculo del tiempo de cambio de formato
                var datosPedido =
                    await contextProg.PedidoProcesado.FirstOrDefaultAsync(o => o.IdPedido == itemProg.Idpedido,
                        ct);
                var tamaño = datosPedido.AnchoHjlta.Value + datosPedido.LargoHjlta.Value;
                int taux;
                if (tamaño != tamaño0)
                {
                    taux = datosMaquina.CambioFormato.Value;
                    aux += taux;
                    descCambio += " Formato: " + taux;
                }
                
                // **************CALCULO DEL TIEMPO DE CAMBIO DE LAS GUÍAS DE LAS BARNIZADORAS (SI NECESARIO)*****
                // si es de punta lo procesamos por el ancho
                var ancho = itemProg.Observaciones.Contains("DE PUNTA", StringComparison.OrdinalIgnoreCase) ? datosPedido.AnchoHjlta.Value :
                    // si no es de punta lo procesamos por el largo
                    datosPedido.LargoHjlta.Value;

                if ((ancho0 > 720 && ancho < 720) || (ancho0 < 720 && ancho > 720))
                {
                    taux = datosMaquina.CambioGuias.Value;
                    aux += taux;
                    descCambio += " Guias: " + taux;
                }

                //**************FIN CALCULO DEL TIEMPO DE CAMBIO DE LAS GUÍAS DE LAS BARNIZADORAS (SI NECESARIO)*****
                //calculo del tiempo de cambio de rodillo, siempre que haya cambio de formato
                //hay que tener en cuenta los cambios de reservas hay fondos
                var tipoBarnizado = datosPedido.TipoBarnizado;
                var formato = datosPedido.Formato.Value;
                var tipoElemento = !string.IsNullOrEmpty(datosPedido.TipoElemento) ? datosPedido.TipoElemento : "0";

                var trodillopedido = itemProg.Observaciones.Contains("RODILLO ESPECI", StringComparison.OrdinalIgnoreCase)
                    ? datosMaquina.CambioRodillo.Value + 15
                    : datosMaquina.CambioRodillo.Value;

                //si hay cambio de reservas a fondo o viceversa entonces hay cambio de rodillo
                if (tipoBarnizado != tipoBarnizado0)
                {
                    taux = trodillopedido;
                    aux += taux;
                    descCambio += " Rodillo: " + taux;
                }

                //si continuamos con tipo de barnizado Longitudinal y es igual a lo que había
                //La comprobación original era "If tipo_barnizado = tipo_barnizado0 And tipo_barnizado = "L" 
                //Que no tiene sentido
                if (tipoBarnizado == tipoBarnizado0 && tipoBarnizado == "L" && formato != formato0)
                {
                    taux = trodillopedido;
                    aux += taux;
                    descCambio += " Rodillo: " + taux;
                }

                //***********CONTADOR DE ORDENES***************************
                var productoOrden = $"{itemProg.Idaplicacion} {itemProg.Producto}";
                //SI HAY CAMBIOS EN EL IDAPLICACION O EN EL PRODUCTO -> SUMAMOS 1 AL ORDEN
                if (productoOrden != producto0Orden && productoOrden != "")
                    orden++;
                //***********FIN DEL CONTADOR DE ORDENES********************

                var datosProducto =
                    await contextProg.TablaProductos.FirstOrDefaultAsync(o => o.Idproducto == itemProg.Idproducto, ct);

                var idproducto = datosProducto?.Idproducto ?? 0;
                // en el caso que el producto actual sea 0 como secados o pases en vacío y que el producto anterior sea <>0
                // entonces asignamos a ultimo producto el producto anterior
                if (idproducto == 0 && idproducto0 != 0)
                {
                    idproductoultimo = idproducto0;
                }

                // en el caso en que salgamos del bucle de producto a 0
                // asignamos a producto anterior el último producto para calcular correctamente las lavadas.
                if (idproducto != 0 && idproducto0 == 0)
                {
                    idproducto0 = idproductoultimo;
                }

                //si el producto del que venimos es distinto al que vamos entonces habría que entrar
                //además tiene que ser distinto de 0
                if (idproducto != idproducto0 && idproducto0 != 0 && idproducto != 0)
                {
                    var datosLavada = await _dataManipulationService.ConsultaLavadas(idproducto0, idproducto, ct);
                    if (datosLavada.Data != Enums.TipoLimpias.NoExiste && !datosLavada.HasInfo)
                    {
                        aux += (int)datosLavada.Data; //tipoLimpia.Duracion.Value;
                        descCambio += " Lavada: " + (int)datosLavada.Data;
                        itemProg.TipoLavada = datosLavada.Data.ToString();
                        taux = 0;
                    }
                    else
                    {
                        result.Data = -1;//Así indicamos que es el caso concreto de las lavadas
                        result.Info = datosLavada.Info;
                        itemProg.TipoLavada = "";
                    }
                }
                else
                {
                    itemProg.TipoLavada = "";
                }

                if (!string.IsNullOrEmpty(itemProg.VarCambios.ToString()))
                {
                    taux = Convert.ToInt32(itemProg.VarCambios);
                    aux += taux;
                    descCambio += " Desvios: " + " " + taux;
                }

                //ALMACENAMOS EL TIEMPO DE LOS CAMBIOS REQUERIDOS
                var tcambios = aux;
                //***************************CALCULO DEL TIEMPO DE TIRADA************************
                //si es el primer pedido solo hay tiempo de tirada no hay cambios de ningún tipo
                if (index == 0)
                {
                    aux = 0;
                    descCambio = "";
                }

                //calculamos el número de pasadas por la barnizadora
                //para ello analizamos el campo observaciones
                //si aparece DOS PASES o UNO Y UNO entonces son dos pasadas
                //en caso contrario será solo una pasada
                var numPasadas = itemProg.Observaciones.Contains("DOS PASE",StringComparison.OrdinalIgnoreCase) || 
                                 itemProg.Observaciones.Contains("UNO Y UNO",StringComparison.OrdinalIgnoreCase)
                    ? 2
                    : 1;

                //***********AJUSTAMOS EL NÚMERO REAL DE HOJAS***************************
                //si hay hojas ya tiradas tenemos que analizar el campo observaciones donde aparezca
                //el texto YA TIRADAS ..... HOJAS
                //ese tiempo habrá que restarlo
                if (itemProg.Observaciones.Contains("YA TIRADAS", StringComparison.OrdinalIgnoreCase))
                {
                    //debemos localizar el texto donde aparecen el número de hojas tiradas...
                    hojasYaTiradas = Convert.ToInt32(_dataManipulationService.DevuelveString(itemProg.Observaciones, "#", "#").Replace(".", ""));
                }
                if (Regex.IsMatch(itemProg.Observaciones.ToUpperInvariant(), @"SOLO.*HOJAS.*"))
                {
                    hojasYaTiradas = datosPedido.HojasPedido.Value -
                                     Convert.ToInt32(_dataManipulationService.DevuelveString(itemProg.Observaciones, "#", "#").Replace(".", ""));
                }

                if (Regex.IsMatch(itemProg.Observaciones.ToUpperInvariant(), @"SON.*HOJAS.*"))
                {
                    hojasYaTiradas = datosPedido.HojasPedido.Value -
                                     Convert.ToInt32(_dataManipulationService.DevuelveString(itemProg.Observaciones, "#", "#").Replace(".", ""));
                }

                if (itemProg.Observaciones.Contains("YA IMPRIMIDO", StringComparison.OrdinalIgnoreCase))
                {
                    hojasYaTiradas = datosPedido.HojasPedido.Value;
                }

                // si es solo tríptico calculamos un tiempo equivalente a 150 hojas
                if (itemProg.Observaciones.Contains("SOLO", StringComparison.OrdinalIgnoreCase) && itemProg.Observaciones.Contains("TRÍPTICO", StringComparison.OrdinalIgnoreCase))
                {
                    hojasYaTiradas = datosPedido.HojasPedido.Value - 150;
                }

                // SE ESTABLECE LA VELOCIDAD POR DEFECTO = A LA STANDARD
                var velocidad = datosMaquina.Velocidadmd.Value;

                // definimos VELOCIDAD si es scroll
                if (_dataManipulationService.GetDatosPedido(itemProg.Idpedido.Value, "DevuelveCaracteristicasHojalata").Text.Contains("SCROLL", StringComparison.OrdinalIgnoreCase))
                {
                    velocidad = Math.Min(datosMaquina.VelocidadScroll.Value, velocidad);
                }

                // definimos VELOCIDAD si es aluminio
                if (_dataManipulationService.GetDatosPedido(itemProg.Idpedido.Value, "DevuelveCaracteristicasHojalata").Text.Contains("ALU", StringComparison.OrdinalIgnoreCase))
                {
                    velocidad = Math.Min(datosMaquina.VelocidadAlu.Value, velocidad);
                }
                //14/10/2024 Ajuste con los cambios de carlos apra temperaturas de la nueva tabla VelocidadesMaquinas
                if (datosMaquina.PosicionTandem == 2 && idproducto == 110146)
                {
                    var datosVelMaq =
                        await _dataManipulationService.GetTemperaturaSecadoV2(idproducto, datosPedido.IdPedido, datosMaquina.Idmaquina, ct);
                    if (datosVelMaq != null)
                    {
                        velocidad = (int)datosVelMaq.VelTiradaMq2;
                    }
                }
                //Para asegurar que encontramos la velocidad de la maquina 1 del tandem, como ahora va por la tabla de velocidades
                //hay que simular que somos la posición 2 del tandem y obtener la velocidad de su registro que dice que es para la maquina 1 del tandem
                if (datosMaquina.PosicionTandem == 1 &&
                    contextProg.VelocidadesMaquina.Any(o => itemProg.Idaplicacion.ToString().StartsWith(o.CodApliMq1Consulta.ToString())))
                {

                    var progLineaSig = await contextProg.TablaProgramacion
                        .Where(o => o.Idpedido == datosPedido.IdPedido && o.Idlinea == datosMaquina.Idmaquina + 1)
                        .OrderByDescending(o => o.Idprogramacion)
                        .FirstOrDefaultAsync(ct);

                    if (progLineaSig is { Idproducto: 110146 })
                    {
                        var datosVelMaq =
                            await _dataManipulationService.GetTemperaturaSecadoV2(110146, datosPedido.IdPedido, datosMaquina.Idmaquina + 1, ct);
                        if (datosVelMaq != null)
                        {
                            //temp = (double)datosVelMaq.TempTiradaMq2;
                            velocidad = (int)datosVelMaq.VelTiradaMq1;
                        }
                    }
                }

                //11/03/2025 Cambio de temperatura en 2ª parte de las tandems cuando la temperatura de la 1ª parte es mayor y codApli no es 6* ni 96*.
                // By Cesar & Javi
                if (datosMaquina.Idmaquina is 12 or 17 && datosMaquina.PosicionTandem == 2 &&
                    !itemProg.Idaplicacion.Value.ToString().StartsWith("6") && !itemProg.Idaplicacion.Value.ToString().StartsWith("96"))
                {
                    var progLineaPrevia = await contextProg.TablaProgramacion
                        .Where(o => o.Idpedido == itemProg.Idpedido &&
                                    o.Idlinea == datosMaquina.Idmaquina - 1 &&
                                    o.Posicion == itemProg.Posicion)
                        .FirstOrDefaultAsync(ct);

                    if (progLineaPrevia is null)
                    {
                        result.Errors.Add($"FALTA PROGRAMACION {itemProg.Idpedido} EN TANDEM PREVIO");
                    }
                    else
                    {
                        if (progLineaPrevia.TemperaturaSecado.HasValue && progLineaPrevia.TemperaturaSecado > itemProg.TemperaturaSecado)
                        {
                            itemProg.TemperaturaSecado = progLineaPrevia.TemperaturaSecado;
                        }
                    }
                }

                //taux = (int)Math.Ceiling((double)((numPasadas * 60 * (datosPedido.HojasPedido.Value - hojasYaTiradas)) / velocidad));
                taux = (int)Math.Ceiling((numPasadas * 60 * (datosPedido.HojasPedido.Value - hojasYaTiradas) / (float)velocidad));

                // ALMACENAMOS EL TIEMPO DE TIRADA EFECTIVA
                var ttirada = taux;

                aux += taux;

                descCambio = $"{descCambio} Tirada: {(numPasadas == 2 ? "(2)" : "")}{taux:#}(V:{velocidad} h/h)";

                //Ajustes de tiempos para máquinas TANDEM cuando hay lavadas/descargas/etc:
                //Partiendo de la posición que tiene una lavada se debe buscar esa misma posición en su máquina pareja
                //Si existe, se añade el tiempo extra asociado al proceso de limpieza.
                //Si no existe, se muestra mensaje de error pero se permite continuar el proceso completo.
                //T1=11; T2=12; T3=16; T4=17
                if ((itemProg.Idlinea is 11 or 12 or 16 or 17)/* && !string.IsNullOrEmpty(itemProg.TipoLavada)*/)
                {
                    var tandemHermana = itemProg.Idlinea switch
                    {
                        11 => await contextProg.Maquinas.FirstOrDefaultAsync(o => o.Idmaquina == 12, ct),
                        12 => await contextProg.Maquinas.FirstOrDefaultAsync(o => o.Idmaquina == 11, ct),
                        16 => await contextProg.Maquinas.FirstOrDefaultAsync(o => o.Idmaquina == 17, ct),
                        17 => await contextProg.Maquinas.FirstOrDefaultAsync(o => o.Idmaquina == 16, ct),
                        _ => null
                    };

                    //var _contextProgExtra = await _programadorLitalsaContextFactory.CreateDbContextAsync(cancellationToken);
                    var posicionEnOtraTandem = await contextProg.TablaProgramacion.FirstOrDefaultAsync(
                        o => o.Idpedido == itemProg.Idpedido && o.Idlinea == tandemHermana.Idmaquina &&
                             o.Posicion == itemProg.Posicion, ct);
                    if (posicionEnOtraTandem != null)
                    {
                        //Si existe en la máquina hermana entonces:
                        //Se añade a la lista de cambios de la linea actual.
                        //Se añade a la lista de cambios de la linea hermana y se ajustan sus tiempos tambien
                        var tipoLavadaTandemActual =
                            await contextProg.TblTipoLimpieza.FirstOrDefaultAsync(
                                o => o.TipoLimpieza == itemProg.TipoLavada, ct);
                        var tipoLavadaOtroTandem =
                            await contextProg.TblTipoLimpieza.FirstOrDefaultAsync(
                                o => o.TipoLimpieza == posicionEnOtraTandem.TipoLavada, ct);
                        //Ajustes maquina actual.
                        descCambio += $" // Tiempo otro Tandem: {tipoLavadaOtroTandem?.Duracion ?? 0}";
                        aux += tipoLavadaOtroTandem?.Duracion ?? 0;
                        //TODO:Lo dejamos fuera por ahora, falla en algo que hace que se repita el texto y no hay tiempo para mirarlo.
                        //Por ahora tiene que ir de una maquina a otra y asi actualizan los tiempos.

                        ////Ajustes para la otra tandem.
                        ////Se obtiene el string de TipoCambios y se parte por '//'
                        ////Tiene este formato:
                        ////Formato: 5 Rodillo: 40 Lavada: 60 Desvios:  0 Tirada: 78(V:4600 h/h) // Ttotal= 183 
                        ////Formato: 5 Rodillo: 40 Lavada: 60 Desvios:  0 Tirada: 78(V:4600 h/h) // Tiempo Otro Tandem: XX // Ttotal= 183 

                        ////Se analiza el string de los tiempos de la máquina actual.
                        ////Si la máquina actual NO tiene el string: Se añade el tiempo de la lavada que tiene EL OTRO TANDEM al string que se está construyendo y
                        ////el tiempo en minutos se añade a la variable aux que es con la que se lleva la cuenta de todo
                        ////Si la máquina actual SI tiene el string: Se sustituye el string al completo y con el nuevo tiempo.
                        ////Para el tiempo total, se resta el valor que ya tenia y se suma el de la nueva lavada.
                        //var tiposCambioOtraTandem = posicionEnOtraTandem.TiposCambio.Split("//");
                        //var oldTiempoTandem = 0;
                        //if (!tiposCambioOtraTandem.Contains(" // Tiempo otro Tandem:"))
                        //{
                        //    tiposCambioOtraTandem[0] += $" // Tiempo otro Tandem: {tipoLavadaTandemActual?.Duracion ?? 0}";
                        //}
                        //else
                        //{
                        //    oldTiempoTandem = int.Parse(tiposCambioOtraTandem[1].Trim().Split(":")[1]);
                        //    tiposCambioOtraTandem[1] += $" // Tiempo otro Tandem: {tipoLavadaTandemActual?.Duracion ?? 0}";
                        //}
                        //var ttotalTexto = tiposCambioOtraTandem.Length == 3
                        //? tiposCambioOtraTandem[2].Trim().Split("=")
                        //: tiposCambioOtraTandem[1].Trim().Split("=");
                        //var tiempoTotal = int.Parse(ttotalTexto[1]) - oldTiempoTandem + tipoLavadaOtroTandem?.Duracion ?? 0;
                        //posicionEnOtraTandem.TiposCambio = tiposCambioOtraTandem.Length == 3
                        //? $"{tiposCambioOtraTandem[0]} // {tiposCambioOtraTandem[1]} // Ttotal= {tiempoTotal}"
                        //: $"{tiposCambioOtraTandem[0]} // Ttotal= {tiempoTotal}";
                        //var extraMinutos = tipoLavadaOtroTandem?.Duracion ?? 0;
                        //posicionEnOtraTandem.HoraFinEstimada.Value.AddMinutes(-oldTiempoTandem).AddMinutes(extraMinutos);
                        //await _contextProgExtra.SaveChangesAsync(cancellationToken);
                    }
                    else
                    {
                        result.Info.Add($"Pedido: {itemProg.Idpedido} Posición: {itemProg.Posicion} NO EXISTE EN {tandemHermana.IdmaquinaG21}");
                    }
                }


                descCambio = $"{descCambio} // Ttotal= {aux:#}";

                // calculamos la hora de final del pedido (la columna cambios se tiene en cuenta más arriba)
                hora2 = hora1.AddMinutes(aux);
                float kgNecesarios = 0;
                if (itemProg.Idproducto.HasValue)
                {
                    // ***********CALCULO DE LOS BARNICES NECESARIOS ***************************
                    // calculamos el consumo de barniz para ese producto mediante la función devuelve_barniz_necesario
                    //La función devuelve_barniz_necesario se combina con otra llamada interna que tiene para la superficie, ya que solo se usa aqui y para litgrafía
                    kgNecesarios = _dataManipulationService.DevuelveBarnizNecesarioConCalculoDePeso(
                        itemProg.Idpedido.Value, itemProg.Idproducto.Value, itemProg.Peso.Value, hojasYaTiradas);
                    tablaConsumos.TryAdd(itemProg.Idproducto.Value, 0);
                    tablaConsumos[itemProg.Idproducto.Value] += kgNecesarios;

                    if (kgNecesarios > (datosProducto.Existencias ?? 0 - datosProducto.CantidadRechazada ?? 0))
                    {
                        result.Info.Add(
                            $"Línea: {itemProg.Posicion}{Environment.NewLine}" +
                            $"No hay suficiente {datosProducto.Denominacion}{Environment.NewLine}" +
                            $"Existencias: {datosProducto.Existencias ?? 0 - datosProducto.CantidadRechazada ?? 0:0} kg{Environment.NewLine}" +
                            $"Necesarios: {kgNecesarios:0} kg");
                    }

                    // ***********FIN DE LOS CALCULO DE LOS BARNICES NECESARIOS ***************************
                }

                //actualizamos el tiempo de comienzo estimado del pedido i
                itemProg.HoraComienzoEstimada = hora1;
                //actualizamos el tiempo de fin estimado del pedido i
                itemProg.HoraFinEstimada = hora2;
                itemProg.TiposCambio = descCambio;
                itemProg.BarnizNecesario = kgNecesarios;
                itemProg.Orden = orden;
                itemProg.TiempoEstimadoCambio = tcambios;
                itemProg.TiempoEstimadoTirada = ttirada;
                //12/10/2020: CAMBIOS PROTEO
                itemProg.PasadasAdicionales = numPasadas - 1;
                //*****ACTUALIZACIÓN DE LAS OBSERVACIONES DE CALIDAD********
                //actualización 28/03/2023 se elimina el primero y se deja el 2º
                //cadena_calidad = devuelve_obs_calidad(!IdPedido, !Idaplicacion, idmaquina, !Idprogramacion)
                var cadenaCalidad = await _dataManipulationService.GetObservacionesCalidadLite(itemProg.Idpedido.Value, itemProg.Idaplicacion);

                if (!string.IsNullOrEmpty(cadenaCalidad))
                {
                    itemProg.ObsCalidad = cadenaCalidad.Length > 255 ? cadenaCalidad.Substring(0, 255) : cadenaCalidad;
                }

                //*****FIN DE LA ACTUALIZACIÓN DE LAS OBSERVACIONES DE CALIDAD********

                //idaplicacion0 = idaplicacion;
                //orden0 = orden;
                tamaño0 = tamaño;
                formato0 = formato;
                ancho0 = ancho;
                //plano0 = plano;
                //tipoElemento0 = tipoElemento;
                idproducto0 = idproducto;
                //producto0 = producto;
                producto0Orden = productoOrden;
                tipoBarnizado0 = tipoBarnizado;
                descCambio = "";
                hojasYaTiradas = 0;

                //Actualizamos los datos
                contextProg.Update(itemProg);

            }
            await contextProg.SaveChangesAsync(ct);
            await CompruebaExistenciasBarnices(datosMaquina, result, ct);

            await contextProg.DisposeAsync();
        }
        catch (Exception e)
        {
            var error = $"ERROR: GetCalcularHorariosQuery - {e.Message}--{(!string.IsNullOrWhiteSpace(e.InnerException?.Message) ? e.InnerException : string.Empty)}";
            result.Errors.Add(error);
        }
        return result;
    }


    /// <summary>
    /// Adaptación de Public Sub comprueba_existencias_barnices(idmaquina As Integer, Desde As Long, hasta As Long)
    /// este procedimiento determina para una máquina (idmaquina)a partir de una posición de la programación (desde)
    /// los consumos estimados de barnices y avisa si no hay existencias suficientes, dando aviso a través
    /// del procedimiento de avisos.
    /// </summary>
    /// <param name="maquina"></param>
    /// <param name="singleResult"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    public async Task CompruebaExistenciasBarnices(Models.ProgramadorLitalsa.Maquinas maquina, SingleResult<int> result, CancellationToken ct)
    {

        var parameters = new[]
        {
            new SqlParameter("@IdMaquina", maquina.Idmaquina),
            new SqlParameter("@Desde", maquina.PosicionDesde.Value)
        };
        var programador = await _programadorLitalsaContextFactory.CreateDbContextAsync(ct);
        var datosBarnices = await programador.Set<CompruebaExistenciasBarnices>().FromSqlRaw("[dbo].[GetDatosBarnices] @IdMaquina, @Desde", parameters)
            .ToListAsync(ct);


        foreach (var barniz in datosBarnices)
        {
            //si las existencias-cantidadrechazada es menor que la cantidad necesaria
            //entramos en el if de aviso de barniz insuficiente.
            if ((barniz.Existencias - barniz.CantidadRechazada) < barniz.TotBarn)
            {
                var datosProducto =
                    await programador.TablaProductos.FirstOrDefaultAsync(o => o.Idproducto == barniz.Idproducto, ct);


                //en el caso que se haya avisado en días anteriores o que nunca se haya dado aviso,
                //se avisa y se actualiza el campo UltAviso
                if (datosProducto.UltAvisoInsuficiente < DateTime.Today ||
                    datosProducto.UltAvisoInsuficiente == DateTime.MinValue ||
                    datosProducto.UltAvisoInsuficiente == null)
                {
                    result.Info.Add($"********** BARNIZ INSUFICIENTE ************");
                    result.Info.Add($"Idproducto {barniz.Idproducto} : {datosProducto.Denominacion}");
                    result.Info.Add($"Necesarios: {barniz.TotBarn:0} kg");
                    result.Info.Add($"Existencias: {barniz.Existencias:0} kg");
                    result.Info.Add($"Rechazados: {barniz.CantidadRechazada:0} kg");
                    result.Info.Add($"Fecha Entrega: {barniz.FechaEntrega:dd/MM}");
                    result.Info.Add($"Días hasta entrega: {(barniz.FechaEntrega != null
                                                                    ? (barniz.FechaEntrega - DateTime.Today).Value.TotalDays
                                                                    : "N/A")}");
                    //actualizamos el momento en el que se ha dado el aviso
                    datosProducto.UltAvisoInsuficiente = DateTime.Now;

                }
            }
        }

        await programador.DisposeAsync();
    }
}