﻿@using ProgramadorGeneralBLZ.Shared
@using ProgramadorGeneralBLZ.Shared.DTO
@inject HttpClient Http
@inject SpinnerService SpinnerService
@inject IToastService ToastService

<div class="h-100 overflow-auto px-2 py-1">
    <DxGrid Data="@destinatarios" SizeMode="SizeMode.Medium"
            CssClass="ch-320 smallFont progGrid" PageSize="50"
            KeyFieldName="Id" Context="GridDest"
            AllowSort="true" EditNewRowPosition="GridEditNewRowPosition.Top"
            ShowFilterRow="true" ValidationEnabled="false"
            EditMode="GridEditMode.EditRow" @ref="Grid"
            EditorRenderMode="GridEditorRenderMode.Integrated"
            EditModelSaving="GridDest_EditModelSaving"
            DataItemDeleting="Grid_DataItemDeleting">
        <Columns>
            <DxGridCommandColumn Width="80px">
                <HeaderTemplate>
                    <a class="oi oi-plus" @onclick="@(() => Grid.StartEditNewRowAsync())" style="text-decoration: none;color: lightskyblue;" href="javascript:void(0);"></a>
                </HeaderTemplate>
                <CellDisplayTemplate>
                    <a class="oi oi-pencil" @onclick="@(() => Grid.StartEditRowAsync(context.VisibleIndex))" style="text-decoration: none; padding-right: 15px; color: #c75fff;" href="javascript:void(0);"></a>
                    <a class="oi oi-x" @onclick="@(() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex))" style="text-decoration: none; color: red;" href="javascript:void(0);"></a>
                </CellDisplayTemplate>
                <CellEditTemplate>
                    <a class="oi oi-arrow-thick-bottom" @onclick="@(() => Grid.SaveChangesAsync())" style="text-decoration: none; padding-right: 15px; color: greenyellow; margin-right: 6px; margin-top: 3px;" href="javascript:void(0);"></a>
                    <a class="oi oi-x" @onclick="@(() => Grid.CancelEditAsync())" style="text-decoration: none; color: red;" href="javascript:void(0);"></a>
                </CellEditTemplate>
            </DxGridCommandColumn>
            <DxGridDataColumn FieldName="Id" Visible="false" />
            <DxGridDataColumn FieldName="@nameof(DestinatarioProgramacionesDTO.Email)" Caption="Email" Width="150" />
            <DxGridDataColumn FieldName="@nameof(DestinatarioProgramacionesDTO.IdGrupoNotificaciones)" Caption="Grupo" Width="150">
                <CellDisplayTemplate Context="cellText">
                    @{
                        int? summary = (int?)cellText.Value;
                    }
                    <DxComboBox Data="@grupos" ReadOnly="true"
                                FilteringMode="DataGridFilteringMode.Contains"
                                TextFieldName="Descripcion"
                                ValueFieldName="Id"
                                ListRenderMode="ListRenderMode.Virtual"
                    @bind-Value="summary">
                    </DxComboBox>
                </CellDisplayTemplate>
            </DxGridDataColumn>
            <DxGridDataColumn FieldName="@nameof(DestinatarioProgramacionesDTO.UltimoEnvio)" Caption="Fecha Ultimo Envio" Width="150" />
            <DxGridDataColumn FieldName="@nameof(DestinatarioProgramacionesDTO.Obsoleto)" Caption="Obsoleto" Width="80">
                <CellDisplayTemplate Context="checkbox">
                    <DxCheckBox Checked="@((bool)checkbox.Value)" />
                </CellDisplayTemplate>
            </DxGridDataColumn>
        </Columns>

        <DataColumnCellEditTemplate>
            @{
                var prog = (DestinatarioProgramacionesDTO)GridDest.EditModel;
            }
            @switch (GridDest.DataColumn.FieldName)
            {
                case "Email":
                    <DxTextBox @bind-Text="@prog.Email"
                               ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                               ShowValidationIcon="true" />
                    break;
                case "IdGrupoNotificaciones":
                    <DxComboBox Data="@grupos"
                                FilteringMode="DataGridFilteringMode.Contains"
                                TextFieldName="Descripcion"
                                ValueFieldName="Id"
                                ListRenderMode="ListRenderMode.Virtual"
                    @bind-Value="prog.IdGrupoNotificaciones">
                    </DxComboBox>
                    break;
                case "Obsoleto":
                    <DxCheckBox CssClass="d-inline-block" @bind-Checked="@prog.Obsoleto"
                                ValueChecked=true ValueUnchecked=false />
                    break;
            }
        </DataColumnCellEditTemplate>
    </DxGrid>

</div>


@code
{
    DxGrid? Grid;
    List<GrupoNotificacionesDTO> grupos = new List<GrupoNotificacionesDTO>();
    List<DestinatarioProgramacionesDTO> destinatarios = new List<DestinatarioProgramacionesDTO>();

    protected override async Task OnInitializedAsync()
    {
        SpinnerService.Show();
        await LoadData();
        SpinnerService.Hide();
    }

    async Task LoadData()
    {
        grupos = await Http.GetFromJsonAsync<List<GrupoNotificacionesDTO>>("GestionTablasV2/GrupoNotificaciones");
        destinatarios = await Http.GetFromJsonAsync<List<DestinatarioProgramacionesDTO>>("GestionTablasV2/DestinatarioProgramaciones");
    }

    async Task GridDest_EditModelSaving(GridEditModelSavingEventArgs e)
    {
        SpinnerService.Show();
        var dest = (DestinatarioProgramacionesDTO)e.EditModel;
        var response = e.IsNew == false  
            ? await Http.PutAsJsonAsync($"GestionTablasV2/DestinatarioProgramaciones/{dest.Id}", dest)
            : await Http.PostAsJsonAsync("GestionTablasV2/DestinatarioProgramaciones", dest);
        if (response.IsSuccessStatusCode)  
            await LoadData();
        else {  
            var error= await response.Content.ReadAsStringAsync();  
            ToastService.ShowError(error);
        }
        SpinnerService.Hide();
    }

    async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        SpinnerService.Show();
        var item = (DestinatarioProgramacionesDTO)e.DataItem;
        var response = await Http.DeleteAsync($"GestionTablasV2/DestinatarioProgramaciones/{item.Id}");
        if (response.IsSuccessStatusCode)
            await LoadData();
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            ToastService.ShowError(error);
        }
        SpinnerService.Hide();
    }
    async Task Delete(DestinatarioProgramacionesDTO item)
    {
        SpinnerService.Show();
        var response = await Http.DeleteAsync($"GestionTablasV2/DestinatarioProgramaciones/{item.Id}");
        if (response.IsSuccessStatusCode)
            await LoadData();
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            ToastService.ShowError(error);
        }
        SpinnerService.Hide();
    }
}
