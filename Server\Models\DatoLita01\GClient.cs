﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace ProgramadorGeneralBLZ.Server.Models.DatoLita01
{
    public partial class GClient
    {
        public string Codigo { get; set; }
        public string Nombre { get; set; }
        public string Nombre2 { get; set; }
        public string Fiscal { get; set; }
        public string Sigla { get; set; }
        public string Direccion { get; set; }
        public string Direccion1 { get; set; }
        public string Direccion2 { get; set; }
        public string Direccio21 { get; set; }
        public string Numero { get; set; }
        public string Numero2 { get; set; }
        public string Poblacion { get; set; }
        public string Poblacion2 { get; set; }
        public string Provincia { get; set; }
        public string Provinci2 { get; set; }
        public string Cif { get; set; }
        public string Postal { get; set; }
        public string Posta2 { get; set; }
        public string Telefono { get; set; }
        public string Fax { get; set; }
        public string Email { get; set; }
        public string <PERSON>co { get; set; }
        public string <PERSON>uenta { get; set; }
        public string <PERSON><PERSON>nco { get; set; }
        public string Domiban2 { get; set; }
        public double? Acucom { get; set; }
        public double? Venci1 { get; set; }
        public double? Venci2 { get; set; }
        public double? Venci3 { get; set; }
        public double? Venci4 { get; set; }
        public double? Venci5 { get; set; }
        public double? Venci6 { get; set; }
        public double? Porcen1 { get; set; }
        public double? Porcen2 { get; set; }
        public double? Porcen3 { get; set; }
        public double? Porcen4 { get; set; }
        public double? Porcen5 { get; set; }
        public double? Porcen6 { get; set; }
        public double? Dias1 { get; set; }
        public double? Dias2 { get; set; }
        public double? Dias3 { get; set; }
        public string Fpago { get; set; }
        public string Zona { get; set; }
        public string Servicios { get; set; }
        public string Equival { get; set; }
        public double? Numnove { get; set; }
        public double? Pendiente { get; set; }
        public double? Notas { get; set; }
        public double? Cancelado { get; set; }
        public DateTime? Fecalta { get; set; }
        public string Representa { get; set; }
        public string Actividad { get; set; }
        public string Contacto1 { get; set; }
        public string Contacto2 { get; set; }
        public DateTime? Fvisita { get; set; }
        public DateTime? Fulvisi { get; set; }
        public string Numprovee { get; set; }
        public DateTime? Cierre0 { get; set; }
        public DateTime? Cierre1 { get; set; }
        public string Motivo { get; set; }
        public double? Comision { get; set; }
        public string Tf { get; set; }
        public double? Descuento { get; set; }
        public string Codprov { get; set; }
        public double? Descuenpp { get; set; }
        public string Observa1 { get; set; }
        public string Observa2 { get; set; }
        public string Observa3 { get; set; }
        public string Observa4 { get; set; }
        public string Observa5 { get; set; }
        public double? Riesgo1 { get; set; }
        public double? Riesgo2 { get; set; }
        public double? Riesgoa1 { get; set; }
        public double? Riesgoa2 { get; set; }
        public string Empresa { get; set; }
        public string Tipoiva { get; set; }
        public string Facturar { get; set; }
        public string Documento { get; set; }
        public string Moneda { get; set; }
        public double? Margen { get; set; }
        public string Codent { get; set; }
        public string Tipocli { get; set; }
        public string Pais { get; set; }
        public string Provexml { get; set; }
        public string Envfacaut { get; set; }
        public string Emailfaut { get; set; }
        public string Credicau { get; set; }
        public string Obsoleto { get; set; }
        public double? Margeng { get; set; }
        public string Domici { get; set; }
        public string Movil { get; set; }
        public string Webuser { get; set; }
        public string Webpass { get; set; }
        public string Web { get; set; }
        public string Valorar { get; set; }
        public double? Copias { get; set; }
        public double? Eticop { get; set; }
        public string Etimod { get; set; }
        public string Competi { get; set; }
        public string Iban { get; set; }
        public string Bic { get; set; }
        public DateTime? Mandato { get; set; }
        public double? Previ1 { get; set; }
        public double? Previ2 { get; set; }
        public double? Previ3 { get; set; }
        public double? Previ4 { get; set; }
        public double? Previ5 { get; set; }
        public double? Previ6 { get; set; }
        public double? Previ7 { get; set; }
        public double? Previ8 { get; set; }
        public double? Previ9 { get; set; }
        public double? Previ10 { get; set; }
        public double? Previ11 { get; set; }
        public double? Previ12 { get; set; }
        public string Pressid { get; set; }
        public string Oficonta { get; set; }
        public string Orgagest { get; set; }
        public string Undtrami { get; set; }
        public string Mandatoid { get; set; }
        public string Memoria { get; set; }
        public string Pais2 { get; set; }
        public string Mandatouni { get; set; }
        public DateTime? Limcaucion { get; set; }
        public string Ocdire { get; set; }
        public string Ocpobla { get; set; }
        public string Ocprovin { get; set; }
        public string Ocpostal { get; set; }
        public string Ocpais { get; set; }
        public string Ogdire { get; set; }
        public string Ogpobla { get; set; }
        public string Ogprovin { get; set; }
        public string Ogpostal { get; set; }
        public string Ogpais { get; set; }
        public string Utdire { get; set; }
        public string Utpobla { get; set; }
        public string Utprovin { get; set; }
        public string Utpostal { get; set; }
        public string Utpais { get; set; }
        public string Rgpd { get; set; }
        public double? Riesgo3 { get; set; }
        public double? Riesgoa3 { get; set; }
        public string Orgapro { get; set; }
        public string Opdire { get; set; }
        public string Oppobla { get; set; }
        public string Opprovin { get; set; }
        public string Oppostal { get; set; }
        public string Oppais { get; set; }
        public string Factxemail { get; set; }
        public string Entregapro { get; set; }
        public string Certifical { get; set; }
        public string Tipodctofa { get; set; }
        public string Tipotarive { get; set; }
        public string Codtrans { get; set; }
        public string Servidor { get; set; }
        public string Grupoempr { get; set; }
        public string Factsimpli { get; set; }
        public string Telefono2 { get; set; }
        public string Puntoopera { get; set; }
        public string Residente { get; set; }
        public string Tipsiva1 { get; set; }
        public string Tipsiva2 { get; set; }
        public string Tipsiva3 { get; set; }
        public string Tipsiva4 { get; set; }
    }
}