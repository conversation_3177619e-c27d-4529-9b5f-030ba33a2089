﻿using DevExpress.XtraReports.UI;
using ProgramadorGeneralBLZ.Server.Reports;

namespace ProgramadorGeneralBLZ.Server
{
    public static class ReportFactory
    {
        public static Dictionary<string, Func<XtraReport>> Reports = new Dictionary<string, Func<XtraReport>>()
        {
            ["ReporteBarnizado"] = () => new BarnizadoReport(),
            ["ReporteBarnizadoV2"] = () => new BarnizadoReportV2(),
            ["ReporteLitografia"] = () => new LitografiaReport(),
            ["ReporteCombinado"] = () => new CombinadoReport(),
            ["ReporteCombinadoV3"] = () => new CombinadoReportV3(),
            ["ReporteEntregas"] = () => new EntregasReport()

        };
    }
}
