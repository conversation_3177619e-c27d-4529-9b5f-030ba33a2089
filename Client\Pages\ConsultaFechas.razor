﻿@page "/consultafechas"

@using Microsoft.AspNetCore.Authorization
@using ProgramadorGeneralBLZ.Shared
@using ProgramadorGeneralBLZ.Client.Pages.Components
@using ProgramadorGeneralBLZ.Shared.DTO
@using ProgramadorGeneralBLZ.Shared.ResponseModels
@using System.Text.Json
@using System.Text.Json.Serialization
@using System.Text

@inject Blazored.LocalStorage.ILocalStorageService localStorage
@inject SpinnerService _spinnerService
@inject IJSRuntime Js
@inject IToastService ToastService
@inject HttpClient Http
@inject AuthenticationStateProvider GetAuthenticationStateAsync
@inject NavigationManager Navigation

@attribute [Authorize(Roles = Roles.Programador)]

<PageTitle>Consulta Fechas por Cliente</PageTitle>




<AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}")">
    <Authorized Context="authContext">
        <DxLayoutBreakpoint DeviceSize="DeviceSize.Large" />
        <div class="h-100 px-2 py-1">
            <DxFormLayout SizeMode="SizeMode.Small" ReadOnly="true">
                <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" BeginRow="true" CssClass="py-3">
                    <DxFormLayoutItem Caption="Cliente" Context="campoDropdown" ColSpanLg="2">
                        <DxComboBox Data="@datosClientes" ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                    FilteringMode="DataGridFilteringMode.Contains"
                                    DropDownWidthMode="DropDownWidthMode.EditorWidth"
                                    TextFieldName="Combinado" @ref="@_component"
                                    ValueFieldName="CodigoCliente"
                                    ListRenderMode="ListRenderMode.Virtual"
                                    SelectedItemChanged="@((ClienteDropdownDTO c) => OnTextChanged(c))"
                                    @bind-Value="_idCliente">
                        </DxComboBox>
                    </DxFormLayoutItem>
                </DxFormLayoutGroup>
                <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" BeginRow="true" CssClass="">

                    <DxLoadingPanel @bind-Visible="PanelVisible"
                                    IsContentBlocked="true"
                                    ApplyBackgroundShading="true"
                                    IndicatorAreaVisible="false"
                                    IndicatorAnimationType="WaitIndicatorAnimationType.Spin"
                                    Text="Cargando..." ZIndex="100"
                                    TextAlignment="LoadingPanelTextAlignment.Right">
                        <DxGrid Data="@DatosPedidos" SizeMode="SizeMode.Small" id="idProg"
                                CssClass="ch-700 smallFont progGrid" PageSize="50"
                                EditMode="GridEditMode.EditRow"
                                EditorRenderMode="GridEditorRenderMode.Integrated"
                                PopupEditFormCssClass="popupEditor"
                                KeyFieldName="IdPedido" Context="GridPedidos"
                                AllowSort="true" EditNewRowPosition="GridEditNewRowPosition.Top"
                                ShowFilterRow="true" ColumnResizeMode="GridColumnResizeMode.ColumnsContainer"
                                SelectionMode="GridSelectionMode.Multiple" ValidationEnabled="false"
                                @bind-SelectedDataItems="_seleccionados"
                                SelectAllCheckboxMode="GridSelectAllCheckboxMode.Page"
                                EditModelSaving="Grid_EditModelSaving" @ref="GridPedidos">
                            <Columns>
                                <DxGridCommandColumn NewButtonVisible="false" DeleteButtonVisible="false" Width="50px">
                                    <CellDisplayTemplate>
                                        <a class="oi oi-pencil" @onclick="@(() => StartEditing(context.VisibleIndex))" style="text-decoration: none; color: #c75fff;" href="javascript:void(0);"></a>
                                    </CellDisplayTemplate>
                                    <CellEditTemplate>
                                        <a class="oi oi-arrow-thick-bottom" @onclick="@(() => GridPedidos.SaveChangesAsync())" style="text-decoration: none; color: greenyellow; margin-right: 6px; margin-top: 3px;" href="javascript:void(0);"></a>
                                        <a class="oi oi-x" @onclick="@(() => GridPedidos.CancelEditAsync())" style="text-decoration: none; color: red;" href="javascript:void(0);"></a>
                                    </CellEditTemplate>
                                </DxGridCommandColumn>
                                <DxGridSelectionColumn Width="50">
                                </DxGridSelectionColumn>
                                <DxGridDataColumn FieldName="IdPedido" Caption="Pedido" TextAlignment="GridTextAlignment.Center"
                                                  Width="90px" DisplayFormat="F0">
                                    <CellDisplayTemplate Context="cellText">
                                        <span class="p-1 d-block text-left" style="cursor: pointer"
                                              @ondblclick="() => VerDatosPedido(cellText.DisplayText)">
                                            @cellText.DisplayText
                                        </span>
                                    </CellDisplayTemplate>
                                </DxGridDataColumn>
                                <DxGridDataColumn FieldName="MantenerAbierto" Caption="Mant.Ab" Width="50px">
                                    <CellDisplayTemplate Context="checkbox">
                                        <DxCheckBox Checked="@((bool)checkbox.Value)" CssClass="noElegir" ReadOnly="true" />
                                    </CellDisplayTemplate>
                                </DxGridDataColumn>
                                <DxGridDataColumn FieldName="FechaPedido" Width="90px" TextAlignment="GridTextAlignment.Center" />
                                <DxGridDataColumn FieldName="FechaEntregaSolicitada" Width="90px" TextAlignment="GridTextAlignment.Center" />
                                <DxGridDataColumn FieldName="RequeridoEnFecha" Caption="Fecha Entrega" Width="90px" TextAlignment="GridTextAlignment.Center" />
                                <DxGridDataColumn FieldName="Supedido" Caption="Pedido Cliente" TextAlignment="GridTextAlignment.Center" Width="80px" />
                                <DxGridDataColumn FieldName="Urgente" Caption="Urg" Width="30px">
                                    <CellDisplayTemplate Context="checkbox">
                                        <DxCheckBox Checked="@((bool)checkbox.Value)" CssClass="noElegir" ReadOnly="true" />
                                    </CellDisplayTemplate>
                                </DxGridDataColumn>
                                <DxGridDataColumn FieldName="HojasPedido" Caption="Nº Hojas" TextAlignment="GridTextAlignment.Center" Width="80px" />
                                <DxGridDataColumn FieldName="Motivos" Width="205px">
                                    <CellDisplayTemplate Context="cellText">
                                        @{
                                            string summary = cellText.Value?.ToString() ?? string.Empty;
                                            <div class="d-block text-truncate" title="@summary" style="max-width: 205px">
                                                @summary
                                            </div>
                                        }
                                    </CellDisplayTemplate>
                                </DxGridDataColumn>
                                <DxGridDataColumn FieldName="Formato" DisplayFormat="N2" Width="65px" />
                                <DxGridDataColumn FieldName="TxtEstado" Caption=" TextoEstado" Width="100%" />
                                <DxGridDataColumn FieldName="ObsACliente" Width="205px">
                                    <CellDisplayTemplate Context="cellText">
                                        @{
                                            string summary = cellText.Value?.ToString() ?? string.Empty;
                                            <div class="d-block text-truncate" title="@summary" style="max-width: 205px">
                                                @summary
                                            </div>
                                        }
                                    </CellDisplayTemplate>
                                </DxGridDataColumn>
                                <DxGridDataColumn FieldName="FechaFtp" Caption="FTP" Width="90px" />
                                <DxGridDataColumn FieldName="FechaHojalata" Caption="F. Hojalata" Width="90px" />
                                <DxGridDataColumn FieldName="FechaLanzamiento" Caption="F. Lanzamiento" Width="90px" />
                                <DxGridDataColumn FieldName="FechaContrato" Caption="F. Contrato" Width="90px" />
                            </Columns>
                            <EditFormTemplate>
                                @{
                                    var pedido = (PedidoProcesadoDTO)GridPedidos.EditModel;
                                }

                                <DxFormLayout CssClass="w-100">
                                    <DxFormLayoutItem ColSpanLg="4" Context="cc1" CssClass="no-m-t">
                                        <DxFormLayoutItem Caption="Nº Pedido" ColSpanLg="12">
                                            <DxSpinEdit @bind-Value="@pedido.IdPedido" ShowSpinButtons="false" ShowValidationIcon="true" ReadOnly="true" />
                                        </DxFormLayoutItem>
                                        <DxFormLayoutItem Caption="Motivos" ColSpanLg="12">
                                            <DxTextBox @bind-Text="@pedido.Motivos" ShowValidationIcon="true" ReadOnly="true" />
                                        </DxFormLayoutItem>
                                        <DxFormLayoutItem Caption="Formato" ColSpanLg="12">
                                            <DxSpinEdit @bind-Value="@pedido.Formato" ShowSpinButtons="false" ShowValidationIcon="true" ReadOnly="true" />
                                        </DxFormLayoutItem>
                                        <DxFormLayoutItem Caption="Fecha Pedido" ColSpanLg="12">
                                            <DxDateEdit @bind-Date="@pedido.FechaPedido" ShowValidationIcon="true"
                                                        ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" ReadOnly="true" />
                                        </DxFormLayoutItem>
                                        <DxFormLayoutItem Caption="Requerido En Fecha" ColSpanLg="12">
                                            <DxDateEdit @bind-Date="@pedido.RequeridoEnFecha" ShowValidationIcon="true"
                                                        ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" />
                                        </DxFormLayoutItem>
                                    </DxFormLayoutItem>

                                    <DxFormLayoutItem ColSpanLg="4" Context="cc1">
                                        <DxFormLayoutItem Caption="Pedido Cliente" ColSpanLg="12">
                                            <DxTextBox @bind-Text="@pedido.Supedido" ShowValidationIcon="true" ReadOnly="true" />
                                        </DxFormLayoutItem>
                                        <DxFormLayoutItem Caption="Texto Estado" ColSpanLg="12">
                                            <DxTextBox @bind-Text="@pedido.TxtEstado" ShowValidationIcon="true" ReadOnly="true" />
                                        </DxFormLayoutItem>
                                        <DxFormLayoutItem Caption="Nº Hojas" ColSpanLg="12">
                                            <DxSpinEdit @bind-Value="@pedido.HojasPedido" ShowSpinButtons="false" ShowValidationIcon="true" ReadOnly="true" />
                                        </DxFormLayoutItem>
                                        <DxFormLayoutItem Caption="Fecha Entrega Solicitada" ColSpanLg="12">
                                            <DxDateEdit @bind-Date="@pedido.FechaEntregaSolicitada" ShowValidationIcon="true" ReadOnly="true" />
                                        </DxFormLayoutItem>
                                        <DxFormLayoutItem Caption="Obs Cliente" ColSpanLg="12">
                                            <DxMemo @bind-Text="@pedido.ObsACliente" ShowValidationIcon="true" Rows="5" />
                                        </DxFormLayoutItem>
                                    </DxFormLayoutItem>

                                    <DxFormLayoutItem ColSpanLg="4" Context="cc1">
                                        <DxFormLayoutItem ColSpanLg="11">
                                            <DxFormLayoutItem ColSpanLg="5" Context="cc" Caption="Urgente" CssClass="">
                                                <DxCheckBox @bind-Checked="@pedido.Urgente" CheckType="CheckType.Switch"
                                                            Alignment="CheckBoxContentAlignment.SpaceAround"
                                                            ValueChecked="true" ValueUnchecked="false" ReadOnly="true" />
                                            </DxFormLayoutItem>
                                            <DxFormLayoutItem ColSpanLg="5" Context="cc" Caption="Mantener Abierto" CssClass="">
                                                <DxCheckBox @bind-Checked="@pedido.MantenerAbierto" CheckType="CheckType.Switch"
                                                            Alignment="CheckBoxContentAlignment.SpaceAround"
                                                            ValueChecked="true" ValueUnchecked="false" ReadOnly="true" />
                                            </DxFormLayoutItem>
                                        </DxFormLayoutItem>
                                        <DxFormLayoutItem ColSpanLg="12" Caption="Fecha FTP">
                                            <DxDateEdit @bind-Date="@pedido.FechaFtp" ShowValidationIcon="true" ReadOnly="true" />
                                        </DxFormLayoutItem>
                                        <DxFormLayoutItem ColSpanLg="12" Caption="Fecha Hojalata">
                                            <DxDateEdit @bind-Date="@pedido.FechaHojalata" ShowValidationIcon="true" ReadOnly="true" />
                                        </DxFormLayoutItem>
                                        <DxFormLayoutItem ColSpanLg="12" Caption="Fecha Lanzamiento">
                                            <DxDateEdit @bind-Date="@pedido.FechaLanzamiento" ShowValidationIcon="true" ReadOnly="true" />
                                        </DxFormLayoutItem>
                                        <DxFormLayoutItem ColSpanLg="12" Caption="Fecha Contrato">
                                            <DxDateEdit @bind-Date="@pedido.FechaContrato" ShowValidationIcon="true" ReadOnly="true" />
                                        </DxFormLayoutItem>
                                    </DxFormLayoutItem>

                                    <DxFormLayoutItem BeginRow="true" ColSpanMd="12">
                                        <DxButton Context="btn" CssClass="btnSizeM izq" Click="@(() => EditarRegistro("anterior", pedido))"
                                                  IconCssClass="oi oi-arrow-thick-left" RenderStyle="ButtonRenderStyle.Info" />
                                        <DxButton Context="btn" CssClass="btnSizeM der" Click="@(() => EditarRegistro("siguiente", pedido))"
                                                  IconCssClass="oi oi-arrow-thick-right" RenderStyle="ButtonRenderStyle.Info" />
                                    </DxFormLayoutItem>
                                </DxFormLayout>

                            </EditFormTemplate>
                        </DxGrid>
                    </DxLoadingPanel>
                </DxFormLayoutGroup>

                <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" BeginRow="true" CssClass="py-3" Visible="@_seleccionados.Any()">
                    <DxFormLayoutItem Context="c1" ColSpanLg="2">
                        <DxDateEdit ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" NullText="Selecciona una fecha..."
                                    DisplayFormat="dd/MM/yyyy" @bind-Date="_fecha" Enabled="@_seleccionados.Any()">
                        </DxDateEdit>
                    </DxFormLayoutItem>
                    <DxFormLayoutItem Context="c2" ColSpanLg="2">
                        <DxButton RenderStyle="ButtonRenderStyle.Success" Enabled="@_fecha.HasValue"
                                  Click="@((e) => ActualizarDatos("fecha"))" Text="Asignar Fecha Entrega">
                        </DxButton>
                        <DxButton RenderStyle="ButtonRenderStyle.Info" Enabled="@_seleccionados.Any()"
                                  Click="@((e) => ActualizarDatos("limpiar"))" Text="Limpiar Observaciones">
                        </DxButton>
                    </DxFormLayoutItem>
                </DxFormLayoutGroup>
                <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" BeginRow="true" CssClass="py-3">
                    <DxFormLayoutItem Context="campoDropdown" ColSpanLg="2">
                        <DxButton Click="@(Grid_ExportXlsx)" RenderStyle="ButtonRenderStyle.Info"
                                  Enabled="@(DatosPedidos!=null)" CssClass="float-right me-2" Text="Exportar" />
                    </DxFormLayoutItem>
                </DxFormLayoutGroup>
            </DxFormLayout>
        </div>
    </Authorized>
    <NotAuthorized>
        <NoPuedesPasar />
    </NotAuthorized>
</AuthorizeView>




@code {

    public int? _idCliente { get; set; }
    public DateTime? _fecha { get; set; }
    ListResult<ClienteDropdownDTO> resultClientes { get; set; }
    SingleResult<DatosGeneralesDTO> resultDatosGenerales { get; set; }
    List<PedidoProcesadoDTO> DatosPedidos { get; set; }
    DxGrid? _gridPedidos;
    IReadOnlyList<object> _seleccionados = new List<object>();
    List<ClienteDropdownDTO> datosClientes { get; set; }
    private int _visibleIndex { get; set; }
    private bool PanelVisible { get; set; }
    DxGrid? GridPedidos;
    DxComboBox<ClienteDropdownDTO, int?> _component;
    JsonSerializerOptions options = new JsonSerializerOptions
        {
            ReferenceHandler = ReferenceHandler.Preserve,
            PropertyNameCaseInsensitive = true
        };

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            await _component.FocusAsync();
        }
    }

    protected override async Task OnInitializedAsync()
    {
        _spinnerService.Show();
        await CargarClientes();
        _spinnerService.Hide();
    }

    private async Task CargarClientes()
    {
        resultDatosGenerales = await Http.GetFromJsonAsync<SingleResult<DatosGeneralesDTO>>("DatosGenerales");
        if (resultDatosGenerales.Errors.Any())
        {
            ToastService.ShowError(resultDatosGenerales.Errors.First());
        }
        else
        {
            datosClientes = resultDatosGenerales.Data.Clientes.OrderBy(o => o.CodigoCliente).ToList();
        }
    }

    private async Task OnTextChanged(ClienteDropdownDTO c)
    {
        _idCliente = c.CodigoCliente;
        await ConsultarFechas();
    }

    private async Task ConsultarFechas()
    {
        PanelVisible = true;
        var resultConsultaFechas = await Http.GetFromJsonAsync<ListResult<PedidoProcesadoDTO>>($"DatosGenerales/consultafechas?idcliente={_idCliente}");
        if (resultConsultaFechas.Errors.Any())
        {
            ToastService.ShowError(resultDatosGenerales.Errors.First());
        }
        else
        {
            DatosPedidos = resultConsultaFechas.Data;
        }
        PanelVisible = false;
    }
    private async Task StartEditing(int index)
    {
        _visibleIndex = index;
        await GridPedidos.StartEditRowAsync(index);
    }
    private async Task EditarRegistro(string posicion, PedidoProcesadoDTO datosGrid)
    {
        await GridPedidos.SaveChangesAsync();

        if (posicion == "anterior")
            _visibleIndex--;
        else
            _visibleIndex++;

        if (_visibleIndex < 0)
            ToastService.ShowWarning($"Estás en el primer registro, no se puede retroceder más");
        else if (_visibleIndex >= DatosPedidos.Count)
            ToastService.ShowWarning($"Estás en el último registro, no se puede avanzar más");
        else
        {
            await GridPedidos.StartEditRowAsync(_visibleIndex);
        }
    }
    async Task Grid_EditModelSaving(GridEditModelSavingEventArgs e)
    {
        PanelVisible = true;
        var editableProg = (PedidoProcesadoDTO)e.EditModel;

        //var jsonSerializerOptions = new JsonSerializerOptions
        //{
        //    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
        //};
        //var jsonString = JsonSerializer.Serialize(editableProg, jsonSerializerOptions);
        //HttpContent content = new StringContent(jsonString, Encoding.UTF8, "application/json");


        var response = await Http.PostAsJsonAsync($"Pedido/actualizardatos", editableProg);
        var dato2s = await response.Content.ReadAsStringAsync();
        var datos = await response.Content.ReadFromJsonAsync<SingleResult<int>>(options);
        if (datos.Errors.Any() && datos.Data != 1)
        {
            ToastService.ShowError(datos.Errors.FirstOrDefault());
        }
        else
        {
            ToastService.ShowSuccess("Datos del pedido actualizados.");

            StateHasChanged();
        }

        await ConsultarFechas();
        PanelVisible = false;

    }

    private async Task VerDatosPedido(string context)
    {
        await Js.InvokeVoidAsync("open", $"{Navigation.BaseUri}ProgramacionPedidos/{context}", "_blank");
    }

    private async Task ActualizarDatos(string dato)
    {
        var lista = _seleccionados.Cast<PedidoProcesadoDTO>().ToList();
        if (!lista.Any() || (dato == "fecha" && !_fecha.HasValue))
        {
            return;
        }
        PanelVisible = true;
        var datos = new PedidosFechaDTO
            {
                Fecha = dato == "fecha" ? _fecha.Value : null,
                LimpiarObs = dato == "limpiar",
                Pedidos = lista.Select(o => o.IdPedido.Value).ToList()
            };

        var response = await Http.PostAsJsonAsync($"Pedido/asignarfechamultiple", datos);
        var result = await response.Content.ReadFromJsonAsync<SingleResult<int>>(options);
        if (result.Errors.Any() && result.Data != 1)
        {
            ToastService.ShowError(result.Errors.FirstOrDefault());
        }
        else
        {
            ToastService.ShowSuccess("Datos actualizados.");
            _seleccionados = new List<object>();
            StateHasChanged();
        }

        await ConsultarFechas();
        PanelVisible = false;
    }
    async Task Grid_ExportXlsx()
    {
        await GridPedidos.ExportToXlsxAsync($"{DateTime.Now:yyyyMMdd}_PedidosHojalata_Filtrados", new GridXlExportOptions()
        {
            //ExportSelectedRowsOnly = ExportSelectedRowsOnly,
            //CustomizeCell = OnCustomizeCell
        });
    }
}