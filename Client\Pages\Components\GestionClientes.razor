﻿@using ProgramadorGeneralBLZ.Shared.DTO
@using ProgramadorGeneralBLZ.Shared
@inject HttpClient Http
@inject SpinnerService SpinnerService
@inject IToastService ToastService

<div class="h-100 overflow-auto px-2 py-1">
    <DxGrid Data="@clientes" SizeMode="SizeMode.Medium"
            CssClass="ch-320 smallFont progGrid" PageSize="18"
            KeyFieldName="Id" Context="GridClientes"
            AllowSort="true" EditNewRowPosition="GridEditNewRowPosition.Top"
            ShowFilterRow="true" ValidationEnabled="true"
            EditMode="GridEditMode.EditRow" @ref="Grid" PagerPosition="GridPagerPosition.TopAndBottom"
            EditorRenderMode="GridEditorRenderMode.Integrated"
            EditModelSaving="GridClientes_EditModelSaving"
            DataItemDeleting="Grid_DataItemDeleting">
        <Columns>
            <DxGridCommandColumn Width="80px">
                <HeaderTemplate>
                    <a class="oi oi-plus" @onclick="@(() => Grid.StartEditNewRowAsync())" style="text-decoration: none;color: lightskyblue;" href="javascript:void(0);"></a>
                </HeaderTemplate>
                <CellDisplayTemplate>
                    <a class="oi oi-pencil" @onclick="@(() => Grid.StartEditRowAsync(context.VisibleIndex))" style="text-decoration: none; padding-right: 15px; color: #c75fff;" href="javascript:void(0);"></a>
                    <a class="oi oi-x" @onclick="@(() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex))" style="text-decoration: none; color: red;" href="javascript:void(0);"></a>
                </CellDisplayTemplate>
                <CellEditTemplate>
                    <a class="oi oi-arrow-thick-bottom" @onclick="@(() => Grid.SaveChangesAsync())" style="text-decoration: none; padding-right: 15px; color: greenyellow; margin-right: 6px; margin-top: 3px;" href="javascript:void(0);"></a>
                    <a class="oi oi-x" @onclick="@(() => Grid.CancelEditAsync())" style="text-decoration: none; color: red;" href="javascript:void(0);"></a>
                </CellEditTemplate>
            </DxGridCommandColumn>
            <DxGridDataColumn FieldName="@nameof(ClienteDTO.Id)" Visible="false" />
            <DxGridDataColumn FieldName="@nameof(ClienteDTO.CodigoCliente)" Caption="Código Cliente" Width="80" TextAlignment="GridTextAlignment.Center"/>
            <DxGridDataColumn FieldName="@nameof(ClienteDTO.NombreCliente)" Caption="Nombre Cliente" Width="180" FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
            <DxGridDataColumn FieldName="@nameof(ClienteDTO.RutaPlanos)" Caption="Ruta Planos" Width="180" FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
            <DxGridDataColumn FieldName="@nameof(ClienteDTO.NombreContacto)" Caption="Nombre Contacto" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="180" />
            <DxGridDataColumn FieldName="@nameof(ClienteDTO.NumeroTelefono)" Caption="Número Teléfono" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="120" />
            <DxGridDataColumn FieldName="@nameof(ClienteDTO.NumeroFax)" Caption="Número Fax" FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
            <DxGridDataColumn FieldName="@nameof(ClienteDTO.Email)" Caption="Email" Width="180" />
            <DxGridDataColumn FieldName="@nameof(ClienteDTO.EmailCargas)" Caption="Email Cargas" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="180" />
            <DxGridDataColumn FieldName="@nameof(ClienteDTO.EmailQ)" Caption="Email Q" FilterRowOperatorType="GridFilterRowOperatorType.Contains"/>
            <DxGridDataColumn FieldName="@nameof(ClienteDTO.PesoMxpaquete)" Caption="Peso Máx. Paquete" FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
            <DxGridDataColumn FieldName="@nameof(ClienteDTO.HojaMuestra)" Caption="Hoja Muestra" FilterRowOperatorType="GridFilterRowOperatorType.Contains">
                <CellDisplayTemplate Context="checkbox">
                    <DxCheckBox Checked="@((bool)checkbox.Value)" ReadOnly="true"/>
                </CellDisplayTemplate>
            </DxGridDataColumn>
            <DxGridDataColumn FieldName="@nameof(ClienteDTO.ObsEmbalaje)" Caption="Observaciones Embalaje" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="280" />
            <DxGridDataColumn FieldName="@nameof(ClienteDTO.Direccion)" Caption="Dirección" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="280" />
        </Columns>
        <DataColumnCellEditTemplate>
            @{
                var cliente = (ClienteDTO)GridClientes.EditModel;
            }
            @switch (GridClientes.DataColumn.FieldName)
            {
                case "CodigoCliente":
                    <DxSpinEdit @bind-Value="@cliente.CodigoCliente" ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "NombreCliente":
                    <DxTextBox @bind-Text="@cliente.NombreCliente" />
                    break;
                case "RutaPlanos":
                    <DxTextBox @bind-Text="@cliente.RutaPlanos" />
                    break;
                case "NombreContacto":
                    <DxTextBox @bind-Text="@cliente.NombreContacto" />
                    break;
                case "NumeroTelefono":
                    <DxTextBox @bind-Text="@cliente.NumeroTelefono" />
                    break;
                case "NumeroFax":
                    <DxTextBox @bind-Text="@cliente.NumeroFax" />
                    break;
                case "Email":
                    <DxTextBox @bind-Text="@cliente.Email" />
                    break;
                case "EmailCargas":
                    <DxTextBox @bind-Text="@cliente.EmailCargas" />
                    break;
                case "EmailQ":
                    <DxTextBox @bind-Text="@cliente.EmailQ" />
                    break;
                case "PesoMxpaquete":
                    <DxSpinEdit @bind-Value="@cliente.PesoMxpaquete" ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "HojaMuestra":
                    <DxCheckBox CssClass="d-inline-block" @bind-Checked="@cliente.HojaMuestra" ValueChecked=true ValueUnchecked=false />
                    break;
                case "ObsEmbalaje":
                    <DxMemo @bind-Text="@cliente.ObsEmbalaje" />
                    break;
                case "Direccion":
                    <DxTextBox @bind-Text="@cliente.Direccion" />
                    break;
            }
        </DataColumnCellEditTemplate>
    </DxGrid>
</div>

@code {
    DxGrid? Grid;
    List<ClienteDTO> clientes = new List<ClienteDTO>();

    protected override async Task OnInitializedAsync()
    {
        SpinnerService.Show();
        await LoadData();
        SpinnerService.Hide();
    }

    async Task LoadData()
    {
        clientes = await Http.GetFromJsonAsync<List<ClienteDTO>>("GestionTablasV2/Cliente");
    }

    async Task GridClientes_EditModelSaving(GridEditModelSavingEventArgs e)
    {
        SpinnerService.Show();
        var dest = (ClienteDTO)e.EditModel;
        var response = e.IsNew == false
            ? await Http.PutAsJsonAsync($"GestionTablasV2/Cliente/{dest.Id}", dest)
            : await Http.PostAsJsonAsync("GestionTablasV2/Cliente", dest);
        if (response.IsSuccessStatusCode)
            await LoadData();
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            ToastService.ShowError(error);
        }
        SpinnerService.Hide();
    }

    async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        SpinnerService.Show();
        var item = (ClienteDTO)e.DataItem;
        var response = await Http.DeleteAsync($"GestionTablasV2/Cliente/{item.Id}");
        if (response.IsSuccessStatusCode)
            await LoadData();
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            ToastService.ShowError(error);
        }
        SpinnerService.Hide();
    }
    async Task Delete(ClienteDTO item)
    {
        SpinnerService.Show();
        var response = await Http.DeleteAsync($"GestionTablasV2/Cliente/{item.Id}");
        if (response.IsSuccessStatusCode)
            await LoadData();
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            ToastService.ShowError(error);
        }
        SpinnerService.Hide();
    }
}
