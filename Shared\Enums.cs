﻿using System.ComponentModel.DataAnnotations;

namespace ProgramadorGeneralBLZ.Shared
{
    public class Enums
    {
        public enum Densidad : int
        {
            Aluminio = 2600,
            Acero = 7860
        }

        public enum TipoReproceso
        {
            Tipo3 = 3,
            Tipo6 = 6
        }
        public enum TipoLimpias
        {
            Encima = 10,
            <PERSON><PERSON><PERSON> = 30,
            <PERSON><PERSON> = 60,
            NoExiste = 0
        }
        public enum TipoFiltroPedidos
        {
            Blanco = 0,
            Procesos = 1,
            Hojalata = 2
        }

        public enum TipoTintas
        {
            [Display(Name="LITO SOLO PINZA")]
            LitoPinza=996,
            [Display(Name= "TINTA MATE")]
            TintaMate = 997,
            [Display(Name= "LITOGRAFÍA INTERIOR (RETIRA)")]
            Retira = 998,
            [Display(Name= "LITOGRAFÍA")]
            Lito = 999
        }
        public enum EstadoTintas
        {
            [Display(Name="Sin Aplicar")]
            SinAplicar,
            [Display(Name = "Programado")]
            Programado,
            [Display(Name = "Aplicado")]
            Aplicado
        }

        public enum TipoFicheros
        {
            Motivo,
            Fotolito,
            FichaTecnica,
            Plano,
            Otro
        }

        public enum TipoPedido
        {
            Barnizado,
            Litografia,
            Corte,
            Seleccion,
            Laminado,
            Combinado
        }
        public enum TipoUpdate
        {
            CabecerasRapido,
            CabecerasRapidoV2,
            CabecerasPuntual,
            HojasPuntual,
            CabecerasTotal,
            HojasTotal,
            Productos,
            Tuberias,
            CorregirMotivos,
            Pruebas
        }

        public enum TipoMaquina
        {
            Todas,
            Barnizadora,
            Impresora,
            LineaCorte,
            Seleccionadora
        }

        public enum StatusOrden
        {
            [Display(Name = "N/A")]
            NoAplica,
            [Display(Name = "Bloqueado")]
            B,
            [Display(Name = "Bloqueo Lib Manual")]
            Bl,
            [Display(Name = "Liberado")]
            L,
            [Display(Name = "Cierre Técnico")]
            C
        }

        public enum StatusOperacion
        {

            [Display(Name = "N/A")]
            NoAplica,
            [Display(Name = "Bloqueado")]
            B,
            [Display(Name = "Prog Secuenciador")]
            P0,
            [Display(Name = "Enviado SAP")]
            P1,
            [Display(Name = "Enviado PROTEO")]
            P2,
            [Display(Name = "Leido PROTEO")]
            P3,
            [Display(Name = "Bloqueado en PROTEO")]
            Pb,
            [Display(Name = "Finalizado")]
            F,
            [Display(Name = "Finalizado Parcial")]
            Fp
        }

        public enum ProgramacionesPantalla_EstadosPedido
        {
            [Display(Name = "Sin Empezar")]
            SinEmpezar = 1,
            [Display(Name = "Empezado")]
            Empezado = 2,
            [Display(Name = "Detenido")]
            Detenido = 3,
            [Display(Name = "Terminado")]
            Terminado = 4,
            [Display(Name = "Retirado")]
            Retirado = 5
        }

        public enum ProgramacionesPantalla_TiposLog
        {
            [Display(Name = "Programación Actualizada")]
            ProgramacionActualizada = 1,
            [Display(Name = "Cambio Estado")]
            CambioEstado = 2,
            [Display(Name = "Actualización Rodillo")]
            ActualizacionRodillo = 3,
            [Display(Name = "Actualización Nota")]
            ActualizacionNota = 4
        }
    }
}
