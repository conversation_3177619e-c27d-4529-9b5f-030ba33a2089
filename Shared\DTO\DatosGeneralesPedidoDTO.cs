﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProgramadorGeneralBLZ.Shared.DTO
{
    public class DatosGeneralesPedidoDTO
    {
        public int? Id { get; set; }
        public int? IdPedido { get; set; }
        public string? Supedido { get; set; }
        public int? IdCliente { get; set; }
        public DateTime? FechaPedido { get; set; }
        public DateTime? RequeridoEnFecha { get; set; }
        public DateTime? ActualizacionFechaRequerida { get; set; } // string?_temporal
        public int? HojasPedido { get; set; }
        public int? HojasTerminadas { get; set; }
        public int? HojasLlevadas { get; set; }
        public string? Motivos { get; set; }
        public string? TipoElemento { get; set; }
        public float? Formato { get; set; }//double
        public string? Plano { get; set; }
        public int? Pi1ped { get; set; }
        public int? Hojaspi1ped { get; set; }
        public int? Pi2ped { get; set; }
        public int? Hojaspi2ped { get; set; }
        public int? Pi3ped { get; set; }
        public int? Hojaspi3ped { get; set; }
        public int? Pe1ped { get; set; }
        public int? Hojaspe1ped { get; set; }
        public int? Pe2ped { get; set; }
        public int? Hojaspe2ped { get; set; }
        public int? Pe3ped { get; set; }
        public int? Hojaspe3ped { get; set; }
        public int? C01ped { get; set; }
        public int? Hojasco1ped { get; set; }
        public int? C02ped { get; set; }
        public int? Hojasco2ped { get; set; }
        public int? C03ped { get; set; }
        public int? Hojasco3ped { get; set; }
        public int? C04ped { get; set; }
        public int? Hojasco4ped { get; set; }
        public int? C05ped { get; set; }
        public int? Hojasco5ped { get; set; }
        public int? C06ped { get; set; }
        public int? Hojasco6ped { get; set; }
        public int? C07ped { get; set; }
        public int? Hojasco7ped { get; set; }
        public int? C08ped { get; set; }
        public int? Hojasco8ped { get; set; }
        public int? C09ped { get; set; }
        public int? Hojasco9ped { get; set; }
        public int? Co10ped { get; set; }
        public int? Hojasco10ped { get; set; }
        public int? Co11ped { get; set; }
        public int? Hojasco11ped { get; set; }
        public int? Cd1ped { get; set; }
        public int? Hojascd1ped { get; set; }
        public int? Cd2ped { get; set; }
        public int? Hojascd2ped { get; set; }
        public int? Cd3ped { get; set; }
        public int? Hojascd3ped { get; set; }
        public int? Cd4ped { get; set; }
        public int? Hojascd4ped { get; set; }
        public int? IdPri1 { get; set; }
        public float? Capai1 { get; set; } // string?_temporal
        public int? IdPri2 { get; set; }
        public float? Capai2 { get; set; } // string?_temporal
        public int? IdPri3 { get; set; }
        public float? Capai3 { get; set; } // string?_temporal
        public int? IdPre1 { get; set; }
        public float? Capae1 { get; set; } // string?_temporal
        public int? IdPre2 { get; set; }
        public float? Capae2 { get; set; } // string?_temporal
        public int? IdPre3 { get; set; }
        public float? Capae3 { get; set; } // string?_temporal
        public int? Ancho_hjlta { get; set; }
        public int? Largo_hjlta { get; set; }
        public int? Espesor_hjlta { get; set; }
        public int? Tipo_hjlta { get; set; }//int???????
        public string? ClaseSustrato { get; set; }
        public string? Corte2 { get; set; }
        public string? Diferencial { get; set; }
        public string? Obs1 { get; set; }
        public string? Obs2 { get; set; }
        public string? Obs3 { get; set; }
        public string? Obsrayas { get; set; }
        public string? Obsflejado { get; set; }
        public string? Obsarticulo { get; set; }
        public string? WO { get; set; }
        public string? EstadoTintas { get; set; }
        public int? LineaTintas { get; set; }
        public bool? PlanchasPedidas { get; set; }
        public DateTime? FechaPlanchas { get; set; }
        public bool? Terminado { get; set; }
        public string? ObservacionesPedido { get; set; }
        public string? TipoPedido { get; set; }
        public bool? Anulado { get; set; }
        public bool? PlanchasPasadas { get; set; }
        public bool? MantenerAbierto { get; set; }
        public string? Obs_a_cliente { get; set; }
        public string? TipoBarnizado { get; set; }
        public string? TipoEnvase { get; set; }
        public string? Embuticion { get; set; }
        public bool? SINCARPETA { get; set; }
        public string? ObsProgramacion { get; set; }
        public bool? VistoObsProgramacion { get; set; }
        public bool? Triptico { get; set; }
        public DateTime? FechaEnvioTriptico { get; set; }
        public DateTime? FechaOKTriptico { get; set; }
        public string? ObsTriptico { get; set; }
        public bool? NoIncluirEnListado { get; set; }
        public int? IdpedidoInformix { get; set; }
        public DateTime? UltimaModificacion { get; set; }
        public string? Estado { get; set; }
        public bool? Revisado { get; set; }
        public DateTime? FechaUltRevision { get; set; }
        public double? Altura_cuerpos { get; set; }
        public bool? Hojalata_YA_sacada { get; set; }
        public bool? URGENTE { get; set; }
        public bool? BarnizadoWoW { get; set; }
        public decimal? PrecioHoja { get; set; }
        public DateTime? FechaFin { get; set; }
        public DateTime? FechaEntregaSolicitada { get; set; }
        public double? Sup { get; set; }//
        public int? Idcodigoaplicacion { get; set; }
        public int? Hojasaplicadas { get; set; }
        public string? Posicion { get; set; }
        public double? GRMBAZ { get; set; }
        public int? CODBARNIZ { get; set; }
        public float? Solidos { get; set; }
        public double? BarnizNecesario { get; set; }
        public int? Idproductoprioritario { get; set; }
        public float? Gramajeprioritario { get; set; }
        public string? ObservacionesAplicacion { get; set; }
        public double? GRMBAZMIN { get; set; }
        public bool? YaProgramado { get; set; }
        public string? DenominacionPrioritario { get; set; }
        public string? DatosCliente { get; set; }
        ////Campos extra para LITO
        //public DateTime? FechaAsignacionImpresora { get; set; }
        //public DateTime? ImpresionDia { get; set; }
        //public string? TextoEscuadra { get; set; }
        //public string? Tintas { get; set; }

        [NotMapped] public string? DescHojalata { get; set; }
        [NotMapped] public string? TextEstado { get; set; }
        [NotMapped] public string? YaProgramadoPosicion { get; set; }
        [NotMapped] public string? YaProgramadoFecha { get; set; }
        [NotMapped] public string? YaProgramadoLinea { get; set; }
        [NotMapped] public string? Pases { get; set; }
    }
}
