﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.DatoLita01;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Pedidos.Query;

public class GetPlanoByPedidoQuery : IRequest<string>   
{
    public GetPlanoByPedidoQuery(string idPedido)
    {
        IdPedido = idPedido;
    }
    public string IdPedido { get; set; }
}

internal class GetPlanoByPedidoQueryHandler : IRequestHandler<GetPlanoByPedidoQuery, string>
{
    private readonly DatoLita01Context _contextLita01;
    private readonly ProgramadorLitalsaContext _programadorContext;
    public GetPlanoByPedidoQueryHandler(DatoLita01Context contextLita01, ProgramadorLitalsaContext programadorContext)
    {
        _contextLita01 = contextLita01;
        _programadorContext = programadorContext;
    }

    public async Task<string> Handle(GetPlanoByPedidoQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var datosPedido =
                await _programadorContext.PedidoProcesado.FirstOrDefaultAsync(o => o.IdPedido==int.Parse(request.IdPedido), cancellationToken);

            var datosConfig =
                await _programadorContext.TablaCfg.FirstOrDefaultAsync(o => o.Iddato == "Ruta_Planos",
                    cancellationToken);

            var rutaBase = datosConfig.Dato;
            var clientePattern = $"{datosPedido.IdCliente.Value:D5}*";
            var directorios = Directory.GetDirectories(rutaBase, clientePattern);

            if (directorios.Length <= 0) 
                return null;
            var rutaPlanos = directorios[0] + "\\";

            var nombreFichero = string.Empty;

            if (!string.IsNullOrEmpty(datosPedido.Plano))
            {
                var archivos = Directory.GetFiles(rutaPlanos, datosPedido.Plano + "*.pdf");
                nombreFichero = archivos.FirstOrDefault();
            }

            return !string.IsNullOrEmpty(nombreFichero) 
                ? nombreFichero 
                : null;
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: GetPlanoByPedidoQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            throw new Exception(errorText, e);
        }
    }
}