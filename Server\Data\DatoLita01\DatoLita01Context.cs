﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable di**ble
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using ProgramadorGeneralBLZ.Server.Models.DatoLita01;

namespace ProgramadorGeneralBLZ.Server.Data.DatoLita01
{
    public partial class DatoLita01Context : DbContext
    {
        public DatoLita01Context()
        {
        }

        public DatoLita01Context(DbContextOptions<DatoLita01Context> options)
            : base(options)
        {
        }

        public virtual DbSet<AArticu> AArticu { get; set; }
        public virtual DbSet<Acaped> Acaped { get; set; }
        public virtual DbSet<Cabped> Cabped { get; set; }
        public virtual DbSet<Codapli> Codapli { get; set; }
        public virtual DbSet<Codigoaplicacion> Codigoaplicacion { get; set; }
        public virtual DbSet<ECorden> ECorden { get; set; }
        public virtual DbSet<EEorden> EEorden { get; set; }
        public virtual DbSet<Fechas> Fechas { get; set; }
        public virtual DbSet<GClient> GClient { get; set; }
        public virtual DbSet<Matped> Matped { get; set; }
        public virtual DbSet<Motivos> Motivos { get; set; }
        public virtual DbSet<MotivosDetallados> MotivosDetallados { get; set; }
        public virtual DbSet<Observa> Observa { get; set; }
        public virtual DbSet<Parteshorariosstr> Parteshorariosstr { get; set; }
        public virtual DbSet<Planoform> Planoform { get; set; }
        public virtual DbSet<Tipohjlta> Tipohjlta { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
#warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see http://go.microsoft.com/fwlink/?LinkId=723263.
                optionsBuilder.UseSqlServer("Data Source=SERVERP\\SQL2019;Initial Catalog=dato01LITA;Persist Security Info=True;User ID=**;Password=**;Encrypt=True");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.UseCollation("Traditional_Spanish_CI_AS");

            modelBuilder.Entity<AArticu>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("A_ARTICU");

                entity.HasIndex(e => e.Codigo, "a_artic1")
                    .IsUnique();

                entity.HasIndex(e => e.Descrip, "a_artic2");

                entity.HasIndex(e => e.Proveedor, "a_artic3");

                entity.HasIndex(e => e.Obsoleto, "a_artic4");

                entity.HasIndex(e => e.Activare, "a_artic5");

                entity.Property(e => e.Activare)
                    .HasMaxLength(1)
                    .HasColumnName("ACTIVARE")
                    .HasDefaultValueSql("('')")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ancho).HasColumnName("ANCHO");

                entity.Property(e => e.Barnizfami)
                    .HasMaxLength(25)
                    .HasColumnName("BARNIZFAMI")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Barniznatu)
                    .HasMaxLength(25)
                    .HasColumnName("BARNIZNATU")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Bobcorte)
                    .HasMaxLength(1)
                    .HasColumnName("BOBCORTE")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Bobmargen).HasColumnName("BOBMARGEN");

                entity.Property(e => e.Bobmedida).HasColumnName("BOBMEDIDA");

                entity.Property(e => e.Bobprecio).HasColumnName("BOBPRECIO");

                entity.Property(e => e.Bpa)
                    .HasMaxLength(1)
                    .HasColumnName("BPA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Calcular)
                    .HasMaxLength(1)
                    .HasColumnName("CALCULAR")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Cantped).HasColumnName("CANTPED");

                entity.Property(e => e.Cantpedcli).HasColumnName("CANTPEDCLI");

                entity.Property(e => e.Certifica)
                    .HasMaxLength(1)
                    .HasColumnName("CERTIFICA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Codcli)
                    .HasMaxLength(5)
                    .HasColumnName("CODCLI")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Codigo)
                    .HasMaxLength(25)
                    .HasColumnName("CODIGO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Codigo2)
                    .HasMaxLength(25)
                    .HasColumnName("CODIGO2")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Codprovee)
                    .HasMaxLength(50)
                    .HasColumnName("CODPROVEE")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Coefic).HasColumnName("COEFIC");

                entity.Property(e => e.Comision).HasColumnName("COMISION");

                entity.Property(e => e.Costoini).HasColumnName("COSTOINI");

                entity.Property(e => e.Costoini1).HasColumnName("COSTOINI1");

                entity.Property(e => e.Cuenta)
                    .HasMaxLength(7)
                    .HasColumnName("CUENTA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Depo1).HasColumnName("DEPO1");

                entity.Property(e => e.Depo2).HasColumnName("DEPO2");

                entity.Property(e => e.Depo3).HasColumnName("DEPO3");

                entity.Property(e => e.Depo4).HasColumnName("DEPO4");

                entity.Property(e => e.Depo5).HasColumnName("DEPO5");

                entity.Property(e => e.Depo6).HasColumnName("DEPO6");

                entity.Property(e => e.Depo7).HasColumnName("DEPO7");

                entity.Property(e => e.Depo8).HasColumnName("DEPO8");

                entity.Property(e => e.Depo9).HasColumnName("DEPO9");

                entity.Property(e => e.Descrip)
                    .HasMaxLength(80)
                    .HasColumnName("DESCRIP")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Descuento).HasColumnName("DESCUENTO");

                entity.Property(e => e.Dias).HasColumnName("DIAS");

                entity.Property(e => e.Disolven)
                    .HasMaxLength(50)
                    .HasColumnName("DISOLVEN")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ean13)
                    .HasMaxLength(50)
                    .HasColumnName("EAN13")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Entradas).HasColumnName("ENTRADAS");

                entity.Property(e => e.Espesor).HasColumnName("ESPESOR");

                entity.Property(e => e.Estadis)
                    .HasMaxLength(6)
                    .HasColumnName("ESTADIS")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ext0)
                    .HasMaxLength(100)
                    .HasColumnName("EXT0")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ext1)
                    .HasMaxLength(100)
                    .HasColumnName("EXT1")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ext10)
                    .HasMaxLength(100)
                    .HasColumnName("EXT10")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ext11)
                    .HasMaxLength(100)
                    .HasColumnName("EXT11")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ext12)
                    .HasMaxLength(100)
                    .HasColumnName("EXT12")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ext13)
                    .HasMaxLength(100)
                    .HasColumnName("EXT13")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ext14)
                    .HasMaxLength(100)
                    .HasColumnName("EXT14")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ext15)
                    .HasMaxLength(100)
                    .HasColumnName("EXT15")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ext16)
                    .HasMaxLength(100)
                    .HasColumnName("EXT16")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ext17)
                    .HasMaxLength(100)
                    .HasColumnName("EXT17")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ext18)
                    .HasMaxLength(100)
                    .HasColumnName("EXT18")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ext19)
                    .HasMaxLength(100)
                    .HasColumnName("EXT19")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ext2)
                    .HasMaxLength(100)
                    .HasColumnName("EXT2")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ext3)
                    .HasMaxLength(100)
                    .HasColumnName("EXT3")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ext4)
                    .HasMaxLength(100)
                    .HasColumnName("EXT4")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ext5)
                    .HasMaxLength(100)
                    .HasColumnName("EXT5")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ext6)
                    .HasMaxLength(100)
                    .HasColumnName("EXT6")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ext7)
                    .HasMaxLength(100)
                    .HasColumnName("EXT7")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ext8)
                    .HasMaxLength(100)
                    .HasColumnName("EXT8")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ext9)
                    .HasMaxLength(100)
                    .HasColumnName("EXT9")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fabrica).HasColumnName("FABRICA");

                entity.Property(e => e.Fecalta)
                    .HasColumnType("datetime")
                    .HasColumnName("FECALTA");

                entity.Property(e => e.Fecentra)
                    .HasColumnType("datetime")
                    .HasColumnName("FECENTRA");

                entity.Property(e => e.Fechab)
                    .HasColumnType("datetime")
                    .HasColumnName("FECHAB");

                entity.Property(e => e.Fecini)
                    .HasColumnType("datetime")
                    .HasColumnName("FECINI");

                entity.Property(e => e.Fecreg)
                    .HasColumnType("datetime")
                    .HasColumnName("FECREG");

                entity.Property(e => e.Fscpor).HasColumnName("FSCPOR");

                entity.Property(e => e.Fscrecicla)
                    .HasMaxLength(1)
                    .HasColumnName("FSCRECICLA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fsctipo)
                    .HasMaxLength(20)
                    .HasColumnName("FSCTIPO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Gramaje).HasColumnName("GRAMAJE");

                entity.Property(e => e.Largo).HasColumnName("LARGO");

                entity.Property(e => e.Lote)
                    .HasMaxLength(30)
                    .HasColumnName("LOTE")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Loteubiobl)
                    .HasMaxLength(1)
                    .HasColumnName("LOTEUBIOBL")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Memoria)
                    .HasColumnType("ntext")
                    .HasColumnName("MEMORIA");

                entity.Property(e => e.Mppt)
                    .HasMaxLength(1)
                    .HasColumnName("MPPT")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Nolistado)
                    .HasMaxLength(1)
                    .HasColumnName("NOLISTADO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Obslabo)
                    .HasMaxLength(254)
                    .HasColumnName("OBSLABO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Obsmaq)
                    .HasMaxLength(254)
                    .HasColumnName("OBSMAQ")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Obsoleto)
                    .HasMaxLength(1)
                    .HasColumnName("OBSOLETO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Pcostomed).HasColumnName("PCOSTOMED");

                entity.Property(e => e.Pcostomed1).HasColumnName("PCOSTOMED1");

                entity.Property(e => e.Pedclimin).HasColumnName("PEDCLIMIN");

                entity.Property(e => e.Peso).HasColumnName("PESO");

                entity.Property(e => e.Pesovar).HasColumnName("PESOVAR");

                entity.Property(e => e.Porceplanr).HasColumnName("PORCEPLANR");

                entity.Property(e => e.Proveedor)
                    .HasMaxLength(5)
                    .HasColumnName("PROVEEDOR")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Psiniva).HasColumnName("PSINIVA");

                entity.Property(e => e.Psiniva1).HasColumnName("PSINIVA1");

                entity.Property(e => e.Registro)
                    .HasMaxLength(9)
                    .HasColumnName("REGISTRO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Reserva).HasColumnName("RESERVA");

                entity.Property(e => e.Salidas).HasColumnName("SALIDAS");

                entity.Property(e => e.Sanitario)
                    .HasMaxLength(1)
                    .HasColumnName("SANITARIO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Stock).HasColumnName("STOCK");

                entity.Property(e => e.Stockini).HasColumnName("STOCKINI");

                entity.Property(e => e.Stockmin).HasColumnName("STOCKMIN");

                entity.Property(e => e.Tempseca1).HasColumnName("TEMPSECA1");

                entity.Property(e => e.Tempseca2).HasColumnName("TEMPSECA2");

                entity.Property(e => e.Tempseca3).HasColumnName("TEMPSECA3");

                entity.Property(e => e.Tempseca4).HasColumnName("TEMPSECA4");

                entity.Property(e => e.Tintabase1)
                    .HasMaxLength(25)
                    .HasColumnName("TINTABASE1")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Tintabase2)
                    .HasMaxLength(25)
                    .HasColumnName("TINTABASE2")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Tintabase3)
                    .HasMaxLength(25)
                    .HasColumnName("TINTABASE3")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Tintabase4)
                    .HasMaxLength(25)
                    .HasColumnName("TINTABASE4")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Tintabase5)
                    .HasMaxLength(25)
                    .HasColumnName("TINTABASE5")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Tintabase6)
                    .HasMaxLength(25)
                    .HasColumnName("TINTABASE6")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Tintapeso1).HasColumnName("TINTAPESO1");

                entity.Property(e => e.Tintapeso2).HasColumnName("TINTAPESO2");

                entity.Property(e => e.Tintapeso3).HasColumnName("TINTAPESO3");

                entity.Property(e => e.Tintapeso4).HasColumnName("TINTAPESO4");

                entity.Property(e => e.Tintapeso5).HasColumnName("TINTAPESO5");

                entity.Property(e => e.Tintapeso6).HasColumnName("TINTAPESO6");

                entity.Property(e => e.Tipocomi)
                    .HasMaxLength(1)
                    .HasColumnName("TIPOCOMI")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Tipoiva)
                    .HasMaxLength(2)
                    .HasColumnName("TIPOIVA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Tipopresu)
                    .HasMaxLength(15)
                    .HasColumnName("TIPOPRESU")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Txtcompra)
                    .HasMaxLength(10)
                    .HasColumnName("TXTCOMPRA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Txtstock)
                    .HasMaxLength(10)
                    .HasColumnName("TXTSTOCK")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ubica)
                    .HasMaxLength(3)
                    .HasColumnName("UBICA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ubicacion)
                    .HasMaxLength(10)
                    .HasColumnName("UBICACION")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Udepo1)
                    .HasMaxLength(10)
                    .HasColumnName("UDEPO1")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Udepo2)
                    .HasMaxLength(10)
                    .HasColumnName("UDEPO2")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Udepo3)
                    .HasMaxLength(10)
                    .HasColumnName("UDEPO3")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Udepo4)
                    .HasMaxLength(10)
                    .HasColumnName("UDEPO4")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Udepo5)
                    .HasMaxLength(10)
                    .HasColumnName("UDEPO5")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Udepo6)
                    .HasMaxLength(10)
                    .HasColumnName("UDEPO6")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Udepo7)
                    .HasMaxLength(10)
                    .HasColumnName("UDEPO7")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Udepo8)
                    .HasMaxLength(10)
                    .HasColumnName("UDEPO8")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Udepo9)
                    .HasMaxLength(10)
                    .HasColumnName("UDEPO9")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ultalba)
                    .HasMaxLength(9)
                    .HasColumnName("ULTALBA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ultcant).HasColumnName("ULTCANT");

                entity.Property(e => e.Ultcosto).HasColumnName("ULTCOSTO");

                entity.Property(e => e.Ultcosto0).HasColumnName("ULTCOSTO0");

                entity.Property(e => e.Ultcosto01).HasColumnName("ULTCOSTO01");

                entity.Property(e => e.Ultcosto1).HasColumnName("ULTCOSTO1");

                entity.Property(e => e.Ultcosto11).HasColumnName("ULTCOSTO11");

                entity.Property(e => e.Ultcostoe).HasColumnName("ULTCOSTOE");

                entity.Property(e => e.Undcompra)
                    .HasMaxLength(2)
                    .HasColumnName("UNDCOMPRA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Undpedido)
                    .HasMaxLength(10)
                    .HasColumnName("UNDPEDIDO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Undstock)
                    .HasMaxLength(2)
                    .HasColumnName("UNDSTOCK")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Undventa)
                    .HasMaxLength(2)
                    .HasColumnName("UNDVENTA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Unidades).HasColumnName("UNIDADES");

                entity.Property(e => e.Valentra).HasColumnName("VALENTRA");

                entity.Property(e => e.Valentra1).HasColumnName("VALENTRA1");

                entity.Property(e => e.Valoracion)
                    .HasMaxLength(1)
                    .HasColumnName("VALORACION")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Val**lid).HasColumnName("VALSALID");

                entity.Property(e => e.Val**lid1).HasColumnName("VALSALID1");

                entity.Property(e => e.Velhorno1).HasColumnName("VELHORNO1");

                entity.Property(e => e.Velhorno2).HasColumnName("VELHORNO2");

                entity.Property(e => e.Velhorno3).HasColumnName("VELHORNO3");

                entity.Property(e => e.Velhorno4).HasColumnName("VELHORNO4");

                entity.Property(e => e.Vismax).HasColumnName("VISMAX");

                entity.Property(e => e.Vismin).HasColumnName("VISMIN");
            });

            modelBuilder.Entity<Acaped>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("acaped");

                entity.Property(e => e.Buaacp).HasColumnName("buaacp");

                entity.Property(e => e.Buiacp).HasColumnName("buiacp");

                entity.Property(e => e.Codcli).HasColumnName("codcli");

                entity.Property(e => e.Empacp)
                    .IsRequired()
                    .HasMaxLength(1)
                    .IsUnicode(false)
                    .HasColumnName("empacp");

                entity.Property(e => e.Numacp).HasColumnName("numacp");

                entity.Property(e => e.Staacp).HasColumnName("staacp");

                entity.Property(e => e.Stiacp).HasColumnName("stiacp");
            });

            modelBuilder.Entity<Cabped>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("CABPED");

                entity.Property(e => e.Altura).HasColumnName("altura");

                entity.Property(e => e.Ancho)
                    .HasMaxLength(25)
                    .HasColumnName("ancho")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Bulped).HasColumnName("bulped");

                entity.Property(e => e.Cliped).HasColumnName("cliped");

                entity.Property(e => e.Codarticulo)
                    .HasMaxLength(80)
                    .HasColumnName("codarticulo")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Codartped)
                    .HasMaxLength(25)
                    .HasColumnName("codartped")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Codigo)
                    .HasMaxLength(7)
                    .HasColumnName("codigo")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Codmot1).HasColumnName("CODMOT1");

                entity.Property(e => e.Codmot10).HasColumnName("CODMOT10");

                entity.Property(e => e.Codmot11).HasColumnName("CODMOT11");

                entity.Property(e => e.Codmot12).HasColumnName("CODMOT12");

                entity.Property(e => e.Codmot2).HasColumnName("CODMOT2");

                entity.Property(e => e.Codmot3).HasColumnName("CODMOT3");

                entity.Property(e => e.Codmot4).HasColumnName("CODMOT4");

                entity.Property(e => e.Codmot5).HasColumnName("CODMOT5");

                entity.Property(e => e.Codmot6).HasColumnName("CODMOT6");

                entity.Property(e => e.Codmot7).HasColumnName("CODMOT7");

                entity.Property(e => e.Codmot8).HasColumnName("CODMOT8");

                entity.Property(e => e.Codmot9).HasColumnName("CODMOT9");

                entity.Property(e => e.DescripHojalata)
                    .HasMaxLength(80)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Diaped).HasColumnName("diaped");

                entity.Property(e => e.Eleped)
                    .HasMaxLength(1)
                    .HasColumnName("eleped")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Embped)
                    .HasMaxLength(25)
                    .HasColumnName("embped")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Espesor)
                    .HasMaxLength(25)
                    .HasColumnName("espesor")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Estado)
                    .HasMaxLength(10)
                    .HasColumnName("estado")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fecentrega)
                    .HasColumnType("datetime")
                    .HasColumnName("FECENTREGA");

                entity.Property(e => e.FechaFinal).HasColumnType("datetime");

                entity.Property(e => e.Fechaped)
                    .HasMaxLength(10)
                    .HasColumnName("FECHAPED");

                entity.Property(e => e.Fecped)
                    .HasColumnType("datetime")
                    .HasColumnName("fecped");

                entity.Property(e => e.Flejaped)
                    .IsRequired()
                    .HasMaxLength(28)
                    .IsUnicode(false)
                    .HasColumnName("flejaped");

                entity.Property(e => e.Fortintaext1)
                    .HasMaxLength(25)
                    .HasColumnName("fortintaext1")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fortintaext10)
                    .HasMaxLength(25)
                    .HasColumnName("fortintaext10")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fortintaext11)
                    .HasMaxLength(25)
                    .HasColumnName("fortintaext11")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fortintaext12)
                    .HasMaxLength(25)
                    .HasColumnName("fortintaext12")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fortintaext2)
                    .HasMaxLength(25)
                    .HasColumnName("fortintaext2")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fortintaext3)
                    .HasMaxLength(25)
                    .HasColumnName("fortintaext3")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fortintaext4)
                    .HasMaxLength(25)
                    .HasColumnName("fortintaext4")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fortintaext5)
                    .HasMaxLength(25)
                    .HasColumnName("fortintaext5")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fortintaext6)
                    .HasMaxLength(25)
                    .HasColumnName("fortintaext6")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fortintaext7)
                    .HasMaxLength(25)
                    .HasColumnName("fortintaext7")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fortintaext8)
                    .HasMaxLength(25)
                    .HasColumnName("fortintaext8")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fortintaext9)
                    .HasMaxLength(25)
                    .HasColumnName("fortintaext9")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fortintaint1)
                    .HasMaxLength(25)
                    .HasColumnName("fortintaint1")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fortintaint2)
                    .HasMaxLength(25)
                    .HasColumnName("fortintaint2")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fortintaint3)
                    .HasMaxLength(25)
                    .HasColumnName("fortintaint3")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fortintaint4)
                    .HasMaxLength(25)
                    .HasColumnName("fortintaint4")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fortintaint5)
                    .HasMaxLength(25)
                    .HasColumnName("fortintaint5")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fortintaint6)
                    .HasMaxLength(25)
                    .HasColumnName("fortintaint6")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Hfaped).HasColumnName("hfaped");

                entity.Property(e => e.Hojalata)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Hojped).HasColumnName("hojped");

                entity.Property(e => e.Horped)
                    .HasMaxLength(5)
                    .HasColumnName("HORPED");

                entity.Property(e => e.Hpfped).HasColumnName("hpfped");

                entity.Property(e => e.Hteped).HasColumnName("hteped");

                entity.Property(e => e.Largo)
                    .HasMaxLength(25)
                    .HasColumnName("largo")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Numped).HasColumnName("numped");

                entity.Property(e => e.Numtinta)
                    .HasMaxLength(25)
                    .HasColumnName("numtinta")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ob1ped)
                    .IsRequired()
                    .HasMaxLength(5)
                    .IsUnicode(false)
                    .HasColumnName("ob1ped");

                entity.Property(e => e.Ob2ped)
                    .IsRequired()
                    .HasMaxLength(1)
                    .IsUnicode(false)
                    .HasColumnName("ob2ped");

                entity.Property(e => e.Ob3ped)
                    .IsRequired()
                    .HasMaxLength(1)
                    .IsUnicode(false)
                    .HasColumnName("ob3ped");

                entity.Property(e => e.Pe1ped).HasColumnName("pe1ped");

                entity.Property(e => e.Pe2ped).HasColumnName("pe2ped");

                entity.Property(e => e.Pe3ped).HasColumnName("pe3ped");

                entity.Property(e => e.Pi1ped).HasColumnName("pi1ped");

                entity.Property(e => e.Pi2ped).HasColumnName("pi2ped");

                entity.Property(e => e.Pi3ped).HasColumnName("pi3ped");

                entity.Property(e => e.Plaped)
                    .HasMaxLength(25)
                    .HasColumnName("plaped")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Preciohoja).HasColumnName("PRECIOHOJA");

                entity.Property(e => e.Rayasped)
                    .HasMaxLength(80)
                    .HasColumnName("rayasped")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Situacion)
                    .HasMaxLength(4)
                    .HasColumnName("situacion")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Stped).HasColumnName("stped");

                entity.Property(e => e.Supped)
                    .HasMaxLength(80)
                    .HasColumnName("supped")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Tenlped)
                    .HasMaxLength(80)
                    .HasColumnName("tenlped")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Tipo)
                    .HasMaxLength(30)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Tipped)
                    .HasMaxLength(1)
                    .HasColumnName("tipped")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Treped)
                    .HasMaxLength(25)
                    .HasColumnName("treped")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Txttrat1)
                    .HasMaxLength(80)
                    .HasColumnName("txttrat1")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Txttrat2)
                    .HasMaxLength(80)
                    .HasColumnName("txttrat2")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Txttrat3)
                    .HasMaxLength(80)
                    .HasColumnName("txttrat3")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Txttrat4)
                    .HasMaxLength(80)
                    .HasColumnName("txttrat4")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Txttrat5)
                    .HasMaxLength(80)
                    .HasColumnName("txttrat5")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Txttrat6)
                    .HasMaxLength(80)
                    .HasColumnName("txttrat6")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");
            });

            modelBuilder.Entity<Codapli>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("codapli");

                entity.Property(e => e.Codbarniz).HasColumnName("CODBARNIZ");

                entity.Property(e => e.Codbaz).HasColumnName("CODBAZ");

                entity.Property(e => e.Empbaz)
                    .IsRequired()
                    .HasMaxLength(1)
                    .IsUnicode(false)
                    .HasColumnName("EMPBAZ");

                entity.Property(e => e.Grmbaz).HasColumnName("GRMBAZ");

                entity.Property(e => e.Nombaz)
                    .HasMaxLength(40)
                    .HasColumnName("NOMBAZ")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Pm2baz).HasColumnName("PM2BAZ");

                entity.Property(e => e.Txtbaz)
                    .HasColumnType("ntext")
                    .HasColumnName("TXTBAZ");
            });

            modelBuilder.Entity<Codigoaplicacion>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("CODIGOAPLICACION");

                entity.Property(e => e.BarnizNom)
                    .HasMaxLength(255)
                    .HasColumnName("BARNIZ_NOM")
                    .UseCollation("Modern_Spanish_CI_AS");

                entity.Property(e => e.BarnizNomAbrev)
                    .HasMaxLength(255)
                    .HasColumnName("BARNIZ_NOM_ABREV")
                    .UseCollation("Modern_Spanish_CI_AS");

                entity.Property(e => e.CapaAplicacion)
                    .HasMaxLength(255)
                    .UseCollation("Modern_Spanish_CI_AS");

                entity.Property(e => e.Cara)
                    .HasMaxLength(255)
                    .HasColumnName("CARA")
                    .UseCollation("Modern_Spanish_CI_AS");

                entity.Property(e => e.CodRef)
                    .HasMaxLength(255)
                    .HasColumnName("COD_REF")
                    .UseCollation("Modern_Spanish_CI_AS");

                entity.Property(e => e.Codapli)
                    .HasMaxLength(255)
                    .HasColumnName("CODAPLI")
                    .UseCollation("Modern_Spanish_CI_AS");

                entity.Property(e => e.CodapliAbrev)
                    .HasMaxLength(255)
                    .HasColumnName("CODAPLI_ABREV")
                    .UseCollation("Modern_Spanish_CI_AS");

                entity.Property(e => e.Codbar)
                    .HasMaxLength(255)
                    .HasColumnName("CODBAR")
                    .UseCollation("Modern_Spanish_CI_AS");

                entity.Property(e => e.CodigoAplicacion1).HasColumnName("CodigoAplicacion");

                entity.Property(e => e.DescripcionClientes)
                    .HasMaxLength(255)
                    .UseCollation("Modern_Spanish_CI_AS");

                entity.Property(e => e.DescripcionInternaLital**)
                    .HasMaxLength(255)
                    .UseCollation("Modern_Spanish_CI_AS");

                entity.Property(e => e.DescripcionProgramaciones)
                    .HasMaxLength(255)
                    .UseCollation("Modern_Spanish_CI_AS");

                entity.Property(e => e.Familia)
                    .HasMaxLength(255)
                    .HasColumnName("FAMILIA")
                    .UseCollation("Modern_Spanish_CI_AS");

                entity.Property(e => e.GrM2).HasColumnName("GR_M2");

                entity.Property(e => e.GrMinM2).HasColumnName("GR_MIN_M2");

                entity.Property(e => e.Referencia)
                    .HasMaxLength(255)
                    .UseCollation("Modern_Spanish_CI_AS");

                entity.Property(e => e.Smin)
                    .HasMaxLength(255)
                    .UseCollation("Modern_Spanish_CI_AS");

                entity.Property(e => e.Uso)
                    .HasMaxLength(255)
                    .HasColumnName("USO")
                    .UseCollation("Modern_Spanish_CI_AS");
            });

            modelBuilder.Entity<ECorden>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("E_CORDEN");

                entity.HasIndex(e => e.Codigo, "e_corde1")
                    .IsUnique();

                entity.HasIndex(e => e.Plantilla, "e_corde2");

                entity.Property(e => e.Agentes).HasColumnName("AGENTES");

                entity.Property(e => e.Agentes0).HasColumnName("AGENTES0");

                entity.Property(e => e.Almacenado).HasColumnName("ALMACENADO");

                entity.Property(e => e.Campo1)
                    .HasMaxLength(30)
                    .HasColumnName("CAMPO1")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Campo2)
                    .HasMaxLength(30)
                    .HasColumnName("CAMPO2")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Campo3)
                    .HasMaxLength(30)
                    .HasColumnName("CAMPO3")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Campo4)
                    .HasMaxLength(30)
                    .HasColumnName("CAMPO4")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Canti01).HasColumnName("CANTI01");

                entity.Property(e => e.Canti02).HasColumnName("CANTI02");

                entity.Property(e => e.Canti03).HasColumnName("CANTI03");

                entity.Property(e => e.Canti04).HasColumnName("CANTI04");

                entity.Property(e => e.Canti05).HasColumnName("CANTI05");

                entity.Property(e => e.Canti06).HasColumnName("CANTI06");

                entity.Property(e => e.Canti07).HasColumnName("CANTI07");

                entity.Property(e => e.Canti08).HasColumnName("CANTI08");

                entity.Property(e => e.Canti09).HasColumnName("CANTI09");

                entity.Property(e => e.Canti10).HasColumnName("CANTI10");

                entity.Property(e => e.Cantidad).HasColumnName("CANTIDAD");

                entity.Property(e => e.Cantidad0).HasColumnName("CANTIDAD0");

                entity.Property(e => e.Ccoste)
                    .HasMaxLength(4)
                    .HasColumnName("CCOSTE")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Cif)
                    .HasMaxLength(25)
                    .HasColumnName("CIF")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Codcli)
                    .HasMaxLength(5)
                    .HasColumnName("CODCLI")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Codigo)
                    .HasMaxLength(7)
                    .HasColumnName("CODIGO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Codigo0)
                    .HasMaxLength(7)
                    .HasColumnName("CODIGO0")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Comision).HasColumnName("COMISION");

                entity.Property(e => e.Comision2).HasColumnName("COMISION2");

                entity.Property(e => e.Comision3).HasColumnName("COMISION3");

                entity.Property(e => e.Comision4).HasColumnName("COMISION4");

                entity.Property(e => e.Comision5).HasColumnName("COMISION5");

                entity.Property(e => e.Comision6).HasColumnName("COMISION6");

                entity.Property(e => e.Contacto)
                    .HasMaxLength(30)
                    .HasColumnName("CONTACTO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Controlok)
                    .HasMaxLength(1)
                    .HasColumnName("CONTROLOK")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Costetot).HasColumnName("COSTETOT");

                entity.Property(e => e.Costetot0).HasColumnName("COSTETOT0");

                entity.Property(e => e.Costetot01).HasColumnName("COSTETOT01");

                entity.Property(e => e.Costetot1).HasColumnName("COSTETOT1");

                entity.Property(e => e.Descrip)
                    .HasMaxLength(80)
                    .HasColumnName("DESCRIP")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Descu).HasColumnName("DESCU");

                entity.Property(e => e.Direccion)
                    .HasMaxLength(40)
                    .HasColumnName("DIRECCION")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Direccion1)
                    .HasMaxLength(40)
                    .HasColumnName("DIRECCION1")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Empre**)
                    .HasMaxLength(2)
                    .HasColumnName("EMPRESA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Enviado).HasColumnName("ENVIADO");

                entity.Property(e => e.Especif1)
                    .HasMaxLength(80)
                    .HasColumnName("ESPECIF1")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Especif2)
                    .HasMaxLength(80)
                    .HasColumnName("ESPECIF2")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Especif3)
                    .HasMaxLength(80)
                    .HasColumnName("ESPECIF3")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Especif4)
                    .HasMaxLength(80)
                    .HasColumnName("ESPECIF4")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Especif5)
                    .HasMaxLength(80)
                    .HasColumnName("ESPECIF5")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Estado)
                    .HasMaxLength(10)
                    .HasColumnName("ESTADO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Estatus)
                    .HasMaxLength(10)
                    .HasColumnName("ESTATUS")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fecfinal)
                    .HasColumnType("datetime")
                    .HasColumnName("FECFINAL");

                entity.Property(e => e.Fecha)
                    .HasColumnType("datetime")
                    .HasColumnName("FECHA");

                entity.Property(e => e.Fecha0)
                    .HasColumnType("datetime")
                    .HasColumnName("FECHA0");

                entity.Property(e => e.Fentreconf)
                    .HasColumnType("datetime")
                    .HasColumnName("FENTRECONF");

                entity.Property(e => e.Fentrega)
                    .HasColumnType("datetime")
                    .HasColumnName("FENTREGA");

                entity.Property(e => e.Fijostot).HasColumnName("FIJOSTOT");

                entity.Property(e => e.Fijostot0).HasColumnName("FIJOSTOT0");

                entity.Property(e => e.Fijostot01).HasColumnName("FIJOSTOT01");

                entity.Property(e => e.Fijostot1).HasColumnName("FIJOSTOT1");

                entity.Property(e => e.Fpago)
                    .HasMaxLength(2)
                    .HasColumnName("FPAGO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fsc)
                    .HasMaxLength(1)
                    .HasColumnName("FSC")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Horastot).HasColumnName("HORASTOT");

                entity.Property(e => e.Horastot0).HasColumnName("HORASTOT0");

                entity.Property(e => e.Inicitot).HasColumnName("INICITOT");

                entity.Property(e => e.Inicitot0).HasColumnName("INICITOT0");

                entity.Property(e => e.Inicitot01).HasColumnName("INICITOT01");

                entity.Property(e => e.Inicitot1).HasColumnName("INICITOT1");

                entity.Property(e => e.Macro)
                    .HasMaxLength(7)
                    .HasColumnName("MACRO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Margen).HasColumnName("MARGEN");

                entity.Property(e => e.Margen0).HasColumnName("MARGEN0");

                entity.Property(e => e.Margen2).HasColumnName("MARGEN2");

                entity.Property(e => e.Memoria)
                    .HasColumnType("ntext")
                    .HasColumnName("MEMORIA");

                entity.Property(e => e.Mercado)
                    .HasMaxLength(2)
                    .HasColumnName("MERCADO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Mermaf).HasColumnName("MERMAF");

                entity.Property(e => e.Mermav).HasColumnName("MERMAV");

                entity.Property(e => e.Nombre)
                    .HasMaxLength(50)
                    .HasColumnName("NOMBRE")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Otimpre**)
                    .HasMaxLength(1)
                    .HasColumnName("OTIMPRESA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Pais)
                    .HasMaxLength(30)
                    .HasColumnName("PAIS")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Parte01)
                    .HasMaxLength(15)
                    .HasColumnName("PARTE01")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Parte02)
                    .HasMaxLength(15)
                    .HasColumnName("PARTE02")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Parte03)
                    .HasMaxLength(15)
                    .HasColumnName("PARTE03")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Parte04)
                    .HasMaxLength(15)
                    .HasColumnName("PARTE04")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Parte05)
                    .HasMaxLength(15)
                    .HasColumnName("PARTE05")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Parte06)
                    .HasMaxLength(15)
                    .HasColumnName("PARTE06")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Parte07)
                    .HasMaxLength(15)
                    .HasColumnName("PARTE07")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Parte08)
                    .HasMaxLength(15)
                    .HasColumnName("PARTE08")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Parte09)
                    .HasMaxLength(15)
                    .HasColumnName("PARTE09")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Parte10)
                    .HasMaxLength(15)
                    .HasColumnName("PARTE10")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Plantilla)
                    .HasMaxLength(1)
                    .HasColumnName("PLANTILLA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Poblacion)
                    .HasMaxLength(30)
                    .HasColumnName("POBLACION")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Postal)
                    .HasMaxLength(15)
                    .HasColumnName("POSTAL")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Pressid)
                    .HasMaxLength(50)
                    .HasColumnName("PRESSID")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Producto)
                    .HasMaxLength(25)
                    .HasColumnName("PRODUCTO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Provincia)
                    .HasMaxLength(30)
                    .HasColumnName("PROVINCIA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Represen)
                    .HasMaxLength(3)
                    .HasColumnName("REPRESEN")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Represen2)
                    .HasMaxLength(2)
                    .HasColumnName("REPRESEN2")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Represen3)
                    .HasMaxLength(2)
                    .HasColumnName("REPRESEN3")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Represen4)
                    .HasMaxLength(2)
                    .HasColumnName("REPRESEN4")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Represen5)
                    .HasMaxLength(2)
                    .HasColumnName("REPRESEN5")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Represen6)
                    .HasMaxLength(2)
                    .HasColumnName("REPRESEN6")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Solicitud)
                    .HasMaxLength(7)
                    .HasColumnName("SOLICITUD")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Tgastos).HasColumnName("TGASTOS");

                entity.Property(e => e.Tgastos1).HasColumnName("TGASTOS1");

                entity.Property(e => e.Tipiva)
                    .HasMaxLength(2)
                    .HasColumnName("TIPIVA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Total).HasColumnName("TOTAL");

                entity.Property(e => e.Total0).HasColumnName("TOTAL0");

                entity.Property(e => e.Total01).HasColumnName("TOTAL01");

                entity.Property(e => e.Total1).HasColumnName("TOTAL1");

                entity.Property(e => e.Ucaja).HasColumnName("UCAJA");

                entity.Property(e => e.Ultacaba)
                    .HasMaxLength(1)
                    .HasColumnName("ULTACABA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ultccoste)
                    .HasMaxLength(4)
                    .HasColumnName("ULTCCOSTE")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ultfecha).HasColumnName("ULTFECHA");

                entity.Property(e => e.Ultope)
                    .HasMaxLength(2)
                    .HasColumnName("ULTOPE")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Unitario).HasColumnName("UNITARIO");

                entity.Property(e => e.Unitario0).HasColumnName("UNITARIO0");

                entity.Property(e => e.Unitario01).HasColumnName("UNITARIO01");

                entity.Property(e => e.Unitario1).HasColumnName("UNITARIO1");

                entity.Property(e => e.Variatot).HasColumnName("VARIATOT");

                entity.Property(e => e.Variatot0).HasColumnName("VARIATOT0");

                entity.Property(e => e.Variatot01).HasColumnName("VARIATOT01");

                entity.Property(e => e.Variatot1).HasColumnName("VARIATOT1");

                entity.Property(e => e.Zona)
                    .HasMaxLength(2)
                    .HasColumnName("ZONA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");
            });

            modelBuilder.Entity<EEorden>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("E_EORDEN");

                entity.HasIndex(e => new { e.Codigo, e.Pagina }, "e_eorde1")
                    .IsUnique();

                entity.Property(e => e.C1)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C10)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C100)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C101)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C102)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C103)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C104)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C105)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C106)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C107)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C108)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C109)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C11)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C110)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C111)
                    .HasMaxLength(80)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C112)
                    .HasMaxLength(80)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C113)
                    .HasMaxLength(80)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C114)
                    .HasMaxLength(80)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C115)
                    .HasMaxLength(80)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C116)
                    .HasMaxLength(80)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C117)
                    .HasMaxLength(80)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C118)
                    .HasMaxLength(80)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C119)
                    .HasMaxLength(80)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C12)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C120)
                    .HasMaxLength(80)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C13)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C14)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C15)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C16)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C17)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C18)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C19)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C2)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C20)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C21)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C22)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C23)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C24)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C25)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C26)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C27)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C28)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C29)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C3)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C30)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C31)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C32)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C33)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C34)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C35)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C36)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C37)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C38)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C39)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C4)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C40)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C41)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C42)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C43)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C44)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C45)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C46)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C47)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C48)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C49)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C5)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C50)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C51)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C52)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C53)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C54)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C55)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C56)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C57)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C58)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C59)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C6)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C60)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C61)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C62)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C63)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C64)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C65)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C66)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C67)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C68)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C69)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C7)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C70)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C71)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C72)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C73)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C74)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C75)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C76)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C77)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C78)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C79)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C8)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C80)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C81)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C82)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C83)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C84)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C85)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C86)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C87)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C88)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C89)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C9)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C90)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C91)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C92)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C93)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C94)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C95)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C96)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C97)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C98)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.C99)
                    .HasMaxLength(25)
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Cantidad).HasColumnName("CANTIDAD");

                entity.Property(e => e.Codigo)
                    .HasMaxLength(7)
                    .HasColumnName("CODIGO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Memoria)
                    .HasColumnType("ntext")
                    .HasColumnName("MEMORIA");

                entity.Property(e => e.Pagina)
                    .HasMaxLength(1)
                    .HasColumnName("PAGINA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");
            });

            modelBuilder.Entity<Fechas>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("FECHAS");

                entity.Property(e => e.Campo1)
                    .HasMaxLength(30)
                    .HasColumnName("CAMPO1")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Codigo).HasColumnName("codigo");

                entity.Property(e => e.Completo)
                    .HasMaxLength(25)
                    .HasColumnName("COMPLETO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Descrip)
                    .HasMaxLength(50)
                    .HasColumnName("DESCRIP")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Especif4)
                    .HasMaxLength(80)
                    .HasColumnName("ESPECIF4")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Facordada)
                    .HasMaxLength(25)
                    .HasColumnName("FACORDADA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fcontrato)
                    .HasMaxLength(25)
                    .HasColumnName("FCONTRATO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fechaentrega)
                    .HasColumnType("datetime")
                    .HasColumnName("FECHAENTREGA");

                entity.Property(e => e.Fechafin)
                    .HasColumnType("datetime")
                    .HasColumnName("fechafin");

                entity.Property(e => e.Fechaftp)
                    .HasMaxLength(25)
                    .HasColumnName("FECHAFTP")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fechahojalata)
                    .HasMaxLength(25)
                    .HasColumnName("FECHAHOJALATA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fechamaxima)
                    .HasMaxLength(25)
                    .HasColumnName("FECHAMAXIMA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fechapedido)
                    .HasMaxLength(25)
                    .HasColumnName("FECHAPEDIDO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fechapresupuesto)
                    .HasColumnType("datetime")
                    .HasColumnName("FECHAPRESUPUESTO");

                entity.Property(e => e.Observa)
                    .HasMaxLength(25)
                    .HasColumnName("OBSERVA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");
            });

            modelBuilder.Entity<GClient>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("G_CLIENT");

                entity.HasIndex(e => e.Codigo, "g_clien1")
                    .IsUnique();

                entity.Property(e => e.Actividad)
                    .HasMaxLength(2)
                    .HasColumnName("ACTIVIDAD")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Acucom).HasColumnName("ACUCOM");

                entity.Property(e => e.Banco)
                    .HasMaxLength(4)
                    .HasColumnName("BANCO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Bic)
                    .HasMaxLength(11)
                    .HasColumnName("BIC")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Cancelado).HasColumnName("CANCELADO");

                entity.Property(e => e.Certifical)
                    .HasMaxLength(1)
                    .HasColumnName("CERTIFICAL")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Cierre0)
                    .HasColumnType("datetime")
                    .HasColumnName("CIERRE0");

                entity.Property(e => e.Cierre1)
                    .HasColumnType("datetime")
                    .HasColumnName("CIERRE1");

                entity.Property(e => e.Cif)
                    .HasMaxLength(25)
                    .HasColumnName("CIF")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Codent)
                    .HasMaxLength(5)
                    .HasColumnName("CODENT")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Codigo)
                    .HasMaxLength(5)
                    .HasColumnName("CODIGO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Codprov)
                    .HasMaxLength(15)
                    .HasColumnName("CODPROV")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Codtrans)
                    .HasMaxLength(3)
                    .HasColumnName("CODTRANS")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Comision).HasColumnName("COMISION");

                entity.Property(e => e.Competi)
                    .HasMaxLength(250)
                    .HasColumnName("COMPETI")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Contacto1)
                    .HasMaxLength(30)
                    .HasColumnName("CONTACTO1")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Contacto2)
                    .HasMaxLength(30)
                    .HasColumnName("CONTACTO2")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Copias).HasColumnName("COPIAS");

                entity.Property(e => e.Credicau)
                    .HasMaxLength(4)
                    .HasColumnName("CREDICAU")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Cuenta)
                    .HasMaxLength(25)
                    .HasColumnName("CUENTA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Descuenpp).HasColumnName("DESCUENPP");

                entity.Property(e => e.Descuento).HasColumnName("DESCUENTO");

                entity.Property(e => e.Dias1).HasColumnName("DIAS1");

                entity.Property(e => e.Dias2).HasColumnName("DIAS2");

                entity.Property(e => e.Dias3).HasColumnName("DIAS3");

                entity.Property(e => e.Direccio21)
                    .HasMaxLength(40)
                    .HasColumnName("DIRECCIO21")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Direccion)
                    .HasMaxLength(40)
                    .HasColumnName("DIRECCION")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Direccion1)
                    .HasMaxLength(40)
                    .HasColumnName("DIRECCION1")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Direccion2)
                    .HasMaxLength(40)
                    .HasColumnName("DIRECCION2")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Documento)
                    .HasMaxLength(2)
                    .HasColumnName("DOCUMENTO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Domiban2)
                    .HasMaxLength(80)
                    .HasColumnName("DOMIBAN2")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Domibanco)
                    .HasMaxLength(80)
                    .HasColumnName("DOMIBANCO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Domici)
                    .HasMaxLength(1)
                    .HasColumnName("DOMICI")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Email)
                    .HasMaxLength(60)
                    .HasColumnName("EMAIL")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Emailfaut)
                    .HasMaxLength(60)
                    .HasColumnName("EMAILFAUT")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Empre**)
                    .HasMaxLength(2)
                    .HasColumnName("EMPRESA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Entregapro)
                    .HasMaxLength(1)
                    .HasColumnName("ENTREGAPRO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Envfacaut)
                    .HasMaxLength(1)
                    .HasColumnName("ENVFACAUT")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Equival)
                    .HasMaxLength(1)
                    .HasColumnName("EQUIVAL")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Eticop).HasColumnName("ETICOP");

                entity.Property(e => e.Etimod)
                    .HasMaxLength(3)
                    .HasColumnName("ETIMOD")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Factsimpli)
                    .HasMaxLength(1)
                    .HasColumnName("FACTSIMPLI")
                    .HasDefaultValueSql("('')")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Facturar)
                    .HasMaxLength(1)
                    .HasColumnName("FACTURAR")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Factxemail)
                    .HasMaxLength(1)
                    .HasColumnName("FACTXEMAIL")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fax)
                    .HasMaxLength(20)
                    .HasColumnName("FAX")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fecalta)
                    .HasColumnType("datetime")
                    .HasColumnName("FECALTA");

                entity.Property(e => e.Fiscal)
                    .HasMaxLength(100)
                    .HasColumnName("FISCAL")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fpago)
                    .HasMaxLength(2)
                    .HasColumnName("FPAGO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fulvisi)
                    .HasColumnType("datetime")
                    .HasColumnName("FULVISI");

                entity.Property(e => e.Fvisita)
                    .HasColumnType("datetime")
                    .HasColumnName("FVISITA");

                entity.Property(e => e.Grupoempr)
                    .HasMaxLength(2)
                    .HasColumnName("GRUPOEMPR")
                    .HasDefaultValueSql("('')")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Iban)
                    .HasMaxLength(34)
                    .HasColumnName("IBAN")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Limcaucion)
                    .HasColumnType("datetime")
                    .HasColumnName("LIMCAUCION");

                entity.Property(e => e.Mandato)
                    .HasColumnType("datetime")
                    .HasColumnName("MANDATO");

                entity.Property(e => e.Mandatoid)
                    .HasMaxLength(35)
                    .HasColumnName("MANDATOID")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Mandatouni)
                    .HasMaxLength(1)
                    .HasColumnName("MANDATOUNI")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Margen).HasColumnName("MARGEN");

                entity.Property(e => e.Margeng).HasColumnName("MARGENG");

                entity.Property(e => e.Memoria)
                    .HasColumnType("ntext")
                    .HasColumnName("MEMORIA");

                entity.Property(e => e.Moneda)
                    .HasMaxLength(2)
                    .HasColumnName("MONEDA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Motivo)
                    .HasMaxLength(1)
                    .HasColumnName("MOTIVO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Movil)
                    .HasMaxLength(25)
                    .HasColumnName("MOVIL")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Nombre)
                    .HasMaxLength(50)
                    .HasColumnName("NOMBRE")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Nombre2)
                    .HasMaxLength(50)
                    .HasColumnName("NOMBRE2")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Notas).HasColumnName("NOTAS");

                entity.Property(e => e.Numero)
                    .HasMaxLength(5)
                    .HasColumnName("NUMERO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Numero2)
                    .HasMaxLength(5)
                    .HasColumnName("NUMERO2")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Numnove).HasColumnName("NUMNOVE");

                entity.Property(e => e.Numprovee)
                    .HasMaxLength(10)
                    .HasColumnName("NUMPROVEE")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Observa1)
                    .HasMaxLength(80)
                    .HasColumnName("OBSERVA1")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Observa2)
                    .HasMaxLength(80)
                    .HasColumnName("OBSERVA2")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Observa3)
                    .HasMaxLength(80)
                    .HasColumnName("OBSERVA3")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Observa4)
                    .HasMaxLength(80)
                    .HasColumnName("OBSERVA4")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Observa5)
                    .HasMaxLength(80)
                    .HasColumnName("OBSERVA5")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Obsoleto)
                    .HasMaxLength(1)
                    .HasColumnName("OBSOLETO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ocdire)
                    .HasMaxLength(100)
                    .HasColumnName("OCDIRE")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ocpais)
                    .HasMaxLength(3)
                    .HasColumnName("OCPAIS")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ocpobla)
                    .HasMaxLength(50)
                    .HasColumnName("OCPOBLA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ocpostal)
                    .HasMaxLength(15)
                    .HasColumnName("OCPOSTAL")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ocprovin)
                    .HasMaxLength(50)
                    .HasColumnName("OCPROVIN")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Oficonta)
                    .HasMaxLength(50)
                    .HasColumnName("OFICONTA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ogdire)
                    .HasMaxLength(100)
                    .HasColumnName("OGDIRE")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ogpais)
                    .HasMaxLength(3)
                    .HasColumnName("OGPAIS")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ogpobla)
                    .HasMaxLength(50)
                    .HasColumnName("OGPOBLA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ogpostal)
                    .HasMaxLength(15)
                    .HasColumnName("OGPOSTAL")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ogprovin)
                    .HasMaxLength(50)
                    .HasColumnName("OGPROVIN")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Opdire)
                    .HasMaxLength(100)
                    .HasColumnName("OPDIRE")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Oppais)
                    .HasMaxLength(3)
                    .HasColumnName("OPPAIS")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Oppobla)
                    .HasMaxLength(50)
                    .HasColumnName("OPPOBLA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Oppostal)
                    .HasMaxLength(15)
                    .HasColumnName("OPPOSTAL")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Opprovin)
                    .HasMaxLength(50)
                    .HasColumnName("OPPROVIN")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Orgagest)
                    .HasMaxLength(50)
                    .HasColumnName("ORGAGEST")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Orgapro)
                    .HasMaxLength(50)
                    .HasColumnName("ORGAPRO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Pais)
                    .HasMaxLength(30)
                    .HasColumnName("PAIS")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Pais2)
                    .HasMaxLength(30)
                    .HasColumnName("PAIS2")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Pendiente).HasColumnName("PENDIENTE");

                entity.Property(e => e.Poblacion)
                    .HasMaxLength(30)
                    .HasColumnName("POBLACION")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Poblacion2)
                    .HasMaxLength(30)
                    .HasColumnName("POBLACION2")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Porcen1).HasColumnName("PORCEN1");

                entity.Property(e => e.Porcen2).HasColumnName("PORCEN2");

                entity.Property(e => e.Porcen3).HasColumnName("PORCEN3");

                entity.Property(e => e.Porcen4).HasColumnName("PORCEN4");

                entity.Property(e => e.Porcen5).HasColumnName("PORCEN5");

                entity.Property(e => e.Porcen6).HasColumnName("PORCEN6");

                entity.Property(e => e.Posta2)
                    .HasMaxLength(15)
                    .HasColumnName("POSTA2")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Postal)
                    .HasMaxLength(15)
                    .HasColumnName("POSTAL")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Pressid)
                    .HasMaxLength(50)
                    .HasColumnName("PRESSID")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Previ1).HasColumnName("PREVI1");

                entity.Property(e => e.Previ10).HasColumnName("PREVI10");

                entity.Property(e => e.Previ11).HasColumnName("PREVI11");

                entity.Property(e => e.Previ12).HasColumnName("PREVI12");

                entity.Property(e => e.Previ2).HasColumnName("PREVI2");

                entity.Property(e => e.Previ3).HasColumnName("PREVI3");

                entity.Property(e => e.Previ4).HasColumnName("PREVI4");

                entity.Property(e => e.Previ5).HasColumnName("PREVI5");

                entity.Property(e => e.Previ6).HasColumnName("PREVI6");

                entity.Property(e => e.Previ7).HasColumnName("PREVI7");

                entity.Property(e => e.Previ8).HasColumnName("PREVI8");

                entity.Property(e => e.Previ9).HasColumnName("PREVI9");

                entity.Property(e => e.Provexml)
                    .HasMaxLength(10)
                    .HasColumnName("PROVEXML")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Provinci2)
                    .HasMaxLength(30)
                    .HasColumnName("PROVINCI2")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Provincia)
                    .HasMaxLength(30)
                    .HasColumnName("PROVINCIA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Puntoopera)
                    .HasMaxLength(13)
                    .HasColumnName("PUNTOOPERA")
                    .HasDefaultValueSql("('')")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Representa)
                    .HasMaxLength(3)
                    .HasColumnName("REPRESENTA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Residente)
                    .HasMaxLength(1)
                    .HasColumnName("RESIDENTE")
                    .HasDefaultValueSql("('')")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Rgpd)
                    .HasMaxLength(1)
                    .HasColumnName("RGPD")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Riesgo1).HasColumnName("RIESGO1");

                entity.Property(e => e.Riesgo2).HasColumnName("RIESGO2");

                entity.Property(e => e.Riesgo3).HasColumnName("RIESGO3");

                entity.Property(e => e.Riesgoa1).HasColumnName("RIESGOA1");

                entity.Property(e => e.Riesgoa2).HasColumnName("RIESGOA2");

                entity.Property(e => e.Riesgoa3).HasColumnName("RIESGOA3");

                entity.Property(e => e.Servicios)
                    .HasMaxLength(1)
                    .HasColumnName("SERVICIOS")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Servidor)
                    .HasMaxLength(50)
                    .HasColumnName("SERVIDOR")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Sigla)
                    .HasMaxLength(2)
                    .HasColumnName("SIGLA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Telefono)
                    .HasMaxLength(25)
                    .HasColumnName("TELEFONO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Telefono2)
                    .HasMaxLength(25)
                    .HasColumnName("TELEFONO2")
                    .HasDefaultValueSql("('')")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Tf)
                    .HasMaxLength(1)
                    .HasColumnName("TF")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Tipocli)
                    .HasMaxLength(2)
                    .HasColumnName("TIPOCLI")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Tipodctofa)
                    .HasMaxLength(3)
                    .HasColumnName("TIPODCTOFA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Tipoiva)
                    .HasMaxLength(2)
                    .HasColumnName("TIPOIVA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Tipotarive)
                    .HasMaxLength(5)
                    .HasColumnName("TIPOTARIVE")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Tipsiva1)
                    .HasMaxLength(2)
                    .HasColumnName("TIPSIVA1")
                    .HasDefaultValueSql("('')")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Tipsiva2)
                    .HasMaxLength(2)
                    .HasColumnName("TIPSIVA2")
                    .HasDefaultValueSql("('')")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Tipsiva3)
                    .HasMaxLength(2)
                    .HasColumnName("TIPSIVA3")
                    .HasDefaultValueSql("('')")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Tipsiva4)
                    .HasMaxLength(2)
                    .HasColumnName("TIPSIVA4")
                    .HasDefaultValueSql("('')")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Undtrami)
                    .HasMaxLength(50)
                    .HasColumnName("UNDTRAMI")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Utdire)
                    .HasMaxLength(100)
                    .HasColumnName("UTDIRE")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Utpais)
                    .HasMaxLength(3)
                    .HasColumnName("UTPAIS")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Utpobla)
                    .HasMaxLength(50)
                    .HasColumnName("UTPOBLA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Utpostal)
                    .HasMaxLength(15)
                    .HasColumnName("UTPOSTAL")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Utprovin)
                    .HasMaxLength(50)
                    .HasColumnName("UTPROVIN")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Valorar)
                    .HasMaxLength(1)
                    .HasColumnName("VALORAR")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Venci1).HasColumnName("VENCI1");

                entity.Property(e => e.Venci2).HasColumnName("VENCI2");

                entity.Property(e => e.Venci3).HasColumnName("VENCI3");

                entity.Property(e => e.Venci4).HasColumnName("VENCI4");

                entity.Property(e => e.Venci5).HasColumnName("VENCI5");

                entity.Property(e => e.Venci6).HasColumnName("VENCI6");

                entity.Property(e => e.Web)
                    .HasMaxLength(200)
                    .HasColumnName("WEB")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Webpass)
                    .HasMaxLength(15)
                    .HasColumnName("WEBPASS")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Webuser)
                    .HasMaxLength(15)
                    .HasColumnName("WEBUSER")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Zona)
                    .HasMaxLength(2)
                    .HasColumnName("ZONA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");
            });

            modelBuilder.Entity<Matped>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("matped");

                entity.Property(e => e.Almacen).HasColumnName("almacen");

                entity.Property(e => e.Ancmpe).HasColumnName("ancmpe");

                entity.Property(e => e.Articulo)
                    .HasMaxLength(25)
                    .HasColumnName("articulo")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Cararayada)
                    .HasMaxLength(100)
                    .HasColumnName("CARARAYADA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Climpe).HasColumnName("climpe");

                entity.Property(e => e.Codigo)
                    .HasMaxLength(7)
                    .HasColumnName("codigo")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Corte)
                    .HasMaxLength(100)
                    .HasColumnName("CORTE")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Dureza)
                    .HasMaxLength(100)
                    .HasColumnName("DUREZA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Espmpe).HasColumnName("espmpe");

                entity.Property(e => e.Grupo)
                    .IsRequired()
                    .HasMaxLength(14)
                    .IsUnicode(false)
                    .HasColumnName("GRUPO");

                entity.Property(e => e.Hojmpe).HasColumnName("hojmpe");

                entity.Property(e => e.Larmpe).HasColumnName("larmpe");

                entity.Property(e => e.Nombre)
                    .HasMaxLength(100)
                    .HasColumnName("NOMBRE")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Nummpe).HasColumnName("nummpe");

                entity.Property(e => e.Pedcliente)
                    .HasMaxLength(100)
                    .HasColumnName("pedcliente")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Procedencia)
                    .HasMaxLength(100)
                    .HasColumnName("procedencia")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Procesos)
                    .HasMaxLength(100)
                    .HasColumnName("PROCESOS")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Prompe)
                    .HasMaxLength(25)
                    .HasColumnName("prompe")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Recubrimiento)
                    .HasMaxLength(100)
                    .HasColumnName("RECUBRIMIENTO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Salmpe).HasColumnName("**lmpe");

                entity.Property(e => e.Siderurgia)
                    .HasMaxLength(100)
                    .HasColumnName("siderurgia")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Sitmpe)
                    .HasMaxLength(25)
                    .HasColumnName("sitmpe")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Tipo)
                    .IsRequired()
                    .HasMaxLength(24)
                    .IsUnicode(false);

                entity.Property(e => e.TipoRigen)
                    .IsRequired()
                    .HasMaxLength(7)
                    .IsUnicode(false)
                    .HasColumnName("TipoRIGEN");

                entity.Property(e => e.Tmampe).HasColumnName("tmampe");

                entity.Property(e => e.Tprmpe).HasColumnName("tprmpe");

                entity.Property(e => e.Tprmpe2)
                    .HasMaxLength(6)
                    .HasColumnName("tprmpe2")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");
            });

            modelBuilder.Entity<Motivos>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("motivos");

                entity.Property(e => e.Cholpe).HasColumnName("cholpe");

                entity.Property(e => e.Codigo)
                    .HasMaxLength(7)
                    .HasColumnName("codigo")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Dialpe).HasColumnName("dialpe");

                entity.Property(e => e.Elelpe)
                    .HasMaxLength(25)
                    .HasColumnName("elelpe")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Emplpe)
                    .IsRequired()
                    .HasMaxLength(1)
                    .IsUnicode(false)
                    .HasColumnName("emplpe");

                entity.Property(e => e.Marlpe)
                    .HasMaxLength(30)
                    .HasColumnName("marlpe")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Modelo)
                    .HasMaxLength(81)
                    .HasColumnName("modelo")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Motivo).HasColumnName("motivo");

                entity.Property(e => e.Nenlpe).HasColumnName("nenlpe");

                entity.Property(e => e.Numlpe).HasColumnName("numlpe");

                entity.Property(e => e.Plantilla)
                    .HasMaxLength(1)
                    .HasColumnName("plantilla")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Tenlpe)
                    .HasMaxLength(80)
                    .HasColumnName("tenlpe")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");
            });

            modelBuilder.Entity<MotivosDetallados>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("MOTIVOS_DETALLADOS");

                entity.Property(e => e.Altura).HasColumnName("ALTURA");

                entity.Property(e => e.Cliente).HasColumnName("cliente");

                entity.Property(e => e.Codigo)
                    .HasMaxLength(7)
                    .HasColumnName("CODIGO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Codnuevo).HasColumnName("CODNUEVO");

                entity.Property(e => e.Coldor1)
                    .HasMaxLength(25)
                    .HasColumnName("COLDOR1")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Coldor2)
                    .HasMaxLength(25)
                    .HasColumnName("COLDOR2")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Coldor3)
                    .HasMaxLength(25)
                    .HasColumnName("COLDOR3")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Coldor4)
                    .HasMaxLength(25)
                    .HasColumnName("COLDOR4")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Color01).HasColumnName("COLOR01");

                entity.Property(e => e.Color02).HasColumnName("COLOR02");

                entity.Property(e => e.Color03).HasColumnName("COLOR03");

                entity.Property(e => e.Color04).HasColumnName("COLOR04");

                entity.Property(e => e.Color05).HasColumnName("COLOR05");

                entity.Property(e => e.Color06).HasColumnName("COLOR06");

                entity.Property(e => e.Color07)
                    .HasMaxLength(25)
                    .HasColumnName("COLOR07")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Color08)
                    .HasMaxLength(25)
                    .HasColumnName("COLOR08")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Color09)
                    .HasMaxLength(25)
                    .HasColumnName("COLOR09")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Color10)
                    .HasMaxLength(25)
                    .HasColumnName("COLOR10")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Color11)
                    .HasMaxLength(25)
                    .HasColumnName("COLOR11")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Diametro).HasColumnName("DIAMETRO");

                entity.Property(e => e.Elemento)
                    .HasMaxLength(25)
                    .HasColumnName("ELEMENTO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Embuticion)
                    .HasMaxLength(25)
                    .HasColumnName("EMBUTICION")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Empre**)
                    .IsRequired()
                    .HasMaxLength(1)
                    .IsUnicode(false)
                    .HasColumnName("EMPRESA");

                entity.Property(e => e.Envase)
                    .HasMaxLength(30)
                    .HasColumnName("ENVASE")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fechamodif)
                    .HasMaxLength(25)
                    .HasColumnName("FECHAMODIF")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Formato)
                    .HasMaxLength(25)
                    .HasColumnName("FORMATO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fotolitos)
                    .HasMaxLength(25)
                    .HasColumnName("FOTOLITOS")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Marca)
                    .HasMaxLength(30)
                    .HasColumnName("MARCA")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.MotivoStr)
                    .HasMaxLength(80)
                    .HasColumnName("MOTIVO_str")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Notafotomec)
                    .HasMaxLength(25)
                    .HasColumnName("NOTAFOTOMEC")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Numenvase)
                    .IsRequired()
                    .HasMaxLength(1)
                    .IsUnicode(false)
                    .HasColumnName("NUMENVASE");

                entity.Property(e => e.Observac)
                    .HasMaxLength(25)
                    .HasColumnName("OBSERVAC")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.PRoint1).HasColumnName("pROINT1");

                entity.Property(e => e.Proext1).HasColumnName("PROEXT1");

                entity.Property(e => e.Proext2).HasColumnName("PROEXT2");

                entity.Property(e => e.Proext3)
                    .HasMaxLength(25)
                    .HasColumnName("PROEXT3")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Proint2)
                    .HasMaxLength(25)
                    .HasColumnName("PROINT2")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Proint3)
                    .HasMaxLength(25)
                    .HasColumnName("PROINT3")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Ultfecped)
                    .IsRequired()
                    .HasMaxLength(1)
                    .IsUnicode(false)
                    .HasColumnName("ULTFECPED");
            });

            modelBuilder.Entity<Observa>(entity =>
            {
                entity
                    .HasNoKey()
                    .ToView("observa");

                entity.Property(e => e.Barniz)
                    .HasMaxLength(15)
                    .UseCollation("SQL_Latin1_General_CP437_BIN")
                    .HasColumnName("barniz");
                entity.Property(e => e.Cantidad).HasColumnName("CANTIDAD");
                entity.Property(e => e.Ccoste)
                    .HasMaxLength(4)
                    .UseCollation("SQL_Latin1_General_CP437_BIN")
                    .HasColumnName("CCOSTE");
                entity.Property(e => e.Dato)
                    .HasMaxLength(15)
                    .UseCollation("SQL_Latin1_General_CP437_BIN")
                    .HasColumnName("dato");
                entity.Property(e => e.Fechaini)
                    .HasColumnType("datetime")
                    .HasColumnName("FECHAINI");
                entity.Property(e => e.Grupoinfo)
                    .HasMaxLength(4)
                    .UseCollation("SQL_Latin1_General_CP437_BIN")
                    .HasColumnName("GRUPOINFO");
                entity.Property(e => e.Horaini)
                    .HasMaxLength(5)
                    .UseCollation("SQL_Latin1_General_CP437_BIN")
                    .HasColumnName("HORAINI");
                entity.Property(e => e.Lote)
                    .HasMaxLength(15)
                    .UseCollation("SQL_Latin1_General_CP437_BIN")
                    .HasColumnName("lote");
                entity.Property(e => e.Minutos).HasColumnName("MINUTOS");
                entity.Property(e => e.Nombreemp)
                    .HasMaxLength(20)
                    .UseCollation("SQL_Latin1_General_CP437_BIN")
                    .HasColumnName("NOMBREEMP");
                entity.Property(e => e.Operacion)
                    .HasMaxLength(2)
                    .UseCollation("SQL_Latin1_General_CP437_BIN")
                    .HasColumnName("OPERACION");
                entity.Property(e => e.Orden)
                    .HasMaxLength(7)
                    .UseCollation("SQL_Latin1_General_CP437_BIN")
                    .HasColumnName("ORDEN");
                entity.Property(e => e.Posicion).HasColumnName("posicion");
                entity.Property(e => e.Registro)
                    .HasMaxLength(11)
                    .UseCollation("SQL_Latin1_General_CP437_BIN")
                    .HasColumnName("REGISTRO");
            });

            modelBuilder.Entity<Parteshorariosstr>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("parteshorariosstr");

                entity.Property(e => e.Cantidad).HasColumnName("CANTIDAD");

                entity.Property(e => e.Ccoste)
                    .HasMaxLength(4)
                    .HasColumnName("CCOSTE")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Dato)
                    .HasMaxLength(15)
                    .HasColumnName("dato")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Empleado)
                    .HasMaxLength(6)
                    .HasColumnName("EMPLEADO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Fechaini)
                    .HasColumnType("datetime")
                    .HasColumnName("FECHAINI");

                entity.Property(e => e.Grupoinfo)
                    .HasMaxLength(4)
                    .HasColumnName("GRUPOINFO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Horaini)
                    .HasMaxLength(5)
                    .HasColumnName("HORAINI")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Minutos).HasColumnName("MINUTOS");

                entity.Property(e => e.Nombrecen)
                    .HasMaxLength(20)
                    .HasColumnName("NOMBRECEN")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Nombreemp)
                    .HasMaxLength(20)
                    .HasColumnName("NOMBREEMP")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Nombreope)
                    .HasMaxLength(20)
                    .HasColumnName("NOMBREOPE")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Operacion)
                    .HasMaxLength(2)
                    .HasColumnName("OPERACION")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Orden).HasColumnName("orden");

                entity.Property(e => e.Posicion).HasColumnName("posicion");

                entity.Property(e => e.Registro)
                    .HasMaxLength(11)
                    .HasColumnName("REGISTRO")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");
            });

            modelBuilder.Entity<Planoform>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("planoform");

                entity.Property(e => e.Cliente).HasColumnName("cliente");

                entity.Property(e => e.De**rrollo).HasColumnName("de**rrollo");

                entity.Property(e => e.Descri)
                    .HasMaxLength(25)
                    .HasColumnName("descri")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Diametro).HasColumnName("diametro");

                entity.Property(e => e.Id)
                    .HasMaxLength(15)
                    .HasColumnName("id")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");

                entity.Property(e => e.Resdcha).HasColumnName("resdcha");

                entity.Property(e => e.Resizda).HasColumnName("resizda");

                entity.Property(e => e.Tipo)
                    .HasMaxLength(25)
                    .HasColumnName("tipo")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");
            });

            modelBuilder.Entity<Tipohjlta>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("tipohjlta");

                entity.Property(e => e.Codtma).HasColumnName("codtma");

                entity.Property(e => e.Nomtma)
                    .HasMaxLength(30)
                    .HasColumnName("nomtma")
                    .UseCollation("SQL_Latin1_General_CP437_BIN");
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}