using System.Text.Json.Serialization;

namespace ProgramadorGeneralBLZ.Shared.ResponseModels
{
    [Serializable]
    public class ListResultHub<T> : PrimitiveResultHub
    {
        public ListResultHub()
        {
            Data = new List<T>();
        }

        [JsonPropertyName("data")]
        public List<T> Data { get; set; }

        [JsonPropertyName("count")]
        public int Count => Data?.Count ?? 0;
    }
}
