﻿@page "/authentication/{action}"
@using Microsoft.AspNetCore.Components.WebAssembly.Authentication
@using ProgramadorGeneralBLZ.Shared
@inject NavigationManager navigationManager
@inject SpinnerService _spinnerService
<RemoteAuthenticatorView Action="@Action">
    <LoggingIn>
        <SplashScreen ScreenText="Accediendo al login." />
    </LoggingIn>
    <Registering>
        <span>2 is loaded....</span>
    </Registering>
    <UserProfile>
        <span>3 is loaded....</span>
    </UserProfile>
@*    <CompletingLoggingIn>
        <span>4 is loaded....</span>
    </CompletingLoggingIn>*@
    <LogInFailed>
        <span>Login failed. Reason: @context</span>
        @*@InvokeAsync(() => navigationManager.NavigateTo("/"))*@
    </LogInFailed>
    <LogOut>
        <span>5 is loaded....</span>
    </LogOut>
    @*    <LogOut>
    <span>CompletingLogOut</span>
    </LogOut>*@
    <LogOutFailed>
        @*<span>Logout failed. Reason: @context</span>*@
        @InvokeAsync(() => navigationManager.NavigateTo("/"))
    </LogOutFailed>
    <LogOut>
        <SplashScreen ScreenText="Adios." />
    </LogOut>
    <LogOutSucceeded>
        @InvokeAsync(() => navigationManager.NavigateTo("/"))
    </LogOutSucceeded>
</RemoteAuthenticatorView>

@code {
    [Parameter]
    public string Action { get; set; }
}