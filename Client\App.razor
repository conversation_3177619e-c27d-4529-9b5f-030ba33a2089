﻿<CascadingAuthenticationState>
    <Router AppAssembly="@typeof(App).Assembly">
        <Found Context="routeData">
            <AuthorizeRouteView RouteData="@routeData" DefaultLayout="@typeof(MainLayout)">
                <Authorizing>
                    <SplashScreen ScreenText="Autorización inicial."/>
                </Authorizing>
                <NotAuthorized>
                    @if (context.User.Identity?.IsAuthenticated != true)
                    {
                        <RedirectToLogin/>
                    }
                    else
                    {
                        <NoPuedesPasar/>
                    }
                </NotAuthorized>
            </AuthorizeRouteView>
            <FocusOnNavigate RouteData="@routeData" Selector="h1" />
        </Found>
        <NotFound>
            <PageTitle>No encontrado</PageTitle>
            <LayoutView Layout="@typeof(MainLayout)">
                <NoPuedesPasar />
            </LayoutView>
        </NotFound>
    </Router>
</CascadingAuthenticationState>
