﻿{
   "CodeGenerationMode": 4,
   "ContextClassName": "ProgramadorLitalsaContext",
   "ContextNamespace": null,
   "FilterSchemas": false,
   "IncludeConnectionString": true,
   "ModelNamespace": null,
   "OutputContextPath": "Data\\ProgramadorLitalsa",
   "OutputPath": "Models\\ProgramadorLitalsa",
   "PreserveCasingWithRegex": true,
   "ProjectRootNamespace": "ProgramadorGeneralBLZ.Server",
   "Schemas": null,
   "SelectedHandlebarsLanguage": 0,
   "SelectedToBeGenerated": 0,
   "T4TemplatePath": null,
    "Tables": [
        {
            "Name": "[dbo].[AuditoriaActualizacionDatosPedido]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[BlockedMachine]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[Clientes]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[codapli]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[codapli_LITO]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[codtintas]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[CompruebaExistenciasBarnices]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[ConnectionLog]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[ControlDatosActualizables]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[dbo_pedproceso]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[DestinatarioProgramaciones]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[DeviceCode]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[Entregas]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[formato]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[GrupoNotificaciones]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[ProgramacionesPantalla]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[ProgramacionesPantalla_EstadosPedido]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[ProgramacionesPantalla_Log]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[HojasTrabajoG21]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[LockedOrder]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[Machine]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[Maquinas]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[matcli]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[matped]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[PedidoLitoProgramadoEnviadoImprimir]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[PedidoProcesado]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[PedidoProgramadoEnviadoImprimir]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[PedidoProgramadoEnviadoImprimirCombinado]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[Plano]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[Tabla_CFG]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[Tabla_codigos_pedido]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[Tabla_Productos]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[TablaAvisos]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[TablaCabeceraTratamientoSAP]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[TablaComentarios]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[TablaDetalleTratamientoSAP]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[TablaFormatosHjltaSH]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[TablaProgramacion]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[TablaTratamientos]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[TblLimpiezas]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[TblTipoLimpieza]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[TipoHjlta]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[UserLite]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[VelocidadesMaquina]",
            "ObjectType": 0
        },
        {
            "Name": "[dbo].[APQ_LOTESNODRIZAS]",
            "ObjectType": 3
        },
        {
            "Name": "[dbo].[CodApli_All]",
            "ObjectType": 3
        },
        {
            "Name": "[dbo].[View_CodApli-TablaProductos]",
            "ObjectType": 3
        }
    ],
   "UiHint": null,
   "UncountableWords": null,
   "UseAsyncStoredProcedureCalls": true,
   "UseBoolPropertiesWithoutDefaultSql": false,
   "UseDatabaseNames": false,
   "UseDatabaseNamesForRoutines": true,
   "UseDateOnlyTimeOnly": false,
   "UseDbContextSplitting": false,
   "UseDecimalDataAnnotationForSprocResult": true,
   "UseFluentApiOnly": true,
   "UseHandleBars": false,
   "UseHierarchyId": false,
   "UseInflector": false,
   "UseInternalAccessModifiersForSprocsAndFunctions": false,
   "UseLegacyPluralizer": false,
   "UseManyToManyEntity": false,
   "UseNoDefaultConstructor": false,
   "UseNoNavigations": false,
   "UseNoObjectFilter": false,
   "UseNodaTime": false,
   "UseNullableReferences": false,
   "UsePrefixNavigationNaming": false,
   "UseSchemaFolders": false,
   "UseSchemaNamespaces": false,
   "UseSpatial": false,
   "UseT4": false,
   "UseT4Split": false
}