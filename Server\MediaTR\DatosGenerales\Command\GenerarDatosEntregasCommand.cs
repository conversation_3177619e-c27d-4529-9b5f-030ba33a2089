﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Services.DatabaseManipulation;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.DatosGenerales.Command;

public class GenerarDatosEntregasCommand : IRequest<SingleResult<int>>
{
}

public class GenerarDatosEntregasCommandHandler : IRequestHandler<GenerarDatosEntregasCommand, SingleResult<int>>
{
    private readonly ProgramadorLitalsaContext _programadorLitalsaContext;
    private readonly IDataManipulationService _dataManipulationService;
    public GenerarDatosEntregasCommandHandler(ProgramadorLitalsaContext programadorLitalsaContext, IDataManipulationService dataManipulationService)
    {
        _programadorLitalsaContext = programadorLitalsaContext;
        _dataManipulationService = dataManipulationService;
    }

    public async Task<SingleResult<int>> Handle(GenerarDatosEntregasCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<int>
        {
            Errors = new List<string>(),
            Data = 0
        };
        try
        {
            //await _programadorLitalsaContext.Entregas.ExecuteDeleteAsync(cancellationToken);
            await _programadorLitalsaContext.Database.ExecuteSqlRawAsync("TRUNCATE TABLE Entregas", cancellationToken: cancellationToken);
            var maquinas = await _programadorLitalsaContext.Maquinas.ToListAsync(cancellationToken);
            var pedidos2 = await _programadorLitalsaContext.Set<Entregas>()
                .FromSqlRaw("[dbo].[sp_DatosEntregasPedidos]")
                .ToListAsync(cancellationToken);
            var listaPedidos = pedidos2.Select(o => o.IdPedido);
            var tablaProgramacion =
                await _programadorLitalsaContext.TablaProgramacion.Where(o => listaPedidos.Contains(o.Idpedido.Value))
                    .ToListAsync(cancellationToken);
            foreach (var p in pedidos2)
            {
                p.Id = null;
                var programaciones = tablaProgramacion
                    .Where(o => o.Idpedido == p.IdPedido)
                    .ToList();
                p.Estado = _dataManipulationService.GetTextoEstadoCodigosAplicacion(p.IdPedido, false, false, maquinas,
                    programaciones);

            }
            await _programadorLitalsaContext.Entregas.AddRangeAsync(pedidos2, cancellationToken);
            await _programadorLitalsaContext.SaveChangesAsync(cancellationToken);
            result.Data = 1;
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: GenerarDatosEntregasCommand - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }
        return result;
    }
}