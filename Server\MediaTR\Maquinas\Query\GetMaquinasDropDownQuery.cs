﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Nelibur.ObjectMapper;
using ProgramadorGeneralBLZ.Server.Data.DatoLita01;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Services;
using ProgramadorGeneralBLZ.Shared;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Maquinas.Query
{
    public class GetMaquinasDropDownQuery : IRequest<ListResult<MaquinaDTO>>
    {
        public GetMaquinasDropDownQuery(Enums.TipoMaquina tipoMaquina)
        {
            TipoMaquina = tipoMaquina;
        }
        public Enums.TipoMaquina TipoMaquina { get; set; }
    }
    public class GetMaquinasDropDownQueryHandler : IRequestHandler<GetMaquinasDropDownQuery, ListResult<MaquinaDTO>>
    {
        private readonly ProgramadorLitalsaContext _contextProg;

        public GetMaquinasDropDownQueryHandler(ProgramadorLitalsaContext contextProg)
        {
            _contextProg = contextProg;
        }

        public async Task<ListResult<MaquinaDTO>> Handle(GetMaquinasDropDownQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var result = new ListResult<MaquinaDTO>()
                {
                    Data = new List<MaquinaDTO>(),
                    Errors = new List<string>()
                };

                List<Models.ProgramadorLitalsa.Maquinas> maquinas;
                if (request.TipoMaquina == Enums.TipoMaquina.Todas)
                {
                    maquinas = await _contextProg.Maquinas.Where(o => 
                            ((o.TipoMaquina == Enums.TipoMaquina.Barnizadora.ToString() || 
                              o.TipoMaquina == Enums.TipoMaquina.Impresora.ToString())
                             && o.IdmaquinaG21 != "FR4"
                             && !o.Retirada))
                        .ToListAsync(cancellationToken);
                }
                else
                {
                    maquinas = await _contextProg.Maquinas.Where(o => o.TipoMaquina == request.TipoMaquina.ToString()
                        && o.IdmaquinaG21 != "FR4"
                        && !o.Retirada)
                        .ToListAsync(cancellationToken);

                    if (!maquinas.Any())
                    {
                        result.Errors.Add("No se han encontrado las máquinas.");
                    }
                    else
                    {
                        //28-08-2023:Desde barnizado se debe poder usar una impresora como barnizadora y que calcule bien los tiempos.
                        //Excluyendo FR4 y M3
                        if (request.TipoMaquina == Enums.TipoMaquina.Barnizadora)
                        {
                            maquinas.AddRange(await _contextProg.Maquinas.Where(o => o.TipoMaquina == Enums.TipoMaquina.Impresora.ToString() && !o.Retirada
                                    && o.IdmaquinaG21 != "FR4" && o.IdmaquinaG21 != "M3")
                                .ToListAsync(cancellationToken));
                        }
                        //FIN
                    }
                }
                
                result.Data = TinyMapper.Map<List<MaquinaDTO>>(maquinas);
                return result;
            }
            catch (Exception e)
            {
                var errorText = $"ERROR: GetMaquinasDropDownQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
                throw new Exception(errorText, e);
            }
        }
    }
}
