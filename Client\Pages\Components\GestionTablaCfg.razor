﻿@using ProgramadorGeneralBLZ.Shared
@using ProgramadorGeneralBLZ.Shared.DTO
@inject HttpClient Http
@inject SpinnerService SpinnerService
@inject IToastService ToastService

<div class="h-100 overflow-auto px-2 py-1">
    <DxGrid Data="@tablaCfg" SizeMode="SizeMode.Medium"
            CssClass="ch-320 smallFont progGrid" PageSize="18"
            KeyFieldName="Id" Context="GridTablaCfg"
            AllowSort="true" EditNewRowPosition="GridEditNewRowPosition.Top"
            ShowFilterRow="true" ValidationEnabled="false"
            EditMode="GridEditMode.EditRow" @ref="Grid" PagerPosition="GridPagerPosition.TopAndBottom"
            EditorRenderMode="GridEditorRenderMode.Integrated"
            EditModelSaving="GridTablaCfg_EditModelSaving"
            DataItemDeleting="Grid_DataItemDeleting">
        <Columns>
            <DxGridCommandColumn Width="80px">
                <HeaderTemplate>
                    <a class="oi oi-plus" @onclick="@(() => Grid.StartEditNewRowAsync())" style="text-decoration: none;color: lightskyblue;" href="javascript:void(0);"></a>
                </HeaderTemplate>
                <CellDisplayTemplate>
                    <a class="oi oi-pencil" @onclick="@(() => Grid.StartEditRowAsync(context.VisibleIndex))" style="text-decoration: none; padding-right: 15px; color: #c75fff;" href="javascript:void(0);"></a>
                    <a class="oi oi-x" @onclick="@(() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex))" style="text-decoration: none; color: red;" href="javascript:void(0);"></a>
                </CellDisplayTemplate>
                <CellEditTemplate>
                    <a class="oi oi-arrow-thick-bottom" @onclick="@(() => Grid.SaveChangesAsync())" style="text-decoration: none; padding-right: 15px; color: greenyellow; margin-right: 6px; margin-top: 3px;" href="javascript:void(0);"></a>
                    <a class="oi oi-x" @onclick="@(() => Grid.CancelEditAsync())" style="text-decoration: none; color: red;" href="javascript:void(0);"></a>
                </CellEditTemplate>
            </DxGridCommandColumn>
            <DxGridDataColumn FieldName="Id" Visible="false" />
            <DxGridDataColumn FieldName="@nameof(TablaCfgDTO.Iddato)" Caption="ID Dato" Width="125"
                              FilterRowOperatorType="GridFilterRowOperatorType.Contains"/>
            <DxGridDataColumn FieldName="@nameof(TablaCfgDTO.Dato)" Caption="Dato"
                              FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
            <DxGridDataColumn FieldName="@nameof(TablaCfgDTO.Detalle)" Caption="Detalle"
                              FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
            <DxGridDataColumn FieldName="@nameof(TablaCfgDTO.Modificable)" Caption="Modificable" Width="80">
                <CellDisplayTemplate Context="checkbox">
                    <DxCheckBox Checked="@((bool)checkbox.Value)" Enabled="false"/>
                </CellDisplayTemplate>
            </DxGridDataColumn>
        </Columns>
        <DataColumnCellEditTemplate>
            @{
                var cfg = (TablaCfgDTO)GridTablaCfg.EditModel;
            }
            @switch (GridTablaCfg.DataColumn.FieldName)
            {
                case "Iddato":
                    <DxTextBox @bind-Text="@cfg.Iddato"
                               ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                               ShowValidationIcon="true" />
                    break;
                case "Dato":
                    <DxTextBox @bind-Text="@cfg.Dato"
                               ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                               ShowValidationIcon="true" />
                    break;
                case "Detalle":
                    <DxMemo @bind-Text="@cfg.Detalle"
                               ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                               ShowValidationIcon="true" />
                    break;
                case "Modificable":
                    <DxCheckBox CssClass="d-inline-block" @bind-Checked="@cfg.Modificable"
                                ValueChecked=true ValueUnchecked=false />
                    break;
            }
        </DataColumnCellEditTemplate>
    </DxGrid>
</div>
@code
{
    DxGrid? Grid;
    List<TablaCfgDTO> tablaCfg = new List<TablaCfgDTO>();

    protected override async Task OnInitializedAsync()
    {
        SpinnerService.Show();
        await LoadData();
        SpinnerService.Hide();
    }

    async Task LoadData()
    {
        tablaCfg = await Http.GetFromJsonAsync<List<TablaCfgDTO>>("GestionTablasV2/TablaCfg");
    }

    async Task GridTablaCfg_EditModelSaving(GridEditModelSavingEventArgs e)
    {
        SpinnerService.Show();
        var dest = (TablaCfgDTO)e.EditModel;
        var response = e.IsNew == false
            ? await Http.PutAsJsonAsync($"GestionTablasV2/TablaCfg/{dest.Id}", dest)
            : await Http.PostAsJsonAsync("GestionTablasV2/TablaCfg", dest);
        if (response.IsSuccessStatusCode)
            await LoadData();
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            ToastService.ShowError(error);
        }
        SpinnerService.Hide();
    }
    async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        SpinnerService.Show();
        var item = (TablaCfgDTO)e.DataItem;
        var response = await Http.DeleteAsync($"GestionTablasV2/TablaCfg/{item.Id}");
        if (response.IsSuccessStatusCode)
            await LoadData();
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            ToastService.ShowError(error);
        }
        SpinnerService.Hide();
    }
    async Task Delete(TablaCfgDTO item)
    {
        SpinnerService.Show();
        var response = await Http.DeleteAsync($"GestionTablasV2/TablaCfg/{item.Id}");
        if (response.IsSuccessStatusCode)
            await LoadData();
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            ToastService.ShowError(error);
        }
        SpinnerService.Hide();
    }
}
