@using ProgramadorGeneralBLZ.Shared.DTO
@*Popup para cambios en bloque*@
<DxPopup HeaderText="Cambios Masivos" ZIndex="2000" MinWidth="380" ShowFooter="true"
CloseOnEscape="true" AllowDrag="true" Scrollable="true"
@bind-Visible="@CambiosMasivosPopUpVisible" Closed="PopupClosed">
    <BodyContentTemplate>
        <div class="ms-3 mb-2">
            <DxComboBox Data="@Maquinas"
            TextFieldName="@nameof(MaquinaDTO.IdmaquinaG21)"
            ValueFieldName="@nameof(MaquinaDTO.Idmaquina)"
            @bind-Value="@Test.MaquinaDto"
            NullText="Máquina..." />
        </div>
        <div class="ms-3 mb-2">
            <DxComboBox Data="@Barnices"
            DropDownWidthMode="DropDownWidthMode.EditorWidth"
            TextFieldName="@nameof(ProductoDTO.DenominacionCompleta)"
            ListRenderMode="ListRenderMode.Virtual"
            ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
            @bind-Value="@Test.ProductoDto" AllowUserInput="true"
            NullText="Barniz..." SearchMode="ListSearchMode.AutoSearch"
            SearchFilterCondition="ListSearchFilterCondition.StartsWith" />
        </div>
        <div class="ms-3 mb-2">
            <DxSpinEdit @bind-Value="@Test.MinCapa"
            ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
            NullText="Capa Min..." DisplayFormat="N1"
            MinValue="0" MaxValue="@Test.MaxCapa"
            BindValueMode="BindValueMode.OnInput" />
        </div>
        <div class="ms-3 mb-2">
            <DxSpinEdit @bind-Value="@Test.MaxCapa"
            ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
            NullText="Capa Max..." DisplayFormat="N1"
            MinValue="@Test.MinCapa"
            BindValueMode="BindValueMode.OnInput" />
        </div>
    </BodyContentTemplate>
    <FooterContentTemplate Context="footer">
        <DxButton Click="@(() => AplicarCambiosMasivos(true))" RenderStyle="ButtonRenderStyle.Success" CssClass="float-right me-2">Guardar</DxButton>
        <DxButton Click="@(() => AplicarCambiosMasivos(false))" RenderStyle="ButtonRenderStyle.Danger" CssClass="float-right cancel-button">Cancelar</DxButton>
    </FooterContentTemplate>
</DxPopup>

@code {
    [Parameter]
    public bool CambiosMasivosPopUpVisible { get; set; } = false;
    [Parameter]
    public List<MaquinaDTO> Maquinas { get; set; }
    [Parameter]
    public List<ProductoDTO> Barnices { get; set; }
    [Parameter]
    public EventCallback<bool> PopUpBulkVisibleEventCallback { get; set; }
    [Parameter]
    public EventCallback<BulkChangesDTO> AplicarCambiosEventCallback { get; set; }
    private ProductoDTO Barniz { get; set; }
    private MaquinaDTO Maquina { get; set; }
    private float? MinCapa { get; set; }
    private float? MaxCapa { get; set; }
    private BulkChangesDTO Test = new BulkChangesDTO();

    private void PopupClosed()
    {
        Barniz = null;
        Maquina = null;
        MinCapa = null;
        MaxCapa = null;
        Test = new BulkChangesDTO();
        PopUpBulkVisibleEventCallback.InvokeAsync(true);
    }
    private void AplicarCambiosMasivos(bool value)
    {
        var newObj = new BulkChangesDTO
            {
                MaquinaDto = Test.MaquinaDto,
                ProductoDto = Test.ProductoDto,
                MinCapa = Test.MinCapa,
                MaxCapa = Test.MaxCapa
            };
        AplicarCambiosEventCallback.InvokeAsync(value ? newObj : null);
    }

}

