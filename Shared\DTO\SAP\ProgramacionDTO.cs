﻿using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace ProgramadorGeneralBLZ.Shared.DTO.SAP
{
    public class ProgramacionDTO : INotifyPropertyChanged
    {
        private int _id;
        private string _nombre;
        private DateTime _fecha;
        private int _cantidad;

        public int Id
        {
            get => _id;
            set => SetField(ref _id, value);
        }

        public string Nombre
        {
            get => _nombre;
            set => SetField(ref _nombre, value);
        }

        public DateTime Fecha
        {
            get => _fecha;
            set => SetField(ref _fecha, value);
        }

        public int Cantidad
        {
            get => _cantidad;
            set => SetField(ref _cantidad, value);
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetField<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }
}