﻿@page "/gestiontablas"


@using Microsoft.AspNetCore.Authorization
@using ProgramadorGeneralBLZ.Shared
@using ProgramadorGeneralBLZ.Client.Pages.Components
@using ProgramadorGeneralBLZ.Shared.DTO
@inject Blazored.LocalStorage.ILocalStorageService localStorage
@inject IJSRuntime Js
@inject IToastService ToastService
@inject HttpClient Http
@inject SpinnerService SpinnerService
@inject AuthenticationStateProvider GetAuthenticationStateAsync
@inject NavigationManager Navigation

@attribute [Authorize(Roles = Roles.Programador)]

<PageTitle>Gestión Tablas</PageTitle>




<AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}")">
    <Authorized Context="authContext">
        <DxLayoutBreakpoint DeviceSize="DeviceSize.Large" />
        <div class="h-100 px-2 py-1">
            <DxFormLayout CssClass="" >
                <DxFormLayoutTabPages RenderMode="TabsRenderMode.OnDemand" ScrollMode="TabsScrollMode.NavButtons" >
                    <DxFormLayoutTabPage Caption="Grupos">
                        <GestionGrupos />
                    </DxFormLayoutTabPage>
                    <DxFormLayoutTabPage Caption="Destinatarios">
                        <GestionDestinatarios />
                    </DxFormLayoutTabPage>
                    <DxFormLayoutTabPage Caption="Comentarios">
                        <GestionComentarios />
                    </DxFormLayoutTabPage>
                    <DxFormLayoutTabPage Caption="Tabla CFG">
                        <GestionTablaCfg />
                    </DxFormLayoutTabPage>
                    <DxFormLayoutTabPage Caption="Tabla Planos">
                        <GestionPlanos />
                    </DxFormLayoutTabPage>
                    <DxFormLayoutTabPage Caption="Tabla Limpiezas">
                        <GestionLimpiezas />
                    </DxFormLayoutTabPage>
                    <DxFormLayoutTabPage Caption="Tabla Clientes">
                        <GestionClientes />
                    </DxFormLayoutTabPage>
                    <DxFormLayoutTabPage Caption="Tabla Maquinas">
                        <GestionMaquinas />
                    </DxFormLayoutTabPage>
                    <DxFormLayoutTabPage Caption="Tabla Avisos">
                        <GestionTablaAvisos />
                    </DxFormLayoutTabPage>
                    <DxFormLayoutTabPage Caption="Tabla Productos">
                        <GestionTablaProductos />
                    </DxFormLayoutTabPage>
                </DxFormLayoutTabPages>
            </DxFormLayout>
        </div>
    </Authorized>
    <NotAuthorized>
        <NoPuedesPasar />
    </NotAuthorized>
</AuthorizeView>




@code {
    
}