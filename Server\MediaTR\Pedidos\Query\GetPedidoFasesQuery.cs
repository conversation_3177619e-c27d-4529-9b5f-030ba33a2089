﻿using System.Reflection;
using MediatR;
using Nelibur.ObjectMapper;
using ProgramadorGeneralBLZ.Server.Data.DatoLita01;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Services;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Pedidos.Query
{
    public class GetPedidoFasesQuery : IRequest<ListResult<FasePedidoDTO>>
    {
        /// <summary>
        /// Obtiene las fases del pedido visualizado
        /// </summary>
        /// <param name="idPedido"></param>
        public GetPedidoFasesQuery(int? idPedido)
        {
            IdPedido = idPedido;
        }
        public int? IdPedido { get; set; }
    }

    public class GetFasesPedidoQueryHandler : IRequestHandler<GetPedidoFasesQuery, ListResult<FasePedidoDTO>>
    {
        private readonly ProgramadorLitalsaContext _contextProg;


        public GetFasesPedidoQueryHandler(ProgramadorLitalsaContext contextProg)
        {
            _contextProg = contextProg;
        }

        /// <summary>
        /// Recuperamos de la BD los datos del pedido indicado.
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<ListResult<FasePedidoDTO>> Handle(GetPedidoFasesQuery request, CancellationToken cancellationToken)
        {
            var result = new ListResult<FasePedidoDTO>()
            {
                Data = new List<FasePedidoDTO>(),
                Errors = new List<string>()
            };
            try
            {

                var query = _contextProg.TablaCodigosPedido.Where(o => o.Idpedido == request.IdPedido).ToList();

                //Mapear a DTO
                var objDto = TinyMapper.Map<List<FasePedidoDTO>>(query);
                foreach (var fase in objDto)
                {
                    //fase.TextoIdCodigoAplicacion = _contextProg.CodApliAll
                    //    .Where(o => o.Codbaz == fase.Idcodigoaplicacion).Select(s => new { texto = $"{s.Codbaz},{s.Nombaz}" }).FirstOrDefault()?.texto ?? "N/A";
                    fase.TextoIdCodigoAplicacion = _contextProg.Codapli
                        .Where(o => o.Codbaz == fase.Idcodigoaplicacion).Select(s => new { texto = $"{s.Codbaz},{s.Nombaz}" }).FirstOrDefault()?.texto ?? "N/A";
                    fase.TextoIdbarniz = _contextProg.TablaProductos.Where(o => o.Idproducto == fase.Idbarniz)
                        .Select(s => new { texto = $"{s.Idproducto},{s.Denominacion}" }).FirstOrDefault()?.texto ?? "N/A";
                }
                result.Data = objDto;
                return result;
            }
            catch (Exception e)
            {
                result.Errors.Add($"{e.Message}--{(!string.IsNullOrWhiteSpace(e.InnerException?.Message) ? e.InnerException : string.Empty)}");
                return result;
            }
        }
    }
}
