﻿using ProgramadorGeneralBLZ.Shared.DTO;
using Blazored.LocalStorage;
using System.Threading.Tasks;

namespace ProgramadorGeneralBLZ.Shared
{
    public class FiltroService
    {
        private readonly ILocalStorageService _localStorageService;

        public FiltroService(ILocalStorageService localStorageService)
        {
            _localStorageService = localStorageService;
        }

        public async Task SetFiltroAsync(FiltroDTO filtro)
        {
            await _localStorageService.SetItemAsync("filtro", filtro);
        }

        public async Task<FiltroDTO> GetFiltroAsync()
        {
            return await _localStorageService.GetItemAsync<FiltroDTO>("filtro");
        }
    }
}