﻿@using ProgramadorGeneralBLZ.Shared
@using ProgramadorGeneralBLZ.Shared.DTO
@inject HttpClient Http
@inject SpinnerService SpinnerService
@inject IToastService ToastService

<div class="h-100 overflow-auto px-2 py-1">
    <DxGrid Data="@comentarios" SizeMode="SizeMode.Medium"
            CssClass="ch-320 smallFont progGrid" PageSize="18" 
            KeyFieldName="Id" Context="GridGrupos"
            AllowSort="true" EditNewRowPosition="GridEditNewRowPosition.Top"
            ShowFilterRow="true" ValidationEnabled="true"
            EditMode="GridEditMode.EditRow" @ref="Grid" PagerPosition="GridPagerPosition.TopAndBottom"
            EditorRenderMode="GridEditorRenderMode.Integrated"
            EditModelSaving="GridGrupos_EditModelSaving"
            DataItemDeleting="Grid_DataItemDeleting">
        <Columns>
            <DxGridCommandColumn Width="80px">
                <HeaderTemplate>
                    <a class="oi oi-plus" @onclick="@(() => Grid.StartEditNewRowAsync())" style="text-decoration: none;color: lightskyblue;" href="javascript:void(0);"></a>
                </HeaderTemplate>
                <CellDisplayTemplate>
                    <a class="oi oi-pencil" @onclick="@(() => Grid.StartEditRowAsync(context.VisibleIndex))" style="text-decoration: none; padding-right: 15px; color: #c75fff;" href="javascript:void(0);"></a>
                    <a class="oi oi-x" @onclick="@(() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex))" style="text-decoration: none; color: red;" href="javascript:void(0);"></a>
                </CellDisplayTemplate>
                <CellEditTemplate>
                    <a class="oi oi-arrow-thick-bottom" @onclick="@(() => Grid.SaveChangesAsync())" style="text-decoration: none; padding-right: 15px; color: greenyellow; margin-right: 6px; margin-top: 3px;" href="javascript:void(0);"></a>
                    <a class="oi oi-x" @onclick="@(() => Grid.CancelEditAsync())" style="text-decoration: none; color: red;" href="javascript:void(0);"></a>
                </CellEditTemplate>
            </DxGridCommandColumn>
            <DxGridDataColumn FieldName="Id" Visible="false" />
            <DxGridDataColumn FieldName="@nameof(ComentariosDTO.Tipo)" Caption="Tipo" Width="80"
                              FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
            <DxGridDataColumn FieldName="@nameof(ComentariosDTO.RefAbuscar)" Caption="Ref. a buscar" Width="125" 
                              FilterRowOperatorType="GridFilterRowOperatorType.Contains"/>
            <DxGridDataColumn FieldName="@nameof(ComentariosDTO.TextoAdevolver)" Caption="Texto a devolver"
                              FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
            <DxGridDataColumn FieldName="@nameof(ComentariosDTO.Comentarios)" Caption="Comentarios"
                              FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
            <DxGridDataColumn FieldName="@nameof(ComentariosDTO.Obsoleto)" Caption="Obsoleto" Width="80" >
                <CellDisplayTemplate Context="checkbox">
                    <DxCheckBox Checked="@((bool)checkbox.Value)" Enabled="false"/>
                </CellDisplayTemplate>
            </DxGridDataColumn>
        </Columns>
        <DataColumnCellEditTemplate>
            @{
                var comm = (ComentariosDTO)GridGrupos.EditModel;
            }
            @switch (GridGrupos.DataColumn.FieldName)
            {
                case "Tipo":
                    <DxTextBox @bind-Text="@comm.Tipo"
                               ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                               ShowValidationIcon="true" />
                    break;
                case "RefAbuscar":
                    <DxTextBox @bind-Text="@comm.RefAbuscar"
                               ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                               ShowValidationIcon="true" />
                    break;
                case "TextoAdevolver":
                    <DxMemo @bind-Text="@comm.TextoAdevolver"
                               ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                               ShowValidationIcon="true" />
                    break;
                case "Comentarios":
                    <DxMemo @bind-Text="@comm.Comentarios" 
                               ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                               ShowValidationIcon="true" />
                    break;
                case "Obsoleto":
                    <DxCheckBox CssClass="d-inline-block" @bind-Checked="@comm.Obsoleto"
                                ValueChecked=true ValueUnchecked=false />
                    break;
            }
        </DataColumnCellEditTemplate>
    </DxGrid>
</div>
@code
{
    DxGrid? Grid;
    List<ComentariosDTO> comentarios = new List<ComentariosDTO>();

    protected override async Task OnInitializedAsync()
    {
        SpinnerService.Show();
        await LoadData();
        SpinnerService.Hide();
    }

    async Task LoadData()
    {
        comentarios = await Http.GetFromJsonAsync<List<ComentariosDTO>>("GestionTablasV2/Comentarios");
    }

    async Task GridGrupos_EditModelSaving(GridEditModelSavingEventArgs e)
    {
        SpinnerService.Show();
        var dest = (ComentariosDTO)e.EditModel;
        var response = e.IsNew == false
            ? await Http.PutAsJsonAsync($"GestionTablasV2/Comentarios/{dest.Id}", dest)
            : await Http.PostAsJsonAsync("GestionTablasV2/Comentarios", dest);
        if (response.IsSuccessStatusCode)
            await LoadData();
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            ToastService.ShowError(error);
        }
        SpinnerService.Hide();
    }
    async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        SpinnerService.Show();
        var item = (ComentariosDTO)e.DataItem;
        var response = await Http.DeleteAsync($"GestionTablasV2/Comentarios/{item.Id}");
        if (response.IsSuccessStatusCode)
            await LoadData();
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            ToastService.ShowError(error);
        }
        SpinnerService.Hide();
    }
    async Task Delete(ComentariosDTO item)
    {
        SpinnerService.Show();
        var response = await Http.DeleteAsync($"GestionTablasV2/Comentarios/{item.Id}");
        if (response.IsSuccessStatusCode)
            await LoadData();
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            ToastService.ShowError(error);
        }
        SpinnerService.Hide();
    }
}
