﻿using System.IO;
using System.Text;
using DevExpress.XtraPrinting.Native.Lines;
using DevExpress.XtraSpellChecker.Parser;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Identity.Web.Resource;
using ProgramadorGeneralBLZ.Server.CustomFilterAttributes;
using ProgramadorGeneralBLZ.Server.MediaTR.DatosGenerales.Command;
using ProgramadorGeneralBLZ.Server.MediaTR.DatosGenerales.Query;
using ProgramadorGeneralBLZ.Server.MediaTR.Pedidos.Query;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;


namespace ProgramadorGeneralBLZ.Server.Controllers
{
    [AllowAnonymous]
    [ApiController]
    [Route("[controller]")]
    public class ConsultaDBController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        public ConsultaDBController(IConfiguration configuration)
        {
            _configuration = configuration;
        }
        [HttpGet("current-database")]
        public ActionResult<bool> GetCurrentDatabase()
        {
            var esDesarrollo = _configuration.GetConnectionString("DefaultConnectionProgramador").Contains("_DES",StringComparison.OrdinalIgnoreCase);

            return esDesarrollo;
        }
    }
}
