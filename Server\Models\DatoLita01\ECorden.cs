﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace ProgramadorGeneralBLZ.Server.Models.DatoLita01
{
    public partial class ECorden
    {
        public string Codigo { get; set; }
        public string Codcli { get; set; }
        public string Nombre { get; set; }
        public string Direccion { get; set; }
        public string Direccion1 { get; set; }
        public string Poblacion { get; set; }
        public string Provincia { get; set; }
        public string Cif { get; set; }
        public DateTime? Fecha { get; set; }
        public DateTime? Fentrega { get; set; }
        public string Descrip { get; set; }
        public string Producto { get; set; }
        public double? Cantidad { get; set; }
        public string Especif1 { get; set; }
        public string Especif2 { get; set; }
        public string Especif3 { get; set; }
        public string Especif4 { get; set; }
        public string Especif5 { get; set; }
        public string Contacto { get; set; }
        public string Campo1 { get; set; }
        public string Campo2 { get; set; }
        public string Campo3 { get; set; }
        public string Campo4 { get; set; }
        public string Postal { get; set; }
        public string <PERSON>ona { get; set; }
        public string Mercado { get; set; }
        public string Parte01 { get; set; }
        public string Parte02 { get; set; }
        public string Parte03 { get; set; }
        public string Parte04 { get; set; }
        public string Parte05 { get; set; }
        public string Parte06 { get; set; }
        public string Parte07 { get; set; }
        public string Parte08 { get; set; }
        public string Parte09 { get; set; }
        public string Parte10 { get; set; }
        public double? Canti01 { get; set; }
        public double? Canti02 { get; set; }
        public double? Canti03 { get; set; }
        public double? Canti04 { get; set; }
        public double? Canti05 { get; set; }
        public double? Canti06 { get; set; }
        public double? Canti07 { get; set; }
        public double? Canti08 { get; set; }
        public double? Canti09 { get; set; }
        public double? Canti10 { get; set; }
        public string Plantilla { get; set; }
        public double? Horastot { get; set; }
        public double? Fijostot { get; set; }
        public double? Fijostot1 { get; set; }
        public double? Variatot { get; set; }
        public double? Variatot1 { get; set; }
        public double? Costetot { get; set; }
        public double? Costetot1 { get; set; }
        public double? Inicitot { get; set; }
        public double? Inicitot1 { get; set; }
        public double? Agentes { get; set; }
        public double? Margen { get; set; }
        public double? Unitario { get; set; }
        public double? Unitario1 { get; set; }
        public double? Total { get; set; }
        public double? Total1 { get; set; }
        public DateTime? Fecfinal { get; set; }
        public string Codigo0 { get; set; }
        public DateTime? Fecha0 { get; set; }
        public double? Cantidad0 { get; set; }
        public double? Horastot0 { get; set; }
        public double? Fijostot0 { get; set; }
        public double? Fijostot01 { get; set; }
        public double? Variatot0 { get; set; }
        public double? Variatot01 { get; set; }
        public double? Costetot0 { get; set; }
        public double? Costetot01 { get; set; }
        public double? Inicitot0 { get; set; }
        public double? Inicitot01 { get; set; }
        public double? Agentes0 { get; set; }
        public double? Margen0 { get; set; }
        public double? Unitario0 { get; set; }
        public double? Unitario01 { get; set; }
        public double? Total0 { get; set; }
        public double? Total01 { get; set; }
        public double? Mermaf { get; set; }
        public double? Mermav { get; set; }
        public double? Enviado { get; set; }
        public string Represen { get; set; }
        public double? Comision { get; set; }
        public string Tipiva { get; set; }
        public string Empresa { get; set; }
        public double? Almacenado { get; set; }
        public double? Margen2 { get; set; }
        public double? Tgastos { get; set; }
        public double? Tgastos1 { get; set; }
        public string Ultccoste { get; set; }
        public string Ultope { get; set; }
        public double? Ultfecha { get; set; }
        public string Estatus { get; set; }
        public string Macro { get; set; }
        public string Estado { get; set; }
        public string Solicitud { get; set; }
        public string Otimpresa { get; set; }
        public string Memoria { get; set; }
        public double? Descu { get; set; }
        public string Fpago { get; set; }
        public string Represen2 { get; set; }
        public string Represen3 { get; set; }
        public string Represen4 { get; set; }
        public string Represen5 { get; set; }
        public string Represen6 { get; set; }
        public double? Comision2 { get; set; }
        public double? Comision3 { get; set; }
        public double? Comision4 { get; set; }
        public double? Comision5 { get; set; }
        public double? Comision6 { get; set; }
        public string Pressid { get; set; }
        public string Ultacaba { get; set; }
        public string Pais { get; set; }
        public string Ccoste { get; set; }
        public double? Ucaja { get; set; }
        public string Fsc { get; set; }
        public string Controlok { get; set; }
        public DateTime? Fentreconf { get; set; }
    }
}