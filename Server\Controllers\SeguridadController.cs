﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Identity.Web.Resource;
using ProgramadorGeneralBLZ.Server.CustomFilterAttributes;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.MediaTR.Maquinas.Query;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;


namespace ProgramadorGeneralBLZ.Server.Controllers
{
    [Authorize]
    [ApiController]
    [Route("[controller]")]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAdB2C:Scopes")]
    //[Authorize(Roles = $"{Roles.Programador},{Roles.Admin}")]
    [ClaimRequirementContainRole("extension_Roles", $"{Roles.Programador},{Roles.Admin},{Roles.Consulta}")]
    public class SeguridadController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<MaquinasController> _logger;
        private readonly ProgramadorLitalsaContext _context;

        public SeguridadController(IMediator mediator, ILogger<MaquinasController> logger, ProgramadorLitalsaContext context)
        {
            _mediator = mediator;
            _logger = logger;
            _context = context;
        }

        [HttpGet("getbloqueopedido/{idpedido}")]
        public async Task<SingleResult<bool>> getbloqueopedido(int idpedido)
        {
            var user = this.User;
            var result = new SingleResult<bool>();
            try
            {
                var registro = await _context.LockedOrder.Where(o => o.OrderId == idpedido)
                    .OrderByDescending(o => o.Date)
                    .FirstOrDefaultAsync();
                if (registro != null)
                {
                    if (registro.Evento == "Bloqueado" && registro.User != user.Identity.Name)
                    {
                        result.Errors.Add($"Info: El pedido {idpedido} está bloqueado por {registro.User}");
                    }
                    else
                    {
                        registro.Date = DateTime.Now;
                        registro.Evento = "Liberado";
                        registro.User = user.Identity.Name;
                    }
                }
                else
                {
                    registro = new LockedOrder
                    {
                        OrderId = idpedido,
                        Date = DateTime.Now,
                        Evento = "Liberado",
                        User = user.Identity.Name,
                    };
                    await _context.LockedOrder.AddAsync(registro);
                }
                await _context.SaveChangesAsync();
                result.Data = false;
            }
            catch (Exception e)
            {
                result.Errors.Add($"Error al registrar el bloqueo/desbloqueo del pedido {idpedido}");
            }
            return result;
        }


        [HttpPost("bloquearpedido/{idpedido}")]
        public async Task<SingleResult<bool>> PostEstadoPedido([FromBody] bool bloquear, int idpedido)
        {
            var user = this.User;
            var result = new SingleResult<bool>();
            try
            {
                var registro = await _context.LockedOrder.Where(o => o.OrderId == idpedido)
                    .OrderByDescending(o => o.Date)
                    .FirstOrDefaultAsync();
                if (registro != null)
                {
                    if (registro.Evento == "Bloqueado" && registro.User != user.Identity.Name)
                    {
                        result.Errors.Add($"Info: El pedido {idpedido} está bloqueado por {registro.User}");
                    }
                    else
                    {
                        registro.Date = DateTime.Now;
                        registro.Evento = bloquear ? "Bloqueado" : "Liberado";
                        registro.ForcedEnd = false;
                        registro.User = user.Identity.Name;
                    }
                }
                else
                {
                    registro = new LockedOrder
                    {
                        OrderId = idpedido,
                        Date = DateTime.Now,
                        Evento = bloquear ? "Bloqueado" : "Liberado",
                        ForcedEnd = false,
                        User = user.Identity.Name,
                    };
                    await _context.LockedOrder.AddAsync(registro);
                }
                await _context.SaveChangesAsync();
                result.Data = true;
            }
            catch (Exception e)
            {
                result.Errors.Add($"Error al registrar el bloqueo/desbloqueo del pedido {idpedido}");
            }
            return result;
        }
        [HttpPost("forzardesbloqueo")]
        public async Task<SingleResult<bool>> PostForzarDesbloqueo([FromBody] int idpedido)
        {
            var user = this.User;
            var result = new SingleResult<bool>();
            try
            {
                var registro = await _context.LockedOrder.FirstOrDefaultAsync(o =>
                    o.OrderId == idpedido && o.Evento == "Bloqueado" && o.User != user.Identity.Name);
                if (registro != null)
                {
                    registro.Date = DateTime.Now;
                    registro.Evento = "Liberado";
                    registro.ForcedEnd = true;
                    registro.User = user.Identity.Name;

                    await _context.SaveChangesAsync();
                }
                result.Data = true;
            }
            catch (Exception e)
            {
                result.Errors.Add($"Error al forzar el desbloqueo del pedido {idpedido}");
            }
            return result;
        }
    }
}
