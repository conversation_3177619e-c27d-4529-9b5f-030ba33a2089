﻿using System.Globalization;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Nelibur.ObjectMapper;
using ProgramadorGeneralBLZ.Server.Data.DatoLita01;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.DatosGenerales.Query;

public class GetDatosTablaAvisosQuery : IRequest<ListResult<TablaAvisosDTO>>
{
    public GetDatosTablaAvisosQuery()
    {
    }
}

internal class GetDatosTablaAvisosQueryHandler : IRequestHandler<GetDatosTablaAvisosQuery, ListResult<TablaAvisosDTO>>
{
    private readonly ProgramadorLitalsaContext _programadorLitalsaContext;
    public GetDatosTablaAvisosQueryHandler(ProgramadorLitalsaContext programadorLitalsaContext)
    {
        _programadorLitalsaContext = programadorLitalsaContext;
    }

    public async Task<ListResult<TablaAvisosDTO>> Handle(GetDatosTablaAvisosQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<TablaAvisosDTO>()
        {
            Data = new List<TablaAvisosDTO>(),
            Errors = new List<string>()
        };
        try
        {
            var datosLimpias =
                await _programadorLitalsaContext.TablaAvisos.ToListAsync(cancellationToken);
            
            result.Data = TinyMapper.Map<List<TablaAvisosDTO>>(datosLimpias);
            return result;
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: GetDatosTablaAvisosQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            throw new Exception(errorText, e);
        }
    }
}