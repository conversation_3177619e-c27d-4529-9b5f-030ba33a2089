﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProgramadorGeneralBLZ.Shared.DTO
{
    public class PedidoBarnizadoImprimirDTO
    {
        public int Idpedido { get; set; }
        public int Idaplicacion { get; set; }
        public int Idlinea { get; set; }
        public int Posicion { get; set; }
        public int Orden { get; set; }
        public string Producto { get; set; }
        public int IdCliente { get; set; }
        public int HojasPedido { get; set; }
        public string TipoElemento { get; set; }
        public string Formato { get; set; }
        public string Plano { get; set; }
        public float Ancho_hjlta { get; set; }
        public float Largo_hjlta { get; set; }
        public float Espesor_hjlta { get; set; }
        public string Tipo_hjlta { get; set; }
        public string ApliProducto { get; set; }
        public bool Flejar { get; set; }
        public float Sup { get; set; }
        public DateTime HoraComienzoEstimada { get; set; }
        public DateTime HoraFinEstimada { get; set; }
        public string TiposCambio { get; set; }
        public DateTime RequeridoEnFecha { get; set; }
        public string Producto2 { get; set; }
        public string Pos_escuadra { get; set; }
        public int Idproducto { get; set; }
        public float Peso { get; set; }
        public float BarnizNecesario { get; set; }
        public string ObsCalidad { get; set; }
        public int HojasAProcesar { get; set; }
        public string CalleAPQ { get; set; }
        public string CaractHjlta { get; set; }
        public string MuestraSH { get; set; }
        public string TipoLavada { get; set; }
        public bool URGENTE { get; set; }
        public string Obspaseposterior { get; set; }
        public string Observaciones { get; set; }
        public string ObsAlmacen { get; set; }
    }
}
