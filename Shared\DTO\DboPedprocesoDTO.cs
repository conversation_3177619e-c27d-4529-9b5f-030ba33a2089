﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace ProgramadorGeneralBLZ.Shared.DTO
{
    public class DboPedprocesoDTO
    {
        public int? Pedido { get; set; }
        public int? Proceso { get; set; }
        public double? Cantidad { get; set; }
        public string Lugar { get; set; }
        public string Barniz { get; set; }
        public string Txtbarniz { get; set; }
        public string Impdia { get; set; }
        public string Posicion { get; set; }
        public int PedprocesoCopiaId { get; set; }
        public string CalculatedId { get; set; }
        public string TextoCaraSiguiente { get; set; }
        //[NotMapped]
        //[JsonIgnore]
        public string Combinado => $"{Proceso} / {Posicion} / {Barniz} / {Lugar}";
    }
}
