﻿using MediatR;
using Nelibur.ObjectMapper;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Pedidos.Command
{
    public class EditFasePedidoCommand : IRequest<SingleResult<int>>
    {
        public EditFasePedidoCommand(int idPedido, int fase, FasePedidoDTO newFasePedido)
        {
            IdPedido = idPedido;
            Fase = fase;
            NewFasePedido = newFasePedido;
        }

        public int IdPedido { get; set; }
        public int Fase { get; set; }
        public FasePedidoDTO NewFasePedido { get; set; }
    }
    public class EditFasePedidoCommandHandler : IRequestHandler<EditFasePedidoCommand, SingleResult<int>>
    {
        private readonly ProgramadorLitalsaContext _contextProg;
        public EditFasePedidoCommandHandler(ProgramadorLitalsaContext contextProg)
        {
            _contextProg = contextProg;
        }


        public async Task<SingleResult<int>> Handle(EditFasePedidoCommand request, CancellationToken cancellationToken)
        {
            var result = new SingleResult<int>{ Errors = new List<string>(), Data = 0 };
            try
            {
                var nuevaFase = request.NewFasePedido;
                var currentFase =
                    _contextProg.TablaCodigosPedido.FirstOrDefault(o =>
                        o.Idproceso == nuevaFase.Idproceso);

                //currentFase = TinyMapper.Map<TablaCodigosPedido>(nuevaFase);
                _contextProg.Entry(currentFase).CurrentValues.SetValues(nuevaFase);

                //MUY IMPORTANTE: marcar creacion manual para asegurarnos que no se pierden estos registros creados a mano durante
                //el proceso de actualización de cabeceras rápido - LimpiarTablaCodigosPedidoNoEnCabpedCommand
                currentFase.CreaccionManual = true;
                currentFase.Hojasaplicadas = nuevaFase.Hojasaplicadas ?? 0;

                var dbResult = await _contextProg.SaveChangesAsync(cancellationToken);
                result.Data = dbResult;
            }
            catch (Exception e)
            {
                result.Errors.Add($"Se ha producido un error en la modificación de la fase del pedido");
            }
            return result;


        }
    }
}
