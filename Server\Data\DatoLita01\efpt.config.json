﻿{
   "CodeGenerationMode": 4,
   "ContextClassName": "DatoLita01Context",
   "ContextNamespace": null,
   "FilterSchemas": false,
   "IncludeConnectionString": true,
   "ModelNamespace": null,
   "OutputContextPath": "Data\\DatoLita01",
   "OutputPath": "Models\\DatoLita01",
   "PreserveCasingWithRegex": true,
   "ProjectRootNamespace": "ProgramadorGeneralBLZ.Server",
   "Schemas": null,
   "SelectedHandlebarsLanguage": 0,
   "SelectedToBeGenerated": 0,
   "T4TemplatePath": null,
   "Tables": [
      {
         "Name": "[dbo].[A_ARTICU]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[E_CORDEN]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[E_EORDEN]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[G_CLIENT]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[acaped]",
         "ObjectType": 3
      },
      {
         "Name": "[dbo].[CABPED]",
         "ObjectType": 3
      },
      {
         "Name": "[dbo].[codapli]",
         "ObjectType": 3
      },
      {
         "Name": "[dbo].[CODIGOAPLICACION]",
         "ObjectType": 3
      },
      {
         "Name": "[dbo].[FECHAS]",
         "ObjectType": 3
      },
      {
         "Name": "[dbo].[matped]",
         "ObjectType": 3
      },
      {
         "Name": "[dbo].[motivos]",
         "ObjectType": 3
      },
      {
         "Name": "[dbo].[MOTIVOS_DETALLADOS]",
         "ObjectType": 3
      },
      {
         "Name": "[dbo].[observa]",
         "ObjectType": 3
      },
      {
         "Name": "[dbo].[parteshorariosstr]",
         "ObjectType": 3
      },
      {
         "Name": "[dbo].[planoform]",
         "ObjectType": 3
      },
      {
         "Name": "[dbo].[tipohjlta]",
         "ObjectType": 3
      }
   ],
   "UiHint": null,
   "UncountableWords": null,
   "UseAsyncStoredProcedureCalls": true,
   "UseBoolPropertiesWithoutDefaultSql": false,
   "UseDatabaseNames": false,
   "UseDatabaseNamesForRoutines": true,
   "UseDateOnlyTimeOnly": false,
   "UseDbContextSplitting": false,
   "UseDecimalDataAnnotationForSprocResult": true,
   "UseFluentApiOnly": true,
   "UseHandleBars": false,
   "UseHierarchyId": false,
   "UseInflector": false,
   "UseInternalAccessModifiersForSprocsAndFunctions": false,
   "UseLegacyPluralizer": false,
   "UseManyToManyEntity": false,
   "UseNoDefaultConstructor": false,
   "UseNoNavigations": false,
   "UseNoObjectFilter": false,
   "UseNodaTime": false,
   "UseNullableReferences": false,
   "UsePrefixNavigationNaming": false,
   "UseSchemaFolders": false,
   "UseSchemaNamespaces": false,
   "UseSpatial": false,
   "UseT4": false,
   "UseT4Split": false
}