﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable di**ble
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLital**;
using ProgramadorGeneralBLZ.Shared.DTO;

namespace ProgramadorGeneralBLZ.Server.Data.ProgramadorLital**
{
    public partial class ProgramadorLital**Context : DbContext
    {
        public ProgramadorLital**Context()
        {
        }

        public ProgramadorLital**Context(DbContextOptions<ProgramadorLital**Context> options)
            : base(options)
        {
        }

        public virtual DbSet<ApqLotesnodrizas> ApqLotesnodrizas { get; set; }
        public virtual DbSet<AuditoriaActualizacionDatosPedido> AuditoriaActualizacionDatosPedido { get; set; }
        public virtual DbSet<BlockedMachine> BlockedMachine { get; set; }
        public virtual DbSet<Clientes> Clientes { get; set; }
        public virtual DbSet<CodApliAll> CodApliAll { get; set; }
        public virtual DbSet<Codapli> Codapli { get; set; }
        public virtual DbSet<CodapliLito> CodapliLito { get; set; }
        public virtual DbSet<Codtintas> Codtintas { get; set; }
        public virtual DbSet<CompruebaExistenciasBarnices> CompruebaExistenciasBarnices { get; set; }
        public virtual DbSet<ConnectionLog> ConnectionLog { get; set; }
        public virtual DbSet<ControlDatosActualizables> ControlDatosActualizables { get; set; }
        public virtual DbSet<DboPedproceso> DboPedproceso { get; set; }
        public virtual DbSet<DestinatarioProgramaciones> DestinatarioProgramaciones { get; set; }
        public virtual DbSet<DeviceCode> DeviceCode { get; set; }
        public virtual DbSet<Entregas> Entregas { get; set; }
        public virtual DbSet<Formato> Formato { get; set; }
        public virtual DbSet<GrupoNotificaciones> GrupoNotificaciones { get; set; }
        public virtual DbSet<ProgramacionesPantalla> ProgramacionesPantalla { get; set; }
        public virtual DbSet<ProgramacionesPantallaEstadosPedido> ProgramacionesPantallaEstadosPedido { get; set; }
        public virtual DbSet<ProgramacionesPantallaLog> ProgramacionesPantallaLog { get; set; }
        public virtual DbSet<HojasTrabajoG21> HojasTrabajoG21 { get; set; }
        public virtual DbSet<LockedOrder> LockedOrder { get; set; }
        public virtual DbSet<Machine> Machine { get; set; }
        public virtual DbSet<Maquinas> Maquinas { get; set; }
        public virtual DbSet<Matcli> Matcli { get; set; }
        public virtual DbSet<Matped> Matped { get; set; }
        public virtual DbSet<PedidoLitoProgramadoEnviadoImprimir> PedidoLitoProgramadoEnviadoImprimir { get; set; }
        public virtual DbSet<PedidoProce**do> PedidoProce**do { get; set; }
        public virtual DbSet<PedidoProgramadoEnviadoImprimir> PedidoProgramadoEnviadoImprimir { get; set; }
        public virtual DbSet<PedidoProgramadoEnviadoImprimirCombinado> PedidoProgramadoEnviadoImprimirCombinado { get; set; }
        public virtual DbSet<Plano> Plano { get; set; }
        public virtual DbSet<TablaAvisos> TablaAvisos { get; set; }
        public virtual DbSet<TablaCabeceraTratamientoSap> TablaCabeceraTratamientoSap { get; set; }
        public virtual DbSet<TablaCfg> TablaCfg { get; set; }
        public virtual DbSet<TablaCodigosPedido> TablaCodigosPedido { get; set; }
        public virtual DbSet<TablaComentarios> TablaComentarios { get; set; }
        public virtual DbSet<TablaDetalleTratamientoSap> TablaDetalleTratamientoSap { get; set; }
        public virtual DbSet<TablaFormatosHjltaSh> TablaFormatosHjltaSh { get; set; }
        public virtual DbSet<TablaProductos> TablaProductos { get; set; }
        public virtual DbSet<TablaProgramacion> TablaProgramacion { get; set; }
        public virtual DbSet<TablaTratamientos> TablaTratamientos { get; set; }
        public virtual DbSet<TblLimpiezas> TblLimpiezas { get; set; }
        public virtual DbSet<TblTipoLimpieza> TblTipoLimpieza { get; set; }
        public virtual DbSet<TipoHjlta> TipoHjlta { get; set; }
        public virtual DbSet<UserLite> UserLite { get; set; }
        public virtual DbSet<VelocidadesMaquina> VelocidadesMaquina { get; set; }
        public virtual DbSet<ViewCodApliTablaProductos> ViewCodApliTablaProductos { get; set; }


        //ESTO NO HAY QUE HACERLO ASI, pero tiramos por ahora, ya que habria que hacer correspondencia con BD o buscar una alternativa.
        //Es nece**rio para poder hacer una consulta sql a la BD y que un proceso sea mucho más rápido.
        //Hay que asegurarse que se mantiene si se hace un refresh o update del context u**ndo las herramientas EF Core Power Tools, porque al regenerar se elimina.
        public virtual DbSet<DatosGeneralesPedidoDTO> PedidoProgramarPedidoDTO { get; set; }
        public virtual DbSet<ConsultaHojalataDTO> ConsultaHojalataDTO { get; set; }
        public virtual DbSet<PedidoProce**doLiteDTO> PedidoProce**doLiteDTO { get; set; }
        public virtual DbSet<EstadoCodigosAplicacionDTO> EstadoPedidosDTO { get; set; }
        public virtual DbSet<DatosGeneralesPedidoFotomecanicaLitoDTO> DatosGeneralesPedidoLITO { get; set; }
        public virtual DbSet<PedidoProgramacionEnPantallaDTO> PedidoProgramacionEnPantalla { get; set; } // 24/06/25 JAVI

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
#warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see http://go.microsoft.com/fwlink/?LinkId=723263.
                optionsBuilder.UseSqlServer("Data Source=QPLANT1;Initial Catalog=ProgramadorLital**;Persist Security Info=True;User ID=**;Password=**;Encrypt=False");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<ApqLotesnodrizas>(entity =>
            {
                entity
                    .HasNoKey()
                    .ToView("APQ_LOTESNODRIZAS");

                entity.Property(e => e.Fecha)
                    .HasColumnType("datetime")
                    .HasColumnName("FECHA");
                entity.Property(e => e.Hora)
                    .HasColumnType("datetime")
                    .HasColumnName("HORA");
                entity.Property(e => e.Idproducto).HasColumnName("IDPRODUCTO");
                entity.Property(e => e.Lote)
                    .HasMaxLength(255)
                    .HasColumnName("LOTE");
                entity.Property(e => e.Nodriza).HasColumnName("NODRIZA");
                entity.Property(e => e.Operario)
                    .HasMaxLength(255)
                    .HasColumnName("OPERARIO");
                entity.Property(e => e.Ubicación)
                    .HasMaxLength(255)
                    .HasColumnName("ubicación");
            });

            modelBuilder.Entity<AuditoriaActualizacionDatosPedido>(entity =>
            {
                entity.Property(e => e.CampoModificado).HasMaxLength(100);
            });

            modelBuilder.Entity<BlockedMachine>(entity =>
            {
                entity.Property(e => e.LockEnd).HasColumnType("datetime");

                entity.Property(e => e.LockStart).HasColumnType("datetime");

                entity.HasOne(d => d.Connection)
                    .WithMany(p => p.BlockedMachine)
                    .HasForeignKey(d => d.ConnectionId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_BlockedMachines_Connections");

                entity.HasOne(d => d.Machine)
                    .WithMany(p => p.BlockedMachine)
                    .HasForeignKey(d => d.MachineId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_BlockedMachines_Machines");
            });

            modelBuilder.Entity<Clientes>(entity =>
            {
                entity.Property(e => e.Direccion).HasMaxLength(255);

                entity.Property(e => e.Email).HasMaxLength(255);

                entity.Property(e => e.EmailCargas).HasMaxLength(255);

                entity.Property(e => e.EmailQ).HasMaxLength(255);

                entity.Property(e => e.NombreCliente)
                    .IsRequired()
                    .HasMaxLength(255);

                entity.Property(e => e.NombreContacto).HasMaxLength(255);

                entity.Property(e => e.NumeroFax).HasMaxLength(50);

                entity.Property(e => e.NumeroTelefono).HasMaxLength(50);

                entity.Property(e => e.ObsEmbalaje).HasMaxLength(255);

                entity.Property(e => e.PesoMxpaquete).HasColumnName("PesoMXPaquete");

                entity.Property(e => e.RutaPlanos).HasMaxLength(255);
            });

            modelBuilder.Entity<CodApliAll>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("CodApli_All");

                entity.Property(e => e.Codbarniz).HasColumnName("CODBARNIZ");

                entity.Property(e => e.Codbaz).HasColumnName("CODBAZ");

                entity.Property(e => e.Grmbaz).HasColumnName("GRMBAZ");

                entity.Property(e => e.Grmbazmin).HasColumnName("GRMBAZMIN");

                entity.Property(e => e.Nombaz).HasColumnName("NOMBAZ");

                entity.Property(e => e.Nombreprogramacion).HasColumnName("NOMBREPROGRAMACION");

                entity.Property(e => e.Txtbaz).HasColumnName("TXTBAZ");
            });

            modelBuilder.Entity<Codapli>(entity =>
            {
                entity.HasKey(e => e.IdExtra);

                entity.ToTable("codapli");

                entity.HasIndex(e => e.Codbaz, "IX_codapli")
                    .IsUnique();

                entity.Property(e => e.Codbarniz).HasColumnName("CODBARNIZ");

                entity.Property(e => e.Codbaz).HasColumnName("CODBAZ");

                entity.Property(e => e.Grmbaz).HasColumnName("GRMBAZ");

                entity.Property(e => e.Grmbazmin).HasColumnName("GRMBAZMIN");

                entity.Property(e => e.Nombaz).HasColumnName("NOMBAZ");

                entity.Property(e => e.Nombreprogramacion).HasColumnName("NOMBREPROGRAMACION");

                entity.Property(e => e.Txtbaz).HasColumnName("TXTBAZ");
            });

            modelBuilder.Entity<CodapliLito>(entity =>
            {
                entity.HasKey(e => e.IdExtra);

                entity.ToTable("codapli_LITO");

                entity.Property(e => e.Codbarniz).HasColumnName("CODBARNIZ");

                entity.Property(e => e.Codbaz).HasColumnName("CODBAZ");

                entity.Property(e => e.Grmbaz).HasColumnName("GRMBAZ");

                entity.Property(e => e.Grmbazmin).HasColumnName("GRMBAZMIN");

                entity.Property(e => e.Nombaz).HasColumnName("NOMBAZ");

                entity.Property(e => e.Nombreprogramacion).HasColumnName("NOMBREPROGRAMACION");

                entity.Property(e => e.Txtbaz).HasColumnName("TXTBAZ");
            });

            modelBuilder.Entity<Codtintas>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("codtintas");

                entity.Property(e => e.Codtin).HasColumnName("CODTIN");

                entity.Property(e => e.Emptin)
                    .HasMaxLength(1)
                    .HasColumnName("EMPTIN");

                entity.Property(e => e.Nomtin)
                    .HasMaxLength(40)
                    .HasColumnName("NOMTIN");
            });

            modelBuilder.Entity<CompruebaExistenciasBarnices>(entity =>
            {
                entity.HasNoKey();

                entity.Property(e => e.FechaEntrega).HasColumnType("date");
            });

            modelBuilder.Entity<ConnectionLog>(entity =>
            {
                entity.Property(e => e.DateLastSeen).HasColumnType("datetime");

                entity.Property(e => e.DateLogin).HasColumnType("datetime");

                entity.Property(e => e.DateOut).HasColumnType("datetime");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.ConnectionLog)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Connections_Users");
            });

            modelBuilder.Entity<ControlDatosActualizables>(entity =>
            {
                entity.Property(e => e.FechaCierrePedido).HasColumnType("date");

                entity.Property(e => e.FechaEntrega).HasColumnType("date");
            });

            modelBuilder.Entity<DboPedproceso>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("dbo_pedproceso");

                entity.Property(e => e.Barniz)
                    .HasMaxLength(250)
                    .HasColumnName("barniz");

                entity.Property(e => e.CalculatedId)
                    .HasMaxLength(250)
                    .HasColumnName("calculatedID");

                entity.Property(e => e.Cantidad).HasColumnName("cantidad");

                entity.Property(e => e.Impdia)
                    .HasMaxLength(250)
                    .HasColumnName("impdia");

                entity.Property(e => e.Lugar)
                    .HasMaxLength(10)
                    .HasColumnName("lugar");

                entity.Property(e => e.Pedido).HasColumnName("pedido");

                entity.Property(e => e.PedprocesoCopiaId)
                    .ValueGeneratedOnAdd()
                    .HasColumnName("pedprocesoCopiaID");

                entity.Property(e => e.Posicion)
                    .HasMaxLength(250)
                    .HasColumnName("posicion");

                entity.Property(e => e.Proceso)
                    .HasMaxLength(250)
                    .HasColumnName("proceso");

                entity.Property(e => e.Txtbarniz)
                    .HasMaxLength(250)
                    .HasColumnName("txtbarniz");
            });

            modelBuilder.Entity<DestinatarioProgramaciones>(entity =>
            {
                entity.Property(e => e.Email)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.UltimoEnvio).HasColumnType("datetime");

                entity.HasOne(d => d.IdGrupoNotificacionesNavigation)
                    .WithMany(p => p.DestinatarioProgramaciones)
                    .HasForeignKey(d => d.IdGrupoNotificaciones)
                    .HasConstraintName("FK_DestinatarioProgramaciones_GrupoNotificaciones");
            });

            modelBuilder.Entity<DeviceCode>(entity =>
            {
                entity.HasKey(e => e.UserCode)
                    .HasName("PK_DeviceCodes");

                entity.HasIndex(e => e.DeviceCode1, "IX_DeviceCodes_DeviceCode")
                    .IsUnique();

                entity.HasIndex(e => e.Expiration, "IX_DeviceCodes_Expiration");

                entity.Property(e => e.UserCode).HasMaxLength(200);

                entity.Property(e => e.ClientId)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.Data).IsRequired();

                entity.Property(e => e.DeviceCode1)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasColumnName("DeviceCode");

                entity.Property(e => e.SubjectId).HasMaxLength(200);
            });

            modelBuilder.Entity<Entregas>(entity =>
            {
                entity.Property(e => e.DatosCliente).IsRequired();

                entity.Property(e => e.Estado).IsRequired();

                entity.Property(e => e.FechaEntrega).HasColumnType("datetime");

                entity.Property(e => e.Formato).HasColumnType("decimal(5, 1)");

                entity.Property(e => e.Motivos).IsRequired();

                entity.Property(e => e.Pases).HasColumnType("decimal(15, 1)");

                entity.Property(e => e.Supedido).IsRequired();
            });

            modelBuilder.Entity<Formato>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("formato");

                entity.Property(e => e.Cliente).HasColumnName("cliente");

                entity.Property(e => e.De**rrollo).HasColumnName("de**rrollo");

                entity.Property(e => e.Descri)
                    .HasMaxLength(25)
                    .HasColumnName("descri");

                entity.Property(e => e.Diametro).HasColumnName("diametro");

                entity.Property(e => e.Id)
                    .HasMaxLength(15)
                    .HasColumnName("id");

                entity.Property(e => e.Resdcha).HasColumnName("resdcha");

                entity.Property(e => e.Resizda).HasColumnName("resizda");

                entity.Property(e => e.Tipo)
                    .HasMaxLength(25)
                    .HasColumnName("tipo");
            });

            modelBuilder.Entity<GrupoNotificaciones>(entity =>
            {
                entity.Property(e => e.Descripcion)
                    .IsRequired()
                    .HasMaxLength(150);
            });

            modelBuilder.Entity<ProgramacionesPantalla>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.HasIndex(e => e.IdProgramacion).IsUnique();

                entity.Property(e => e.FechaCreacion).HasDefaultValueSql("(getdate())");
                entity.Property(e => e.FechaUltimaModificacion).HasDefaultValueSql("(getdate())");
                entity.Property(e => e.Rodillo).HasMaxLength(50);

                entity.HasOne(d => d.IdEstadoNavigation).WithMany(p => p.ProgramacionesPantalla)
                    .HasForeignKey(d => d.IdEstado)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ProgramacionesPantalla_Estado");

                entity.HasOne(d => d.IdProgramacionNavigation).WithOne(p => p.ProgramacionesPantalla)
                    .HasForeignKey<ProgramacionesPantalla>(d => d.IdProgramacion)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ProgramacionesPantalla_Programacion");
            });

            modelBuilder.Entity<ProgramacionesPantallaEstadosPedido>(entity =>
            {
                entity.HasKey(e => e.IdEstado);

                entity.ToTable("ProgramacionesPantalla_EstadosPedido");

                entity.HasIndex(e => e.NombreEstado).IsUnique();

                entity.Property(e => e.Activo).HasDefaultValue(true);
                entity.Property(e => e.Descripcion).HasMaxLength(200);
                entity.Property(e => e.FechaCreacion).HasDefaultValueSql("(getdate())");
                entity.Property(e => e.NombreEstado)
                    .IsRequired()
                    .HasMaxLength(50);
            });

            modelBuilder.Entity<ProgramacionesPantallaLog>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.ToTable("ProgramacionesPantalla_Log");

                entity.Property(e => e.Fecha).HasDefaultValueSql("(getdate())");
                entity.Property(e => e.IdUsuario)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.HasOne(d => d.IdProgramacionNavigation).WithMany(p => p.ProgramacionesPantallaLog)
                    .HasForeignKey(d => d.IdProgramacion)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ProgramacionesPantalla_Log_Programacion");

                entity.HasOne(d => d.IdLineaNavigation).WithMany(p => p.ProgramacionesPantallaLog)
                    .HasForeignKey(d => d.IdLinea)
                    .OnDelete(DeleteBehavior.SetNull)
                    .HasConstraintName("FK_ProgramacionesPantalla_Log_Maquinas");
            });

            modelBuilder.Entity<HojasTrabajoG21>(entity =>
            {
                entity.HasKey(e => e.IdExtra);

                entity.Property(e => e.IdExtra).ValueGeneratedNever();

                entity.Property(e => e.Cantidad).HasColumnName("CANTIDAD");

                entity.Property(e => e.Ccoste)
                    .HasMaxLength(4)
                    .HasColumnName("CCOSTE");

                entity.Property(e => e.Dato)
                    .HasMaxLength(15)
                    .HasColumnName("dato");

                entity.Property(e => e.Empleado)
                    .HasMaxLength(6)
                    .HasColumnName("EMPLEADO");

                entity.Property(e => e.Fechaini)
                    .HasColumnType("datetime")
                    .HasColumnName("FECHAINI");

                entity.Property(e => e.Grupoinfo)
                    .HasMaxLength(4)
                    .HasColumnName("GRUPOINFO");

                entity.Property(e => e.Horaini)
                    .HasMaxLength(5)
                    .HasColumnName("HORAINI");

                entity.Property(e => e.Minutos).HasColumnName("MINUTOS");

                entity.Property(e => e.Nombrecen)
                    .HasMaxLength(20)
                    .HasColumnName("NOMBRECEN");

                entity.Property(e => e.Nombreemp)
                    .HasMaxLength(20)
                    .HasColumnName("NOMBREEMP");

                entity.Property(e => e.Nombreope)
                    .HasMaxLength(20)
                    .HasColumnName("NOMBREOPE");

                entity.Property(e => e.Operacion)
                    .HasMaxLength(2)
                    .HasColumnName("OPERACION");

                entity.Property(e => e.Orden).HasColumnName("orden");

                entity.Property(e => e.Posicion).HasColumnName("posicion");

                entity.Property(e => e.Registro)
                    .HasMaxLength(11)
                    .HasColumnName("REGISTRO");
            });

            modelBuilder.Entity<LockedOrder>(entity =>
            {
                entity.HasIndex(e => e.OrderId, "IX_LockedOrder");

                entity.Property(e => e.Date).HasColumnType("datetime");

                entity.Property(e => e.Evento)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.User)
                    .IsRequired()
                    .HasMaxLength(150);
            });

            modelBuilder.Entity<Machine>(entity =>
            {
                entity.Property(e => e.ExternalId)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(50);
            });

            modelBuilder.Entity<Maquinas>(entity =>
            {
                entity.HasKey(e => e.Idmaquina);

                entity.Property(e => e.Idmaquina).ValueGeneratedNever();

                entity.Property(e => e.Abreviatura).HasMaxLength(50);

                entity.Property(e => e.IdmaquinaG21).HasMaxLength(50);

                entity.Property(e => e.Nombremaquina).HasMaxLength(50);

                entity.Property(e => e.PosicionDesde).HasColumnName("Posicion_desde");

                entity.Property(e => e.PosicionHasta).HasColumnName("Posicion_hasta");

                entity.Property(e => e.ProductosXsistema)
                    .HasMaxLength(255)
                    .HasColumnName("ProductosXSistema");

                entity.Property(e => e.PuestoTrabajoSap)
                    .HasMaxLength(50)
                    .HasColumnName("PuestoTrabajoSAP");

                entity.Property(e => e.TipoMaquina).HasMaxLength(50);
            });

            modelBuilder.Entity<Matcli>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("matcli");

                entity.Property(e => e.Almtc).HasColumnName("almtc");

                entity.Property(e => e.Ancmtc).HasColumnName("ancmtc");

                entity.Property(e => e.Calmtc).HasColumnName("calmtc");

                entity.Property(e => e.Climtc).HasColumnName("climtc");

                entity.Property(e => e.Empmtc)
                    .HasMaxLength(1)
                    .HasColumnName("empmtc");

                entity.Property(e => e.Espmtc).HasColumnName("espmtc");

                entity.Property(e => e.Larmtc).HasColumnName("larmtc");

                entity.Property(e => e.Promtc)
                    .HasMaxLength(100)
                    .HasColumnName("promtc");

                entity.Property(e => e.Refmtc)
                    .HasMaxLength(76)
                    .HasColumnName("refmtc");

                entity.Property(e => e.Resmtc).HasColumnName("resmtc");

                entity.Property(e => e.Stimtc).HasColumnName("stimtc");

                entity.Property(e => e.Stomtc).HasColumnName("stomtc");

                entity.Property(e => e.Tmamtc).HasColumnName("tmamtc");

                entity.Property(e => e.Tprmtc).HasColumnName("tprmtc");
            });

            modelBuilder.Entity<Matped>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("matped");

                entity.HasIndex(e => e.Nummpe, "IX_matped");

                entity.Property(e => e.Almacen).HasColumnName("almacen");

                entity.Property(e => e.Ancmpe).HasColumnName("ancmpe");

                entity.Property(e => e.Articulo)
                    .HasMaxLength(25)
                    .HasColumnName("articulo");

                entity.Property(e => e.Cararayada)
                    .HasMaxLength(100)
                    .HasColumnName("CARARAYADA");

                entity.Property(e => e.Climpe).HasColumnName("climpe");

                entity.Property(e => e.Codigo)
                    .HasMaxLength(7)
                    .HasColumnName("codigo");

                entity.Property(e => e.Corte)
                    .HasMaxLength(100)
                    .HasColumnName("CORTE");

                entity.Property(e => e.Dureza)
                    .HasMaxLength(100)
                    .HasColumnName("DUREZA");

                entity.Property(e => e.Espmpe).HasColumnName("espmpe");

                entity.Property(e => e.Grupo)
                    .HasMaxLength(14)
                    .HasColumnName("GRUPO");

                entity.Property(e => e.Hojmpe).HasColumnName("hojmpe");

                entity.Property(e => e.Larmpe).HasColumnName("larmpe");

                entity.Property(e => e.Nombre)
                    .HasMaxLength(30)
                    .HasColumnName("NOMBRE");

                entity.Property(e => e.Nummpe).HasColumnName("nummpe");

                entity.Property(e => e.Pedcliente)
                    .HasMaxLength(100)
                    .HasColumnName("pedcliente");

                entity.Property(e => e.Procedencia)
                    .HasMaxLength(100)
                    .HasColumnName("procedencia");

                entity.Property(e => e.Procesos)
                    .HasMaxLength(100)
                    .HasColumnName("PROCESOS");

                entity.Property(e => e.Prompe)
                    .HasMaxLength(25)
                    .HasColumnName("prompe");

                entity.Property(e => e.Recubrimiento)
                    .HasMaxLength(100)
                    .HasColumnName("RECUBRIMIENTO");

                entity.Property(e => e.Salmpe).HasColumnName("**lmpe");

                entity.Property(e => e.Siderurgia)
                    .HasMaxLength(100)
                    .HasColumnName("siderurgia");

                entity.Property(e => e.Sitmpe)
                    .HasMaxLength(25)
                    .HasColumnName("sitmpe");

                entity.Property(e => e.Tipo).HasMaxLength(24);

                entity.Property(e => e.TipoRigen)
                    .HasMaxLength(7)
                    .HasColumnName("TipoRIGEN");

                entity.Property(e => e.Tmampe).HasColumnName("tmampe");

                entity.Property(e => e.Tprmpe).HasColumnName("tprmpe");

                entity.Property(e => e.Tprmpe2)
                    .HasMaxLength(6)
                    .HasColumnName("tprmpe2");
            });

            modelBuilder.Entity<PedidoLitoProgramadoEnviadoImprimir>(entity =>
            {
                entity.Property(e => e.Ft01ext)
                    .HasMaxLength(50)
                    .HasColumnName("FT01Ext");

                entity.Property(e => e.Ft02ext)
                    .HasMaxLength(50)
                    .HasColumnName("FT02Ext");

                entity.Property(e => e.Ft03ext)
                    .HasMaxLength(50)
                    .HasColumnName("FT03Ext");

                entity.Property(e => e.Ft04ext)
                    .HasMaxLength(50)
                    .HasColumnName("FT04Ext");

                entity.Property(e => e.Ft05ext)
                    .HasMaxLength(50)
                    .HasColumnName("FT05Ext");

                entity.Property(e => e.Ft06ext)
                    .HasMaxLength(50)
                    .HasColumnName("FT06Ext");

                entity.Property(e => e.Ft07ext)
                    .HasMaxLength(50)
                    .HasColumnName("FT07Ext");

                entity.Property(e => e.Ft08ext)
                    .HasMaxLength(50)
                    .HasColumnName("FT08Ext");

                entity.Property(e => e.Ft09ext)
                    .HasMaxLength(50)
                    .HasColumnName("FT09Ext");

                entity.Property(e => e.Ft10ext)
                    .HasMaxLength(50)
                    .HasColumnName("FT10Ext");

                entity.Property(e => e.Ft11ext)
                    .HasMaxLength(50)
                    .HasColumnName("FT11Ext");

                entity.Property(e => e.HoraComienzoEstimada).HasColumnType("datetime");

                entity.Property(e => e.HoraFinEstimada).HasColumnType("datetime");

                entity.Property(e => e.RequeridoEnFecha).HasColumnType("datetime");

                entity.Property(e => e.T01ext)
                    .HasMaxLength(50)
                    .HasColumnName("T01Ext");

                entity.Property(e => e.T01int)
                    .HasMaxLength(50)
                    .HasColumnName("T01Int");

                entity.Property(e => e.T02ext)
                    .HasMaxLength(50)
                    .HasColumnName("T02Ext");

                entity.Property(e => e.T02int)
                    .HasMaxLength(50)
                    .HasColumnName("T02Int");

                entity.Property(e => e.T03ext)
                    .HasMaxLength(50)
                    .HasColumnName("T03Ext");

                entity.Property(e => e.T03int)
                    .HasMaxLength(50)
                    .HasColumnName("T03Int");

                entity.Property(e => e.T04ext)
                    .HasMaxLength(50)
                    .HasColumnName("T04Ext");

                entity.Property(e => e.T04int)
                    .HasMaxLength(50)
                    .HasColumnName("T04Int");

                entity.Property(e => e.T05ext)
                    .HasMaxLength(50)
                    .HasColumnName("T05Ext");

                entity.Property(e => e.T06ext)
                    .HasMaxLength(50)
                    .HasColumnName("T06Ext");

                entity.Property(e => e.T07ext)
                    .HasMaxLength(50)
                    .HasColumnName("T07Ext");

                entity.Property(e => e.T08ext)
                    .HasMaxLength(50)
                    .HasColumnName("T08Ext");

                entity.Property(e => e.T09ext)
                    .HasMaxLength(50)
                    .HasColumnName("T09Ext");

                entity.Property(e => e.T10ext)
                    .HasMaxLength(50)
                    .HasColumnName("T10Ext");

                entity.Property(e => e.T11ext)
                    .HasMaxLength(50)
                    .HasColumnName("T11Ext");
            });

            modelBuilder.Entity<PedidoProce**do>(entity =>
            {
                entity.Property(e => e.ActualizacionFechaRequerida).HasColumnType("datetime");

                entity.Property(e => e.AlturaCuerpos).HasColumnName("Altura_cuerpos");

                entity.Property(e => e.AnchoHjlta).HasColumnName("Ancho_hjlta");

                entity.Property(e => e.C01ped).HasColumnName("c01ped");

                entity.Property(e => e.C02ped).HasColumnName("c02ped");

                entity.Property(e => e.C03ped).HasColumnName("c03ped");

                entity.Property(e => e.C04ped).HasColumnName("c04ped");

                entity.Property(e => e.C05ped).HasColumnName("c05ped");

                entity.Property(e => e.C06ped).HasColumnName("c06ped");

                entity.Property(e => e.C07ped).HasColumnName("c07ped");

                entity.Property(e => e.C08ped).HasColumnName("c08ped");

                entity.Property(e => e.C09ped).HasColumnName("c09ped");

                entity.Property(e => e.Cd1ped).HasColumnName("cd1ped");

                entity.Property(e => e.Cd2ped).HasColumnName("cd2ped");

                entity.Property(e => e.Cd3ped).HasColumnName("cd3ped");

                entity.Property(e => e.Cd4ped).HasColumnName("cd4ped");

                entity.Property(e => e.ClaseSustrato).HasMaxLength(255);

                entity.Property(e => e.Co10ped).HasColumnName("co10ped");

                entity.Property(e => e.Co11ped).HasColumnName("co11ped");

                entity.Property(e => e.Corte2).HasMaxLength(255);

                entity.Property(e => e.Diferencial).HasMaxLength(255);

                entity.Property(e => e.Embuticion).HasMaxLength(1);

                entity.Property(e => e.EspesorHjlta).HasColumnName("Espesor_hjlta");

                entity.Property(e => e.Estado).HasMaxLength(50);

                entity.Property(e => e.EstadoTintas).HasMaxLength(150);

                entity.Property(e => e.FechaEntregaSolicitada).HasColumnType("datetime");

                entity.Property(e => e.FechaEnvioTriptico).HasColumnType("datetime");

                entity.Property(e => e.FechaFin).HasColumnType("datetime");

                entity.Property(e => e.FechaOktriptico)
                    .HasColumnType("datetime")
                    .HasColumnName("FechaOKTriptico");

                entity.Property(e => e.FechaPedido).HasColumnType("datetime");

                entity.Property(e => e.FechaPlanchas).HasColumnType("datetime");

                entity.Property(e => e.FechaUltRevision).HasColumnType("datetime");

                entity.Property(e => e.HojalataYaSacada).HasColumnName("Hojalata_YA_**cada");

                entity.Property(e => e.Hojascd1ped).HasColumnName("hojascd1ped");

                entity.Property(e => e.Hojascd2ped).HasColumnName("hojascd2ped");

                entity.Property(e => e.Hojascd3ped).HasColumnName("hojascd3ped");

                entity.Property(e => e.Hojascd4ped).HasColumnName("hojascd4ped");

                entity.Property(e => e.Hojasco10ped).HasColumnName("hojasco10ped");

                entity.Property(e => e.Hojasco11ped).HasColumnName("hojasco11ped");

                entity.Property(e => e.Hojasco1ped).HasColumnName("hojasco1ped");

                entity.Property(e => e.Hojasco2ped).HasColumnName("hojasco2ped");

                entity.Property(e => e.Hojasco3ped).HasColumnName("hojasco3ped");

                entity.Property(e => e.Hojasco4ped).HasColumnName("hojasco4ped");

                entity.Property(e => e.Hojasco5ped).HasColumnName("hojasco5ped");

                entity.Property(e => e.Hojasco6ped).HasColumnName("hojasco6ped");

                entity.Property(e => e.Hojasco7ped).HasColumnName("hojasco7ped");

                entity.Property(e => e.Hojasco8ped).HasColumnName("hojasco8ped");

                entity.Property(e => e.Hojasco9ped).HasColumnName("hojasco9ped");

                entity.Property(e => e.LargoHjlta).HasColumnName("Largo_hjlta");

                entity.Property(e => e.Motivos).HasMaxLength(200);

                entity.Property(e => e.ObsACliente).HasColumnName("Obs_a_cliente");

                entity.Property(e => e.ObsTriptico).HasMaxLength(50);

                entity.Property(e => e.Ob**rticulo).HasMaxLength(100);

                entity.Property(e => e.Obsflejado).HasMaxLength(150);

                entity.Property(e => e.Obsrayas).HasMaxLength(150);

                entity.Property(e => e.Plano).HasMaxLength(120);

                entity.Property(e => e.PrecioHoja).HasColumnType("money");

                entity.Property(e => e.RequeridoEnFecha).HasColumnType("datetime");

                entity.Property(e => e.Sincarpeta).HasColumnName("SINCARPETA");

                entity.Property(e => e.Supedido).HasMaxLength(150);

                entity.Property(e => e.TipoBarnizado).HasMaxLength(1);

                entity.Property(e => e.TipoElemento).HasMaxLength(150);

                entity.Property(e => e.TipoEnvase).HasMaxLength(50);

                entity.Property(e => e.TipoHjlta).HasColumnName("Tipo_hjlta");

                entity.Property(e => e.TipoPedido).HasMaxLength(1);

                entity.Property(e => e.UltimaModificacion).HasColumnType("datetime");

                entity.Property(e => e.Urgente).HasColumnName("URGENTE");

                entity.Property(e => e.Wo)
                    .HasMaxLength(150)
                    .HasColumnName("WO");
            });

            modelBuilder.Entity<PedidoProgramadoEnviadoImprimir>(entity =>
            {
                entity.Property(e => e.HoraComienzoEstimada).HasColumnType("datetime");

                entity.Property(e => e.HoraFinEstimada).HasColumnType("datetime");

                entity.Property(e => e.RequeridoEnFecha).HasColumnType("datetime");
            });

            modelBuilder.Entity<PedidoProgramadoEnviadoImprimirCombinado>(entity =>
            {
                entity.Property(e => e.Ft01ext)
                    .HasMaxLength(50)
                    .HasColumnName("FT01Ext");

                entity.Property(e => e.Ft02ext)
                    .HasMaxLength(50)
                    .HasColumnName("FT02Ext");

                entity.Property(e => e.Ft03ext)
                    .HasMaxLength(50)
                    .HasColumnName("FT03Ext");

                entity.Property(e => e.Ft04ext)
                    .HasMaxLength(50)
                    .HasColumnName("FT04Ext");

                entity.Property(e => e.Ft05ext)
                    .HasMaxLength(50)
                    .HasColumnName("FT05Ext");

                entity.Property(e => e.Ft06ext)
                    .HasMaxLength(50)
                    .HasColumnName("FT06Ext");

                entity.Property(e => e.Ft07ext)
                    .HasMaxLength(50)
                    .HasColumnName("FT07Ext");

                entity.Property(e => e.Ft08ext)
                    .HasMaxLength(50)
                    .HasColumnName("FT08Ext");

                entity.Property(e => e.Ft09ext)
                    .HasMaxLength(50)
                    .HasColumnName("FT09Ext");

                entity.Property(e => e.Ft10ext)
                    .HasMaxLength(50)
                    .HasColumnName("FT10Ext");

                entity.Property(e => e.Ft11ext)
                    .HasMaxLength(50)
                    .HasColumnName("FT11Ext");

                entity.Property(e => e.HoraComienzoEstimada).HasColumnType("datetime");

                entity.Property(e => e.HoraFinEstimada).HasColumnType("datetime");

                entity.Property(e => e.RequeridoEnFecha).HasColumnType("datetime");

                entity.Property(e => e.T01ext)
                    .HasMaxLength(50)
                    .HasColumnName("T01Ext");

                entity.Property(e => e.T01int)
                    .HasMaxLength(50)
                    .HasColumnName("T01Int");

                entity.Property(e => e.T02ext)
                    .HasMaxLength(50)
                    .HasColumnName("T02Ext");

                entity.Property(e => e.T02int)
                    .HasMaxLength(50)
                    .HasColumnName("T02Int");

                entity.Property(e => e.T03ext)
                    .HasMaxLength(50)
                    .HasColumnName("T03Ext");

                entity.Property(e => e.T03int)
                    .HasMaxLength(50)
                    .HasColumnName("T03Int");

                entity.Property(e => e.T04ext)
                    .HasMaxLength(50)
                    .HasColumnName("T04Ext");

                entity.Property(e => e.T04int)
                    .HasMaxLength(50)
                    .HasColumnName("T04Int");

                entity.Property(e => e.T05ext)
                    .HasMaxLength(50)
                    .HasColumnName("T05Ext");

                entity.Property(e => e.T06ext)
                    .HasMaxLength(50)
                    .HasColumnName("T06Ext");

                entity.Property(e => e.T07ext)
                    .HasMaxLength(50)
                    .HasColumnName("T07Ext");

                entity.Property(e => e.T08ext)
                    .HasMaxLength(50)
                    .HasColumnName("T08Ext");

                entity.Property(e => e.T09ext)
                    .HasMaxLength(50)
                    .HasColumnName("T09Ext");

                entity.Property(e => e.T10ext)
                    .HasMaxLength(50)
                    .HasColumnName("T10Ext");

                entity.Property(e => e.T11ext)
                    .HasMaxLength(50)
                    .HasColumnName("T11Ext");

                entity.Property(e => e.TipoPedido).HasMaxLength(50);
            });

            modelBuilder.Entity<Plano>(entity =>
            {
                entity.HasKey(e => e.Idplano);

                entity.Property(e => e.CodigoPlano)
                    .HasMaxLength(50)
                    .HasColumnName("Codigo_Plano");

                entity.Property(e => e.Escuadra).HasMaxLength(1);

                entity.Property(e => e.FormaBarnizado)
                    .HasMaxLength(1)
                    .HasColumnName("Forma_Barnizado");

                entity.Property(e => e.FormaLitografía)
                    .HasMaxLength(50)
                    .HasColumnName("Forma_Litografía");
            });

            modelBuilder.Entity<TablaAvisos>(entity =>
            {
                entity.HasKey(e => e.Idaviso);

                entity.Property(e => e.Fecha).HasColumnType("datetime");
            });

            modelBuilder.Entity<TablaCabeceraTratamientoSap>(entity =>
            {
                entity.ToTable("TablaCabeceraTratamientoSAP");

                entity.Property(e => e.Id).HasColumnName("ID");

                entity.Property(e => e.Activo).HasColumnName("ACTIVO");

                entity.Property(e => e.Descripcion)
                    .HasMaxLength(255)
                    .HasColumnName("DESCRIPCION");

                entity.Property(e => e.DescripcionCorta).HasMaxLength(255);

                entity.Property(e => e.Fechaalta)
                    .HasColumnType("datetime")
                    .HasColumnName("FECHAALTA");

                entity.Property(e => e.Fechaobsoleto)
                    .HasColumnType("datetime")
                    .HasColumnName("FECHAOBSOLETO");

                entity.Property(e => e.Idtratamiento).HasColumnName("IDTRATAMIENTO");

                entity.Property(e => e.Tipotratamiento)
                    .HasMaxLength(1)
                    .HasColumnName("TIPOTRATAMIENTO");

                entity.Property(e => e.Version)
                    .HasMaxLength(2)
                    .HasColumnName("VERSION");
            });

            modelBuilder.Entity<TablaCfg>(entity =>
            {
                entity.ToTable("Tabla_CFG");

                entity.Property(e => e.Detalle).HasMaxLength(255);

                entity.Property(e => e.Iddato).HasMaxLength(255);
            });

            modelBuilder.Entity<TablaCodigosPedido>(entity =>
            {
                entity.HasKey(e => e.Idproceso)
                    .HasName("PK_Tabla_codigos_pedido_1");

                entity.ToTable("Tabla_codigos_pedido");

                entity.Property(e => e.EstadoTintas).HasMaxLength(50);

                entity.Property(e => e.FechaAsignacionImpresora).HasColumnType("datetime");

                entity.Property(e => e.Idstrpedproceso).HasMaxLength(255);

                entity.Property(e => e.ObservacionesAplicacion).HasMaxLength(100);

                entity.Property(e => e.Posicion).HasMaxLength(50);

                entity.Property(e => e.Sincarpeta).HasColumnName("SINCARPETA");
            });

            modelBuilder.Entity<TablaComentarios>(entity =>
            {
                entity.Property(e => e.Comentarios).HasMaxLength(255);

                entity.Property(e => e.RefAbuscar)
                    .HasMaxLength(255)
                    .HasColumnName("RefABuscar");

                entity.Property(e => e.TextoAdevolver)
                    .HasMaxLength(255)
                    .HasColumnName("TextoADevolver");

                entity.Property(e => e.Tipo).HasMaxLength(255);
            });

            modelBuilder.Entity<TablaDetalleTratamientoSap>(entity =>
            {
                entity.HasKey(e => e.Iddetalletratamiento);

                entity.ToTable("TablaDetalleTratamientoSAP");

                entity.Property(e => e.Iddetalletratamiento).HasColumnName("IDDETALLETRATAMIENTO");

                entity.Property(e => e.Caraaaplicar)
                    .HasMaxLength(255)
                    .HasColumnName("CARAAAPLICAR");

                entity.Property(e => e.Fase).HasColumnName("FASE");

                entity.Property(e => e.FechaAlta).HasColumnType("datetime");

                entity.Property(e => e.Idcodigoaplicacion)
                    .HasMaxLength(255)
                    .HasColumnName("IDCODIGOAPLICACION");

                entity.Property(e => e.Idtratamiento).HasColumnName("IDTRATAMIENTO");

                entity.Property(e => e.Maquinapordefecto)
                    .HasMaxLength(255)
                    .HasColumnName("MAQUINAPORDEFECTO");

                entity.Property(e => e.Temperaturasecado).HasColumnName("TEMPERATURASECADO");
            });

            modelBuilder.Entity<TablaFormatosHjltaSh>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("TablaFormatosHjltaSH");

                entity.Property(e => e.DenoMuestra).HasMaxLength(50);
            });

            modelBuilder.Entity<TablaProductos>(entity =>
            {
                entity.HasKey(e => e.Idproducto);

                entity.ToTable("Tabla_Productos");

                entity.Property(e => e.Idproducto).ValueGeneratedNever();

                entity.Property(e => e.CalleApq)
                    .HasMaxLength(10)
                    .HasColumnName("CalleAPQ");

                entity.Property(e => e.Denominacion).HasMaxLength(50);

                entity.Property(e => e.FechaEntrega).HasColumnType("datetime");

                entity.Property(e => e.Inflamable).HasMaxLength(50);

                entity.Property(e => e.NombreProducto).HasMaxLength(100);

                entity.Property(e => e.Observaciones).HasMaxLength(111);

                entity.Property(e => e.Precio).HasColumnType("money");

                entity.Property(e => e.PuntoEbullición).HasColumnName("Punto Ebullición");

                entity.Property(e => e.PuntoInflamacion).HasColumnName("Punto Inflamacion");

                entity.Property(e => e.StockMinimo).HasColumnName("Stock_Minimo");

                entity.Property(e => e.Stockminimo1).HasColumnName("Stockminimo");

                entity.Property(e => e.TipoProducto).HasMaxLength(50);

                entity.Property(e => e.Tratamientos).HasMaxLength(100);

                entity.Property(e => e.Tóxico).HasMaxLength(50);

                entity.Property(e => e.UltAvisoInsuficiente).HasColumnType("datetime");
            });

            modelBuilder.Entity<TablaProgramacion>(entity =>
            {
                entity.HasKey(e => e.Idprogramacion);

                entity.Property(e => e.CaraAaplicar)
                    .HasMaxLength(50)
                    .HasColumnName("CaraAAplicar");

                entity.Property(e => e.DatosPedido).HasMaxLength(250);

                entity.Property(e => e.DiaReal).HasColumnType("date");

                entity.Property(e => e.HojasAproce**r).HasColumnName("HojasAProce**r");

                entity.Property(e => e.HoraComienzoEstimada).HasColumnType("datetime");

                entity.Property(e => e.HoraFinEstimada).HasColumnType("datetime");

                entity.Property(e => e.ObsCalidad).HasMaxLength(255);

                entity.Property(e => e.Posicionaplicacionanterior).HasMaxLength(255);

                entity.Property(e => e.Posicionaplicacionposterior).HasMaxLength(255);

                entity.Property(e => e.Producto).HasMaxLength(125);

                entity.Property(e => e.Programador).HasMaxLength(255);

                entity.Property(e => e.TipoLavada).HasMaxLength(50);

                entity.Property(e => e.TiposCambio).HasMaxLength(255);

                entity.HasOne(d => d.IdaplicacionNavigation)
                    .WithMany(p => p.TablaProgramacion)
                    .HasPrincipalKey(p => p.Codbaz)
                    .HasForeignKey(d => d.Idaplicacion)
                    .HasConstraintName("FK_TablaProgramacion_codapli");

                entity.HasOne(d => d.IdlineaNavigation)
                    .WithMany(p => p.TablaProgramacion)
                    .HasForeignKey(d => d.Idlinea)
                    .HasConstraintName("FK_TablaProgramacion_Maquinas");

                entity.HasOne(d => d.IdproductoNavigation)
                    .WithMany(p => p.TablaProgramacion)
                    .HasForeignKey(d => d.Idproducto)
                    .HasConstraintName("FK_TablaProgramacion_Tabla_Productos");
            });

            modelBuilder.Entity<TablaTratamientos>(entity =>
            {
                entity.HasKey(e => e.CodigoTratamiento);

                entity.Property(e => e.CodigoTratamiento).HasMaxLength(50);

                entity.Property(e => e.DescripcionTratamiento)
                    .IsRequired()
                    .HasMaxLength(250);
            });

            modelBuilder.Entity<TblLimpiezas>(entity =>
            {
                entity.HasKey(e => e.Idlavada);

                entity.Property(e => e.AidProducto).HasColumnName("AIdProducto");

                entity.Property(e => e.Aproductoxparatirar).HasMaxLength(50);

                entity.Property(e => e.DeProductoxparatirar).HasMaxLength(50);

                entity.Property(e => e.TipoLimpieza).HasMaxLength(50);
            });

            modelBuilder.Entity<TblTipoLimpieza>(entity =>
            {
                entity.HasNoKey();

                entity.Property(e => e.TipoLimpieza).HasMaxLength(255);
            });

            modelBuilder.Entity<TipoHjlta>(entity =>
            {
                entity.HasNoKey();

                entity.Property(e => e.Codtma).HasColumnName("codtma");

                entity.Property(e => e.Nomtma)
                    .HasMaxLength(255)
                    .HasColumnName("nomtma");

                entity.Property(e => e.Tfs).HasColumnName("TFS");
            });

            modelBuilder.Entity<UserLite>(entity =>
            {
                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(256);

                entity.Property(e => e.PasswordHash).IsRequired();
            });

            modelBuilder.Entity<ViewCodApliTablaProductos>(entity =>
            {
                entity.HasNoKey();

                entity.ToView("View_CodApli-TablaProductos");

                entity.Property(e => e.DenominacionCompleta).HasMaxLength(81);

                entity.Property(e => e.Observaciones).HasMaxLength(111);

                entity.Property(e => e.Precio).HasColumnType("money");
            });

            modelBuilder.Entity<VelocidadesMaquina>(entity =>
            {
                entity.Property(e => e.MaterialConsulta).HasMaxLength(10);
                entity.Property(e => e.ReservasConsulta)
                    .HasMaxLength(1)
                    .IsUnicode(false)
                    .IsFixedLength();
                entity.Property(e => e.TempConsulta).HasColumnType("decimal(5, 2)");
                entity.Property(e => e.TempTiradaMq1).HasColumnType("decimal(5, 2)");
                entity.Property(e => e.TempTiradaMq2).HasColumnType("decimal(5, 2)");
                entity.Property(e => e.TipoHojalataConsulta).HasMaxLength(50);
            });

            // COSAS OBLIGATORIAS
            modelBuilder.Entity<PedidoProgramacionEnPantallaDTO>(entity =>
            {
                entity.HasNoKey();
            });
            modelBuilder.Entity<DatosGeneralesPedidoDTO>(entity =>
            {
                entity.HasNoKey();
            });
            modelBuilder.Entity<EstadoCodigosAplicacionDTO>(entity =>
            {
                entity.HasNoKey();
            });
            modelBuilder.Entity<DatosGeneralesPedidoFotomecanicaLitoDTO>(entity =>
            {
                entity.HasNoKey();
            });
            modelBuilder.Entity<ConsultaHojalataDTO>(entity =>
            {
                entity.HasNoKey();
            });
            modelBuilder.Entity<PedidoProce**doLiteDTO>(entity =>
            {
                entity.HasNoKey();
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}