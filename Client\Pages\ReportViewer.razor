﻿@page "/reportviewer"
@using DevExpress.DocumentServices.ServiceModel.DataContracts

<DxWasmDocumentViewer Id="VisorReportes" ReportName="@ParamNombreReport"  Height="calc(100vh - 100px)" Width="calc(100vw - 100px)">
    <DxDocumentViewerExportSettings UseSameTab="false">
    </DxDocumentViewerExportSettings>
    <DxWasmDocumentViewerRequestOptions InvokeAction="DXXRDV">
    </DxWasmDocumentViewerRequestOptions> 
    <DxDocumentViewerCallbacks BeforeRender="ReportingViewerCustomization.onBeforeRender" />
                               @*CustomizeParameterEditors="ReportingViewerCustomization.onCustomizeParameterEditors"/>*@
</DxWasmDocumentViewer>

@code{

    [Parameter] public string ParamNombreReport { get; set; }
}