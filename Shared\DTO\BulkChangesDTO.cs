﻿using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProgramadorGeneralBLZ.Shared.DTO
{
    public class BulkChangesDTO
    {
        public List<int> Posiciones { get; set; }
        public MaquinaDTO CurrentMaquina {get; set;}
        public MaquinaDTO? MaquinaDto {get; set;}
        public CodigoAplicacionDTO? CodigoAplicacionDto { get; set; }
        public ProductoDTO? ProductoDto { get; set; }
        public float? MinCapa { get; set; }
        public float? MaxCapa { get; set; }
    }
}
