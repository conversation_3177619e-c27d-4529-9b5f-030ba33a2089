﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.DatoLita01;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Services.DatabaseManipulation;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Pedidos.Command;

public class GenerarDatosImpresionCombinadoCommand : IRequest<SingleResult<int>>
{
    public GenerarDatosImpresionCombinadoCommand(ImprimirPedidosDTO datosImpresion, bool esProgramacionPorPantalla, string nombreReporte)
    {
        DatosImpresion = datosImpresion;
        EsProgramacionPorPantalla = esProgramacionPorPantalla;
        NombreReporte = nombreReporte;
    }
    public ImprimirPedidosDTO DatosImpresion { get; set; }
    public bool EsProgramacionPorPantalla { get; }
    public string NombreReporte { get; }
}

public class GenerarDatosImpresionCombinadoCommandHandler : IRequestHandler<GenerarDatosImpresionCombinadoCommand, SingleResult<int>>
{
    private readonly ProgramadorLitalsaContext _programadorLitalsaContext;
    private readonly DatoLita01Context _datoLita01Context;
    private readonly IDataManipulationService _dataManipulationService;

    public GenerarDatosImpresionCombinadoCommandHandler(ProgramadorLitalsaContext programadorLitalsaContext, DatoLita01Context datoLita01Context, IDataManipulationService dataManipulationService)
    {
        _programadorLitalsaContext = programadorLitalsaContext;
        _datoLita01Context = datoLita01Context;
        _dataManipulationService = dataManipulationService;
    }

    public async Task<SingleResult<int>> Handle(GenerarDatosImpresionCombinadoCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<int>();
        try
        {
            await _programadorLitalsaContext.PedidoProgramadoEnviadoImprimirCombinado
                .Where(o => o.Idlinea == request.DatosImpresion.Maquina.Idmaquina
                            && o.EsTipoProgramacionPorPantalla == request.EsProgramacionPorPantalla)
                .ExecuteDeleteAsync(cancellationToken);
            //await _programadorLitalsaContext.SaveChangesAsync(cancellationToken);
            await _dataManipulationService.GenerarDatosImpresionBarnizado(request.DatosImpresion, request.EsProgramacionPorPantalla, request.NombreReporte, cancellationToken);
            await _dataManipulationService.GenerarDatosImpresionLitografia(request.DatosImpresion, request.EsProgramacionPorPantalla, request.NombreReporte, cancellationToken);
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: GenerarDatosImpresionCommand - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }
        return result;
    }

}