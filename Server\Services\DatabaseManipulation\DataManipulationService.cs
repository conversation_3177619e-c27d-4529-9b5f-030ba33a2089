﻿using System.Data;
using System.Diagnostics;
using System.Globalization;
using System.Runtime.Intrinsics.X86;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Nelibur.ObjectMapper;
using ProgramadorGeneralBLZ.Server.Data.DatoLita01;
using ProgramadorGeneralBLZ.Server.Data.LitalsaDataWarehouse;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.DatoLita01;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;
using static DevExpress.Xpo.Helpers.AssociatedCollectionCriteriaHelper;
using Matped = ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa.Matped;

namespace ProgramadorGeneralBLZ.Server.Services.DatabaseManipulation
{
    public class DataManipulationService : IDataManipulationService
    {
        private readonly ProgramadorLitalsaContext _programadorLitalsaContext;
        private readonly DatoLita01Context _datoLita01Context;
        private readonly LitalsaDataWarehouseContext _litalsaDataWarehouseContext;
        private readonly IServiceProvider _serviceProvider;

        public DataManipulationService(ProgramadorLitalsaContext programadorLitalsaContext, DatoLita01Context datoLita01Context,
            LitalsaDataWarehouseContext litalsaDataWarehouseContext, IServiceProvider serviceProvider)
        {
            _programadorLitalsaContext = programadorLitalsaContext;
            _datoLita01Context = datoLita01Context;
            _litalsaDataWarehouseContext = litalsaDataWarehouseContext;
            _serviceProvider = serviceProvider;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="idCliente"></param>
        /// <returns></returns>
        public string GetNombreCliente(int idCliente)
        {
            var datosCliente = _programadorLitalsaContext.Clientes.FirstOrDefault(o => o.CodigoCliente == idCliente);

            return datosCliente == null ? "DESCONOCIDO" : $"{datosCliente.CodigoCliente} {datosCliente.NombreCliente}";
        }
        //OPTIMIZADO
        public string GetTextoEstadoCodigosAplicacion(int idPedido, bool soloProcesos = false, bool descripcionCliente = false,
            List<Maquinas> maquinas = null, List<TablaProgramacion> programaciones = null)
        {
            var cadena = new StringBuilder();
            var strInterior = new StringBuilder();
            var strExterior = new StringBuilder();

            var param = new SqlParameter("@num_pedido", System.Data.SqlDbType.Int)
            {
                Value = idPedido
            };

            var codigosPedido = _programadorLitalsaContext.Set<EstadoCodigosAplicacionDTO>()
                .FromSqlRaw("[dbo].[SP_DevuelveEstadoCodigosAplicacion] @num_pedido", param).ToList();

            programaciones ??= _programadorLitalsaContext.TablaProgramacion
                .Where(o => o.Idpedido == idPedido)
                .ToList();
            maquinas ??= _programadorLitalsaContext.Maquinas.ToList();

            foreach (var cp in codigosPedido)
            {
                string abreviaturaMaquina = "";
                if (descripcionCliente)
                {
                    var datosAplicacion = DevuelveDatosAplicacion(cp.Idcodigoaplicacion.Value, "NombreAplicacionCliente");
                    if (cp.Posicion.StartsWith("i"))
                    {
                        strInterior.Append(strInterior.Length == 0 ? "Int:" : "+ ").Append(datosAplicacion).Append(" ");
                    }
                    else if (cp.Posicion.StartsWith("e"))
                    {
                        strExterior.Append(strExterior.Length == 0 ? "//Ext:" : "+ ").Append(datosAplicacion).Append(" ");
                    }
                }
                else
                {
                    if (cp.Posicion.StartsWith("e") && !cadena.ToString().Contains("//"))
                        cadena.Append("//");

                    var isTintas = cp.Idcodigoaplicacion <= 1000;
                    if (isTintas)
                    {
                        cadena.Append(cp.Idcodigoaplicacion);
                        if (!soloProcesos)
                        {
                            abreviaturaMaquina = maquinas.FirstOrDefault(o => o.Idmaquina == cp.LineaTintas)?.Abreviatura;
                            if (string.IsNullOrEmpty(cp.EstadoTintas) || cp.EstadoTintas == "0")
                            {
                                cadena.Append(":0 ");
                            }
                            else if (cp.EstadoTintas.StartsWith("Sin Ap"))
                            {
                                cadena.Append($":A{abreviaturaMaquina} ");
                            }
                            else if (cp.EstadoTintas.StartsWith("Program"))
                            {
                                var estadoLito = GetEstadoLitografia(idPedido);
                                cadena.Append($":{estadoLito}{abreviaturaMaquina} ");
                            }
                            else
                            {
                                cadena.Append(' ');
                            }
                        }
                    }
                    else
                    {
                        cadena.Append(cp.Idcodigoaplicacion);
                        if (!soloProcesos)
                        {
                            var programacion = programaciones.FirstOrDefault(o => o.CaraAaplicar == cp.Posicion && o.Idaplicacion == cp.Idcodigoaplicacion);
                            var hojasAux = cp.Hojasaplicadas;
                            if (programacion != null)
                            {
                                abreviaturaMaquina = maquinas.FirstOrDefault(o => o.Idmaquina == programacion.Idlinea)?.Abreviatura;
                            }

                            if (hojasAux < 0.05 * cp.HojasPedido.Value && programacion != null)
                            {
                                cadena.Append($":{abreviaturaMaquina} ");
                            }
                            else
                            {
                                var identificadorEstadoPedido = GetIdentificadorEstadoPedido(hojasAux.Value, cp.HojasPedido.Value);
                                cadena.Append($":{identificadorEstadoPedido} ");
                            }
                        }
                        else
                        {
                            cadena.Append(' ');
                        }
                    }
                }
            }
            return descripcionCliente ? $" {strInterior} {strExterior} " : cadena.ToString();
        }


        /// <summary>
        /// Devuelve el texto mágico que t0do el mundo entiende a pesar de ser un jeroglifico.
        /// Creo que es Function devuelve_estado_pedido2(num_pedido As Long, Optional SOLO_PROCESOS As Boolean, Optional descripcioncliente As Boolean, Optional ordenprocesos As Boolean, Optional cara_a_valorar As String) As String   'REFORMADO
        /// </summary>
        /// <param name="idPedido"></param>
        /// <param name="soloProcesos"></param>
        /// <param name="descripcionCliente"></param>
        /// <returns></returns>
        public string GetTextoEstadoCodigosAplicacion2(int idPedido, bool soloProcesos = false, bool descripcionCliente = false)
        {
            var cadena = string.Empty;
            var strInterior = string.Empty;
            var strExterior = string.Empty;

            var param = new SqlParameter()
            {
                ParameterName = "@num_pedido",
                SqlDbType = System.Data.SqlDbType.Int,
                Value = idPedido
            };

            var codigosPedido = _programadorLitalsaContext.Set<EstadoCodigosAplicacionDTO>()
                .FromSqlRaw("[dbo].[SP_DevuelveEstadoCodigosAplicacion] @num_pedido", param).ToList();


            //var codigosPedido2 =
            //    from tcp in _programadorLitalsaContext.TablaCodigosPedido
            //    join top in _programadorLitalsaContext.TablaOrdenPosicion on tcp.Posicion equals top.IdPosicion into ttop
            //    from t in ttop.DefaultIfEmpty()
            //    join pp in _programadorLitalsaContext.PedidoProcesado on tcp.Idpedido equals pp.IdPedido into ppp
            //    from p in ppp.DefaultIfEmpty()
            //    where tcp.Idpedido == idPedido
            //    orderby t.Orden
            //    select new
            //    {
            //        tcp.Fase,
            //        tcp.Idcodigoaplicacion,
            //        tcp.Hojasaplicadas,
            //        tcp.Posicion,
            //        tcp.LineaTintas,
            //        tcp.EstadoTintas,
            //        t.Orden,
            //        p.HojasPedido,
            //        tcp.Idpedido
            //    };

            foreach (var cp in codigosPedido)
            {
                var linea = 0;
                var abreviaturaMaquina = "";

                if (descripcionCliente)
                {
                    var datosAplicacion = DevuelveDatosAplicacion(cp.Idcodigoaplicacion.Value, "NombreAplicacionCliente");
                    if (cp.Posicion.StartsWith("i"))
                    {
                        strInterior += strInterior == "" ? " Int: " : "+ ";

                        strInterior += datosAplicacion + " ";
                    }

                    if (cp.Posicion.StartsWith("e"))
                    {
                        strExterior += strExterior == "" ? " // Ext: " : "+ ";

                        strExterior += datosAplicacion + " ";
                    }
                }
                else
                {
                    if (cp.Posicion.StartsWith("e") && !cadena.Contains("//"))
                        cadena += " // ";

                    if (cp.Idcodigoaplicacion > 1000)
                    {
                        cadena += cp.Idcodigoaplicacion;
                        if (!soloProcesos)
                        {
                            var hojasAux = cp.Hojasaplicadas;
                            //sino se ha tirado ninguna hoja y el pedido está programado
                            //Si está en la tabla es que está programado
                            var programacion = _programadorLitalsaContext.TablaProgramacion
                                .FirstOrDefault(o => o.Idpedido == idPedido && o.CaraAaplicar == cp.Posicion &&
                                                     o.Idaplicacion == cp.Idcodigoaplicacion);

                            if (programacion != null)
                            {
                                linea = programacion.Idlinea ?? 0;
                                if (linea != 0)
                                {
                                    abreviaturaMaquina = _programadorLitalsaContext.Maquinas.FirstOrDefault(o =>
                                        o.Idmaquina == programacion.Idlinea)?.Abreviatura;
                                }
                            }

                            if (hojasAux < 0.05 * cp.HojasPedido.Value && programacion != null)
                            {
                                //lo marcamos como P -- Y PORQUE NO HAY UNA P!!!??
                                cadena += $": {abreviaturaMaquina} ";
                            }
                            else
                            {
                                //sino evaluamos su estado en función del número de hojas procesadas
                                var identificadorEstadoPedido =
                                    GetIdentificadorEstadoPedido(hojasAux.Value, cp.HojasPedido.Value);
                                cadena += $": {identificadorEstadoPedido} ";
                            }

                        }
                        else
                        {
                            cadena += " ";
                        }
                    }
                    else
                    {
                        cadena += cp.Idcodigoaplicacion;
                        if (!soloProcesos)
                        {
                            //Se comprueba si tiene máquina asignada consultando el campo "EstadoTintas"...
                            if (string.IsNullOrEmpty(cp.EstadoTintas) || cp.EstadoTintas == "0")
                            {
                                cadena += ":0 ";
                            }
                            if (!string.IsNullOrEmpty(cp.EstadoTintas) && cp.EstadoTintas.StartsWith("Sin Ap"))
                            {
                                cadena += $":A {abreviaturaMaquina} ";
                            }
                            if (!string.IsNullOrEmpty(cp.EstadoTintas) && cp.EstadoTintas.StartsWith("Program"))
                            {
                                var estadoLito = GetEstadoLitografia(idPedido);
                                cadena += $": {estadoLito} {abreviaturaMaquina} ";
                            }
                        }
                        else
                        {
                            cadena += " ";
                        }
                    }
                }
            }
            return descripcionCliente ? $" {strInterior} {strExterior} " : cadena;
        }


        //OPTIMIZADO
        public string GetEstadoLitografia(int idPedido)
        {
            var todasTiradas = true;
            var empezado = false;

            var datosPedido = _programadorLitalsaContext.PedidoProcesado.FirstOrDefault(o => o.IdPedido == idPedido);
            if (datosPedido == null)
            {
                return "P"; // o algún valor por defecto, dado que no se encontró el pedido.
            }

            var hojasPedido = datosPedido.HojasPedido;

            for (var i = 1; i <= 8; i++)
            {
                // Usamos TryParse en lugar de Parse para manejar posibles errores.
                if (!long.TryParse(GetPropertyValueByName(datosPedido, $"C0{i}ped"), out long codTintas))
                    continue;

                if (codTintas == 0)
                    continue;

                //if (!long.TryParse(GetPropertyValueByName(datosPedido, $"Hojasco{i}ped"), out long hojasAux))
                //    continue;

                long hojasAux;
                var hojasValue = GetPropertyValueByName(datosPedido, $"Hojasco{i}ped");

                if (hojasValue == null)
                {
                    hojasAux = 0;
                }
                else
                {
                    if (!long.TryParse(hojasValue, out hojasAux))
                    {
                        continue; // Si no es null pero la conversión falla, sigue con la próxima iteración
                    }
                }

                var lowerLimit = 0.1 * hojasPedido;
                var upperLimit = 0.85 * hojasPedido;

                if (hojasAux < lowerLimit)
                {
                    todasTiradas = false;
                }
                else if (lowerLimit <= hojasAux && hojasAux <= upperLimit)
                {
                    todasTiradas = false;
                    empezado = true;
                }
                else if (hojasAux > upperLimit)
                {
                    empezado = true;
                    todasTiradas = true;
                }
            }

            var result = "P";
            if (todasTiradas)
                result = "*";
            if (!todasTiradas && empezado)
                result = "I";
            return result;
        }

        public string GetPropertyValueByName(object obj, string propName)
        {
            var type = obj.GetType();
            var prop = type.GetProperty(propName);
            return prop?.GetValue(obj)?.ToString();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="numHojas"></param>
        /// <param name="pesoHoja"></param>
        /// <returns></returns>
        public int CalcularPesoTotal(int numHojas, double pesoHoja)
        {
            return (int)(numHojas * pesoHoja);
        }

        /// <summary>
        /// Calcula el peso por hoja, a parte de los parámetros se busca si el pedido es con Aluminio o no,
        /// para ello se consulta el GRUPO (0102->Aluminio) -- TODO: Sé que no va a pasar, pero habría que refinarlo
        /// Luego se obtiene la densidad de la tabla de constantes del programador: CFG
        /// </summary>
        /// <param name="idPedido"></param>
        /// <param name="ancho"></param>
        /// <param name="largo"></param>
        /// <param name="espesor"></param>
        /// <returns></returns>
        public double CalcularPesoPorHoja(int idPedido, int ancho, int largo, int espesor)
        {
            var densidadTipo = _datoLita01Context.EEorden
                .Where(o => o.Pagina == "0" && o.Codigo == idPedido.ToString())
                .Select(o => o.C44.StartsWith("0102") || o.C46.StartsWith("0102") ? "DensidadAluminio" : "DensidadAcero")
                .FirstOrDefault();

            if (densidadTipo == null)
            {
                return 0.0;
            }

            int result;
            var densidadValor = _programadorLitalsaContext.TablaCfg
                .FirstOrDefault(o => o.Iddato == densidadTipo)
                .Dato;
            int densidadInt = int.Parse(densidadValor);
            var densidad = decimal.Round(ancho * largo * (decimal)espesor / 100 * densidadInt / 1_000_000_000m, 5, MidpointRounding.AwayFromZero);

            return (double)densidad;
        }

        public string GetIdentificadorEstadoPedido(int hojasProcesadas, int hojasTotales)
        {
            // devuelve el estado de un pedido en función de las hojas procesadas...

            if (hojasProcesadas >= 0.85 * hojasTotales)
                return "*";
            if (hojasProcesadas < 0.85 * hojasTotales & hojasProcesadas > 0.05 * hojasTotales)
                return "I";
            if (hojasProcesadas < 0.05 * hojasTotales)
                return "0";
            if (hojasProcesadas > 1.02 * hojasTotales)
                return "^";

            return string.Empty;
        }

        /// <summary>
        /// El campo HOJALATA, en la vista de pedido, antes se obtenia de esta llamada:
        /// =devuelve_datos_pedido([idpedido];"DevuelveCaracteristicasHojalata") & " " & devuelve_datos_tipo_hjlta([idpedido];"Procesos")
        /// </summary>
        /// <param name="idPedido"></param>
        /// <returns></returns>
        public string GetCaracteristicasHojalata(int idPedido, PedidoProcesado datosPedido = null, Matped datosMatPed = null)
        {
            // Crea el parámetro para la consulta
            var idParam = new SqlParameter("@idPedido", idPedido);

            // Consulta para datosPedido
            datosPedido ??= _programadorLitalsaContext.PedidoProcesado
                .FromSqlRaw("SELECT TOP 1 * FROM PedidoProcesado WHERE IdPedido = @idPedido", idParam)
                .AsEnumerable()
                .SingleOrDefault();

            // Consulta para datosMatPed
            datosMatPed ??= _programadorLitalsaContext.Matped
                .FromSqlRaw("SELECT TOP 1 * FROM Matped WHERE Nummpe = @idPedido", idParam)
                .AsEnumerable()
                .FirstOrDefault();


            // If neither of the two queries returned a result, it's pointless to continue, so return some default value.
            if (datosPedido == null || datosMatPed == null)
            {
                return string.Empty; // or some other default value
            }

            var grupo = string.IsNullOrEmpty(datosMatPed.Grupo) ? "ER" : datosMatPed.Grupo;

            var dimensiones = $"{datosPedido.EspesorHjlta}x{datosPedido.AnchoHjlta}x{datosPedido.LargoHjlta}";

            var recubrimiento = grupo == "ALUMINIO" ? "-" : datosMatPed.Recubrimiento ?? "N/A";//EXT17

            var cadenaMaterial = grupo == "HOJALATA" ? " HJ " : $" {grupo} ";

            cadenaMaterial += (datosMatPed.Corte?.ToUpper() ?? string.Empty) == "SCROLL" ? "Scroll " : string.Empty;//a.EXT4

            return $"{dimensiones} {recubrimiento} {cadenaMaterial} {datosMatPed.Procesos}";//EXT16
        }


        /// <summary>
        /// Adaptación a pelo de Public Function devuelve_datos_pedido(IdPedido As Long, tipo_dato As String) As Variant
        /// Eliminados los casos que son una consulta directa a una tabla, por ej las fechas.
        /// </summary>
        /// <param name="valorBuscado"></param>
        /// <param name="tipoDato"></param>
        /// <returns></returns>
        public MultiResponseTypes GetDatosPedido(int valorBuscado, string tipoDato)
        {
            /*'devuelve datos del pedido cogiendolos de la tabla Pedidos_Prueba - Aquí es PedidoProcesado
              'si tipo_dato=
              '"F" formato
              '"An" ancho hojalata
              '"Lg" largo hjlta
              '"hojas" numero hojas
              '"el" tipo elemento
              '"pl" plano
              '"Esc" escuadra
              '"Tb" Tipo barnizado
              '"Motivo" Motivo
              '"EstadoCodigosAplicacion"
              'si el idpedido es nulo o 0 salimos de la funcion
            */
            var response = new MultiResponseTypes();
            var datosPedido = _programadorLitalsaContext.PedidoProcesado.FirstOrDefault(o => o.IdPedido == valorBuscado);
            if (datosPedido == null)
            {
                throw new Exception("No se han encontrado datos del pedido");
            }
            var dimensionMax = Math.Max(datosPedido.LargoHjlta.Value, datosPedido.AnchoHjlta.Value);
            var dimensMin = Math.Min(datosPedido.LargoHjlta.Value, datosPedido.AnchoHjlta.Value);
            var datosFormato = _programadorLitalsaContext.TablaFormatosHjltaSh.FirstOrDefault(o =>
                o.Largo == dimensionMax && o.Ancho == dimensMin || o.Largo == dimensMin && o.Ancho == dimensionMax);
            switch (tipoDato)
            {
                case "Cliente":
                    response.Num = datosPedido.IdCliente;
                    break;

                case "F":
                    response.Float = datosPedido.Formato;
                    break;

                case "pl":
                    response.Text = datosPedido.Plano;
                    break;

                case "hojas":
                    response.Num = datosPedido.HojasPedido;
                    break;

                case "el":
                    response.Text = datosPedido.TipoElemento;
                    break;

                case "An":
                    response.Num = datosPedido.AnchoHjlta;
                    break;

                case "Lg":
                    response.Num = datosPedido.LargoHjlta;
                    break;

                case "Espesor":
                    response.Num = datosPedido.EspesorHjlta;
                    break;

                case "num_tintas":
                    response.Num = GetNumeroTintas(valorBuscado, 0);
                    break;

                case "Tb":
                    response.Text = datosPedido.TipoBarnizado;
                    break;

                case "Esc":
                    response.Text = GetEscuadra(valorBuscado);
                    break;


                case "Motivo":
                    response.Text = datosPedido.Motivos;
                    break;

                case "PesoHoja":
                    response.Double = CalcularPesoPorHoja(valorBuscado, datosPedido.AnchoHjlta.Value, datosPedido.LargoHjlta.Value, datosPedido.EspesorHjlta.Value);
                    break;

                case "Hojalata_YA_sacada":
                    response.Bool = datosPedido.HojalataYaSacada;
                    break;

                case "EstadoCodigosAplicacion":
                    response.Text = GetTextoEstadoCodigosAplicacion(valorBuscado, false, false);
                    break;


                case "WO_CROWN":
                    // devuelve_datos_pedido = devuelve_wo_crown(IdPedido)
                    response.Bool = false;
                    break;

                case "Supedido":
                    response.Text = datosPedido.Supedido;
                    break;

                case "TipoPedido":
                    response.Text = datosPedido.TipoPedido;
                    break;

                case "VSP":
                    // se devuelve que es un tipo de pedido de leches VSP
                    // cuando el TipoEnvase sea ALIMENT y el tipoPedido sea L (Litografía)
                    response.Bool = datosPedido.TipoEnvase.Contains("ALIME") && datosPedido.TipoPedido == "L";
                    break;


                case "Fecha_entrega":
                    response.Fecha = datosPedido.RequeridoEnFecha;
                    break;

                case "Observaciones_Pedido":
                    response.Text = datosPedido.Obs1;
                    break;

                case "Tipo_Hjlta":
                    response.Num = datosPedido.TipoHjlta;
                    break;

                case "DensidadMaterial":
                    {
                        response.Text = GetDatosTipoHojalata(0, "DensidadMaterial", valorBuscado).Text;
                        break;
                    }

                case "DevuelveCaracteristicasHojalata":
                    {
                        var dimensiones = $"{datosPedido.EspesorHjlta}x{datosPedido.AnchoHjlta}x{datosPedido.LargoHjlta}";
                        var recubrimiento = $"{GetDatosTipoHojalata(0, "Recubrimiento", valorBuscado).Text}";
                        var cadenamaterial = $"{GetDatosTipoHojalata(0, "CadenaMaterial", valorBuscado).Text}";
                        var dureza = $"{GetDatosTipoHojalata(0, "Dureza", valorBuscado).Text}";

                        response.Text = $"{dimensiones} {recubrimiento} {cadenamaterial} {dureza}";
                        break;
                    }

                case "FormaFlejado":
                    response.Text = datosPedido.Obsflejado;
                    break;

                case "Obs_a_cliente":
                    response.Text = datosPedido.ObsACliente;
                    break;


                case "ObservacionesFechas":
                    response.Text = _datoLita01Context.Fechas.First(o => o.Codigo == valorBuscado).Observa;
                    break;


                case "HojasTerminadas":
                    response.Double = _datoLita01Context.Acaped.First(o => o.Numacp == valorBuscado).Staacp;
                    break;


                case "CodigoDolite":
                    {
                        response.Text = datosFormato?.Dolites ?? false
                            ? datosFormato.DenoMuestra
                            : string.Empty;
                        break;
                        //devuelve_datos_pedido = DevuelveDatosMuestraSH(dimensionMax, dimensMin, "CodigoDolite");
                        //break;
                    }

                case "TieneDolite":
                    {
                        response.Bool = datosFormato?.Dolites;
                        break;
                        //devuelve_datos_pedido = DevuelveDatosMuestraSH(dimensionMax, dimensMin, "Dolite");
                        //break;
                    }

                case "EnviarDoliteCliente":
                    {
                        response.Bool = datosFormato?.EnviarDoliteCliente;
                        break;
                        //devuelve_datos_pedido = DevuelveDatosMuestraSH(dimensionMax, dimensMin, "EnviarDoliteCliente");
                        //break;
                    }

                case "PedidoMaxLito":
                    {
                        response.Errors.Add("No existe PedidoMaxLito");
                        break;
                        //Eliminado, se gestiona de forma diferente directamente en GetPedidoProgramadoQueryHandler nada más recibir 
                        //la request. Se deja una excepción por si, a medida que se va portando la aplicación, algo más llama aquí.
                    }

                case "PedidoMaxBarn":
                    {
                        response.Errors.Add("No existe PedidoMaxBarn");
                        break;
                        //Eliminado, se gestiona de forma diferente directamente en GetPedidoProgramadoQueryHandler nada más recibir 
                        //la request. Se deja una excepción por si, a medida que se va portando la aplicación, algo más llama aquí.
                    }

                case "ExistePedidoReproceso":
                    {
                        response.Errors.Add("No existe ExistePedidoReproceso");
                        break;
                    }
                default:
                    response.Errors.Add("No existe parametro buscado");
                    break;
            }
            return response;
        }

        /// <summary>
        /// Adaptación directa de Public Function devuelve_escuadra(IdPedido As Long) As String
        /// </summary>
        /// <param name="idPedido"></param>
        /// <returns></returns>
        public string GetEscuadra(int idPedido)
        {
            var datosPedido = _programadorLitalsaContext.PedidoProcesado.First(o => o.IdPedido == idPedido);

            var plano = datosPedido.Plano;
            var cliente = datosPedido.IdCliente;

            var escuadra = _programadorLitalsaContext.Plano
                .FirstOrDefault(o => o.Idcliente == cliente && o.CodigoPlano == plano)?.Escuadra ?? string.Empty;

            return escuadra;
        }

        /// <summary>
        /// Adaptación directa de Function devuelve_numero_tintas(IdPedido As Long, Optional num_tinta_mate As Long) As Integer
        /// </summary>
        /// <param name="idPedido"></param>
        /// <param name="numTintaMate"></param>
        /// <param name="contarTintasMate"></param>
        /// <param name="restarTintaMate"></param>
        /// <returns></returns>
        public int GetNumeroTintas(int idPedido, int numTintaMate, bool contarTintasMate = false, bool restarTintaMate = false)
        {
            var numeroTintas = 0;
            var pp = new PedidoProcesado();
            var type = pp.GetType();
            var datosPedido = _programadorLitalsaContext.PedidoProcesado.FirstOrDefault(o => o.IdPedido == idPedido);
            for (var i = 1; i <= 11; i++)
            {
                // hay que localizar el codigo de las tintas que estén en la posicion i
                // y ver si es distinto de 0 o no nulo

                var campoC0Ped = i < 10
                    ? type.GetProperty($"C0{i}ped")?.GetValue(datosPedido)
                    : type.GetProperty($"Co{i}ped")?.GetValue(datosPedido);
                var codTintas = campoC0Ped != null ? int.Parse(campoC0Ped.ToString()) : 0;
                if (codTintas == 1000)
                    numTintaMate++;
                if (codTintas != 0)
                    numeroTintas++;
            }

            if (contarTintasMate)
                return numTintaMate;

            return restarTintaMate ? numeroTintas - numTintaMate : numeroTintas;
        }


        public int? GetPedidoReproceso(int idPedido)
        {
            if (idPedido < 1800000)
                return null;

            var digitoControlReprocesos = idPedido.ToString().Substring(2, 1);

            return digitoControlReprocesos switch
            {
                "0" => _programadorLitalsaContext.PedidoProcesado.Any(o => o.IdPedido == idPedido + 30000)
                    ? idPedido + 30000
                    : null,
                "3" => _programadorLitalsaContext.PedidoProcesado.Any(o => o.IdPedido == idPedido + 30000)
                    ? idPedido + 30000
                    : idPedido - 30000,
                "6" => idPedido - 60000,
                _ => null
            };
        }

        /// <summary>
        /// Public Function devuelve_datos_aplicacion(Idaplicacion As Long, tipo_dato As String) As Variant
        /// Esta... necesidad es porque las dos tablas tienen la misma estructura pero los datos están separados y
        /// como mejor no tocar la estructura de las tablas... pues eso.
        /// V2: Se usa una vista creada en la BD que no es mas que un UNION de codaply y codapli_LITO
        /// </summary>
        /// <param name="idAplicacion"></param>
        /// <param name="tipoDato"></param>
        /// <returns></returns>
        public string DevuelveDatosAplicacion(int idAplicacion, string tipoDato)
        {
            var regTabla = _programadorLitalsaContext.Codapli.FirstOrDefault(o => o.Codbaz == idAplicacion);
            return tipoDato switch
            {
                "NombreAplicacion" => regTabla?.Txtbaz ?? string.Empty,
                "NombreAplicacionCliente" => regTabla?.Nombaz ?? DevuelveDatosAplicacion(idAplicacion, "NombreAplicacion"),
                "NombreAplicacionCliente_short" => regTabla?.Nombaz ?? string.Empty,
                "codigobarnizasociado" => regTabla?.Codbarniz.ToString() ?? string.Empty,
                "gramaje" => regTabla?.Grmbaz.ToString() ?? string.Empty,
                "gramajemin" => regTabla?.Grmbazmin.ToString() ?? string.Empty,
                "NombreApliProgramacion" => regTabla?.Nombreprogramacion ?? string.Empty,
                _ => string.Empty
            };
        }

        /// <summary>
        /// Adaptación directa de Public Function devuelve_datos_tipo_hjlta(Idhjlta As Long, tipo_dato As String, Optional IdPedido As Long) As Variant
        /// </summary>
        /// <param name="idHojalata"></param>
        /// <param name="tipoDato"></param>
        /// <param name="idPedido"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public MultiResponseTypes GetDatosTipoHojalata(int idHojalata, string tipoDato, int idPedido = 0)
        {
            string aux;
            var resultado = new MultiResponseTypes();
            if (idHojalata > 1500000)
                idPedido = idHojalata;

            // si entramos por el número de pedido hay que buscar en la tabla matped
            if (idPedido != 0)
            {
                //ATENCION: Se pueden encontrar dos registros en la tabla/vista MATPED pero como el idPedido es unico
                //se obtiene directamente un registro, ya que los procesos del pedido son los mismos
                //La consulta no se hace a la matped del programador, sino a la matped de datolita01... Que a saber como será con SAP
                //01/09/2023: La consulta AHORA SI se hace a matped del programador porque se incorporó a los procesos de actualización
                var datosMatPed = _programadorLitalsaContext.Matped.FirstOrDefault(o => o.Nummpe == idPedido);
                if (datosMatPed == null)
                {
                    throw new Exception($"Pedido: {idPedido} - No se han encontrado datos en MATPED");
                }
                aux = datosMatPed.Grupo ?? "ER";

                switch (tipoDato)
                {
                    case "Dif":
                        {
                            if (aux == "HOJALATA")
                            {
                                //'aux2 = DLookup("[RECUBRIMIENTO]", "matped", "[nummpe]=" & IdPedido)
                                resultado.Text = _programadorLitalsaContext.Matped.FirstOrDefault(o => o.Nummpe == idPedido && o.Recubrimiento.Contains("D")).Recubrimiento;
                                //'en el caso que sea hojalata con proceso y no se indique el tipo de recubrimiento marcamos como que no es diferencial
                                //Pedidos de prueba con valores raros en "Recubrimiento" = 2104870, 2104943
                                if (_programadorLitalsaContext.Matped.Any(o => o.Nummpe == idPedido && o.Tipo.Contains("PROCESO") &&
                                                                               (string.IsNullOrEmpty(o.Recubrimiento) || o.Recubrimiento == "N/A")))
                                    resultado.Bool = false;
                            }
                            else
                                resultado.Bool = false;
                            break;
                        }

                    case "Scroll":
                        {
                            resultado.Bool = datosMatPed.Corte == "Scroll";
                            break;
                        }

                    case "Tipo":
                        {
                            resultado.Text = aux;
                            break;
                        }

                    case "Aluminio":
                        {
                            resultado.Bool = aux == "ALUMINIO";
                            break;
                        }

                    case "TFS":
                        {
                            resultado.Bool = aux == "TFS";
                            break;
                        }

                    case "Recubrimiento":
                        {
                            resultado.Text = aux == "ALUMINIO"
                                ? "-"
                                : datosMatPed.Recubrimiento;
                            break;
                        }

                    case "Procesos":
                        {
                            resultado.Text = datosMatPed.Procesos;
                            break;
                        }

                    case "CARARAYADA":
                        {
                            resultado.Text = datosMatPed.Cararayada;
                            break;
                        }

                    case "DensidadMaterial":
                        {
                            resultado.Text = datosMatPed.Grupo == "ALUMINO"
                                ? _programadorLitalsaContext.TablaCfg.SingleOrDefault(o => o.Iddato == "DensidadAluminio")?.Dato ?? "0"
                                : _programadorLitalsaContext.TablaCfg.SingleOrDefault(o => o.Iddato == "DensidadAcero")?.Dato ?? "0";
                            break;
                        }

                    case "Dureza":
                        {
                            resultado.Text = datosMatPed.Dureza;
                            break;
                        }

                    case "CadenaMaterial":
                        {
                            // devuelve_datos_tipo_hjlta = devuelve_datos_tipo_hjlta & devuelve_datos_tipo_hjlta(0, "tipo", idpedido)
                            resultado.Text = datosMatPed.Grupo == "HOJALATA"
                                ? " HJ "
                                : $" {aux} ";

                            if ((datosMatPed.Corte?.ToUpper() ?? string.Empty) == "SCROLL")
                                resultado.Text += "Scroll ";
                            break;
                        }
                }
            }
            else
            {
                var datosTipoHojalata = _programadorLitalsaContext.TipoHjlta.FirstOrDefault(o => o.Codtma == idHojalata);
                switch (tipoDato)
                {
                    case "Dif":
                        {
                            resultado.Bool = datosTipoHojalata?.Diferencial;
                            break;
                        }

                    case "Scroll":
                        {
                            resultado.Bool = datosTipoHojalata?.Scroll;
                            break;
                        }

                    case "Aluminio":
                        {
                            resultado.Bool = datosTipoHojalata?.Aluminio;
                            break;
                        }

                    case "DensidadMaterial":
                        {
                            //En Access -1 es TRUE
                            //if (DLookup("[Aluminio]", "TipoHjlta", "[codtma]=" + idHojalata) == -1)
                            resultado.Text = datosTipoHojalata.Aluminio
                                ? _programadorLitalsaContext.TablaCfg.SingleOrDefault(o => o.Iddato == "DensidadAluminio")?.Dato ?? "0"
                                : _programadorLitalsaContext.TablaCfg.SingleOrDefault(o => o.Iddato == "DensidadAcero")?.Dato ?? "0";
                            break;
                        }

                    case "TFS":
                        {
                            resultado.Bool = datosTipoHojalata?.Tfs;
                            break;
                        }

                    case "CadenaMaterial":
                        {
                            if (datosTipoHojalata.Tfs)
                                resultado.Text += " TFS ";
                            else if (datosTipoHojalata.Aluminio)
                                resultado.Text += " ALU ";
                            else
                                resultado.Text += " HJ ";
                            if (datosTipoHojalata.Scroll)
                                resultado.Text += "Scroll ";
                            break;
                        }
                }
            }
            return resultado;
        }

        /// <summary>
        /// Adaptación directa de Public Function devuelve_datos_muestra_SH(Largo As Integer, Ancho As Integer, tipo_dato As String) As Variant
        /// </summary>
        /// <param name="largo"></param>
        /// <param name="ancho"></param>
        /// <param name="tipoDato"></param>
        /// <returns></returns>
        public bool GetDatosMuestraSH(int largo, int ancho, string tipoDato)
        {
            var datosFormato = _programadorLitalsaContext.TablaFormatosHjltaSh.FirstOrDefault(o =>
                o.Largo == largo && o.Ancho == ancho || o.Largo == ancho && o.Ancho == largo);
            switch (tipoDato)
            {
                case "Dolite":
                    {
                        return datosFormato?.Dolites ?? false;
                    }

                case "CodigoDolite":
                    {
                        if (datosFormato?.Dolites ?? false)
                        {
                            return !string.IsNullOrEmpty(datosFormato.DenoMuestra);// datosFormato.DenoMuestra;
                        }
                        return false;
                    }

                case "EnviarDoliteCliente":
                    {
                        return (datosFormato?.Dolites ?? false) && datosFormato.EnviarDoliteCliente;
                    }
                default: return false;
            }
        }

        /// <summary>
        /// Adaptación directa de Public Function devuelve_datos_plano(IdCliente As Long, Plano As String, tipo_dato As String) As Variant
        /// </summary>
        /// <param name="idCliente"></param>
        /// <param name="plano"></param>
        /// <param name="tipoDato"></param>
        /// <returns></returns>
        public string GetDatosPlano(int idCliente, string plano, string tipoDato)
        {
            var datos = _programadorLitalsaContext.Plano.FirstOrDefault(o => o.Idcliente == idCliente && o.CodigoPlano == plano);
            return tipoDato switch
            {
                "Es" => datos?.Escuadra ?? "?",
                "Ba" => datos?.FormaBarnizado ?? "?",
                "Lit" => datos?.FormaLitografía ?? "?",
                _ => "?"
            };
        }

        /// <summary>
        /// Adaptación directa de Public Function devuelve_obs_almacen(IdPedido As Long, esprimerproceso As Boolean,
        /// esultimoproceso As Boolean, Optional yaprogramado As Boolean) As String
        /// </summary>
        /// <param name="idPedido"></param>
        /// <param name="esPrimerProceso"></param>
        /// <param name="esUltimoProceso"></param>
        /// <param name="yaProgramado"></param>
        /// <returns></returns>
        public string GetObservacionesAlmacen(int idPedido, bool esPrimerProceso, bool esUltimoProceso, bool? yaProgramado)
        {
            // esta función devuelve datos para almacén
            // devuelve los datos de la hojalata y la calle en la que se encuentra ubicada la hojalata
            // si es el último y el cliente es Crown

            string obsArticulo;
            var cadena = "";
            //RECUERDA: MATPED SIEMPRE DE DATOLITA01--YA NO ahora se ha incluido en los procesos de actualización diarios y tiene que ser del PROGRAMADOR
            var datosMatPed = _programadorLitalsaContext.Matped.Where(o => o.Nummpe == idPedido);

            foreach (var matped in datosMatPed)
            {
                if (esPrimerProceso & matped.Tmampe != 57)
                {
                    cadena += $" {matped.Hojmpe}:{matped.Prompe} ";
                    if (matped.Tipo.Contains("PROCESO"))
                    {
                        cadena += string.IsNullOrEmpty(matped.Procesos) || matped.Procesos == "0"
                            ? "CON PROCESO"
                            : matped.Procesos;
                    }
                    else
                    {
                        cadena += $" {(string.IsNullOrEmpty(matped.Recubrimiento) ? "0" : matped.Recubrimiento)}";
                    }
                    cadena += $" {matped.Sitmpe} / ";
                }
                else if (esPrimerProceso && matped.Tmampe == 57)
                {
                    throw new Exception("Pedido virtual, indicarlo en la programación");
                }

                if (esUltimoProceso & matped.Climpe == 91)
                {
                    obsArticulo = _programadorLitalsaContext.PedidoProcesado
                        .FirstOrDefault(o => o.IdPedido == idPedido)?.Obs1 ?? string.Empty;
                    cadena += $" {GetDestinoCrown(obsArticulo)} ";
                }

                // ******************* MARCAMOS SI LA HOJALATA ESTABA YA SACADA**************

                if (idPedido == 0) continue;
                var hojalataYaSacada = GetDatosPedido(idPedido, "Hojalata_YA_sacada").Bool.Value;

                // en el caso que sea primer proceso y bien la hjlta esté ya sacada o bien esté ya programado
                // aparece en la cadena almacén la coletilla (YP) que identifica que ya se ha sacado la hjlta.

                if (esPrimerProceso && (hojalataYaSacada || yaProgramado == true))
                    cadena += " (YP) ";
            }
            return cadena;
        }

        /// <summary>
        /// Adaptación directa de Public Function devuelve_destino_CROWN(cadena As String) As String
        /// </summary>
        /// <param name="cadena"></param>
        /// <returns></returns>
        public string GetDestinoCrown(string cadena)
        {
            var resultado = string.Empty;
            if (cadena.Contains("AG"))
            {
                resultado = "Destino: AGON.";
            }
            else if (cadena.Contains("MU"))
            {
                resultado = "Destino: MU.";
            }

            return resultado;
        }

        /// <summary>
        /// Adaptación directa de Public Function devuelve_obs_calidad(IdPedido As Long, Optional Idaplicacion As Long,
        /// Optional idmaquina As Integer, Optional idposicion As Long, Optional borrado As Boolean) As String
        /// Se hace una versión LITE ya que en el access no está funcionando bien, y para la versión nueva
        /// se deja sólo como lectura, por lo que únicamente recupera los registros de calidad.
        /// </summary>
        /// <param name="idPedido"></param>
        /// <param name="idAplicacion"></param>
        /// <returns></returns>
        public async Task<string> GetObservacionesCalidadLite(int idPedido, int? idAplicacion)
        {
            //var obs = _litalsaDataWarehouseContext.NotasProgramacion.Where(o=>o.PedidoId==idPedido)
            //    .Select(s => new { texto = $"{s.AplicacionId}-{s.Texto}-{s.ObservacionCalidadId}" }).ToString() ?? string.Empty;

            //return string.Join("<br>", obs);

            var res = string.Empty;
            //Va a consultar a calidad.NotasProgramacion que es UNA VISTA
            var obs = _litalsaDataWarehouseContext.NotasProgramacion.Where(o => o.PedidoId == idPedido).AsQueryable();
            if (idAplicacion != null)
            {
                obs = obs.Where(o => o.AplicacionId == idAplicacion);
            }

            if (!obs.Any())
                return string.Empty;
            var listaTextos = new List<string>();
            foreach (var item in obs)
            {
                listaTextos.Add($"{item.Texto} - {item.AplicacionId} - {item.ObservacionCalidadId}");
            }

            res = string.Join(Environment.NewLine, listaTextos);
            return res;

        }
        public string GetTratamientosByPedido(int idPedido)
        {
            var result = _programadorLitalsaContext.TablaCodigosPedido
                .Where(p => p.Idpedido == idPedido && p.Idcodigoaplicacion != 100000006)
                .OrderBy(p => p.Fase)
                .ToList();

            var cadenaInterior = string.Join(" ", result.Where(p => p.Posicion.StartsWith("i")).Select(o => o.Idcodigoaplicacion));
            var cadenaExterior = string.Join(" ", result.Where(p => p.Posicion.StartsWith("e")).Select(o => o.Idcodigoaplicacion));

            return $"{(!string.IsNullOrEmpty(cadenaInterior) ? cadenaInterior + " " : string.Empty)}//{(!string.IsNullOrEmpty(cadenaExterior) ? " " + cadenaExterior : string.Empty)}";
        }

        public async Task<double?> GetVelocidadMaxima(int? idProducto, int idLinea, CancellationToken ct)
        {
            double aux = 0;
            string velocidadColumn = null;

            int[] maquinaIds1 = { 2, 3, 8, 12, 17 };//para barnizadoras en general
            int[] maquinaIds2 = { 7, 15 };//Sprint-M4
            int[] maquinaIds3 = { 6 };//M2
            int[] maquinaIds4 = { 11, 16 };//T1-T3

            if (maquinaIds1.Contains(idLinea))
                velocidadColumn = "Velocidad1";
            else if (maquinaIds2.Contains(idLinea))
                velocidadColumn = "Velocidad2";
            else if (maquinaIds3.Contains(idLinea))
                velocidadColumn = "Velocidad3";
            else if (maquinaIds4.Contains(idLinea))
                velocidadColumn = "Velocidad4";

            if (velocidadColumn != null)
            {
                aux = await _programadorLitalsaContext.TablaProductos
                    .Where(p => p.Idproducto == idProducto)
                    .Select(p => p.GetType().GetProperty(velocidadColumn).GetValue(p) ?? 0)
                    .Cast<double>()
                    .FirstOrDefaultAsync(ct);
            }
            return aux;
        }
        public async Task<int?> GetTemperaturaSecado_LITO(int? idProducto, int idLinea, CancellationToken ct)
        {
            double aux = 0;
            string velocidadColumn = null;

            int[] maquinaIds1 = { 2, 3, 8, 12, 17 };//para barnizadoras en general
            int[] maquinaIds2 = { 7, 15 };//Sprint
            int[] maquinaIds3 = { 6 };//M2
            int[] maquinaIds4 = { 11, 16 };//T1

            if (maquinaIds1.Contains(idLinea))
                velocidadColumn = "TemperaturaSecado1";
            else if (maquinaIds2.Contains(idLinea))
                velocidadColumn = "TemperaturaSecado2";
            else if (maquinaIds3.Contains(idLinea))
                velocidadColumn = "TemperaturaSecado3";
            else if (maquinaIds4.Contains(idLinea))
                velocidadColumn = "TemperaturaSecado4";

            if (velocidadColumn != null)
            {
                aux = await _programadorLitalsaContext.TablaProductos
                    .Where(p => p.Idproducto == idProducto)
                    .Select(p => p.GetType().GetProperty(velocidadColumn).GetValue(p) ?? 0)
                    .Cast<double>()
                    .FirstOrDefaultAsync(ct);
            }
            return (int?)aux;
        }

        public async Task<double> GetTemperaturaSecado(int? idProducto, int? idPedido, MaquinaDTO maquina, CancellationToken cancellationToken)
        {
            if (!idProducto.HasValue)
                return 0;

            var datosProducto = await _programadorLitalsaContext.TablaProductos.FindAsync(idProducto.Value, cancellationToken);

            if (datosProducto == null)
                return 0;
            //B3, B4, B5, T2, Sprint, M4, M2, T4
            int[] maquinaIds = { 2, 3, 8, 12, 7, 15, 6, 17 };
            //T1, T3
            int[] maquinaIds2 = { 11, 16 };

            var temperaturaSecado = maquinaIds.Contains(maquina.Idmaquina)
                ? datosProducto.TemperaturaSecado1.GetValueOrDefault()
                : maquinaIds2.Contains(maquina.Idmaquina)
                    ? datosProducto.TemperaturaSecado4.GetValueOrDefault()
                    : 0;

            return temperaturaSecado;
        }
        public async Task<VelocidadesMaquina> GetTemperaturaSecadoV2(int? idProducto, int? idPedido, int idMaquina, CancellationToken cancellationToken)
        {
            if (!idProducto.HasValue)
                return null;

            var datosProducto = await _programadorLitalsaContext.TablaProductos.FindAsync(idProducto, cancellationToken);

            if (datosProducto == null)
                return null;

            var progLineaPrevia = await _programadorLitalsaContext.TablaProgramacion
                .Where(o => o.Idpedido == idPedido && o.Idlinea == idMaquina - 1)
                .OrderByDescending(o => o.Idprogramacion)
                .FirstOrDefaultAsync(cancellationToken);
            VelocidadesMaquina temperaturaSecado = null;
            if (progLineaPrevia != null && progLineaPrevia.Idproducto != 0 && progLineaPrevia.Idproducto != null)
            {
                var pedido = await _programadorLitalsaContext.PedidoProcesado
                    .Where(o => o.IdPedido == idPedido)
                    .FirstOrDefaultAsync(cancellationToken);
                var datosMatped =
                    await _programadorLitalsaContext.Matped.FirstOrDefaultAsync(
                        o => o.Codigo == pedido.IdPedido.Value.ToString(), cancellationToken);

                //La consulta se hace así porque cuando SI hay ReservasConsultas es para todos MENOS para ALU
                //Cuando es ALU, es para todas las reservas pero con el material concreto ALU.
                //(por eso se usa NULL en la tabla, para no escribir dos lineas con mismos valores que solo se diferencian en 1 columna)
                var datos = await _programadorLitalsaContext.VelocidadesMaquina.FirstOrDefaultAsync(o =>
                    o.IdMaquina == idMaquina - 1 &&
                    (
                        (o.ReservasConsulta == pedido.TipoBarnizado && !datosMatped.Grupo.Contains("ALU")) ||
                        (o.ReservasConsulta == null && datosMatped.Grupo.Contains(o.MaterialConsulta))
                    )
                    && progLineaPrevia.Idaplicacion.ToString().StartsWith(o.CodApliMq1Consulta.ToString())
                    && o.CodApliMq2Consulta == idProducto, cancellationToken);

                if (datos != null)
                {
                    //Actualizamos aqui los datos de la programación de la linea previa, para tenerlos centralizados.
                    temperaturaSecado = datos;
                    if (progLineaPrevia.VelocidadMaxima != datos.VelTiradaMq1)
                    {
                        // Ejecutar la actualización directamente usando SQLRAW
                        var sql = "UPDATE TablaProgramacion SET VelocidadMaxima = @VelTiradaMq1 WHERE Idprogramacion = @Idprogramacion";

                        await _programadorLitalsaContext.Database.ExecuteSqlRawAsync(
                            sql,
                            new[] {
                                new SqlParameter("@VelTiradaMq1", datos.VelTiradaMq1),
                                new SqlParameter("@Idprogramacion", progLineaPrevia.Idprogramacion)
                            },
                            cancellationToken
                        );

                    }
                }
            }
            return temperaturaSecado;
        }
        public async Task<double> GetTemperaturaSecadoBarnizado(int? idProducto, int idLinea, CancellationToken cancellationToken)
        {
            if (!idProducto.HasValue)
                return 0;

            var datosProducto = await _programadorLitalsaContext.TablaProductos.FindAsync(idProducto.Value, cancellationToken);

            if (datosProducto == null)
                return 0;

            //B3, B4, B5, T2, Sprint, M4, M2, T4
            int[] maquinaIds = { 2, 3, 8, 12, 7, 15, 6, 17 };
            //T1, T3
            int[] maquinaIds2 = { 11, 16 };

            var temperaturaSecado = maquinaIds.Contains(idLinea)
                ? datosProducto.TemperaturaSecado1.GetValueOrDefault()
                : maquinaIds2.Contains(idLinea)
                    ? datosProducto.TemperaturaSecado4.GetValueOrDefault()
                    : 0;

            return temperaturaSecado;
        }

        /// <summary>
        /// Public Function DEVUELVE_ORDEN_PROCESOS(IdPedido As Long, codigo_aplicacion As Long, total_procesos As Integer,
        /// Optional proceso_anterior As Long, Optional proceso_posterior As Long, Optional lugar As String,
        /// Optional cara_anterior As String, Optional cara_posterior As String) As Integer
        /// 16/01/21 SE ACTUALIZA PARA QUE LOS DATOS LOS COJA DE TABLA_CODIGOS_PEDIDO
        /// NUEVO
        /// esta funcion devuelve el orden del proceso para un pedido y un proceso dado
        /// ->input: número de pedido y codigo de aplicacion
        /// ->output: el orden de ese proceso , 0 si no se localiza en la tabla (y el total de procesos devuelto por valor)
        /// </summary>
        /// <param name="idPedido"></param>
        /// <param name="codigoAplicacion"></param>
        /// <param name="totalProcesos"></param>
        /// <param name="lugar"></param>
        /// <returns></returns>
        public async Task<DatosFasesDTO> DevuelveOrdenProcesos(int idPedido, int codigoAplicacion, int totalProcesos, string lugar = null)
        {
            var respuesta = new DatosFasesDTO();

            var query = _programadorLitalsaContext.TablaCodigosPedido
                .Where(o => o.Idpedido == idPedido && o.Idcodigoaplicacion == codigoAplicacion)
                .AsQueryable();

            if (!string.IsNullOrEmpty(lugar))
            {
                query = query.Where(o => o.Posicion == lugar);
            }

            var resultQuery = await query.FirstOrDefaultAsync(CancellationToken.None);

            if (resultQuery?.Fase != null)
            {
                respuesta.Fase = resultQuery.Fase.Value;
                respuesta.Cara = resultQuery.Posicion;
                respuesta.CodApli = codigoAplicacion;

                var datosPedido = await _programadorLitalsaContext.TablaCodigosPedido
                    .Where(o => o.Idpedido == idPedido)
                    .ToListAsync(CancellationToken.None);

                totalProcesos = datosPedido.Count;

                var primeraFase = datosPedido.Min(o => o.Fase).Value;

                if (respuesta.Fase == primeraFase)
                {
                    respuesta.CodApliAnterior = -1;
                }
                else
                {
                    respuesta.FaseAnterior = datosPedido
                        .Where(o => o.Fase < respuesta.Fase && o.Idpedido == idPedido)
                        .Max(o => o.Fase.Value);

                    var datosPedidoFaseAnterior = datosPedido
                        .FirstOrDefault(o => o.Fase == respuesta.FaseAnterior);

                    respuesta.CodApliAnterior = datosPedidoFaseAnterior.Idcodigoaplicacion.Value;
                    respuesta.CaraAnterior = datosPedidoFaseAnterior.Posicion;
                }

                //respuesta.FasePosterior = datosPedido
                //    .Where(o => o.Fase > respuesta.Fase && o.Idpedido == idPedido)
                //    .Min(o => o.Fase.Value);

                if (datosPedido.Any(o => o.Fase > respuesta.Fase && o.Idpedido == idPedido))
                {
                    respuesta.FasePosterior = datosPedido
                        .Where(o => o.Fase > respuesta.Fase && o.Idpedido == idPedido)
                        .Min(o => o.Fase.Value);
                }

                if (respuesta.FasePosterior != 0)
                {
                    var datosPedidoFasePosterior = datosPedido
                        .FirstOrDefault(o => o.Fase == respuesta.FasePosterior);

                    respuesta.CodApliPosterior = datosPedidoFasePosterior.Idcodigoaplicacion.Value;
                    respuesta.CaraPosterior = datosPedidoFasePosterior.Posicion;
                }

                if (respuesta.CodApliPosterior == 0 && totalProcesos > 0)
                {
                    respuesta.CodApliPosterior = -1;
                }
            }

            return respuesta;
        }

        public string DevuelvePosicionRayas(string cara, string observaciones)
        {
            var posicionRayas = "";
            var cadena = "";

            var isLitog = observaciones.Contains("LITOG");
            var isSinR = observaciones.Contains("SIN R");
            var isConR = observaciones.Contains("CON R");
            var isExterior = observaciones.Contains("EXTERIOR");
            var isInterior = observaciones.Contains("INTERIOR");
            var isCaraE = cara.StartsWith("e");
            var isCaraI = cara.StartsWith("i");

            if (isLitog && isSinR || isExterior && isSinR)
                posicionRayas = "I";
            else if (isLitog && isConR || isExterior && isConR)
                posicionRayas = "E";
            else if (isInterior && isSinR)
                posicionRayas = "E";
            else if (isInterior && isConR)
                posicionRayas = "I";

            if (!string.IsNullOrEmpty(posicionRayas))
            {
                cadena = posicionRayas switch
                {
                    "E" => isCaraE ? "POR RAYAS" : "POR LISA",
                    "I" => isCaraE ? "POR LISA" : "POR RAYAS",
                    _ => cadena
                };
            }

            return cadena;
        }
        public async Task<double> DevuelveDesarrollo(int idCliente, float formato, string tipoElemento, EEorden eeorden, string tipo = "")
        {
            Planoform formatoDb;
            try
            {
                double desarrollo = 0;
                //Valorar si usar por posibles errores por redondeo al perder precisión en la comparación del formato.
                //formatoDB = await _programadorLitalsaContext.Formato.SingleOrDefaultAsync(f => f.Cliente == idCliente && Math.Abs(f.Diametro - formato * 100) < 0.01 && f.Tipo == tipoElemento);

                if (!string.IsNullOrEmpty(eeorden.C109))
                {
                    formatoDb = await _datoLita01Context.Planoform
                        .SingleOrDefaultAsync(o => o.Id == eeorden.C109.Trim().Replace(" ", "").ToUpperInvariant());
                }
                else if (!string.IsNullOrEmpty(eeorden.C1) && !string.IsNullOrEmpty(eeorden.C11))
                {
                    formatoDb = await _datoLita01Context.Planoform.SingleOrDefaultAsync(f => f.Cliente == idCliente &&
                        Math.Abs((float)(f.Diametro * 100) - (float)(formato * 100)) < 0.01 &&
                        f.Tipo == tipoElemento && f.Resizda.ToString() == eeorden.C1.Replace(",", ".") &&
                        f.Resdcha.ToString() == eeorden.C11.Replace(",", "."));
                }
                else
                {
                    formatoDb = await _datoLita01Context.Planoform.SingleOrDefaultAsync(f => f.Cliente == idCliente &&
                        Math.Abs((float)(f.Diametro * 100) - (float)(formato * 100)) < 0.01 &&
                        f.Tipo == tipoElemento);
                }


                switch (tipo)
                {
                    case "":
                    case "D":
                        desarrollo = formatoDb?.Desarrollo ?? 0;
                        break;
                    case "R":
                        desarrollo = formatoDb?.Resizda ?? 0;
                        break;
                }

                return desarrollo;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                return 0;
            }
        }

        /// <summary>
        /// SI ES DE PUNTA O NO ->Calcular el ancho de los desarrollos multiplicando el formato* número de cpos
        /// para localizar el número de desarrollos hay que localizar en el plano el número después de la L
        /// y luego comparando con el ancho y alto de la hjlta.
        /// problema con IMF (10/2/07) aparece que son Cuerpos en el tipo de elemento pero realmente no lo son
        /// para corregirlo se evalua si despues de la letra clave L aparece un numero o no en el plano
        /// no funciona si reservas transversales
        /// </summary>
        /// <param name="idCliente"></param>
        /// <param name="plano"></param>
        /// <param name="tipoElemento"></param>
        /// <param name="formato"></param>
        /// <param name="eeorden"></param>
        /// <returns></returns>
        public async Task<double> DevuelveAnchuraDesarrollos(int idCliente, string plano, string tipoElemento, float formato, EEorden eeorden)
        {
            try
            {
                if (tipoElemento != "C" || string.IsNullOrEmpty(plano))
                    return -1;

                var index = plano.LastIndexOf("L") + 1;
                if (!int.TryParse(plano.Substring(index, 1), out var numCuerpos))
                    return -1;

                var desarrollo = await DevuelveDesarrollo(idCliente, formato, tipoElemento, eeorden);
                // Calculamos el ancho
                if (!(Math.Abs(desarrollo) > 0))
                    return 0;

                var anchoDesarrollo = (desarrollo * numCuerpos) + 3;
                return anchoDesarrollo;
            }
            catch
            {
                return -1;
            }
        }
        /// <summary>
        /// esta función devuelve la forma en la que se barniza un pedido de reservas longitudinales
        /// en primer lugar hace una búsqueda en la tabla Planos para verificar si está introducido como excepción
        /// si está definido la forma de tirar en dicha tabla se adjudica directamente, sino está definido
        /// se calcula el ancho de los desarrollos y se compara con el ancho y el largo de la hjlta, en el caso
        /// que el ancho de los desarrollos sea inferior al ancho y largo de la hjlta teóricamente se deberá barnizar
        /// de punta para tener mayor aprovechamiento de la hjlta.
        /// </summary>
        /// <param name="idCliente"></param>
        /// <param name="plano"></param>
        /// <param name="tipoElemento"></param>
        /// <param name="formato"></param>
        /// <param name="anchoHjlta"></param>
        /// <param name="largoHjlta"></param>
        /// <param name="idPedido"></param>
        /// <returns></returns>
        public async Task<string> DevuelveFormaBarnizado(int idCliente, string plano, string tipoElemento, float formato, float anchoHjlta, float largoHjlta, int idPedido)
        {
            var formaBarnizadoPlano =
                // Obtiene la forma de barnizado: si es nulo o no está devuelve "?"
                _programadorLitalsaContext.Plano
                .FirstOrDefault(o => o.Idcliente == idCliente && o.CodigoPlano == plano.Trim())?.FormaBarnizado;

            if (formaBarnizadoPlano != null)
            {
                switch (formaBarnizadoPlano)
                {
                    case "P":
                        return $"{Environment.NewLine}DE PUNTA";
                    case "A":
                        return "";
                    case "T":
                        return " TRANSVERSAL";
                }
            }

            // Si la forma de barnizado no está indicada en el plano y son reservas longitudinales
            // Lo hacemos a través del cálculo de los desarrollos y las anchuras de la hojalata.
            if (formaBarnizadoPlano == null && GetDatosPedido(idPedido, "Tb").Text == "L")
            {
                var datosEeorden = await _datoLita01Context.EEorden
                    .FirstOrDefaultAsync(o => o.Codigo == idPedido.ToString() && o.Pagina == "0"
                        , CancellationToken.None);
                var anchoDesarrollo = await DevuelveAnchuraDesarrollos(idCliente, plano, tipoElemento, formato, datosEeorden);

                if (!(anchoDesarrollo > 0))
                    // Copiamos tabla formatos
                    //NotificaError("Cierra todas las ventanas y copia desde el formulario de actualización la tabla de formatos. Luego vuelve a programar.");
                    return $"{Environment.NewLine}ERROR: REVISAR FORMA BARNIZADO: FORMATO NO DEFINIDO. ACTUALIZAR TABLA FORMATOS";
                if (anchoDesarrollo < anchoHjlta && anchoDesarrollo < largoHjlta)
                {
                    return $"{Environment.NewLine}DE PUNTA";
                }

                return "";
            }

            return "";
        }

        /// <summary>
        /// Public Function devuelve_hojas_mx(IdPedido As Long) As Long
        /// </summary>
        /// <param name="idPedido"></param>
        /// <returns></returns>
        public double DevuelveHojasMax(int idPedido)
        {
            double hojas;
            double hojasMaximas = 0;

            var idCliente = GetDatosPedido(idPedido, "Cliente")?.Num.Value ?? 0;

            switch (idCliente)
            {
                case 9:
                    hojas = GetDatosPedido(idPedido, "hojas").Num.Value;
                    hojasMaximas = (long)(hojas * 1.1);
                    break;

                case 84:
                    var plano = GetDatosPedido(idPedido, "pl").Text.Trim();

                    // ES UN PEDIDO TIPO ABBOTT
                    if (plano.StartsWith("100L3-11") || plano.StartsWith("100L3-12") ||
                        plano.StartsWith("127L2-11") || plano.StartsWith("127L2-20") ||
                        plano.StartsWith("100L3-14") || plano.StartsWith("100L3-15") ||
                        plano.StartsWith("127L2-21") ||
                        plano.StartsWith("100L3-14;15") || plano.StartsWith("127L2-20;21") ||
                        plano.StartsWith("ABBOT"))
                    {
                        var porcentajeMxAbbott = Convert.ToDouble(_programadorLitalsaContext.TablaCfg.FirstOrDefault(o => o.Iddato == "Porcentaje_max_abbott").Dato);
                        var porcentajeMnAbbott = Convert.ToDouble(_programadorLitalsaContext.TablaCfg.FirstOrDefault(o => o.Iddato == "Porcentaje_min_abbott").Dato);

                        hojas = DevuelveHojasMin(idPedido);

                        hojasMaximas = Math.Ceiling(((hojas / (1 + porcentajeMnAbbott)) * (1 + porcentajeMxAbbott)));
                    }

                    break;
            }

            return hojasMaximas;
        }

        public double DevuelveHojasMin(int idPedido)
        {
            double hojasMinimas = 0;
            double mermas = 0;
            var datosPedido = _programadorLitalsaContext.PedidoProcesado.FirstOrDefault(o => o.IdPedido == idPedido);
            var idCliente = datosPedido.IdCliente;
            double hojas = datosPedido.HojasPedido ?? 0;
            var observaciones = datosPedido.Obs1;
            string numero;
            int valorNumerico = 0;
            switch (idCliente)
            {
                case 2:
                case 3:
                    var pasesBarnizado = _programadorLitalsaContext.TablaCodigosPedido.Count(o => o.Idpedido == idPedido);
                    var numColores = DevuelveNumeroColores(idPedido);
                    var pases = pasesBarnizado + numColores;
                    mermas = hojas switch
                    {
                        >= 0 and <= 2000 => 14 * pases,
                        >= 2001 and <= 3400 => 18 * pases,
                        >= 3401 and <= 5600 => 25 * pases,
                        >= 5601 and <= 11500 => 27 * pases,
                        > 11500 => (long)(0.0035 * hojas * pases),
                        _ => mermas
                    };

                    hojasMinimas = hojas - mermas;
                    break;

                case 9:
                    //SE CAMBIA POR CONVERSACIÓN CON CHARO 27/02/2020 hojas_minimas = Hojas * 0.95
                    numero = DevuelveString(observaciones, "MINIMO DE", "HOJAS");
                    if (int.TryParse(numero, out valorNumerico))
                    {
                        hojasMinimas = valorNumerico;
                    }
                    break;

                case 100:
                    hojasMinimas = (long)(hojas * 0.97);
                    break;

                case 84:
                    var plano = GetDatosPedido(idPedido, "pl").Text.Trim();

                    if (plano.StartsWith("100L3-11") || plano.StartsWith("100L3-12") || plano.StartsWith("127L2-11") || plano.StartsWith("127L2-20") ||
                        plano.StartsWith("100L3-14") || plano.StartsWith("100L3-15") ||
                        plano.StartsWith("127L2-21") || plano.StartsWith("100L3-14;15") || plano.StartsWith("127L2-20;21") || plano.StartsWith("ABBOT"))
                    {
                        numero = DevuelveString(observaciones, "MINIMO DE", "HOJAS");
                        if (int.TryParse(numero, out valorNumerico))
                        {
                            hojasMinimas = valorNumerico;
                        }
                        else
                        {
                            numero = DevuelveString(observaciones, "MINIMO DE", "HJS");
                            if (int.TryParse(numero, out valorNumerico))
                            {
                                hojasMinimas = valorNumerico;
                            }
                        }
                    }
                    break;
            }

            return hojasMinimas;
        }

        /// <summary>
        /// Public Function devuelve_numero_colores(IdPedido As Long) As Integer
        /// </summary>
        /// <param name="idPedido"></param>
        /// <returns></returns>
        public int DevuelveNumeroColores(int idPedido)
        {
            var datosPedido = _programadorLitalsaContext.PedidoProcesado.FirstOrDefault(o => o.IdPedido == idPedido);
            if (datosPedido == null)
            {
                return 0;
            }

            int devuelveNumeroColores = 0;
            var type = datosPedido.GetType();

            // Se comprueba el estado de las tintas. 
            for (int i = 1; i <= 8; i++)
            {
                // Localiza el código de las tintas que estén en la posición i
                // y verifica si es distinto de 0 o no nulo

                var campoC0Ped = type.GetProperty($"C0{i}ped")?.GetValue(datosPedido);
                if (campoC0Ped == null)
                {
                    continue;
                }

                int codTintas = int.TryParse(campoC0Ped.ToString(), out int result) ? result : 0;

                // Si es así, busca el número de hojas procesadas y compáralas con el número de hojas del pedido
                if (codTintas == 0)
                {
                    continue;
                }

                devuelveNumeroColores += DColor(codTintas).StartsWith("2") ? 2 : 1;
            }
            return devuelveNumeroColores;
        }


        public string DColor(int numColor)
        {
            var codigoColor = numColor switch
            {
                0 => "",
                1 => "C",
                3 => "M",
                2 => "Y",
                4 => "K",
                900 => "B",
                9000 => "2B",
                _ => BuscarColorEspecial(numColor)
            };

            return codigoColor;
        }
        public string BuscarColorEspecial(int numColor)
        {
            //Codtintas bebe de a_articu así que la sustituimos por su verdadero origen
            var descripcionColor = _datoLita01Context.AArticu
                .Where(o => o.Estadis.Contains("TI") && o.Codigo2 == numColor.ToString())
                .Select(o => o.Descrip)
                .FirstOrDefault();

            if (string.IsNullOrEmpty(descripcionColor))
            {
                return "??";
            }

            return descripcionColor.StartsWith("2")
                ? $"2{descripcionColor.Substring(2, 2)}"
                : $"{descripcionColor.Substring(0, 2)}".ToLower();
        }
        /// <summary>
        /// Implementación de Public Function devuelve_string(texto_completo As String, entre As String, y As String) As String
        /// </summary>
        /// <param name="textoCompleto"></param>
        /// <param name="entre"></param>
        /// <param name="y"></param>
        /// <returns></returns>
        public string DevuelveString(string textoCompleto, string entre, string y)
        {
            var pos1 = textoCompleto.IndexOf(entre);
            if (pos1 == -1)
                return "0";

            var pos2 = textoCompleto.IndexOf(y, pos1 + entre.Length);
            if (pos2 == -1)
                return "0";

            var aux3 = textoCompleto.Substring(pos1 + entre.Length, pos2 - pos1 - entre.Length).Trim();
            return string.IsNullOrEmpty(aux3) ? "0" : aux3;
        }

        public string ObtenerMarcadoTamponExternalInternal(int idPedido, int idLinea, bool esPrimerProceso, int ordenProceso)
        {
            var resultado = "";
            var posicionTandem = _programadorLitalsaContext.Maquinas.FirstOrDefault(o => o.Idmaquina == idLinea)?.PosicionTandem ?? 0;

            // Se marca únicamente cuando es una línea normal (posicionTandem=0) y es el primer proceso
            // o bien cuando es el 1er o el 2º proceso y es la segunda máquina del tandem (posicionTandem=2)
            if ((esPrimerProceso && posicionTandem == 0) || (ordenProceso <= 20 && posicionTandem == 2))
            {
                var estadoPedido = GetTextoEstadoCodigosAplicacion(idPedido, true, true);

                if ((Regex.IsMatch(estadoPedido, ".*Int: ORO.*") && Regex.IsMatch(estadoPedido, ".*Ext: ORO.*")) ||
                    (Regex.IsMatch(estadoPedido, ".*Int: INCOL.*") && Regex.IsMatch(estadoPedido, ".*Ext: .*ENGANC.*")) ||
                    (Regex.IsMatch(estadoPedido, ".*Int: ORO.*") && Regex.IsMatch(estadoPedido, ".*Ext: .*DORADO.*")) ||
                    (Regex.IsMatch(estadoPedido, ".*Int: INCOL.*") && Regex.IsMatch(estadoPedido, ".*Ext: INCOL.*")) ||
                    (Regex.IsMatch(estadoPedido, ".*Int: PORCE.*") && Regex.IsMatch(estadoPedido, ".*Ext: .*ESMAL.*")))
                {
                    resultado = resultado + Environment.NewLine +
                                "MARCAR EN EL LATERAL CON TAMPÓN EXTERNAL/INTERNAL EN SALIDA.";
                }
            }

            return resultado;
        }

        public string AñadeObservacionesCodigosYBarnices(string codigoAplicacion, string idProducto, string idCliente)
        {
            var cadena = ConcatenaComentarios(codigoAplicacion, "CA") +
                         ConcatenaComentarios(idProducto, "BA") +
                         ConcatenaComentarios(idCliente, "CL");

            if (!string.IsNullOrEmpty(cadena))
                cadena = Environment.NewLine + cadena + Environment.NewLine;

            return cadena;
        }

        private string ConcatenaComentarios(string refAbuscar, string tipo)
        {
            var comentarios = _programadorLitalsaContext.TablaComentarios
                .Where(o => o.RefAbuscar == refAbuscar && o.Tipo == tipo && !o.Obsoleto)
                .Select(o => o.TextoAdevolver);

            return comentarios.Any() ? string.Join(",", comentarios) : string.Empty;
        }



        public string DevuelveObservacionesFlejado(int idPedido)
        {
            var cadena = "";

            var pedido = _programadorLitalsaContext.PedidoProcesado.FirstOrDefault(o => o.IdPedido == idPedido);
            var cliente = _programadorLitalsaContext.Clientes.FirstOrDefault(o => o.CodigoCliente == pedido.IdCliente.Value);
            var tipoPedido = pedido.TipoPedido;
            var idCliente = pedido.IdCliente.Value;
            var motivos = pedido.Motivos ?? "";
            var caracteristicasHojalata = GetDatosPedido(idPedido, "DevuelveCaracteristicasHojalata").Text;
            var plano = pedido.Plano;
            //MUCHA ATENCION AQUI ya que se miran las OBS1 y hay que vigilar si van bien informadas ya que en los procesos de actualización
            //no hacemos la ñapa del access de cargar y combinar estas obs con las obs de matped, e igual hay que hacer una busqueda adicional
            var observaciones = pedido.Obs1;

            // SE INCLUYE EN EL CASO QUE ESTÉ DEFINIDO ASÍ EN LA TABLA DE CLIENTES QUE SE COJA HOJA DE MUESTRA
            // Y LAS OBSERVACIONES DEL CAMPO OBSEMBALAJE
            if (cliente.HojaMuestra)
            {
                cadena = "COGER HOJA DE MUESTRA PARA CLIENTE.";
            }

            if (!string.IsNullOrEmpty(cliente.ObsEmbalaje))
            {
                cadena += $"{cliente.ObsEmbalaje}.";
            }

            // ***** RESTO DE CASOS ESPECIALES PARA INDICACIONES EN ÚLTIMO PROCESO ********

            // *** COGER 2 HOJAS DE MUESTRA PARA LA PARED ********
            if (DevuelveNumeroColores(idPedido) == 0 && DateTime.Now < DateTime.ParseExact("07/25/2018", "MM/dd/yyyy", CultureInfo.InvariantCulture).AddDays(20))
            {
                cadena += Environment.NewLine + "RECORDATORIO: COGER 2 HOJAS DE MUESTRA PARA LA PARED.";
            }

            switch (idCliente)
            {
                case 2:
                    // Si es un pedido de Auximara o Metalgrafica y último proceso, poner COLOCAR ETIQUETAS DE FLEJADO EN EL FRENTE.
                    cadena += Environment.NewLine + "COLOCAR ETIQUETAS DE FLEJADO EN EL FRENTE. ";
                    if (plano.Contains("T162L4-14", StringComparison.OrdinalIgnoreCase))
                    {
                        cadena += "TACOS PARALELOS A 790 ";
                    }
                    break;
                case 3:
                    // Si es un pedido de Auximara o Metalgrafica y último proceso, poner COLOCAR ETIQUETAS DE FLEJADO EN EL FRENTE.
                    cadena += Environment.NewLine + "COLOCAR ETIQUETAS DE FLEJADO EN EL FRENTE. ";
                    break;
                case 4 when tipoPedido == "L":
                    // Según reunión con Juan 07/06/2019, coger muestras adicionales de lito para AC.
                    // Según indicaciones nuevas de cliente 30/09/2020.
                    cadena += Environment.NewLine + "COGER 2 HOJAS DE MUESTRA ADICIONALES PARA LABO PARA ENVIO A CLIENTE. " + Environment.NewLine;
                    break;
                case 4 when (plano.StartsWith("T066B6-01") || plano.StartsWith("T153B3-01") || plano.StartsWith("T083B5-04")):
                    // 20/05/2022: POSICIONADO DE LOS PALETS
                    cadena += "OJO POSICION PALETS-TRANSVERSALES AL SCROLL.";
                    break;
                case 5:
                    cadena += "CLIENTE MUY DELICADO. ATENTOS A POSIBLES PORQUERÍAS EN LA SUPERFICIE DE LA HOJA, SI SE DETECTAN AVISAR.";
                    break;
                case 6 when pedido.ObservacionesPedido != null && pedido.ObservacionesPedido.Contains("NUEVO DISEÑO", StringComparison.OrdinalIgnoreCase):
                    // 07/06/2021 COLOCAR ETIQUETA AMARILLA SI ES UN NUEVO MODELO DE FRINSA
                    cadena += "MARCAR CON ETIQUETA AMARILLA ATT. DANIEL PATO. NUEVO DISEÑO.";
                    break;
                case 7:
                    // Si es un pedido de Combalia y último proceso, poner cartón entre palet y primera hoja.
                    // ********** AGITAR MUY BIEN EL BARNIZ - RECLAMACIÓN 2016-RC-241 ***************
                    cadena += "PONER CARTÓN ENTRE PALET Y PRIMERA HOJA. ";
                    cadena += "NO APILAR. MARCAR PAQUETES CON ETIQUETA AMARILLA: NO APILAR.";
                    break;
                case 44:
                    // Si es un pedido de Iberembal y último proceso, cliente 44, poner COLOCAR ETIQUETAS DE FLEJADO EN ESCUADRA.
                    cadena += "COLOCAR ETIQUETAS DE FLEJADO EN ESCUADRA. ";
                    break;
                case 62 when plano.StartsWith("T083B5-01"):
                case 62 when plano.StartsWith("T083B5-02"):
                    // *** 24/05/21: ***
                    cadena += "OJO POSICION PALETS-TRANSVERSALES AL SCROLL.";
                    break;
                case 76 when tipoPedido == "B" && !(plano.Contains("065*", StringComparison.OrdinalIgnoreCase)):
                    // Si es un pedido de Calvo de barnizado, hay que identificarlo con etiquetas amarillas.
                    cadena += "IDENTIFICAR CON ETIQUETA AMARILLA. ";
                    break;
                case 76 when caracteristicasHojalata.Trim().Contains("SCROLL", StringComparison.OrdinalIgnoreCase):
                    // Si es un pedido de Calvo y último proceso, cliente 76, poner MARCAR ESCUADRA
                    cadena += "MARCAR PINZA. ETIQUETAS DE FLEJADO EN PINZA. ";
                    break;
                //case 84 when (caracteristicasHojalata.Trim().Contains("21x899x943", StringComparison.OrdinalIgnoreCase) ||
                //              caracteristicasHojalata.Trim().Contains("23x950x803", StringComparison.OrdinalIgnoreCase) ||
                //              caracteristicasHojalata.Trim().Contains("21x950x899", StringComparison.OrdinalIgnoreCase) ||
                //              caracteristicasHojalata.Trim().Contains("18x864x943", StringComparison.OrdinalIgnoreCase)):
                //    // SEGÚN EMAIL DE MIGUEL MARÍN DEL DÍA 28/11/18
                //    // Y RECTIFICADO EL 11/12/18
                //    // Y VUELTO A RECTIFIAR EL 18/01/18
                //    // Y VUELTO A RECTIFICAR EL 30/09/20 SEGÚN EMAIL DE ISABEL ESPUELAS
                //    cadena += Environment.NewLine + "COGER HOJA DE MUESTRA PARA ALMACÉN, IDENTIFICAR LA HOJA CON EL TEXTO: '" + pedido.Supedido + " A RIOJA  ATT. ISABEL ESPUELAS.'";
                //    break;
                //case 84 when (observaciones.ToUpperInvariant().Contains("SEVILL", StringComparison.OrdinalIgnoreCase) || observaciones.ToUpperInvariant().Contains("OSUN", StringComparison.OrdinalIgnoreCase)):
                //    cadena += Environment.NewLine + "COGER HOJA DE MUESTRA PARA ALMACÉN, IDENTIFICAR LA HOJA CON EL TEXTO: '" + pedido.Supedido + " A OSUNA ATT. LIDIA TOLEDO.'";
                //    break;
                //case 84 when (observaciones.ToUpperInvariant().Contains("MERID", StringComparison.OrdinalIgnoreCase)):
                //    cadena += Environment.NewLine + "COGER HOJA DE MUESTRA PARA ALMACÉN, IDENTIFICAR LA HOJA CON EL TEXTO: '" + pedido.Supedido + " A MERIDA ATT. ROCIO MOLINA.'";
                //    break;

                // INICIO 16/06/25. Javi.
                // Si se trata de EVIOSYS:
                // - OSUNA || SEVILLA --> Si las dimensiones son de una hojalata para leches no Abbot, se añade comentario de envío a La Rioja también
                // - RIOJA --> solo a la rioja
                // - MÉRIDA --> solo a MÉRIDA
                case 84 when (observaciones.ToUpperInvariant().Contains("SEVILL", StringComparison.OrdinalIgnoreCase) || observaciones.ToUpperInvariant().Contains("OSUN", StringComparison.OrdinalIgnoreCase)):
                    // son leches NO abbot?
                    // esto se saca, actualmente, en base a las dimensiones de la hojalata
                    // no es fiable 100x100, pero de momento es lo que hay
                    // listado pasado por Carlos a 18/06/25
                    // "Esas son las dimensiones de los pedidos de leches de Eviosys que no son Abbott"
                    // DESTINO LARGO   ANCHO ESPESOR
                    // OSUNA   803 950 23
                    // RIOJA   902 950 19
                    // RIOJA   943 899 21
                    // RIOJA   899 950 21
                    // ASTURIAS    943 909 18
                    // RIOJA   896 950 19
                    // RIOJA   943 876 21
                    // RIOJA   943 864 18
                    var esLecheNoAbbot = (caracteristicasHojalata.Trim().Contains("23x950x803", StringComparison.OrdinalIgnoreCase)
                                          || caracteristicasHojalata.Trim().Contains("19x950x902", StringComparison.OrdinalIgnoreCase)
                                          || caracteristicasHojalata.Trim().Contains("21x899x943", StringComparison.OrdinalIgnoreCase)
                                          || caracteristicasHojalata.Trim().Contains("21x950x899", StringComparison.OrdinalIgnoreCase)
                                          || caracteristicasHojalata.Trim().Contains("18x909x943", StringComparison.OrdinalIgnoreCase)
                                          || caracteristicasHojalata.Trim().Contains("19x950x896", StringComparison.OrdinalIgnoreCase)
                                          || caracteristicasHojalata.Trim().Contains("21x876x943", StringComparison.OrdinalIgnoreCase)
                                          || caracteristicasHojalata.Trim().Contains("18x864x943", StringComparison.OrdinalIgnoreCase));

                    cadena += Environment.NewLine + "COGER HOJA DE MUESTRA PARA ALMACÉN, IDENTIFICAR LA HOJA CON EL TEXTO: '" + pedido.Supedido + " A OSUNA ATT. MOISÉS GIRALDEZ.'";
                    if (esLecheNoAbbot)
                        cadena += Environment.NewLine + "COGER HOJA DE MUESTRA PARA ALMACÉN, IDENTIFICAR LA HOJA CON EL TEXTO: '" + pedido.Supedido + " A RIOJA  ATT. ISABEL ESPUELAS.'";
                    break;
                case 84 when (observaciones.ToUpperInvariant().Contains("RIOJA", StringComparison.OrdinalIgnoreCase)):
                    cadena += Environment.NewLine + "COGER HOJA DE MUESTRA PARA ALMACÉN, IDENTIFICAR LA HOJA CON EL TEXTO: '" + pedido.Supedido + " A RIOJA  ATT. ISABEL ESPUELAS.'";
                    break;
                case 84 when (observaciones.ToUpperInvariant().Contains("MERID", StringComparison.OrdinalIgnoreCase)):
                    cadena += Environment.NewLine + "COGER HOJA DE MUESTRA PARA ALMACÉN, IDENTIFICAR LA HOJA CON EL TEXTO: '" + pedido.Supedido + " A MERIDA ATT. ROCIO MOLINA.'";
                    break;
                // FIN 16/06/25. Javi.
                case 90 when caracteristicasHojalata.Trim().Contains("966 x 819"):
                    // Si es un pedido de Ardagh Roye de 966*819 y último proceso, quitamos tabla aglomerado de Litalsa.
                    cadena += "QUITAR TABLA AGLOMERADO LITALSA. ";
                    break;
                case 91:
                    // Si es el último proceso y el cliente es Crown, buscamos el destino del material
                    cadena += Environment.NewLine + "COLOCAR ETIQUETAS DE FLEJADO EN ESCUADRA.";
                    break;
                case 100:
                    cadena += "OJO, VIGILAR POSIBLES HOJAS GIRADAS, HA HABIDO RECLAMACIONES.";
                    break;
            }



            // 23/06/22: PARA NESTLÉ BOUÉ, INCLUIR EN OBSERVACIONES CUANDO ES UN PEDIDO CON MOTIVOS DE TERRAPIN
            // QUE HASTA LA FECHA SON:
            // NESTLE S-26 GOLD 3 LWPB030C-1 TIN 6X800GR SWC MAT
            // NESTLE S-26 GOLD 2 LWP020-1 TIN 6X800GR SWC MAT
            // 08/03/23, SE ELIMINA POR INDICACIÓN DE DANIEL OLANO.
            // if (IdCliente == 12 && (Motivos.Contains("NESTLE S-26 GOLD 3 LWPB030C-1 TIN 6X800GR SWC MAT") || Motivos.Contains("NESTLE S-26 GOLD 2 LWP020-1 TIN 6X800GR SWC MAT")))
            // {
            //     cadena += Environment.NewLine + "LABO: AVISAR ALICIA O DANIEL PARA ENVÍO CERTIFICADO TERRAPIN.";
            // }

            // FIN 23/06/22


            // ********************************************
            // SEGÚN REUNIÓN CON CHARO Y CARLOS, TIENEN QUE APARECER PARA EL FLEJADO TODOS LOS COMENTARIOS QUE APAREZCAN EN OBSERVACIONES ENTRE ##
            // 13/09/2021
            // 25/10/21 SE CAMBIA POR < TEXTO >
            if (observaciones.Contains("<"))
            {
                cadena += Environment.NewLine + DevuelveString(observaciones, "<", ">");
            }


            var dimensionMax = Math.Max(pedido.LargoHjlta.Value, pedido.AnchoHjlta.Value);
            var dimensMin = Math.Min(pedido.LargoHjlta.Value, pedido.AnchoHjlta.Value);
            var datosFormato = _programadorLitalsaContext.TablaFormatosHjltaSh.FirstOrDefault(o =>
                o.Largo == dimensionMax && o.Ancho == dimensMin || o.Largo == dimensMin && o.Ancho == dimensionMax);
            // ************ 21/07/17: RETIRAR DOLITES ************
            // ************ 26/02/18: O MANTENERLOS PARA ENVIAR AL CLIENTE ************
            if (datosFormato is { Dolites: true })
            {
                cadena += datosFormato.EnviarDoliteCliente
                    ? "ENVIAR DOLITES CODIGO " + datosFormato.DenoMuestra + " A CLIENTE."
                    : "RETIRAR DOLITES CODIGO " + datosFormato.DenoMuestra + ".";
            }

            // ********************** SI HAY LIMITACIÓN DE PESO *********************************
            // p.e. si es IMF hay que hacer paquetes como máximo a 1100 kg
            // 06/06/2025 -- Javi al habla. Carlos vía Teams 'El comentario de "HACER PAQUETES COMO MÁXIMO A 1.000 HOJAS" hay que quitarlo, ya no hace falta'
            //double pesoMxPaquete = cliente.PesoMxpaquete ?? 0;
            //if (pesoMxPaquete != 0)
            //{
            //    var aux = pesoMxPaquete / (GetDatosPedido(idPedido, "PesoHoja")?.Double ?? 0);
            //    // Redondeamos la cantidad de hojas
            //    aux = 100 * Math.Floor(aux / 100);
            //    // si el número de hojas del pedido es mayor que el peso de los paquetes
            //    // se indica el número máximo de hojas de los paquetes
            //    if (aux < pedido.HojasPedido)
            //    {
            //        cadena += Environment.NewLine + "HACER PAQUETES COMO MÁXIMO A " + aux + " HOJAS. ";
            //    }
            //}

            // ********************** FIN SI HAY LIMITACIÓN DE PESO *********************************

            return cadena;

        }
        public float DevuelveBarnizNecesarioConCalculoDePeso(int idPedido, int idProducto, float gramaje, int hojasYaTiradas)
        {
            var datosPedido = _programadorLitalsaContext.PedidoProcesado.FirstOrDefault(o => o.IdPedido == idPedido);
            var datosProductos = _programadorLitalsaContext.TablaProductos.FirstOrDefault(o => o.Idproducto == idProducto);
            float kgNecesarios = 0;

            if (idProducto == 0 || gramaje == 0)
                return kgNecesarios;
            var sup = ((double)(datosPedido.HojasPedido - hojasYaTiradas) * datosPedido.AnchoHjlta * datosPedido.LargoHjlta) / 1000000;

            //Función adaptada: Public Function devuelve_peso_necesario(superficie As Double, peso As Single, Solidos As Single, Optional tipo_proceso As String) As Single
            //Pero nunca se llama en ningun otro lado ni se pasa el parametro opcional "tipo_proceso".
            //Por tanto dejamos esa parte comentada.
            //var tipoProceso = ""; // Proporciona el valor adecuado para el parámetro tipoProceso

            //if (tipoProceso.Contains("UNO Y UNO") || tipoProceso.Contains("DOS PASES"))
            //{
            //    sup *= 2;
            //}

            var peso2 = (datosProductos.Solidos != 0) ? (100 * sup * gramaje / (datosProductos.Solidos * 1000)) : 0;
            kgNecesarios = (float)(peso2 ?? 0);

            return kgNecesarios;
        }
        public async Task RenumerarProgramacionAsync(long desde, int idMaquina)
        {
            // CÓDIGO ANTERIOR COMENTADO - Lógica de renumeración hacia atrás
            // Cargar todos los registros que necesitamos
            //var tablaProgramacion = await _programadorLitalsaContext.TablaProgramacion
            //    .Where(x => x.Idlinea == idMaquina && x.Posicion > desde)
            //    .OrderBy(x => x.Posicion)
            //    .ToListAsync();

            //// Si no hay registros, salimos de la función
            //if (!tablaProgramacion.Any()) return;

            //// Calculamos las nuevas posiciones
            //var posicionInicial = (tablaProgramacion.First().Posicion / 10) * 10;
            //if (posicionInicial % 10 != 0)
            //    posicionInicial += 10;

            //var posicionFinal = posicionInicial + (tablaProgramacion.Count - 1) * 10;

            //// Renumeramos las posiciones y registramos cambios
            //for (var i = tablaProgramacion.Count - 1; i >= 0; i--)
            //{
            //    tablaProgramacion[i].Posicion = posicionFinal;
            //    posicionFinal -= 10;
            //}

            //// Guardar cambios en TablaProgramacion
            //await _programadorLitalsaContext.SaveChangesAsync();

            // NUEVA LÓGICA - Asignar posiciones múltiplos de 10 secuencialmente
            // Cargar todos los registros que necesitamos
            var tablaProgramacion = await _programadorLitalsaContext.TablaProgramacion
                .Where(x => x.Idlinea == idMaquina && x.Posicion > desde)
                .OrderBy(x => x.Posicion)
                .ToListAsync();

            // Si no hay registros, salimos de la función
            if (!tablaProgramacion.Any()) return;

            // Calcular la siguiente posición disponible múltiplo de 10 a partir de 'desde'
            var siguientePosicionMultiploDe10 = ((desde / 10) + 1) * 10;
            var posicionActual = siguientePosicionMultiploDe10;

            // Asignar posiciones secuenciales múltiplos de 10
            foreach (var item in tablaProgramacion)
            {
                item.Posicion = (int)posicionActual;
                posicionActual += 10; // Avanzar al siguiente múltiplo de 10
            }

            // Guardar cambios en TablaProgramacion
            await _programadorLitalsaContext.SaveChangesAsync();
        }

        public async Task GenerarDatosImpresionBarnizado(ImprimirPedidosDTO datosImpresion, bool esProgramacionPorPantalla, string nombreReporte, CancellationToken cancellationToken)
        {
            var maquina = datosImpresion.Maquina;
            var idLineaParameter = new SqlParameter("@IdLinea", SqlDbType.Int) { Value = maquina.Idmaquina };
            var posicionDesdeParameter = new SqlParameter("@PosicionDesde", SqlDbType.Int) { Value = datosImpresion.Posiciones.Min() };
            var posicionHastaParameter = new SqlParameter("@PosicionHasta", SqlDbType.Int) { Value = datosImpresion.Posiciones.Max() };
            var esProgramacionPorPantallaParameter = new SqlParameter("@esProgramacionPorPantalla", SqlDbType.Bit) { Value = esProgramacionPorPantalla };
            var nombreReporteParameter = new SqlParameter("@nombreReporte", SqlDbType.NVarChar) { Value = nombreReporte };

            var listadoImprimir = _programadorLitalsaContext.PedidoProgramadoEnviadoImprimirCombinado
                .FromSqlRaw("EXECUTE dbo.sp_GenerarDatosImpresionProgramacionBarnizado @IdLinea, @PosicionDesde, @PosicionHasta, @esProgramacionPorPantalla, @nombreReporte",
                    idLineaParameter, posicionDesdeParameter, posicionHastaParameter, esProgramacionPorPantallaParameter, nombreReporteParameter)
                .ToList();

            // Variables para calcular el campo Orden basándose en cambios en APLIPRODUCTO
            var orden = 1;
            string apliProductoAnterior = null;

            foreach (var item in listadoImprimir)
            {
                item.Id = null;
                item.TipoPedido = "Barnizado";
                item.ObsCalidad = !string.IsNullOrEmpty(item.ObsCalidad)
                    ? item.ObsCalidad
                    : await GetObservacionesCalidadLite(item.Idpedido, item.Idaplicacion);
                item.CaractHjlta =
                    GetDatosPedido(item.Idpedido, "DevuelveCaracteristicasHojalata").Text;
                item.MuestraSh = ObtenerDatosMuestraSH((double?)item.LargoHjlta, (double?)item.AnchoHjlta);
                item.TextoEstadoCodApli = await GetTextosEstadoCodApli(item.Idpedido, cancellationToken);
                if (item is { AnchoHjlta: not null, LargoHjlta: not null, HojasPedido: not null })
                    item.Sup = Math.Round(item.LargoHjlta.Value / (decimal)1000.0 * (item.AnchoHjlta.Value / (decimal)1000.0) * (item.HojasPedido.Value / (decimal)1000.0), 3);
                if (item.Idproducto != null)
                {
                    item.Tuberia = maquina.ProductosXsistema.ToUpperInvariant().Contains(item.Idproducto.Value.ToString().ToUpperInvariant());
                }

                var datosPedidoProgramado = await _programadorLitalsaContext.TablaProgramacion.FirstOrDefaultAsync(
                    o => o.Idpedido == item.Idpedido && o.Idaplicacion == item.Idaplicacion && o.Idproducto == item.Idproducto && o.Posicion == item.Posicion,
                    cancellationToken);

                item.Producto =
                    $"{_programadorLitalsaContext.TablaProductos.FirstOrDefault(o => o.Idproducto == item.Idproducto)?.Denominacion ?? ""} Peso: {datosPedidoProgramado.Peso} - {datosPedidoProgramado.PesoMin} g/m2";
                item.ApliProducto = await ObtenerTextoAplicacionYProducto(item.Idpedido, item.Idaplicacion, item.Idproducto, item.Posicion, maquina, cancellationToken);

                // Esto viene de un error de ordenamiento de las posiciones en la programación
                // El reporte ordena de manera que agrupa por APLIPRODUCTO y ORDEN, de manera que puede haber posiciones en la ORDEN 2, que sean mayores que posiciones en la ORDEN 3,
                // Aquí, como ya tenemos la cadena de apliproducto completa... podemos ordenar por ella debidamente.
                // Donde se asigna la orden antes de este fix, no tiene todos los valores y por eso falla.
                // Calcular el campo Orden basándose en cambios en APLIPRODUCTO
                // TODO: pendiente estudiar si con este FIX es necesario el campo orden en la tabla TablaProgramacion
                if (apliProductoAnterior != null && item.ApliProducto != apliProductoAnterior)
                    orden++;
                item.Orden = orden;
                apliProductoAnterior = item.ApliProducto;
            }

            _programadorLitalsaContext.PedidoProgramadoEnviadoImprimirCombinado.AddRange(listadoImprimir);

            // Crear registros en ProgramacionesPantalla si es programación por pantalla
            if (esProgramacionPorPantalla)
                await CrearRegistrosProgramacionesPantalla(listadoImprimir, cancellationToken);

            await _programadorLitalsaContext.SaveChangesAsync(cancellationToken);
        }

        public async Task GenerarDatosImpresionLitografia(ImprimirPedidosDTO datosImpresion, bool esProgramacionPorPantalla, string nombreReporte, CancellationToken cancellationToken)
        {
            Dictionary<string, string> _fieldMapping = new()
            {
                { "C01ped", "T01ext" },
                { "C02ped", "T02ext" },
                { "C03ped", "T03ext" },
                { "C04ped", "T04ext" },
                { "C05ped", "T05ext" },
                { "C06ped", "T06ext" },
                { "C07ped", "T07ext" },
                { "C08ped", "T08ext" },
                { "C09ped", "T09ext" },
                { "C10ped", "T10ext" },
                { "C11ped", "T11ext" },
                { "Cd1ped", "T01int" },
                { "Cd2ped", "T02int" },
                { "Cd3ped", "T03int" },
                { "Cd4ped", "T04int" }
            };

            var maquina = datosImpresion.Maquina;
            var idLineaParameter = new SqlParameter("@IdLinea", SqlDbType.Int) { Value = maquina.Idmaquina };
            var posicionDesdeParameter = new SqlParameter("@PosicionDesde", SqlDbType.Int) { Value = datosImpresion.Posiciones.Min() };
            var posicionHastaParameter = new SqlParameter("@PosicionHasta", SqlDbType.Int) { Value = datosImpresion.Posiciones.Max() };
            var esProgramacionPorPantallaParameter = new SqlParameter("@esProgramacionPorPantalla", SqlDbType.Bit) { Value = esProgramacionPorPantalla };
            var nombreReporteParameter = new SqlParameter("@nombreReporte", SqlDbType.NVarChar) { Value = nombreReporte };

            var listadoImprimir = _programadorLitalsaContext.PedidoProgramadoEnviadoImprimirCombinado
                .FromSqlRaw("EXECUTE dbo.sp_GenerarDatosImpresionProgramacionLitografia @IdLinea, @PosicionDesde, @PosicionHasta, @esProgramacionPorPantalla, @nombreReporte",
                    idLineaParameter, posicionDesdeParameter, posicionHastaParameter, esProgramacionPorPantallaParameter, nombreReporteParameter)
                .ToList();
            var listaIds = listadoImprimir.Select(o => o.Idpedido.ToString());
            var datosCabped = await _datoLita01Context.Cabped.Where(o => listaIds.Contains(o.Codigo))
                .ToListAsync(cancellationToken);

            // Variables para calcular el campo Orden basándose en cambios en APLIPRODUCTO
            var orden = 1;
            string apliProductoAnterior = null;

            foreach (var item in listadoImprimir)
            {
                item.Maculas = _programadorLitalsaContext.Database.SqlQuery<bool>($"SELECT dbo.fn_DevuelveGuardarMaculasLito({item.Idpedido})").AsEnumerable().First();

                item.Id = null;
                item.TipoPedido = "Litografia";
                var itemCabped = datosCabped.Find(o => o.Codigo == item.Idpedido.ToString());
                for (int i = 1; i <= 11; i++)//Asignar item.Ft01ext = itemCabped.Fortintaext1; hasta el 11
                {
                    var propName = $"Ft{i:D2}ext";
                    var itemCabpedProperty = itemCabped.GetType().GetProperty($"Fortintaext{i}");
                    var itemProperty = item.GetType().GetProperty(propName);

                    if (itemProperty != null && itemCabpedProperty != null)
                    {
                        itemProperty.SetValue(item, itemCabpedProperty.GetValue(itemCabped));
                    }
                }

                foreach (var pair in _fieldMapping)
                {
                    var cabpedProperty = typeof(Cabped).GetProperty(pair.Key);
                    var itemProperty = typeof(PedidoProgramadoEnviadoImprimirCombinado).GetProperty(pair.Value);

                    if (cabpedProperty == null || itemProperty == null)
                        continue;

                    var cabpedValue = cabpedProperty.GetValue(itemCabped);
                    if (cabpedValue == null)
                        continue;

                    var cabpedStr = cabpedValue.ToString();
                    if (string.IsNullOrEmpty(cabpedStr))
                        continue;
                    //Esto son tintas
                    var value = await _datoLita01Context.AArticu
                        .Where(o => o.Estadis.Contains("TI") && o.Codigo2 != null &&
                                    o.Codigo2 == cabpedStr).Select(o => $"{o.Codigo2}, {o.Descrip}")
                        .FirstOrDefaultAsync(cancellationToken);

                    itemProperty.SetValue(item, value);
                }

                //item.MinHojas = (int)DevuelveHojasMin(item.Idpedido);
                var plano = await _programadorLitalsaContext.Plano.
                    FirstOrDefaultAsync(o => o.Idcliente == item.IdCliente && o.CodigoPlano == item.Plano,
                        cancellationToken: cancellationToken);
                item.Escuadra = plano?.Escuadra ?? string.Empty;
                item.ObsCalidad = !string.IsNullOrEmpty(item.ObsCalidad)
                    ? item.ObsCalidad
                    : await GetObservacionesCalidadLite(item.Idpedido, item.Idaplicacion);
                item.CaractHjlta =
                    GetDatosPedido(item.Idpedido, "DevuelveCaracteristicasHojalata").Text;
                item.TextoEstadoCodApli = await GetTextosEstadoCodApli(item.Idpedido, cancellationToken);
                if (item is { AnchoHjlta: not null, LargoHjlta: not null, HojasPedido: not null })
                    item.Sup = Math.Round(item.LargoHjlta.Value / (decimal)1000.0 * (item.AnchoHjlta.Value / (decimal)1000.0) * (item.HojasPedido.Value / (decimal)1000.0), 3);
                var datosMatped = await _datoLita01Context.Matped
                    .Where(m => m.Nummpe == item.Idpedido && m.Tipo.StartsWith("PROCES"))
                    .Select(m => $"{m.Procesos ?? "EN BLANCO"} {m.Prompe} {m.Hojmpe} HJS. {m.Sitmpe} HOJAS COGIDAS ________")
                    .ToListAsync(cancellationToken);

                item.CogerDe = string.Join(Environment.NewLine, datosMatped);
                if (maquina.WetonWet)
                {
                    var apliProducto = new StringBuilder();
                    var tablaProgramacion = await _programadorLitalsaContext.TablaProgramacion.FirstOrDefaultAsync(
                        o => o.Idpedido == item.Idpedido && o.Idaplicacion == item.Idaplicacion && o.Idproducto == item.Idproducto,
                        cancellationToken);
                    if (item.TiposCambio.ToUpperInvariant().Contains("LAVAD"))
                    {
                        apliProducto.Append("LAVAR A: ");
                    }
                    if (tablaProgramacion.AplicacionSimultanea != null && tablaProgramacion.AplicacionSimultanea != 0)
                    {
                        apliProducto.Append($"{tablaProgramacion.AplicacionSimultanea} {tablaProgramacion.Idproducto} ");

                        // Aquí asumo que tienes acceso a la función devuelve_datos_productos
                        apliProducto.Append($"{_programadorLitalsaContext.TablaProductos.FirstOrDefault(o => o.Idproducto == tablaProgramacion.Idproducto).Denominacion} Peso: ");
                        apliProducto.Append($"{tablaProgramacion.PesoMin} - {tablaProgramacion.Peso} g/m2 T: ");
                        apliProducto.Append($"{tablaProgramacion.TemperaturaSecado}ºC Vmx:{tablaProgramacion.VelocidadMaxima} ");

                        // Aquí asumo que tienes acceso a la función devuelve_datos_maquina
                        if (maquina.ProductosXsistema.ToUpperInvariant().Contains(tablaProgramacion.Idproducto.ToString().ToUpperInvariant()))
                        {
                            apliProducto.Append("(TUBERIA) ");
                        }

                        // asumiendo que BarnizNecesario es de tipo decimal o similar
                        apliProducto.Append($"KG: {tablaProgramacion.BarnizNecesario:N0}");
                    }
                    item.FormaFlejado = tablaProgramacion.Flejar ? GetDatosPedido(item.Idpedido, "FormaFlejado").Text : " NO FLEJAR.";
                    item.ApliProducto = apliProducto.ToString();
                }

                // Esto viene de un error de ordenamiento de las posiciones en la programación
                // El reporte ordena de manera que agrupa por APLIPRODUCTO y ORDEN, de manera que puede haber posiciones en la ORDEN 2, que sean mayores que posiciones en la ORDEN 3,
                // Aquí, como ya tenemos la cadena de apliproducto completa... podemos ordenar por ella debidamente.
                // Donde se asigna la orden antes de este fix, no tiene todos los valores y por eso falla.
                // Calcular el campo Orden basándose en cambios en APLIPRODUCTO
                // TODO: pendiente estudiar si con este FIX es necesario el campo orden en la tabla TablaProgramacion
                if (apliProductoAnterior != null && item.ApliProducto != apliProductoAnterior)
                    orden++;
                item.Orden = orden;
                apliProductoAnterior = item.ApliProducto;
            }

            _programadorLitalsaContext.PedidoProgramadoEnviadoImprimirCombinado.AddRange(listadoImprimir);

            // Crear registros en ProgramacionesPantalla si es programación por pantalla
            if (esProgramacionPorPantalla)
                await CrearRegistrosProgramacionesPantalla(listadoImprimir, cancellationToken);

            await _programadorLitalsaContext.SaveChangesAsync(cancellationToken);
        }

        //public async Task<string> GetTextoLavadaImpresion(int? idLinea, int? idProducto, CancellationToken ct)
        //{
        //    var datosMaquina =
        //        await _programadorLitalsaContext.Maquinas.FirstOrDefaultAsync(o => o.Idmaquina == idLinea, ct);

        //    var tipoLimpieza = datosMaquina.ProductosXsistema.Contains(idProducto.ToString())?"(TUBERIA)":string.Empty;

        //}

        public int[] VolcaTintas2Vector(int idPedido)
        {
            var tintas = new int[12];
            var pedido = _programadorLitalsaContext.PedidoProcesado.FirstOrDefault(p => p.IdPedido == idPedido);

            if (pedido == null) return tintas;
            for (var i = 1; i <= 11; i++)
            {
                var codTintas = i < 10 ? $"C0{i}ped" : $"C{i}ped";
                var propiedad = pedido.GetType().GetProperty(codTintas);
                tintas[i] = propiedad != null ? Convert.ToInt32(propiedad.GetValue(pedido, null)) : 0;
            }
            return tintas;
        }

        private async Task<string> GetTextosEstadoCodApli(int idPedido, CancellationToken ct)
        {
            var textosCodApliPorCara = GetTratamientosByPedido(idPedido);
            var codigoTratamiento =
                await _programadorLitalsaContext.TablaTratamientos.FirstOrDefaultAsync(
                    o => o.DescripcionTratamiento == textosCodApliPorCara, ct);
            var res = codigoTratamiento != null
                ? $"{codigoTratamiento.CodigoTratamiento}: {GetTextoEstadoCodigosAplicacion(idPedido)}"
                : $"T????: {GetTextoEstadoCodigosAplicacion(idPedido)}";
            return res;
        }
        private string ObtenerDatosMuestraSH(double? largo, double? ancho)
        {
            var datos = _programadorLitalsaContext.TablaFormatosHjltaSh.FirstOrDefault(o =>
                o.YaCogida && o.YaBarnizada &&
                (o.Largo == largo && o.Ancho == ancho ||
                 o.Ancho == largo && o.Largo == ancho));

            return datos != null
                    ? $"SH: {datos.Idmuestra}"
                : string.Empty;

        }

        private async Task<string> ObtenerTextoAplicacionYProducto(int idPedido, int? idAplicacion, int? idProducto,
            int? posicion, MaquinaDTO maquina, CancellationToken ct)
        {
            var pedido =
                await _programadorLitalsaContext.TablaProgramacion.FirstOrDefaultAsync(o =>
                        o.Idpedido == idPedido &&
                        o.Idaplicacion == idAplicacion &&
                        o.Idproducto == idProducto &&
                        o.Posicion == posicion,
                    cancellationToken: ct);

            var aplicacion =
                //await _programadorLitalsaContext.CodApliAll.FirstOrDefaultAsync(o => o.Codbaz == idAplicacion, ct);
                await _programadorLitalsaContext.Codapli.FirstOrDefaultAsync(o => o.Codbaz == idAplicacion, ct);
            var tempProgramacion = pedido.TemperaturaSecado ?? 0;

            //Datos a obtener de nueva versión de cálculo de temperatura, usando tabla VelocidadesMaquina
            double temp = 0;
            double? vel = 0;

            if (maquina.PosicionTandem == 2 && pedido.Idproducto == 110146)
            {
                var datosVelMaq =
                    await GetTemperaturaSecadoV2(idProducto, idPedido, maquina.Idmaquina, ct);
                if (datosVelMaq != null)
                {
                    temp = (double)datosVelMaq.TempTiradaMq2;
                    vel = datosVelMaq.VelTiradaMq2;
                }
            }
            //Para asegurar que encontramos la velocidad de la maquina 1 del tandem, como ahora va por la tabla de velocidades
            //hay que simular que somos la posición 2 del tandem y obtener la velocidad de su registro que dice que es para la maquina 1 del tandem
            if (maquina.PosicionTandem == 1 &&
                _programadorLitalsaContext.VelocidadesMaquina.Any(o => idAplicacion.ToString().StartsWith(o.CodApliMq1Consulta.ToString())))
            {

                var progLineaSig = await _programadorLitalsaContext.TablaProgramacion
                    .Where(o => o.Idpedido == idPedido && o.Idlinea == maquina.Idmaquina + 1)
                    .OrderByDescending(o => o.Idprogramacion)
                    .FirstOrDefaultAsync(ct);

                if (progLineaSig != null && progLineaSig.Idproducto == 110146)
                {
                    var datosVelMaq =
                        await GetTemperaturaSecadoV2(progLineaSig.Idproducto, idPedido, maquina.Idmaquina + 1, ct);
                    if (datosVelMaq?.VelTiradaMq1 != null)
                    {
                        //temp = (double)datosVelMaq.TempTiradaMq2;
                        vel = (int)datosVelMaq.VelTiradaMq1;
                    }
                }
            }

            if (temp == 0)
            {
                temp = await GetTemperaturaSecado(idProducto, idPedido, maquina, ct);
            }
            if (vel == 0)
            {
                vel = await GetVelocidadMaxima(idProducto, maquina.Idmaquina, ct);
            }

            if (idAplicacion < 100100000) //SON APLICACIONES ESPECIALES SIN PRODUCTO SIN GRAMAJE
                return aplicacion.Txtbaz.Contains("SECADO")
                    ? $"{idAplicacion} SECADO T: {(temp != tempProgramacion ? tempProgramacion : temp)} ºC VMax: 6100"
                    : $"{idAplicacion} {aplicacion.Txtbaz}";//SON APLICACION ESPECIALES SIN BARNIZ NI GRAMAJE NI TEMPERATURA

            var denomProducto = _programadorLitalsaContext.TablaProductos
                .FirstOrDefault(o => o.Idproducto == idProducto)?.Denominacion ?? "";
            if (tempProgramacion != 0 && tempProgramacion != temp)
            {
                temp = tempProgramacion;
            }
            return $"{idAplicacion} {idProducto} {denomProducto} Peso: {pedido.PesoMin} - {pedido.Peso} g/m2 T: {temp} ºC VMax: {vel}";// APLICACIONES NORMALES
        }

        public async Task<SingleResult<Enums.TipoLimpias>> ConsultaLavadas(int productoPrevio, int productoActual, CancellationToken ct)
        {
            var result = new SingleResult<Enums.TipoLimpias>();
            var limpieza = await _programadorLitalsaContext.TblLimpiezas.FirstOrDefaultAsync(
                  o => o.DeIdProducto == productoPrevio && o.AidProducto == productoActual, ct);

            if (limpieza == null)
            {
                var datosIdProd =
                    await _programadorLitalsaContext.TablaProductos.FirstOrDefaultAsync(o => o.Idproducto == productoActual,
                        ct);
                var datosIdProd0 =
                    await _programadorLitalsaContext.TablaProductos.FirstOrDefaultAsync(o => o.Idproducto == productoPrevio,
                        ct);
                result.Data = Enums.TipoLimpias.NoExiste;
                result.Info = new List<string>
                        {
                            datosIdProd0.Idproducto.ToString(),
                            datosIdProd0.Denominacion,
                            datosIdProd.Idproducto.ToString(),
                            datosIdProd.Denominacion
                        };
                return result;
            }

            var tipoLimpia =
                await _programadorLitalsaContext.TblTipoLimpieza.FirstOrDefaultAsync(
                    o => o.TipoLimpieza == limpieza.TipoLimpieza, ct);
            //Si exite el tipo de limpia y el tiempo que da es uno de los valores de la enum, devuelvo el valor de la enum encontrado
            //si no devuelvo el valor de la enum "NoExiste"
            if (tipoLimpia is { Duracion: not null })
            {
                result.Data = Enum.IsDefined(typeof(Enums.TipoLimpias), (Enums.TipoLimpias)tipoLimpia.Duracion.Value)
                    ? (Enums.TipoLimpias)tipoLimpia.Duracion.Value
                    : Enums.TipoLimpias.NoExiste;
            }
            return result;
        }

        private async Task CrearRegistrosProgramacionesPantalla(List<PedidoProgramadoEnviadoImprimirCombinado> listadoImprimir, CancellationToken cancellationToken)
        {
            var fechaActual = DateTime.Now;

            // Obtener los IdProgramacion que ya existen en ProgramacionesPantalla para evitar duplicados
            var idsProgramacionExistentes = await _programadorLitalsaContext.ProgramacionesPantalla
                .Where(p => listadoImprimir.Select(li => li.Idprogramacion).Contains(p.IdProgramacion))
                .Select(p => p.IdProgramacion)
                .ToListAsync(cancellationToken);

            // Crear registros para los IdProgramacion que no existen
            var nuevosRegistros = listadoImprimir
                .Where(li => !idsProgramacionExistentes.Contains(li.Idprogramacion))
                .Select(li => new ProgramacionesPantalla
                {
                    IdProgramacion = li.Idprogramacion,
                    IdEstado = (int)Enums.ProgramacionesPantalla_EstadosPedido.SinEmpezar,
                    Rodillo = null,
                    NotaJefeTurno = null,
                    FechaCreacion = fechaActual,
                    FechaUltimaModificacion = fechaActual
                })
                .ToList();

            if (nuevosRegistros.Any())
            {
                _programadorLitalsaContext.ProgramacionesPantalla.AddRange(nuevosRegistros);
            }
        }
    }
}
