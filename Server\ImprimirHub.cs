﻿using DevExpress.CodeParser;
using DevExpress.XtraPrinting.Native.Lines;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Nelibur.ObjectMapper;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.DatoLita01;
using ProgramadorGeneralBLZ.Server.Models.InterfaseBD;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;
using System.Collections.Concurrent;
using System.Data;
using System.Data.SqlTypes;
using System.IdentityModel.Claims;
using System.ServiceModel.Channels;
using System.Threading;

namespace ProgramadorGeneralBLZ.Server;

using DevExpress.XtraRichEdit.Import.Html;
using Shared.ResponseModels;
using System.ComponentModel.DataAnnotations;
using static System.Runtime.InteropServices.JavaScript.JSType;

public class ImprimirHub(ImprimirBackgroundService imprimirService) : Hub
{
    public override async Task OnConnectedAsync()
    {
        //// Agrega el cliente cuando se conecta
        //imprimirService.AddClient(Context.ConnectionId);
        //await base.OnConnectedAsync();

        // Por ejemplo, si el cliente inicia sin filtro, por defecto se asigna el primer ID de la tabla Maquinas
        await Groups.AddToGroupAsync(Context.ConnectionId, "-1");
        imprimirService.AddClient(Context.ConnectionId);
        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        //// Remueve el cliente cuando se desconecta
        //imprimirService.RemoveClient(Context.ConnectionId);
        //await base.OnDisconnectedAsync(exception);
        // Remover el cliente de todos los grupos (o del grupo correspondiente si lo controlas en una variable)
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, "-1"); // ejemplo por defecto
        imprimirService.RemoveClient(Context.ConnectionId);
        await base.OnDisconnectedAsync(exception);
    }

    private async Task NotificarAlGrupo(string tipoMensaje, int idLinea, PrimitiveResultHub result)
    {
        await Clients.GroupExcept(idLinea.ToString(), Context.ConnectionId).SendAsync(tipoMensaje, result);
    }

    private async Task NotificarClienteEmisor(string tipoMensaje, PrimitiveResultHub result)
    {
        await Clients.Caller.SendAsync(tipoMensaje, result);
    }

    public async Task SendProgramacionActualizada(int idLinea, string nombreReporte, string nombreUsuario)
    {
        try
        {
            // Obtener datos para los receptores
            var resultReceptores = await imprimirService.GetPedidosProgramacionPantallaPorIdLinea(idLinea, nombreReporte, nombreUsuario);
            var resultEmisor = new SingleResultHub<int> { Data = 0 };

            if (!resultReceptores.HasErrors)
            {
                // 2. Registrar mensaje en la base de datos para cada programación
                var mensajeHub = new ProgramacionesPantallaLogDTO
                {
                    IdProgramacion = null, // Publicación general, no específica de un idProgramacion
                    IdLinea = idLinea, // Informar IdLinea cuando es ProgramacionActualizada
                    Mensaje = "Se ha actualizado la programación.",
                    IdUsuario = nombreUsuario,
                    IdTipoLog = (int)Enums.ProgramacionesPantalla_TiposLog.ProgramacionActualizada,
                    Fecha = DateTime.Now,
                    Extra = $"Reporte: {nombreReporte}",
                    ValorAnterior = null,
                    ValorNuevo = null
                };
                var resultRegistro = await imprimirService.RegistrarLog(mensajeHub);

                // Verificar si hubo errores durante el registro del mensaje
                if (resultRegistro.HasErrors)
                {
                    // Si hay errores en el registro, copiar los errores y notificar al emisor
                    resultEmisor.Errors = resultRegistro.Errors;
                    await NotificarClienteEmisor(TiposMensajesImprimirHub.ProgramacionActualizada_Emisor, resultEmisor);
                    return; // Salir sin continuar el flujo
                }

                // 3. Crear mensaje para el hub y avisar a receptores
                resultReceptores.Info.Add(resultRegistro.Data ?? mensajeHub);
                await NotificarAlGrupo(
                    TiposMensajesImprimirHub.ProgramacionActualizada_Receptores,
                    idLinea,
                    resultReceptores);

                // 4. Avisar a cliente emisor de que se ha publicado la programación
                resultEmisor.Data = 1;
                resultEmisor.Info.Add(resultRegistro.Data ?? mensajeHub);
                await NotificarClienteEmisor(TiposMensajesImprimirHub.ProgramacionActualizada_Emisor, resultEmisor);
            }
            else
            {
                // 4. Avisar a cliente de que ha habido un error en la publicación de la programación
                resultEmisor.Errors = resultReceptores.Errors;
                await NotificarClienteEmisor(TiposMensajesImprimirHub.ProgramacionActualizada_Emisor, resultEmisor);
            }
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: SendProgramacionActualizada - {(!string.IsNullOrEmpty(ex.InnerException?.Message) ? ex.InnerException?.Message : ex.Message)}";
            throw new HubException(errorText);
        }
    }

    public async Task SendAbandonarGrupo(string nombreUsuario)
    {
        try
        {
            var connectionId = Context.ConnectionId;

            // Obtener idLinea anterior y remover del grupo si fuera necesario
            int idLineaAnterior;
            bool abandonarGrupo = false;
            if (imprimirService.GetIdLineaAnterior(connectionId, out idLineaAnterior))
                abandonarGrupo = true;

            if (abandonarGrupo)
            {
                await Groups.RemoveFromGroupAsync(connectionId, idLineaAnterior.ToString());
                imprimirService.UpdateClient(connectionId, -1);
            }

            // avisamos al emisor
            var result = new SingleResultHub<int>()
            {
                Data = 1,
                Info = new List<ProgramacionesPantallaLogDTO>()
                {
                    new ProgramacionesPantallaLogDTO()
                    {
                        IdUsuario = nombreUsuario,
                        Mensaje = abandonarGrupo ? $"Grupo {idLineaAnterior} abandonado correctamente." : string.Empty,
                        Fecha = DateTime.Now
                    }
                }
            };
            await NotificarClienteEmisor(TiposMensajesImprimirHub.GrupoAbandonado, result);
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: SendAbandonarGrupo - {(!string.IsNullOrEmpty(ex.InnerException?.Message) ? ex.InnerException?.Message : ex.Message)}";
            throw new HubException(errorText);
        }
    }

    public async Task SendUnirseAGrupo(string nombreUsuario, int idLinea)
    {
        try
        {
            var connectionId = Context.ConnectionId;
            await Groups.AddToGroupAsync(connectionId, idLinea.ToString());
            imprimirService.UpdateClient(connectionId, idLinea);

            // avisamos al emisor
            var result = new SingleResultHub<int>()
            {
                Data = 1,
                Info = new List<ProgramacionesPantallaLogDTO>()
                {
                    new ProgramacionesPantallaLogDTO()
                    {
                        IdUsuario = nombreUsuario,
                        Mensaje = $"Usuario unido al grupo {idLinea} con éxito.",
                        Fecha = DateTime.Now
                    }
                }
            };
            await NotificarClienteEmisor(TiposMensajesImprimirHub.UnidoANuevoGrupo, result);
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: SendUnirseAGrupo - {(!string.IsNullOrEmpty(ex.InnerException?.Message) ? ex.InnerException?.Message : ex.Message)}";
            throw new HubException(errorText);
        }
    }

    public async Task SendIdLinea(int idLinea, string nombreUsuario)
    {
        try
        {
            // ejecutamos query y mandamos mensajes a emisor
            var result = await imprimirService.GetPedidosProgramacionPantallaPorIdLinea(idLinea, null, nombreUsuario);
            await NotificarClienteEmisor(TiposMensajesImprimirHub.IdLineaActualizada_Emisor, result);
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: SendIdLinea - {(!string.IsNullOrEmpty(ex.InnerException?.Message) ? ex.InnerException?.Message : ex.Message)}";
            throw new HubException(errorText);
        }
    }

    public async Task SendUpdatePedido(PedidoProgramacionEnPantallaDTO dto, Enums.ProgramacionesPantalla_EstadosPedido? nuevoEstado, string campo, string nuevoValor, int idLinea, string nombreUsuario)
    {
        try
        {
            // 1. Actualizar datos en BD - ahora devuelve el DTO actualizado y el mensaje de log
            var resultEmisor = await imprimirService.ActualizarPedido(dto, nuevoEstado, campo, nuevoValor, idLinea, nombreUsuario);

            // 2. Si hay error durante actualización, avisamos a emisor
            if (resultEmisor.HasErrors)
            {
                await NotificarClienteEmisor(TiposMensajesImprimirHub.PedidoActualizado_Emisor, resultEmisor);
                return;
            }

            // 3. Registrar el mensaje de log que viene de ActualizarPedido
            if (resultEmisor.Info.Any())
            {
                var mensajeLog = resultEmisor.Info.First();
                await imprimirService.RegistrarLog(mensajeLog);
            }

            // 4. Avisar al emisor (el DTO ya viene actualizado y el mensaje ya está en Info)
            await NotificarClienteEmisor(TiposMensajesImprimirHub.PedidoActualizado_Emisor, resultEmisor);

            // 5. Obtener datos actualizados para los receptores
            var resultReceptores = await imprimirService.GetPedidosProgramacionPantallaPorIdLinea(idLinea, null, nombreUsuario);
            if (!resultReceptores.HasErrors)
            {
                // 6. Agregar el mensaje de log a los receptores (reutilizar el mismo mensaje)
                if (resultEmisor.Info.Any())
                    resultReceptores.Info.Add(resultEmisor.Info.First());

                // 7. Notificar a otros clientes del grupo
                await NotificarAlGrupo(TiposMensajesImprimirHub.PedidoActualizado_Receptores,
                    idLinea,
                    resultReceptores);

                // 8. Crear resultado para el emisor (confirmación de que ha llegado a receptores)
                var resultEmisor03 = new SingleResultHub<int> { Data = 1 };
                await NotificarClienteEmisor(TiposMensajesImprimirHub.PedidoPublicado_Emisor, resultEmisor03);
            }
            else
            {
                // 8. Si hay errores al obtener datos, notificar al emisor
                var resultEmisor04 = new SingleResultHub<int> { Data = 0 };
                resultEmisor04.Errors = resultReceptores.Errors;
                await NotificarClienteEmisor(TiposMensajesImprimirHub.PedidoPublicado_Emisor, resultEmisor04);
            }
        }
        catch (Exception ex)
        {
            var errorText = $"ERROR: SendUpdatePedido - {(!string.IsNullOrEmpty(ex.InnerException?.Message) ? ex.InnerException?.Message : ex.Message)}";
            throw new HubException(errorText);
        }
    }
}

public class ImprimirBackgroundService
{
    private readonly IHubContext<ImprimirHub> _hubContext;
    private readonly IServiceScopeFactory _scopeFactory;
    private readonly string _connectionString;
    private readonly Dictionary<string, int> _idLineaPorCliente = new();

    public ImprimirBackgroundService(IHubContext<ImprimirHub> hubContext, IServiceScopeFactory scopeFactory, IConfiguration configuration)
    {
        _hubContext = hubContext;
        _scopeFactory = scopeFactory;
        _connectionString = configuration.GetConnectionString("DefaultConnectionProgramador");
        // No se inicia el timer hasta tener clientes conectados
    }

    public void AddClient(string connectionId)
    {
        if (_idLineaPorCliente.TryAdd(connectionId, -1))
        {
            // Se puede definir un valor inicial para el filtro, por ejemplo 0
        }

        //StartTimerIfNeeded();
    }

    public void RemoveClient(string connectionId)
    {
        _idLineaPorCliente.Remove(connectionId);
        if (_idLineaPorCliente.Count == 0)
        {
            //StopTimer();
        }
    }

    public void UpdateClient(string connectionId, int idLinea)
    {
        if (_idLineaPorCliente.ContainsKey(connectionId))
            _idLineaPorCliente[connectionId] = idLinea;
    }

    public bool GetIdLineaAnterior(string connectionId, out int idLineaAnterior)
    {
        idLineaAnterior = -1;
        return _idLineaPorCliente.TryGetValue(connectionId, out idLineaAnterior);
    }

    public async Task<ListResultHub<PedidoProgramacionEnPantallaDTO>> GetPedidosProgramacionPantallaPorIdLinea(int idLinea, string nombreReporte, string usuario)
    {
        var result = new ListResultHub<PedidoProgramacionEnPantallaDTO>()
        {
            Data = [],
            Errors = []
        };

        try
        {
            using var scope = _scopeFactory.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<ProgramadorLitalsaContext>();

            var respSp = await dbContext
                .Set<PedidoProgramacionEnPantallaDTO>()
                .FromSqlRaw("[dbo].[sp_PedidosProgramados] @IdMaquinaParameter, @PosicionParameter, @NombreReporte, @EsTipoProgramacionPorPantalla",
                    new SqlParameter("@IdMaquinaParameter", SqlDbType.Int) { Value = idLinea },
                    new SqlParameter("@PosicionParameter", SqlDbType.Int) { Value = 0 },
                    new SqlParameter("@NombreReporte", SqlDbType.NVarChar) { Value = (object)nombreReporte ?? DBNull.Value },
                    new SqlParameter("@EsTipoProgramacionPorPantalla", SqlDbType.Bit) { Value = true })
                .ToListAsync();
            result.Data = respSp.ToList();
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: GetPedidosProgramacionPantallaPorIdLinea - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            var mensajeError = new ProgramacionesPantallaLogDTO
            {
                IdUsuario = "Sistema",
                Mensaje = errorText,
                Fecha = DateTime.Now
            };
            result.Errors.Add(mensajeError);
        }

        return result;
    }

    public async Task<SingleResultHub<PedidoProgramacionEnPantallaDTO>> ActualizarPedido(PedidoProgramacionEnPantallaDTO dto, Enums.ProgramacionesPantalla_EstadosPedido? nuevoEstado, string campo, string nuevoValor, int idLinea, string usuario)
    {
        var result = new SingleResultHub<PedidoProgramacionEnPantallaDTO>
        {
            Errors = [],
            Data = dto
        };

        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();
            using var transaction = connection.BeginTransaction();

            try
            {
                // 1. Validar estado si se proporcionó
                if (nuevoEstado.HasValue)
                {
                    var strQuery = "SELECT COUNT(*) FROM ProgramacionesPantalla_EstadosPedido WHERE IdEstado = @idEstado AND Activo = 1";
                    using var sqlCommand01 = new SqlCommand(strQuery, connection, transaction);
                    sqlCommand01.Parameters.Add(new SqlParameter("@idEstado", SqlDbType.Int) { Value = (int)nuevoEstado.Value });

                    var estadoValido = (int)await sqlCommand01.ExecuteScalarAsync();
                    if (estadoValido == 0)
                    {
                        var mensajeError = new ProgramacionesPantallaLogDTO
                        {
                            IdUsuario = "Sistema",
                            Mensaje = $"Estado {(int)nuevoEstado.Value} no válido o inactivo.",
                            Fecha = DateTime.Now
                        };
                        result.Errors.Add(mensajeError);
                        transaction.Rollback();
                        return result;
                    }
                }

                // 2. Obtener el estado actual de la programación desde ProgramacionesPantalla
                var estadoActualQuery = @"
                        SELECT IdEstado, Rodillo, NotaJefeTurno, IdProgramacion
                        FROM ProgramacionesPantalla
                        WHERE IdProgramacion = @idProgramacion";

                var sqlCommandEstado = new SqlCommand(estadoActualQuery, connection, transaction);
                sqlCommandEstado.Parameters.AddWithValue("@idProgramacion", dto.IdProgramacion);

                var estadoActual = new { IdEstado = 1, Rodillo = "", NotaJefeTurno = "" };
                bool existeRegistro = false;

                using (var reader = await sqlCommandEstado.ExecuteReaderAsync())
                {
                    if (reader.Read())
                    {
                        existeRegistro = true;
                        estadoActual = new
                        {
                            IdEstado = reader.GetInt32("IdEstado"),
                            Rodillo = reader.IsDBNull("Rodillo") ? "" : reader.GetString("Rodillo"),
                            NotaJefeTurno = reader.IsDBNull("NotaJefeTurno") ? "" : reader.GetString("NotaJefeTurno")
                        };
                    }
                }

                // 3. Determinar los nuevos valores
                var nuevoIdEstado = nuevoEstado.HasValue ? (int)nuevoEstado.Value : estadoActual.IdEstado;
                var nuevoRodillo = campo == "Rodillo" ? nuevoValor : estadoActual.Rodillo;
                var nuevaNotaJefeTurno = campo == "NotaJefeTurno" ? nuevoValor : estadoActual.NotaJefeTurno;

                // 4. Si no existe registro... puede ser que sea porque Carlos ha eliminado dicha programación de la tabla y no le ha dado a publicar aún.
                if (!existeRegistro)
                {
                    var mensajeError = new ProgramacionesPantallaLogDTO
                    {
                        IdUsuario = "Sistema",
                        Mensaje = "Contactar con oficinas para confirmar si la posición ha sido eliminada, y no se ha publicado aún.",
                        Fecha = DateTime.Now
                    };
                    result.Errors.Add(mensajeError);
                    return result;
                }

                // 5. Actualizar registro existente en ProgramacionesPantalla
                var updateProgramacionQuery = @"
                            UPDATE ProgramacionesPantalla
                            SET IdEstado = @idEstado,
                                Rodillo = @rodillo,
                                NotaJefeTurno = @notaJefeTurno,
                                FechaUltimaModificacion = @fecha
                            WHERE IdProgramacion = @idProgramacion";

                var sqlCommandUpdate = new SqlCommand(updateProgramacionQuery, connection, transaction);
                sqlCommandUpdate.Parameters.AddWithValue("@idProgramacion", dto.IdProgramacion);
                sqlCommandUpdate.Parameters.AddWithValue("@idEstado", nuevoIdEstado);
                sqlCommandUpdate.Parameters.AddWithValue("@rodillo", !string.IsNullOrEmpty(nuevoRodillo) ? nuevoRodillo : (object)DBNull.Value);
                sqlCommandUpdate.Parameters.AddWithValue("@notaJefeTurno", !string.IsNullOrEmpty(nuevaNotaJefeTurno) ? nuevaNotaJefeTurno : (object)DBNull.Value);
                sqlCommandUpdate.Parameters.AddWithValue("@fecha", DateTime.Now);
                await sqlCommandUpdate.ExecuteNonQueryAsync();

                // 6. Actualizar DTO y crear mensaje de log según el tipo de cambio
                int idTipoLog = (int)Enums.ProgramacionesPantalla_TiposLog.ProgramacionActualizada;
                string valorAnterior = "";
                string valorNuevo = "";
                string mensajeDescriptivo = "";

                if (nuevoEstado.HasValue)
                {
                    idTipoLog = (int)Enums.ProgramacionesPantalla_TiposLog.CambioEstado;
                    valorAnterior = estadoActual.IdEstado.ToString();
                    valorNuevo = nuevoIdEstado.ToString();

                    // Verificar si hay cambio real
                    if (valorAnterior == valorNuevo)
                    {
                        // No hay cambio real, no generar log
                        transaction.Commit();
                        return result;
                    }

                    // Actualizar DTO
                    result.Data.IdEstado = nuevoIdEstado;
                    result.Data.NombreEstado = nuevoEstado.GetAttribute<DisplayAttribute>().Name;

                    // Crear mensaje usando Display Names
                    var tipoCambio = (Enums.ProgramacionesPantalla_TiposLog)idTipoLog;
                    var nombreTipoCambio = tipoCambio.GetAttribute<DisplayAttribute>().Name;
                    var estadoAnterior = (Enums.ProgramacionesPantalla_EstadosPedido)estadoActual.IdEstado;
                    var estadoAnteriorNombre = estadoAnterior.GetAttribute<DisplayAttribute>().Name;
                    var estadoNuevoNombre = nuevoEstado.Value.GetAttribute<DisplayAttribute>().Name;

                    mensajeDescriptivo = $"{nombreTipoCambio}: {estadoAnteriorNombre} → {estadoNuevoNombre}";
                }
                else if (campo == "Rodillo")
                {
                    idTipoLog = (int)Enums.ProgramacionesPantalla_TiposLog.ActualizacionRodillo;

                    var valorAnteriorNormalizado = string.IsNullOrWhiteSpace(estadoActual.Rodillo) ? null : estadoActual.Rodillo.Trim();
                    var valorNuevoNormalizado = string.IsNullOrWhiteSpace(nuevoRodillo) ? null : nuevoRodillo.Trim();
                    valorAnterior = valorAnteriorNormalizado;
                    valorNuevo = valorNuevoNormalizado;

                    // Verificar si hay cambio real 
                    if (string.Equals(valorAnterior, valorNuevo, StringComparison.Ordinal))
                    {
                        // No hay cambio real, no generar log
                        transaction.Commit();
                        return result;
                    }

                    // Actualizar DTO
                    result.Data.Rodillo = nuevoRodillo;

                    // Crear mensaje usando Display Name
                    var tipoCambio = (Enums.ProgramacionesPantalla_TiposLog)idTipoLog;
                    var nombreTipoCambio = tipoCambio.GetAttribute<DisplayAttribute>().Name;

                    // Manejar diferentes casuísticas para el mensaje
                    if (valorAnterior == null)
                    {
                        mensajeDescriptivo = $"{nombreTipoCambio}: (vacío) → {valorNuevo}";
                    }
                    else if (valorNuevo == null)
                    {
                        mensajeDescriptivo = $"{nombreTipoCambio}: {valorAnterior} → (vacío)";
                    }
                    else
                    {
                        mensajeDescriptivo = $"{nombreTipoCambio}: {valorAnterior} → {valorNuevo}";
                    }
                }
                else if (campo == "NotaJefeTurno")
                {
                    idTipoLog = (int)Enums.ProgramacionesPantalla_TiposLog.ActualizacionNota;

                    // Normalizar valores: trim y convertir a null si está vacío o solo espacios
                    var valorAnteriorNormalizado = string.IsNullOrWhiteSpace(estadoActual.NotaJefeTurno) ? null : estadoActual.NotaJefeTurno.Trim();
                    var valorNuevoNormalizado = string.IsNullOrWhiteSpace(nuevaNotaJefeTurno) ? null : nuevaNotaJefeTurno.Trim();

                    valorAnterior = valorAnteriorNormalizado;
                    valorNuevo = valorNuevoNormalizado;

                    // Verificar si hay cambio real (comparación que maneja null correctamente)
                    if (string.Equals(valorAnterior, valorNuevo, StringComparison.Ordinal))
                    {
                        // No hay cambio real, no generar log
                        transaction.Commit();
                        return result;
                    }

                    // Actualizar DTO
                    result.Data.NotaJefeTurno = nuevaNotaJefeTurno;

                    // Crear mensaje usando Display Name
                    var tipoCambio = (Enums.ProgramacionesPantalla_TiposLog)idTipoLog;
                    var nombreTipoCambio = tipoCambio.GetAttribute<DisplayAttribute>().Name;

                    // Manejar diferentes casuísticas para el mensaje
                    if (valorAnterior == null)
                    {
                        mensajeDescriptivo = $"{nombreTipoCambio}: (vacío) → {valorNuevo}";
                    }
                    else if (valorNuevo == null)
                    {
                        mensajeDescriptivo = $"{nombreTipoCambio}: {valorAnterior} → (vacío)";
                    }
                    else
                    {
                        mensajeDescriptivo = $"{nombreTipoCambio}: {valorAnterior} → {valorNuevo}";
                    }
                }

                // 7. Agregar el mensaje de log al resultado (para que SendUpdatePedido lo registre)
                var logEntry = new ProgramacionesPantallaLogDTO
                {
                    IdUsuario = usuario,
                    Mensaje = mensajeDescriptivo,
                    Fecha = DateTime.Now,
                    IdProgramacion = dto.IdProgramacion,
                    IdTipoLog = idTipoLog,
                    ValorAnterior = valorAnterior,
                    ValorNuevo = valorNuevo,
                    Extra = $"Pedido {dto.Idpedido} - Posicion {dto.Posicion}",
                };
                result.Info.Add(logEntry);

                // 8. Confirmar transacción
                transaction.Commit();
            }
            catch (Exception)
            {
                transaction.Rollback();
                throw;
            }
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: ActualizarPedido - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            var mensajeError = new ProgramacionesPantallaLogDTO
            {
                IdUsuario = "Sistema",
                Mensaje = errorText,
                Fecha = DateTime.Now
            };
            result.Errors.Add(mensajeError);
        }

        return result;
    }

    public async Task<SingleResultHub<ProgramacionesPantallaLogDTO>> RegistrarLog(ProgramacionesPantallaLogDTO logDto)
    {
        var result = new SingleResultHub<ProgramacionesPantallaLogDTO>
        {
            Errors = [],
            Data = null
        };

        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var insertLogQuery = @"
                    INSERT INTO ProgramacionesPantalla_Log
                    (IdProgramacion, IdLinea, IdTipoLog, IdUsuario, Fecha, Mensaje, Extra, ValorAnterior, ValorNuevo)
                    OUTPUT INSERTED.Id, INSERTED.IdProgramacion, INSERTED.IdLinea, INSERTED.IdTipoLog, INSERTED.IdUsuario,
                           INSERTED.Fecha, INSERTED.Mensaje, INSERTED.Extra, INSERTED.ValorAnterior, INSERTED.ValorNuevo
                    VALUES (@idProgramacion, @idLinea, @idTipoLog, @idUsuario, @fecha, @mensaje, @extra, @valorAnterior, @valorNuevo)";

            using var sqlCommand = new SqlCommand(insertLogQuery, connection);
            sqlCommand.Parameters.AddWithValue("@idProgramacion", logDto.IdProgramacion ?? (object)DBNull.Value);
            sqlCommand.Parameters.AddWithValue("@idLinea", logDto.IdLinea ?? (object)DBNull.Value);
            sqlCommand.Parameters.AddWithValue("@idTipoLog", logDto.IdTipoLog);
            sqlCommand.Parameters.AddWithValue("@idUsuario", logDto.IdUsuario);
            var fecha = logDto.Fecha == default ? DateTime.Now : logDto.Fecha;
            sqlCommand.Parameters.AddWithValue("@fecha", fecha);
            sqlCommand.Parameters.AddWithValue("@mensaje", logDto.Mensaje ?? "");
            sqlCommand.Parameters.AddWithValue("@extra", logDto.Extra ?? "");
            sqlCommand.Parameters.AddWithValue("@valorAnterior", logDto.ValorAnterior ?? (object)DBNull.Value);
            sqlCommand.Parameters.AddWithValue("@valorNuevo", logDto.ValorNuevo ?? (object)DBNull.Value);

            using var reader = await sqlCommand.ExecuteReaderAsync();
            if (reader.Read())
            {
                result.Data = new ProgramacionesPantallaLogDTO
                {
                    Id = reader.GetInt64("Id"),
                    IdProgramacion = reader.IsDBNull("IdProgramacion") ? null : reader.GetInt32("IdProgramacion"),
                    IdLinea = reader.IsDBNull("IdLinea") ? null : reader.GetInt32("IdLinea"),
                    IdTipoLog = reader.GetInt32("IdTipoLog"),
                    IdUsuario = reader.GetString("IdUsuario"),
                    Fecha = reader.GetDateTime("Fecha"),
                    Mensaje = reader.IsDBNull("Mensaje") ? "" : reader.GetString("Mensaje"),
                    Extra = reader.IsDBNull("Extra") ? "" : reader.GetString("Extra"),
                    ValorAnterior = reader.IsDBNull("ValorAnterior") ? null : reader.GetString("ValorAnterior"),
                    ValorNuevo = reader.IsDBNull("ValorNuevo") ? null : reader.GetString("ValorNuevo")
                };
            }
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: RegistrarMensajeHub - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            var mensajeError = new ProgramacionesPantallaLogDTO
            {
                IdUsuario = "Sistema",
                Mensaje = errorText,
                Fecha = DateTime.Now
            };
            result.Errors.Add(mensajeError);
        }

        return result;
    }
}