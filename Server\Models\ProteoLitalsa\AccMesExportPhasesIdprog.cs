﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace ProgramadorGeneralBLZ.Server.Models.ProteoLitalsa
{
    public partial class AccMesExportPhasesIdprog
    {
        public int Idprogramacion { get; set; }
        public double WorkOrder { get; set; }
        public int Phase { get; set; }
        public string Fraction { get; set; }
        public double? DescripPhase { get; set; }
        public DateTime? StartPlanned { get; set; }
        public string PreviousPhase { get; set; }
        public string PostPhase { get; set; }
        public double? QtyPrevPhase { get; set; }
        public string MachinePlanned { get; set; }
        public double? DurationPlanned { get; set; }
        public DateTime? DateTimeDownload { get; set; }
        public double? CycleTime { get; set; }
        public double? MaxWorkersOnWorkOrder { get; set; }
        public DateTime? DateTimeModif { get; set; }
        public string Free1 { get; set; }
        public string Free2 { get; set; }
        public string Free3 { get; set; }
        public string Free4 { get; set; }
        public double? RecUpdated { get; set; }
        public double? PhaseErased { get; set; }
        public string PhaseStatus { get; set; }
        public bool Flip { get; set; }
        public bool? Wet { get; set; }
        public int? CodigoAplicacionWet { get; set; }
        public int? CodigoBarniz { get; set; }
        public string DescripcionBarniz { get; set; }
        public double? GramajeMinimo { get; set; }
        public double? GramajeMaximo { get; set; }
        public int? TemperaturaHorno { get; set; }
        public int? VelocidadMaxima { get; set; }
        public int? CodigoAplicacionAnterior { get; set; }
        public int? CodigoAplicacionPosterior { get; set; }
        public bool? CogerHojasMuestra { get; set; }
        public string ObservacionesHojasMuestra { get; set; }
        public string ObservacionesCalidad { get; set; }
        public string ObservacionesGenerales { get; set; }
        public double? BarnizNecesario { get; set; }
        public int? HojasProcesadas { get; set; }
        public string TipoLimpieza { get; set; }
        public int? PasadasAdicionales { get; set; }
        public int? Posicion { get; set; }
        public bool Bloqueada { get; set; }
        public bool? EmpezadaProteo { get; set; }
        public DateTime? FechaEmpezadaProteo { get; set; }
        public bool Descartar { get; set; }
        public DateTime? FechaTerminadaProteo { get; set; }
        public bool? Revisado { get; set; }
    }
}