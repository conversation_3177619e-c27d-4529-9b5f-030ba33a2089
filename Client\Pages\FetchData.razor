﻿@page "/fetchdata"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.WebAssembly.Authentication
@using ProgramadorGeneralBLZ.Shared
@using System.Security.Claims
@attribute [Authorize]
@inject HttpClient Http
@inject AuthenticationStateProvider GetAuthenticationStateAsync

<PageTitle>Weather forecast</PageTitle>

<h1>Weather forecast</h1>

<p>This component demonstrates fetching data from the server.</p>
<AuthorizeView>
    @if (forecasts == null)
    {
        <p><em>Loading...</em></p>
    }
    else
    {
        <ul>
            @foreach (var claim in context.User.Claims)
            {
                @if (claim.Type == "extension_Roles")
                {
                    <b>@context.User.Identity?.Name!</b>
                }
                <li><b>@claim.Type</b>: @claim.Value</li>
            }
        </ul>
        <table class="table">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Temp. (C)</th>
                    <th>Temp. (F)</th>
                    <th>Summary</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var forecast in forecasts)
                {
                    <tr>
                        <td>@forecast.Date.ToShortDateString()</td>
                        <td>@forecast.TemperatureC</td>
                        <td>@forecast.TemperatureF</td>
                        <td>@forecast.Summary</td>
                    </tr>
                }
            </tbody>
        </table>
    }
</AuthorizeView>
@code {
    private WeatherForecast[]? forecasts;
    private ClaimsPrincipal User;
    protected override async Task OnInitializedAsync()
    {
        try
        {
            //forecasts = await Http.GetFromJsonAsync<WeatherForecast[]>("WeatherForecast");
            var authstate = await GetAuthenticationStateAsync.GetAuthenticationStateAsync();
            var user1 = authstate.User;
            var name = user1.Identity.Name;
            var listaRoles = new List<string>();
            foreach (var claim in user1.Claims)
            {
                if (claim.Type == "role")
                {
                    listaRoles.Add(claim.Value);
                }

            }
        }
        catch (AccessTokenNotAvailableException exception)
        {
            exception.Redirect();
        }
    }
}
