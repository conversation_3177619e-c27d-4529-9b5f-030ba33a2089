﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace ProgramadorGeneralBLZ.Server.Models.InterfaseBD;

public partial class Orden
{
    public int Id { get; set; }

    public string NumOrden { get; set; }

    public string ClaseOrden { get; set; }

    public string Centro { get; set; }

    public string NumPedidoCliente { get; set; }

    public string NumPosicionPedidoCliente { get; set; }

    public string Material { get; set; }

    public string UnidadMedida { get; set; }

    public decimal CantidadOrden { get; set; }

    public string ResponsableProduccion { get; set; }

    public bool Transformado { get; set; }

    public string Status { get; set; }

    public string IdCliente { get; set; }

    public string IdPlano { get; set; }

    public string IdMotivo { get; set; }

    public DateTime? FechaPedido { get; set; }

    public DateTime? FechaFtp { get; set; }

    public DateTime? FechaHojalata { get; set; }

    public DateTime? FechaLanzamiento { get; set; }

    public DateTime? FechaContrato { get; set; }

    public DateTime? FechaAcordada { get; set; }

    public DateTime? FechaEntrega { get; set; }

    public DateTime? FechaPedidoCliente { get; set; }

    public string Observaciones { get; set; }

    public string ObsArticulo { get; set; }

    public string ObsCalidad { get; set; }

    public string ObsRayas { get; set; }

    public string ObsFlejado { get; set; }

    public bool Urgente { get; set; }

    public bool Nuevo { get; set; }

    public bool Modificado { get; set; }

    public bool Borrado { get; set; }

    public DateTime? FechaRegistro { get; set; }

    public DateTime? FechaModificacion { get; set; }

    public DateTime? FechaBorrado { get; set; }

    public virtual Motivos MotivoNavigation { get; set; }

    public virtual Matmas MaterialNavigation { get; set; }

    public virtual ICollection<Operaciones> Operaciones { get; set; } = new List<Operaciones>();

    public virtual ICollection<Reservas> Reservas { get; set; } = new List<Reservas>();

    public virtual Responsable ResponsableProduccionNavigation { get; set; }

    public virtual UnidadMedida UnidadMedidaNavigation { get; set; }
}