﻿namespace ProgramadorGeneralBLZ.Server.Reports
{
    partial class CombinadoReport
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.DataAccess.Sql.CustomSqlQuery customSqlQuery1 = new DevExpress.DataAccess.Sql.CustomSqlQuery();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(CombinadoReport));
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.BarnizadoSubReport = new DevExpress.XtraReports.UI.XRSubreport();
            this.MaquinaNombreParameter = new DevExpress.XtraReports.Parameters.Parameter();
            this.VerCambiosParameter = new DevExpress.XtraReports.Parameters.Parameter();
            this.LitografiaSubreport = new DevExpress.XtraReports.UI.XRSubreport();
            this.WoWParameter = new DevExpress.XtraReports.Parameters.Parameter();
            this.sqlDataSource1 = new DevExpress.DataAccess.Sql.SqlDataSource(this.components);
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // TopMargin
            // 
            this.TopMargin.HeightF = 32.29F;
            this.TopMargin.Name = "TopMargin";
            // 
            // BottomMargin
            // 
            this.BottomMargin.HeightF = 27.84F;
            this.BottomMargin.Name = "BottomMargin";
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.BarnizadoSubReport,
            this.LitografiaSubreport});
            this.Detail.HeightF = 202.972F;
            this.Detail.KeepTogether = true;
            this.Detail.KeepTogetherWithDetailReports = true;
            this.Detail.Name = "Detail";
            this.Detail.SortFields.AddRange(new DevExpress.XtraReports.UI.GroupField[] {
            new DevExpress.XtraReports.UI.GroupField("Posicion", DevExpress.XtraReports.UI.XRColumnSortOrder.Ascending)});
            // 
            // BarnizadoSubReport
            // 
            this.BarnizadoSubReport.GenerateOwnPages = true;
            this.BarnizadoSubReport.LocationFloat = new DevExpress.Utils.PointFloat(0F, 30.29167F);
            this.BarnizadoSubReport.Name = "BarnizadoSubReport";
            this.BarnizadoSubReport.ParameterBindings.Add(new DevExpress.XtraReports.UI.ParameterBinding("MaquinaNombreParameter", this.MaquinaNombreParameter));
            this.BarnizadoSubReport.ParameterBindings.Add(new DevExpress.XtraReports.UI.ParameterBinding("TipoLavadoParameter", null, null));
            this.BarnizadoSubReport.ParameterBindings.Add(new DevExpress.XtraReports.UI.ParameterBinding("VerCambiosParameter", this.VerCambiosParameter));
            this.BarnizadoSubReport.ReportSource = new ProgramadorGeneralBLZ.Server.Reports.BarnizadoReportV2();
            this.BarnizadoSubReport.SizeF = new System.Drawing.SizeF(1069F, 31.33334F);
            // 
            // MaquinaNombreParameter
            // 
            this.MaquinaNombreParameter.Description = "MaquinaNombreParameter";
            this.MaquinaNombreParameter.Name = "MaquinaNombreParameter";
            this.MaquinaNombreParameter.Visible = false;
            // 
            // VerCambiosParameter
            // 
            this.VerCambiosParameter.Description = "VerCambiosParameter";
            this.VerCambiosParameter.Name = "VerCambiosParameter";
            this.VerCambiosParameter.Type = typeof(bool);
            this.VerCambiosParameter.ValueInfo = "True";
            this.VerCambiosParameter.Visible = false;
            // 
            // LitografiaSubreport
            // 
            this.LitografiaSubreport.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.LitografiaSubreport.Name = "LitografiaSubreport";
            this.LitografiaSubreport.ParameterBindings.Add(new DevExpress.XtraReports.UI.ParameterBinding("MaquinaNombreParameter", this.MaquinaNombreParameter));
            this.LitografiaSubreport.ParameterBindings.Add(new DevExpress.XtraReports.UI.ParameterBinding("VerCambiosParameter", this.VerCambiosParameter));
            this.LitografiaSubreport.ParameterBindings.Add(new DevExpress.XtraReports.UI.ParameterBinding("WoWParameter", this.WoWParameter));
            this.LitografiaSubreport.ReportSource = new ProgramadorGeneralBLZ.Server.Reports.LitografiaReport();
            this.LitografiaSubreport.Scripts.OnBeforePrint = "LitografiaSubreport_BeforePrint";
            this.LitografiaSubreport.SizeF = new System.Drawing.SizeF(1069F, 30.29167F);
            // 
            // WoWParameter
            // 
            this.WoWParameter.Description = "WoWParameter";
            this.WoWParameter.Name = "WoWParameter";
            this.WoWParameter.Type = typeof(bool);
            this.WoWParameter.ValueInfo = "False";
            this.WoWParameter.Visible = false;
            // 
            // sqlDataSource1
            // 
            this.sqlDataSource1.ConnectionName = "DefaultConnectionProgramador";
            this.sqlDataSource1.Name = "sqlDataSource1";
            customSqlQuery1.Name = "PedidoProgramadoEnviadoImprimirCombinado_1";
            customSqlQuery1.Sql = resources.GetString("customSqlQuery1.Sql");
            this.sqlDataSource1.Queries.AddRange(new DevExpress.DataAccess.Sql.SqlQuery[] {
            customSqlQuery1});
            this.sqlDataSource1.ResultSchemaSerializable = resources.GetString("sqlDataSource1.ResultSchemaSerializable");
            // 
            // CombinadoReport
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.TopMargin,
            this.BottomMargin,
            this.Detail});
            this.ComponentStorage.AddRange(new System.ComponentModel.IComponent[] {
            this.sqlDataSource1});
            this.DataMember = "PedidoProgramadoEnviadoImprimirCombinado_1";
            this.DataSource = this.sqlDataSource1;
            this.Font = new DevExpress.Drawing.DXFont("Arial", 9.75F);
            this.Landscape = true;
            this.Margins = new DevExpress.Drawing.DXMargins(43F, 57F, 32.29F, 27.84F);
            this.PageHeight = 827;
            this.PageWidth = 1169;
            this.PaperKind = DevExpress.Drawing.Printing.DXPaperKind.A4;
            this.ParameterPanelLayoutItems.AddRange(new DevExpress.XtraReports.Parameters.ParameterPanelLayoutItem[] {
            new DevExpress.XtraReports.Parameters.ParameterLayoutItem(this.MaquinaNombreParameter, DevExpress.XtraReports.Parameters.Orientation.Horizontal),
            new DevExpress.XtraReports.Parameters.ParameterLayoutItem(this.VerCambiosParameter, DevExpress.XtraReports.Parameters.Orientation.Horizontal),
            new DevExpress.XtraReports.Parameters.ParameterLayoutItem(this.WoWParameter, DevExpress.XtraReports.Parameters.Orientation.Horizontal)});
            this.Parameters.AddRange(new DevExpress.XtraReports.Parameters.Parameter[] {
            this.MaquinaNombreParameter,
            this.VerCambiosParameter,
            this.WoWParameter});
            this.ReportPrintOptions.DetailCount = 1;
            this.Scripts.OnBeforePrint = "CombinadoReport_BeforePrint";
            this.ScriptsSource = "\r\nprivate void CombinadoReport_BeforePrint(object sender, System.ComponentModel.C" +
    "ancelEventArgs e) {\r\n    \r\n}\r\n";
            this.ShowPrintMarginsWarning = false;
            this.Version = "23.1";
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.XRSubreport BarnizadoSubReport;
        private DevExpress.XtraReports.UI.XRSubreport LitografiaSubreport;
        private DevExpress.XtraReports.Parameters.Parameter MaquinaNombreParameter;
        private DevExpress.XtraReports.Parameters.Parameter VerCambiosParameter;
        private DevExpress.XtraReports.Parameters.Parameter WoWParameter;
        private DevExpress.DataAccess.Sql.SqlDataSource sqlDataSource1;
    }
}
