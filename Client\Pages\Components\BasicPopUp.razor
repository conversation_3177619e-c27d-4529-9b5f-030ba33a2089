﻿<DxPopup Width="@WidthSize" Height="800" MaxHeight="1200" CloseOnEscape="true" AllowDrag="true" Scrollable="true"
         @bind-Visible="@PopupVisible" Closed="PopupClosed">
    @*HeaderText="@($"Pedido: {IdPedido}")"*@
    <BodyContentTemplate>
        @switch (Contenido)
        {
            case "Partes":
                <Grid_Partes IdPedido="Codigo" />
                break;
            case "TablaProgramacion":
                <Grid_TblProgramacion IdPedido="Codigo" />
                break;
            case "Limpias":
                <Grid_Datos_Limpias CodBarniz="Codigo" />
                break;
            case "Mensajes":
                @foreach (var mensaje in ListaMensajes)
                {
                    <div style="max-height: 1200px;">
                        @*<p>@(mensaje)</p>*@
                        <p>@((MarkupString)mensaje)</p>
                    </div>
                }
                break;
            case "Otros":
                <Grid_Otros />
                break;
        }
    </BodyContentTemplate>
</DxPopup>

@code {
    [Parameter]
    public string WidthSize { get; set; } = "1800";
    [Parameter]
    public bool PopupVisible { get; set; } = false;
    [Parameter]
    public string Contenido { get; set; }
    [Parameter]
    public List<string>? ListaMensajes { get; set; }
    [Parameter]
    public int? Codigo { get; set; }
    [Parameter]
    public EventCallback<bool> PopUpVisibleEventCallback { get; set; }
    private void PopupClosed()
    {
        PopUpVisibleEventCallback.InvokeAsync(true);
    }
}
