﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Identity.Web.Resource;
using ProgramadorGeneralBLZ.Shared;
using ProgramadorGeneralBLZ.Server.CustomFilterAttributes;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;
using ProgramadorGeneralBLZ.Server.MediaTR.Programaciones.Command;
using ProgramadorGeneralBLZ.Server.MediaTR.Programaciones.Query;

namespace ProgramadorGeneralBLZ.Server.Controllers
{
    [Authorize]
    [ApiController]
    [Route("[controller]")]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAdB2C:Scopes")]
    //[Authorize(Roles = $"{Roles.Programador},{Roles.Admin}")]
    [ClaimRequirementContainRole("extension_Roles", $"{Roles.Programador},{Roles.Admin}")]
    public class ProgramacionController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<ProgramacionController> _logger;
        private readonly IWebHostEnvironment _env;

        public ProgramacionController(IMediator mediator, ILogger<ProgramacionController> logger, IWebHostEnvironment env)
        {
            _mediator = mediator;
            _logger = logger;
            _env = env;
        }
        
        [HttpGet("calcularhorarios")]
        public async Task<SingleResult<int>> CalcularHorariosPorTipoYMaquina(Enums.TipoPedido tipo, int maquina)
        {
            var result = tipo switch
            {
                Enums.TipoPedido.Barnizado => await _mediator.Send(new GetRevisaProgramacionBarnizadoQuery(tipo, maquina)),
                Enums.TipoPedido.Litografia => await _mediator.Send(new GetRevisaProgramacionLitografiaQuery(tipo, maquina)),
                //Enums.TipoPedido.Corte => await _mediator.Send(new GetRevisaProgramacionBarnizadoQuery(tipo, maquina)),
                //Enums.TipoPedido.Seleccion => await _mediator.Send(new GetRevisaProgramacionBarnizadoQuery(tipo, maquina)),
                //Enums.TipoPedido.Laminado => await _mediator.Send(new GetRevisaProgramacionBarnizadoQuery(tipo, maquina)),
                _ => throw new ArgumentOutOfRangeException()
            };
            return result;
        }


        [HttpPut("{posicion}/edit")]
        public async Task<SingleResult<int>> EditarPosicionProgramacion(TablaProgramacionDTO content, int posicion, CancellationToken ct = default)
        {
            var model = await _mediator.Send(new EditarPosicionProgramacionCommand(posicion, content), ct);
            return model;
        }
        
        [HttpPost("insertlimpias")]
        public async Task<SingleResult<int>> InsertarDatosLimpias([FromBody] TblLimpiezasDTO datos, CancellationToken ct = default)
        {
            var model = await _mediator.Send(new InsertDatosLimpiasCommand(datos), ct);
            return model;
        }
        [HttpPost("borrarprogramaciones/{tp}")]
        public async Task<SingleResult<int>> BorrarProgramaciones([FromBody] Dictionary<int, int> content, Enums.TipoPedido tp, CancellationToken ct = default)
        {
            var model = await _mediator.Send(new BorrarProgramacionesCommand(content, tp), ct);
            return model;
        }
        [HttpDelete("borrarsolotabla/{idProg}")]
        public async Task<SingleResult<int>> BorrarProgramaciones(int idProg, CancellationToken ct = default)
        {
            var model = await _mediator.Send(new BorrarRegistroDeTablaProgramacionCommand(idProg), ct);
            return model;
        }
        [HttpGet("renumerar")]
        public async Task<SingleResult<int>> RenumerarProgramacione(int maquina, int desde)
        {
            var model = await _mediator.Send(new RenumerarProgramacionesCommand(maquina, desde));
            return model;
        }
        [HttpGet("comprobarprogramacion")]
        public async Task<ListResult<string>> ComprobarProgramacion(int maquina)
        {
            var model = await _mediator.Send(new CompruebaHorariosCommand(maquina));
            return model;
        }
        [HttpPost("recalcular")]
        public async Task<SingleResult<int>> RecalcularPosicionesProgramacione(int maquina, int desde, [FromBody] List<PedidoIndexCodigoDTO> content)
        {
            var model = await _mediator.Send(new RecalcularProgramacionesCommand(maquina, desde, content));
            return model;
        }
        [HttpPost("enviarprogramacionproteobymaquina")]
        public async Task<SingleResult<int>> EnviarProgramacionProteoByMaquina([FromBody] int idMaquina)
        {
            var model = await _mediator.Send(new EnviarProgramacionCommand(idMaquina));
            return model;
        }
        [HttpPost("forzarcargaobsalmacen")]
        public async Task<SingleResult<int>> ForzarCargaObsAlmacen([FromBody] TablaProgramacionDTO item)
        {
            var model = await _mediator.Send(new ForzarCargaObsAlmacenCommand(item));
            return model;
        }

        [HttpPost("bulkchanges")]
        public async Task<SingleResult<int>> BulkChanges([FromBody] BulkChangesDTO datos, CancellationToken ct = default)
        {
            var model = await _mediator.Send(new EditBulkChangesProgramacionesCommand(datos), ct);
            return model;
        }
    }
}
