﻿{
   "CodeGenerationMode": 2,
   "ContextClassName": "ProteoLitalsaContext",
   "ContextNamespace": null,
   "DefaultDacpacSchema": null,
   "FilterSchemas": false,
   "IncludeConnectionString": true,
   "ModelNamespace": null,
   "OutputContextPath": "Data\\ProteoLitalsa",
   "OutputPath": "Models\\ProteoLitalsa",
   "PreserveCasingWithRegex": true,
   "ProjectRootNamespace": "ProgramadorGeneralBLZ.Server",
   "Schemas": null,
   "SelectedHandlebarsLanguage": 0,
   "SelectedToBeGenerated": 0,
   "Tables": [
      {
         "Name": "[dbo].[ACC_MES_EXPORT_PHASES_IDPROG]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[ACC_MES_EXPORT_WORK_ORDERS]",
         "ObjectType": 0
      }
   ],
   "UiHint": "QPLANT2\\PROTEO.proteo_litalsadb",
   "UncountableWords": null,
   "UseBoolPropertiesWithoutDefaultSql": false,
   "UseDatabaseNames": false,
   "UseDateOnlyTimeOnly": false,
   "UseDbContextSplitting": false,
   "UseFluentApiOnly": true,
   "UseHandleBars": false,
   "UseHierarchyId": false,
   "UseInflector": false,
   "UseLegacyPluralizer": false,
   "UseManyToManyEntity": false,
   "UseNoDefaultConstructor": false,
   "UseNoObjectFilter": false,
   "UseNodaTime": false,
   "UseNullableReferences": false,
   "UseSchemaFolders": false,
   "UseSpatial": false,
   "UseT4": false
}