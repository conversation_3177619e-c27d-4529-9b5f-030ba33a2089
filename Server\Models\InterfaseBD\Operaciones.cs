﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace ProgramadorGeneralBLZ.Server.Models.InterfaseBD;

public partial class Operaciones
{
    public int Id { get; set; }

    public string NumOrden { get; set; }

    public string NumOperacion { get; set; }

    public string IdRecurso { get; set; }

    public string TextoBreveOp { get; set; }

    public string UnidadMedida { get; set; }

    public decimal CantidadOperacion { get; set; }

    public string PuestoTrabajo { get; set; }

    public DateTime FechaInicio { get; set; }

    public TimeSpan HoraInicio { get; set; }

    public DateTime FechaFin { get; set; }

    public TimeSpan HoraFin { get; set; }

    public string CodigoAplicacion { get; set; }

    public string Cara { get; set; }

    public string EstatusUsuario { get; set; }

    public decimal? Temperatura { get; set; }

    public decimal? Gramaje { get; set; }

    public decimal? GramajeMinimo { get; set; }

    public string DescripcionLarga { get; set; }

    public string DescripcionCorta { get; set; }

    public string DescripcionProgramacion { get; set; }

    public bool Nuevo { get; set; }

    public bool Modificado { get; set; }

    public bool Borrado { get; set; }

    public DateTime? FechaRegistro { get; set; }

    public DateTime? FechaModificacion { get; set; }

    public DateTime? FechaBorrado { get; set; }

    public bool Transformado { get; set; }

    public virtual Orden NumOrdenNavigation { get; set; }

    public virtual UnidadMedida UnidadMedidaNavigation { get; set; }
}