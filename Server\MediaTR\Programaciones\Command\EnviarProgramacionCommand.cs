﻿using System.Threading;
using MediatR;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Data.ProteoLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProteoLitalsa;
using ProgramadorGeneralBLZ.Server.Services.DatabaseManipulation;
using ProgramadorGeneralBLZ.Shared.ResponseModels;
using static DevExpress.Xpo.Helpers.AssociatedCollectionCriteriaHelper;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Programaciones.Command;

public class EnviarProgramacionCommand : IRequest<SingleResult<int>>
{
    //Se pasa solo la maquina para obtener de ahi los datos,
    //aunque si hace falta se pueden pasar DESDE y HASTA también desde la pantalla.
    public int Linea { get; set; }

    public EnviarProgramacionCommand(int linea)
    {
        Linea = linea;
    }
}

public class EnviarProgramacionCommandHandler : IRequestHandler<EnviarProgramacionCommand, SingleResult<int>>
{
    private readonly ProgramadorLitalsaContext _contextProg;
    private readonly ProteoLitalsaContext _contextProteo;
    private readonly IDataManipulationService _dataManipulationService;
    public EnviarProgramacionCommandHandler(ProgramadorLitalsaContext contextProg, ProteoLitalsaContext contextProteo, IDataManipulationService dataManipulationService)
    {
        _contextProg = contextProg;
        _contextProteo = contextProteo;
        _dataManipulationService = dataManipulationService;
    }

    public async Task<SingleResult<int>> Handle(EnviarProgramacionCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<int> { Errors = new List<string>(), Data = 0 };
        try
        {
            var maquina = await _contextProg.Maquinas.FirstOrDefaultAsync(o => o.Idmaquina == request.Linea, cancellationToken);
            //ATENCION: La posición tiene que ser MAYOR QUE, porque se hizo un cambio para mostrar por pantalla siempre las que sean mayor que
            // lo que indica el campo "Posicion Desde" ya que es como estaban acostumbrados Miguel y Carlos
            var datosProgramacion = await _contextProg.TablaProgramacion
                .Where(o => o.Idlinea == maquina.Idmaquina &&
                            o.Posicion > maquina.PosicionDesde &&
                            o.Posicion <= maquina.PosicionHasta)
                .OrderBy(o => o.Posicion)
                .ToListAsync(cancellationToken);
            var listadoPedidos = datosProgramacion.Select(o => o.Idpedido).Distinct();
            var datosPedidoCabecera = await _contextProg.PedidoProcesado.Where(o => listadoPedidos.Contains(o.IdPedido))
                .ToListAsync(cancellationToken);
            foreach (var prog in datosProgramacion)
            {

                var pedidoCabecera = datosPedidoCabecera.First(o => o.IdPedido == prog.Idpedido);
                //await GestionDataBaseConEfCore(dp, datosPedidos, maquina, cancellationToken);
                var datos = await GestionDataBaseConSqlRaw(prog, pedidoCabecera, maquina, cancellationToken);
                if (!string.IsNullOrEmpty(datos))
                {
                    result.Errors.Add(datos);
                }
                else
                {
                    //TODO: Hay que refinar esto porque PHASE es 1:1 con programacion pero WORKORDER,
                    //TODO: al ser la cabecera, debería registrarse una única vez,
                    //TODO: como está ahora se graba cada vez que se recorre TablaProgramación
                    var workOrder =
                        await _contextProteo.AccMesExportWorkOrders.FirstOrDefaultAsync(o =>
                            o.WorkOrder == prog.Idpedido, cancellationToken);

                    if (workOrder == null)
                    {
                        workOrder = new AccMesExportWorkOrders//Estos dos campos son NOT NULL en la BD
                        {
                            WorkOrder = (double)prog.Idpedido,
                            WorkOrderStatus = "F"
                        };
                        _contextProteo.AccMesExportWorkOrders.Add(workOrder);
                    }

                    workOrder.WorkOrder = (double)prog.Idpedido;
                    workOrder.DescripWorkOrder = prog.Idpedido;
                    workOrder.QtyPlanned = pedidoCabecera.HojasPedido;
                    workOrder.StartPlanned = prog.HoraComienzoEstimada;
                    workOrder.WorkOrderStatus = "F";
                    workOrder.RecUpdated = 1;
                    workOrder.DateTimeDownload = DateTime.Now;
                    workOrder.DateTimeUpdate = DateTime.Now;
                    workOrder.Metalwork =
                        $"{_dataManipulationService.GetDatosPedido(prog.Idpedido.Value, "DevuelveCaracteristicasHojalata").Text} {_dataManipulationService.GetDatosTipoHojalata(prog.Idpedido.Value, "Procesos").Text}";

                    // EF Core rastrea los cambios en las entidades, por lo que no necesitas llamar a Update().

                    await _contextProteo.SaveChangesAsync(cancellationToken);
                }
            }
            //await _contextProteo.SaveChangesAsync(cancellationToken);

        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: EnviarProgramacionCommand - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }

    private async Task<string> GestionDataBaseConSqlRaw(TablaProgramacion dp, PedidoProcesado pedido,
        Models.ProgramadorLitalsa.Maquinas maquina, CancellationToken cancellationToken)
    {
        var pedidoProteo =
            await _contextProteo.AccMesExportPhasesIdprog.FirstOrDefaultAsync(
                o => o.Idprogramacion == dp.Idprogramacion, cancellationToken);

        var datosFases = await _dataManipulationService.DevuelveOrdenProcesos(dp.Idpedido.Value, dp.Idaplicacion.Value, 0, dp.CaraAaplicar);
        if (pedidoProteo is { Bloqueada: true })
        {
            return $"Programación de Pedido {dp.Idpedido}, Posición {dp.Posicion} BLOQUEADA en PROTEO.";
        }

        string sql = @"
            IF EXISTS (SELECT 1 FROM ACC_MES_EXPORT_PHASES_IDPROG WHERE Idprogramacion = @p0)
            UPDATE ACC_MES_EXPORT_PHASES_IDPROG 
            SET 
                Work_Order = @p1, 
                Phase = @p2, 
                Fraction = @p3, 
                Descrip_Phase = @p4, 
                Start_Planned = @p5, 
                Previous_Phase = @p6, 
                Post_Phase = @p7, 
                Qty_Prev_Phase = @p8, 
                Machine_Planned = @p9, 
                Duration_Planned = @p10, 
                Date_Time_Download = @p11, 
                Cycle_Time = @p12, 
                Max_Workers_On_Work_Order = @p13, 
                Date_Time_Modif = @p14, 
                Free1 = @p15, 
                Free2 = @p16, 
                Free3 = @p17, 
                Free4 = @p18, 
                Rec_Updated = @p19, 
                Phase_Erased = @p20, 
                Phase_Status = @p21, 
                Flip = @p22, 
                Wet = @p23, 
                Codigo_Aplicacion_Wet = @p24, 
                Codigo_Barniz = @p25, 
                Descripcion_Barniz = @p26, 
                Gramaje_Minimo = @p27, 
                Gramaje_Maximo = @p28, 
                Temperatura_Horno = @p29, 
                Velocidad_Maxima = @p30, 
                Codigo_Aplicacion_Anterior = @p31, 
                Codigo_Aplicacion_Posterior = @p32, 
                Coger_Hojas_Muestra = @p33, 
                Observaciones_Hojas_Muestra = @p34, 
                Observaciones_Calidad = @p35, 
                Observaciones_Generales = @p36, 
                Barniz_Necesario = @p37, 
                Hojas_Procesadas = @p38, 
                Tipo_Limpieza = @p39, 
                Pasadas_Adicionales = @p40,
                Posicion = @p41
            WHERE 
                Idprogramacion = @p0
            ELSE
            INSERT INTO ACC_MES_EXPORT_PHASES_IDPROG 
                (IDPROGRAMACION, WORK_ORDER, PHASE, FRACTION, DESCRIP_PHASE, START_PLANNED,PREVIOUS_PHASE,POST_PHASE,QTY_PREV_PHASE,
                MACHINE_PLANNED,DURATION_PLANNED,DATE_TIME_DOWNLOAD,CYCLE_TIME,MAX_WORKERS_ON_WORK_ORDER,DATE_TIME_MODIF,FREE1,FREE2,
                FREE3,FREE4,REC_UPDATED,PHASE_ERASED,PHASE_STATUS,FLIP,WET,CODIGO_APLICACION_WET,CODIGO_BARNIZ,
                DESCRIPCION_BARNIZ,GRAMAJE_MINIMO,GRAMAJE_MAXIMO,TEMPERATURA_HORNO,VELOCIDAD_MAXIMA,CODIGO_APLICACION_ANTERIOR,
                CODIGO_APLICACION_POSTERIOR,COGER_HOJAS_MUESTRA,OBSERVACIONES_HOJAS_MUESTRA,OBSERVACIONES_CALIDAD,OBSERVACIONES_GENERALES,
                BARNIZ_NECESARIO,HOJAS_PROCESADAS,TIPO_LIMPIEZA,PASADAS_ADICIONALES,POSICION)
            VALUES 
                (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, 
                @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, 
                @p37, @p38, @p39, @p40, @p41)";

        // Use the data from pedidoProteo to fill the values in the query.
        var denominacion = string.Empty;
        if (dp.Idproducto != null & dp.Idproducto != 0)
        {
            var producto = await _contextProg.TablaProductos.FirstOrDefaultAsync(o => o.Idproducto == dp.Idproducto, cancellationToken);
            denominacion = producto.Denominacion;
        }

        await _contextProteo.Database.ExecuteSqlRawAsync(sql,
            dp.Idprogramacion,          //@p0 - Id de programación
            dp.Idpedido.Value,              //@p1 - Id de pedido
            datosFases.Fase,                //@p2 - Fase de datos
            "0",                            //@p3 - Fracción, valor fijo
            dp.Idaplicacion,                //@p4 - Id de la aplicación
            dp.HoraComienzoEstimada,        //@p5 - Hora estimada de inicio
            datosFases.CodApliAnterior.ToString(),     //@p6 - Fase anterior
            datosFases.CodApliPosterior.ToString(),    //@p7 - Fase posterior
            pedido.HojasPedido,             //@p8 - Hojas de pedido
            maquina.Idmaquinainformix.Value.ToString("D4"), //@p9 - Id de la máquina
            (int)(dp.HoraFinEstimada.Value - dp.HoraComienzoEstimada.Value).TotalMinutes, //@p10 - Duración estimada
            DateTime.Now,                   //@p11 - Fecha y hora de descarga
            0,                              //@p12 - Tiempo de ciclo, valor fijo
            0,                              //@p13 - Máximo de trabajadores en la orden de trabajo, valor fijo
            DateTime.Now,                   //@p14 - Fecha y hora de modificación
            dp.CaraAaplicar,                //@p15 - Cara a aplicar
            dp.Idproducto != null ? $"{dp.Idproducto}, {dp.Producto}" : string.Empty,  //@p16 - Id del producto y nombre del producto
            dp.Observaciones != null ? $"{dp.Observaciones}" : string.Empty,              //@p17 - Observaciones
            dp.Obspaseposterior != null ? $"{dp.Obspaseposterior}" : string.Empty,           //@p18 - Observaciones de fase posterior
            1,                              //@p19 - RecUpdated, valor fijo
            dp.Idprogramacion,              //@p20 - Fase borrada
            "P",                            //@p21 - Estado de la fase, valor fijo
            false,                          //@p22 - ¿Flip?, valor fijo
            dp.AplicacionSimultanea != null && dp.AplicacionSimultanea != 0, //@p23 - Wet
            dp.AplicacionSimultanea ?? 0,   //@p24 - Código de la aplicación Wet
            dp.Idproducto ?? 0,                  //@p25 - Código de barniz
            denominacion,                   //@p26 - Descripción del barniz
            dp.PesoMin ?? 0,                     //@p27 - Gramaje mínimo
            dp.Peso ?? 0,                        //@p28 - Gramaje máximo
            dp.TemperaturaSecado ?? 0,           //@p29 - Temperatura del horno
            dp.VelocidadMaxima ?? 0,             //@p30 - Velocidad máxima
            0,                              //@p31 - Código de la aplicación anterior, valor fijo
            dp.Idaplicacionposterior,       //@p32 - Código de la aplicación posterior
            false,                          //@p33 - ¿Coger hojas de muestra?, valor fijo
            string.Empty,                   //@p34 - Observaciones hojas de muestra, valor fijo
            dp.ObsCalidad,                  //@p35 - Observaciones de calidad
            string.Empty,                   //@p36 - Observaciones generales, valor fijo
            dp.BarnizNecesario,             //@p37 - Barniz necesario
            0,                              //@p38 - Hojas procesadas, valor fijo
            dp.TipoLavada,                  //@p39 - Tipo de limpieza
            dp.PasadasAdicionales,          //@p40 - Pasadas adicionales
            dp.Posicion                     //@p41 - Posición
        );

        return string.Empty;
    }

    private async Task GestionDataBaseConEfCore(TablaProgramacion dp, PedidoProcesado pedido,
        Models.ProgramadorLitalsa.Maquinas maquina, CancellationToken cancellationToken)
    {
        var pedidoProteo =
            await _contextProteo.AccMesExportPhasesIdprog.FirstOrDefaultAsync(
                o => o.Idprogramacion == dp.Idprogramacion, cancellationToken);
        if (pedidoProteo == null)
        {
            pedidoProteo = new AccMesExportPhasesIdprog();
        }
        if (pedidoProteo.Bloqueada)
        {
            //result.Errors.Add($"Programación {dp.Idprogramacion} BLOQUEADA en PROTEO.");
        }

        var datosFases = await _dataManipulationService.DevuelveOrdenProcesos(dp.Idpedido.Value, dp.Idaplicacion.Value, 0);
        pedidoProteo.Idprogramacion = dp.Idprogramacion;
        pedidoProteo.WorkOrder = dp.Idpedido.Value;
        pedidoProteo.Phase = datosFases.Fase;
        pedidoProteo.Fraction = "0";
        pedidoProteo.DescripPhase = dp.Idaplicacion;
        pedidoProteo.StartPlanned = dp.HoraComienzoEstimada;
        pedidoProteo.PreviousPhase = datosFases.FaseAnterior.ToString();
        pedidoProteo.PostPhase = datosFases.FasePosterior.ToString();
        pedidoProteo.QtyPrevPhase = pedido.HojasPedido;
        pedidoProteo.MachinePlanned = maquina.Idmaquinainformix.Value.ToString("D4");
        pedidoProteo.DurationPlanned =
            (int)(dp.HoraComienzoEstimada.Value - dp.HoraFinEstimada.Value).TotalMinutes;
        pedidoProteo.DateTimeDownload = DateTime.Now;
        pedidoProteo.CycleTime = 0;
        pedidoProteo.MaxWorkersOnWorkOrder = 0;
        pedidoProteo.DateTimeModif = DateTime.Now;
        pedidoProteo.Free1 = dp.CaraAaplicar;
        pedidoProteo.Free2 = $"{dp.Idproducto}, {dp.Producto[..1000]}";
        pedidoProteo.Free3 = $"{dp.Observaciones[..1000]}";
        pedidoProteo.Free4 = $"{dp.Obspaseposterior[..1000]}";
        pedidoProteo.RecUpdated = 1;
        pedidoProteo.PhaseErased = dp.Idprogramacion;
        pedidoProteo.PhaseStatus = "P";
        pedidoProteo.Flip = false;

        if (dp.AplicacionSimultanea != null && dp.AplicacionSimultanea != 0)
        {
            pedidoProteo.Wet = true;
            pedidoProteo.CodigoAplicacionWet = dp.AplicacionSimultanea;
        }
        else
        {
            pedidoProteo.Wet = false;
            pedidoProteo.CodigoAplicacionWet = 0;
        }

        pedidoProteo.CodigoBarniz = dp.Idproducto;
        if (dp.Idproducto != null & dp.Idproducto != 0)
        {
            var producto = await _contextProg.TablaProductos.FirstOrDefaultAsync(o => o.Idproducto == dp.Idproducto, cancellationToken);
            pedidoProteo.DescripcionBarniz = producto.Denominacion;
        }

        pedidoProteo.GramajeMinimo = dp.PesoMin;
        pedidoProteo.GramajeMaximo = dp.Peso;
        pedidoProteo.TemperaturaHorno = dp.TemperaturaSecado;
        pedidoProteo.VelocidadMaxima = dp.VelocidadMaxima;
        pedidoProteo.CodigoAplicacionAnterior = 0;
        pedidoProteo.CodigoAplicacionPosterior = dp.Idaplicacionposterior;
        pedidoProteo.CogerHojasMuestra = false;
        pedidoProteo.ObservacionesHojasMuestra = string.Empty;
        pedidoProteo.ObservacionesCalidad = dp.ObsCalidad;
        pedidoProteo.ObservacionesGenerales = String.Empty;
        pedidoProteo.BarnizNecesario = dp.BarnizNecesario;
        pedidoProteo.HojasProcesadas = 0;
        pedidoProteo.TipoLimpieza = dp.TipoLavada;
        pedidoProteo.Posicion = dp.Posicion;
        pedidoProteo.PasadasAdicionales = dp.PasadasAdicionales;
    }
}