﻿using System.Security.Policy;
using System.Text;
using MediatR;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Services.DatabaseManipulation;
using ProgramadorGeneralBLZ.Shared;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Programaciones.Command;

public class CompruebaHorariosCommand : IRequest<ListResult<string>>
{
    //Se pasa solo la maquina para obtener de ahi los datos,
    //aunque si hace falta se pueden pasar DESDE y HASTA también desde la pantalla.
    public int Linea { get; set; }

    public CompruebaHorariosCommand(int linea)
    {
        Linea = linea;
    }
}

public class CompruebaHorariosCommandHandler : IRequestHandler<CompruebaHorariosCommand, ListResult<string>>
{
    private readonly IDataManipulationService _dataManipulationService;
    private readonly ProgramadorLitalsaContext _contextProg;
    public CompruebaHorariosCommandHandler(ProgramadorLitalsaContext contextProg, IDataManipulationService dataManipulationService)
    {
        _contextProg = contextProg;
        _dataManipulationService = dataManipulationService;
    }

    public async Task<ListResult<string>> Handle(CompruebaHorariosCommand request, CancellationToken cancellationToken)
    {
        var result = new ListResult<string> { Errors = new List<string>(), Data = new List<string>() };
        try
        {
            var maquina = await _contextProg.Maquinas
                .FirstOrDefaultAsync(o => o.Idmaquina == request.Linea, cancellationToken);

            var stringBuilder = new StringBuilder();
            stringBuilder.AppendLine("<style>");
            stringBuilder.AppendLine("table { border-collapse: collapse; }");
            stringBuilder.AppendLine("td, th { border: 1px solid white; padding-left: 8px; padding-right: 8px; }");
            stringBuilder.AppendLine("th { font-weight: bold; }");
            stringBuilder.AppendLine("</style>");
            stringBuilder.AppendLine($"COMPROBACIÓN PROGRAMACIONES LINEA {maquina.IdmaquinaG21}<br>");
            stringBuilder.AppendLine("BASADO EN LAS HOJAS DE LOS PARTES DE LA TABLA HOJASTRABAJOG21<br>");
            stringBuilder.AppendLine("--------------------------------------------------------------------------------<br>");

            stringBuilder.AppendLine("<table border=\"1\">");
            stringBuilder.AppendLine("<thead>");
            stringBuilder.AppendLine("<tr>");

            if (maquina.TipoMaquina==Enums.TipoMaquina.Impresora.ToString())
            {
                var registros = await _contextProg.TablaProgramacion
                    .Where(o => o.Idlinea == maquina.Idmaquina && o.Posicion >= maquina.PosicionDesde &&
                                o.Posicion <= maquina.PosicionHasta).OrderBy(o=>o.Posicion).ToListAsync(cancellationToken);

                stringBuilder.AppendLine("<th>Posición</th>");
                stringBuilder.AppendLine("<th>Cliente</th>");
                stringBuilder.AppendLine("<th>Pedido</th>");
                stringBuilder.AppendLine("<th>Tintas</th>");
                stringBuilder.AppendLine("<th>Motivos</th>");
                stringBuilder.AppendLine("<th style='width: 350px;'>Estado Pedido</th>");
                // ... Aquí van todas las otras cabeceras
                stringBuilder.AppendLine("</tr>");
                stringBuilder.AppendLine("</thead>");
                stringBuilder.AppendLine("<tbody>");


                foreach (var item in registros)
                {
                    var pp = await _contextProg.PedidoProcesado.FirstOrDefaultAsync(o => o.IdPedido == item.Idpedido,
                        cancellationToken);

                    var estado = _dataManipulationService.GetTextoEstadoCodigosAplicacion(item.Idpedido.Value);
                    var tintas = $"{_contextProg.Database.SqlQuery<string>($"SELECT dbo.dcolor({pp.C01ped ?? 0})").AsEnumerable().First()}" +
                                 $"{_contextProg.Database.SqlQuery<string>($"SELECT dbo.dcolor({pp.C02ped ?? 0})").AsEnumerable().First()}" +
                                 $"{_contextProg.Database.SqlQuery<string>($"SELECT dbo.dcolor({pp.C03ped ?? 0})").AsEnumerable().First()}" +
                                 $"{_contextProg.Database.SqlQuery<string>($"SELECT dbo.dcolor({pp.C04ped ?? 0})").AsEnumerable().First()}" +
                                 $"{_contextProg.Database.SqlQuery<string>($"SELECT dbo.dcolor({pp.C05ped ?? 0})").AsEnumerable().First()}" +
                                 $"{_contextProg.Database.SqlQuery<string>($"SELECT dbo.dcolor({pp.C06ped ?? 0})").AsEnumerable().First()}" +
                                 $"{_contextProg.Database.SqlQuery<string>($"SELECT dbo.dcolor({pp.C07ped ?? 0})").AsEnumerable().First()}" +
                                 $"{_contextProg.Database.SqlQuery<string>($"SELECT dbo.dcolor({pp.C08ped ?? 0})").AsEnumerable().First()}" +
                                 $"{_contextProg.Database.SqlQuery<string>($"SELECT dbo.dcolor({pp.C09ped ?? 0})").AsEnumerable().First()}" +
                                 $"{_contextProg.Database.SqlQuery<string>($"SELECT dbo.dcolor({pp.Co10ped ?? 0})").AsEnumerable().First()}";

                    //stringBuilder.AppendLine($"Cliente: {_dataManipulationService.GetDatosPedido(item.Idpedido.Value, "Cliente").Num.Value}"+
                    //    $" | Pedido: {item.Idpedido.Value} " +
                    //    $" | Tintas: {tintas} " +
                    //    $" | Motivos: {pp.Motivos} " +
                    //    $" | Estado Pedido: {estado} ");

                    stringBuilder.AppendLine("<tr>");
                    stringBuilder.AppendLine($"<td>{item.Posicion}</td>");
                    stringBuilder.AppendLine($"<td>{_dataManipulationService.GetDatosPedido(item.Idpedido.Value, "Cliente").Num.Value}</td>");
                    stringBuilder.AppendLine($"<td>{item.Idpedido.Value}</td>");
                    stringBuilder.AppendLine($"<td>{tintas}</td>");
                    stringBuilder.AppendLine($"<td>{pp.Motivos}</td>");
                    stringBuilder.AppendLine($"<td>{estado}</td>");
                    // ... Aquí van todas las otras celdas
                    stringBuilder.AppendLine("</tr>");

                }
            }
            else
            {
                var datosProgramaciones = await _contextProg.TablaProgramacion
                .Where(o => o.Idlinea == maquina.Idmaquina && o.Posicion >= maquina.PosicionDesde && o.Posicion <= maquina.PosicionHasta)
                .OrderBy(o => o.Posicion).ToListAsync(cancellationToken);

                var listadoPedidos = datosProgramaciones.Select(o => o.Idpedido);

                var datosPedidos = await _contextProg.PedidoProcesado
                    .Where(o => listadoPedidos.Contains(o.IdPedido))
                    .ToListAsync(cancellationToken);

                var datosHojasG21Query = _contextProg.HojasTrabajoG21
                    .Where(o => listadoPedidos.Contains(o.Orden))
                    .AsQueryable();

                if (maquina.Idmaquinainformix != null)
                {
                    var maquinaInformixCode = maquina.Idmaquinainformix.Value.ToString("D4");
                    datosHojasG21Query = datosHojasG21Query.Where(o => o.Ccoste == maquinaInformixCode).AsQueryable();
                }

                var datosHojas = await datosHojasG21Query.ToListAsync(cancellationToken);

                stringBuilder.AppendLine("<th>Posición</th>");
                stringBuilder.AppendLine("<th>Pedido</th>");
                stringBuilder.AppendLine("<th>Apli</th>");
                stringBuilder.AppendLine("<th>HojasPedido</th>");
                stringBuilder.AppendLine("<th>HojasTiradas</th>");
                stringBuilder.AppendLine("<th>Notas</th>");
                // ... Aquí van todas las otras cabeceras
                stringBuilder.AppendLine("</tr>");
                stringBuilder.AppendLine("</thead>");
                stringBuilder.AppendLine("<tbody>");

                foreach (var item in datosProgramaciones)
                {
                    var idPedido = item.Idpedido;
                    var idAplicacion = item.Idaplicacion;
                    var hojasPedido = datosPedidos.FirstOrDefault(o => o.IdPedido == idPedido)?.HojasPedido ?? 0;

                    var doblePasada = item.Observaciones.Contains("DOS PASE") || item.Observaciones.Contains("UNO Y UNO");
                    if (doblePasada)
                    {
                        hojasPedido *= 2;
                    }

                    var hojasProcesadas = datosHojas
                        .Where(o => o.Orden == idPedido && o.Dato == idAplicacion.ToString())
                        .Sum(o => o.Cantidad);

                    var idPedidoSubstring = idPedido.ToString().Substring(2, 1);
                    var isPedidoReproceso = idPedidoSubstring == "1" || idPedidoSubstring == "3";

                    if (hojasProcesadas < 0.9 * hojasPedido || hojasProcesadas > 1.2 * hojasPedido)
                    {
                        //stringBuilder.AppendLine($"Posición: {item.Posicion}  " +
                        //                         $" | Pedido: {idPedido} " +
                        //                         $" | Apli: {idAplicacion} " +
                        //                         $" | HojasPedido: {hojasPedido} " +
                        //                         $" | HojasTiradas: {hojasProcesadas}");

                        stringBuilder.AppendLine("<tr>");
                        stringBuilder.AppendLine($"<td>{item.Posicion}</td>");
                        stringBuilder.AppendLine($"<td>{idPedido}</td>");
                        stringBuilder.AppendLine($"<td>{idAplicacion}</td>");
                        stringBuilder.AppendLine($"<td>{hojasPedido}</td>");
                        stringBuilder.AppendLine($"<td>{hojasProcesadas}</td>");

                        if (isPedidoReproceso)
                        {
                            stringBuilder.AppendLine(" <---PEDIDO DE REPROCESO");
                        }
                        if (doblePasada)
                        {
                            stringBuilder.AppendLine(" <---DOBLE PASADA");
                        }
                        if (item.Observaciones.Contains("PTICO"))
                        {
                            stringBuilder.AppendLine(" <---TRÍPTICO");
                        }
                        if (idAplicacion == 10006)
                        {
                            stringBuilder.AppendLine(" <---PASE EN VACÍO");
                        }
                        // ... Aquí van todas las otras celdas
                        stringBuilder.AppendLine("</tr>");
                    }
                }
            }
            stringBuilder.AppendLine("</tbody>");
            stringBuilder.AppendLine("</table>");
            result.Data = new List<string> { stringBuilder.ToString() };
            //result.Data = stringBuilder.ToString().Split(new[] { '\n' }, StringSplitOptions.RemoveEmptyEntries).ToList();
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: CompruebaHorariosCommand - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }
        return result;
    }

}