﻿namespace ProgramadorGeneralBLZ.Server.Controllers
{
    using CustomFilterAttributes;
    using MediatR;
    using MediaTR.Maquinas.Query;
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Identity.Web.Resource;
    using Shared;
    using Shared.DTO;
    using Shared.ResponseModels;

    [Authorize]
    [ApiController]
    [Route("[controller]")]
    //[Authorize(Roles = $"{Roles.Programador},{Roles.Admin}")]
    //IMPORTANTE: Hay que añadir aqui los roles de las máquinas, para que puedan consultar.
    //EDIT: Importante también añadir los roles sin espacios entre ellos, sino no los pilla bien.
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAdB2C:Scopes")]
    [ClaimRequirementContainRole("extension_Roles", $"{Roles.Programador},{Roles.Admin},{Roles.Consulta}," +
                                                    $"{Roles.Encargado},{Roles.JefeTurno},{Roles.Rodillos}," +
                                                    $"{Roles.Maquina}")]
    public class MaquinasController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<MaquinasController> _logger;

        public MaquinasController(IMediator mediator, ILogger<MaquinasController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }
        
        [HttpGet("dropdownbytipo/{tipoMaquina}")]
        public async Task<ListResult<MaquinaDTO>> GetLastPedido(Enums.TipoMaquina tipoMaquina)
        {
            var ser = this.User.Claims;
            var model = await _mediator.Send(new GetMaquinasDropDownQuery(tipoMaquina));
            return model;
        }
    }
}
