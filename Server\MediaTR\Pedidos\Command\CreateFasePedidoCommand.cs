﻿using MediatR;
using Nelibur.ObjectMapper;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Pedidos.Command
{
    public class CreateFasePedidoCommand : IRequest<SingleResult<int>>
    {
        public CreateFasePedidoCommand(int? idPedido, FasePedidoDTO fasePedido)
        {
            IdPedido = idPedido;
            FasePedido = fasePedido;
        }

        public int? IdPedido { get; set; }
        public FasePedidoDTO FasePedido { get; set; }
    }
    public class CreateFasePedidoCommandHandler : IRequestHandler<CreateFasePedidoCommand, SingleResult<int>>
    {
        private readonly ProgramadorLitalsaContext _contextProg;
        public CreateFasePedidoCommandHandler(ProgramadorLitalsaContext contextProg)
        {
            _contextProg = contextProg;
        }


        public async Task<SingleResult<int>> Handle(CreateFasePedidoCommand request, CancellationToken cancellationToken)
        {
            var result = new SingleResult<int>
            {
                Errors = new List<string>(),
                Data = 0
            };
            try
            {
                var nuevaFase = request.FasePedido;
                nuevaFase.Idpedido = request.IdPedido;
                var obj = TinyMapper.Map<TablaCodigosPedido>(nuevaFase);
                //MUY IMPORTANTE: marcar creacion manual para asegurarnos que no se pierden estos registros creados a mano durante
                //el proceso de actualización de cabeceras rápido - LimpiarTablaCodigosPedidoNoEnCabpedCommand
                obj.CreaccionManual = true;
                obj.Hojasaplicadas = nuevaFase.Hojasaplicadas ?? 0;
                _contextProg.TablaCodigosPedido.Add(obj);
                var dbResult = await _contextProg.SaveChangesAsync(cancellationToken);
                result.Data = dbResult;
            }
            catch (Exception e)
            {
                result.Errors.Add($"Se ha producido un error en la creación de una nueva fase para el pedido");
            }
            return result;

        }
    }
}
