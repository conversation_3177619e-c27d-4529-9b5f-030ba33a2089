﻿using System.Data;
using System.Diagnostics;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.DatoLita01;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Services.DatabaseManipulation;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Pedidos.Query;

public class GetPedidosParaProgramarBarnizadoQuery : IRequest<ListResult<DatosGeneralesPedidoDTO>>
{
    /// <summary>
    /// Obtiene el listado de pedidos programados para barnizado según los parámetros del filtro
    /// </summary>
    /// <param name="codProductoBarniz"></param>
    public GetPedidosParaProgramarBarnizadoQuery(int codProductoBarniz)
    {
        Filtro = codProductoBarniz;
    }
    public int Filtro { get; set; }
}

internal class GetPedidosParaProgramarBarnizadoQueryHandler : IRequestHandler<GetPedidosParaProgramarBarnizadoQuery, ListResult<DatosGeneralesPedidoDTO>>
{
    private readonly ProgramadorLitalsaContext _programadorLitalsaContext;
    private readonly DatoLita01Context _datoLita01Context;
    private readonly IDbContextFactory<ProgramadorLitalsaContext> _programadorLitalsaContextFactory;
    private readonly IDataManipulationService _dataManipulationService;
    public GetPedidosParaProgramarBarnizadoQueryHandler(ProgramadorLitalsaContext programadorLitalsaContext, IDbContextFactory<ProgramadorLitalsaContext> programadorLitalsaContextFactory, IDataManipulationService dataManipulationService, DatoLita01Context datoLita01Context)
    {
        _programadorLitalsaContext = programadorLitalsaContext;
        _programadorLitalsaContextFactory = programadorLitalsaContextFactory;
        _dataManipulationService = dataManipulationService;
        _datoLita01Context = datoLita01Context;
    }

    public async Task<ListResult<DatosGeneralesPedidoDTO>> Handle(GetPedidosParaProgramarBarnizadoQuery request, CancellationToken cancellationToken)
    {
        Stopwatch stopwatch = new Stopwatch();
        stopwatch.Start();
        //ATENTO A LAS OBS1!!!!! Igual hay que construirlas a mano, porque se ha dejado fuera de los pasos de actualizaciones la ñapa que hace el access
        //de leer y traer la información, concatenar y re-actualizar el campo en la tabla de los pedidos.
        var result = new ListResult<DatosGeneralesPedidoDTO>()
        {
            Data = new List<DatosGeneralesPedidoDTO>(),
            Errors = new List<string>()
        };
        try
        {
            var param = new SqlParameter()
            {
                ParameterName = "@CodBarniz",
                SqlDbType = System.Data.SqlDbType.Int,
                Value = request.Filtro
            };
            var programador = await _programadorLitalsaContextFactory.CreateDbContextAsync(cancellationToken);
            var pedidos = await programador.Set<DatosGeneralesPedidoDTO>().FromSqlRaw("[dbo].[sp_PedidosBarnizadoParaProgramar] @CodBarniz", param)
                .ToListAsync(cancellationToken);
            //Se eliminan los pedidos de más de dos años que no están marcados como mantener abierto,
            //para evitar cargar restos de info vieja que ya en su día arrastraba el access.
            var lp = pedidos.Where(o =>
                o.FechaPedido < DateTime.Now.AddYears(-2) &&
                (o.MantenerAbierto.HasValue && !o.MantenerAbierto.Value)).Select(o => o.IdPedido);
            var pedidosFiltrados = pedidos.RemoveAll(o => lp.Contains(o.IdPedido));

            if (pedidos.Any() && pedidos.Count>0)
            {
                var listaPedidos = pedidos.Select(o => o.IdPedido).ToList();
                // Crear la cadena con los Ids para la consulta SQL
                string idList = string.Join(", ", listaPedidos);
                // Consulta para datosPedido
                var datosPedido = _programadorLitalsaContext.PedidoProcesado
                    .FromSqlRaw($"SELECT * FROM PedidoProcesado WHERE IdPedido IN ({idList})")
                    .ToList();
                var datosMatPed = _programadorLitalsaContext.Matped
                    .FromSqlRaw($"SELECT * FROM Matped WHERE Nummpe IN ({idList})")
                    .ToList();
                var datosProgramaciones = _programadorLitalsaContext.TablaProgramacion
                    .FromSqlRaw($"SELECT * FROM TablaProgramacion WHERE Idpedido IN ({idList})")
                    .ToList();
                var maquinas = _programadorLitalsaContext.Maquinas.ToList();
                foreach (var p in pedidos)
                {
                    var programaciones = datosProgramaciones
                        .Where(o => o.Idpedido == p.IdPedido)
                        .ToList();

                    //Debug.WriteLine($"GetCaracteristicasHojalata INICIO: {stopwatch.ElapsedMilliseconds}");
                    p.DescHojalata = _dataManipulationService.GetCaracteristicasHojalata(p.IdPedido.Value,
                        datosPedido.Single(o => o.IdPedido == p.IdPedido),
                        datosMatPed.FirstOrDefault(o => o.Codigo == p.IdPedido.Value.ToString()));
                    //Debug.WriteLine($"GetCaracteristicasHojalata INICIO: {stopwatch.ElapsedMilliseconds}");

                    //Debug.WriteLine($"GetTextoEstadoCodigosAplicacion INICIO: {stopwatch.ElapsedMilliseconds}");
                    p.TextEstado = _dataManipulationService.GetTextoEstadoCodigosAplicacion(p.IdPedido.Value, false, false,
                        maquinas, programaciones);
                    //Debug.WriteLine($"GetTextoEstadoCodigosAplicacion FIN: {stopwatch.ElapsedMilliseconds}");
                    if (p.YaProgramado.HasValue && p.YaProgramado.Value)
                    {
                        var datosAnteriores = await _programadorLitalsaContext.TablaProgramacion
                            .Where(o => o.Idpedido == p.IdPedido && o.Idaplicacion == p.Idcodigoaplicacion && p.Posicion == o.CaraAaplicar)
                            .FirstOrDefaultAsync(cancellationToken);

                        if (datosAnteriores == null) continue;

                        p.YaProgramadoFecha = $"{datosAnteriores.HoraComienzoEstimada:dd/MM/yyyy - HH:mm}";
                        p.YaProgramadoPosicion = datosAnteriores.Posicion.ToString() ?? "N/A";
                        p.YaProgramadoLinea = maquinas.FirstOrDefault(o => o.Idmaquina == datosAnteriores.Idlinea)?.IdmaquinaG21.ToString() ?? "N/A";
                    }
                }
            }
            result.Data = pedidos;

            await programador.DisposeAsync();
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: GetPedidosParaProgramarBarnizadoQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }
        stopwatch.Stop();
        return result;
    }
}