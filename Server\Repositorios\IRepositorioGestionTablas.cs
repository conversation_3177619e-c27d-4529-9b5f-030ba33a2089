﻿using Microsoft.AspNetCore.Mvc;
using ProgramadorGeneralBLZ.Shared;

namespace ProgramadorGeneralBLZ.Server.Repositorios;

public interface IRepositorioGestionTablas
{
    Task<ActionResult<List<TDto>>> GetAllEntities<TEntity, TDto>() where TEntity : class;
    Task<ActionResult> AddEntity<TEntity, TDto>(TDto dto, string getIdMethodName, string idProperty) where TEntity : class;
    Task<ActionResult> UpdateEntity<TEntity, TDto>(int id, TDto dto, string idProperty) where TEntity : class;
    Task<ActionResult> DeleteEntity<TEntity>(int id, string idProperty) where TEntity : class;
}