using Blazored.LocalStorage;
using Blazored.Toast;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Authentication;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using Microsoft.AspNetCore.SignalR.Client;
using ProgramadorGeneralBLZ.Client;
using ProgramadorGeneralBLZ.Shared;

//Origen de arquitectura: https://github.com/carlfranklin/MsalAuthInBlazorWasm

var builder = WebAssemblyHostBuilder.CreateDefault(args);
builder.RootComponents.Add<App>("#app");
builder.RootComponents.Add<HeadOutlet>("head::after");

builder.Services.AddBlazoredToast();

builder.Services.AddBlazoredLocalStorage();

builder.Services.AddDevExpressBlazor();
builder.Services.Configure<DevExpress.Blazor.Configuration.GlobalOptions>(options => {
    options.BootstrapVersion = DevExpress.Blazor.BootstrapVersion.v5;
});

builder.Services.AddScoped<SpinnerService>();
builder.Services.AddScoped<FiltroService>();
builder.Services.AddScoped<DatabaseService>();
builder.Services.AddHttpClient("ProgramadorGeneralBLZ.ServerAPI", client => client.BaseAddress = new Uri(builder.HostEnvironment.BaseAddress))
    .AddHttpMessageHandler<BaseAddressAuthorizationMessageHandler>();

// Supply HttpClient instances that include access tokens when making requests to the server project
builder.Services.AddScoped(sp => sp.GetRequiredService<IHttpClientFactory>().CreateClient("ProgramadorGeneralBLZ.ServerAPI"));
builder.Services.AddHttpClient("DatabaseAPI", client =>
{
    client.BaseAddress = new Uri(builder.HostEnvironment.BaseAddress);
});
builder.Services.AddMsalAuthentication(options =>
{
    builder.Configuration.Bind("AzureAdB2C", options.ProviderOptions.Authentication);
    options.ProviderOptions.DefaultAccessTokenScopes.Add("https://LitalsaAuthService.onmicrosoft.com/f7013c5c-8837-4d9d-8f6f-c72abd38e400/api_access");
    options.ProviderOptions.LoginMode = "redirect";
    //Hack/Chapuza/Genialidad para usar el custom attribute llamado extension_Roles, del AAD B2C, como si fuesen roles normales
    options.UserOptions.RoleClaim = "extension_Roles";
    //options.UserOptions.RoleClaim = "role";
});

builder.Services.AddApiAuthorization()
    .AddAccountClaimsPrincipalFactory<CustomUserFactory>();

// Configurar el HubConnection como un servicio
builder.Services.AddSingleton(sp => new HubConnectionBuilder()
    .WithUrl(sp.GetRequiredService<NavigationManager>().ToAbsoluteUri("/imprimirHub"))
    .WithAutomaticReconnect()
    .Build());

await builder.Build().RunAsync();
