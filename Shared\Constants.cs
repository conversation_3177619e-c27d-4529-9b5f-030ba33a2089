﻿namespace ProgramadorGeneralBLZ.Shared
{
    public class Constants
    {
        public const string ReprocesosPattern = "^2.[3|6]....";
        public const int MaxPedidoBarnizado = 7000;
    }
    public static class TiposMensajesImprimirHub
    {
        public const string GrupoAbandonado = "GrupoAbandonado";
        public const string UnidoANuevoGrupo = "UnidoANuevoGrupo";

        public const string IdLineaActualizada_Emisor = "IdLineaActualizada_Emisor";

        public const string PedidoActualizado_Emisor = "PedidoActualizado_Emisor";  // notifica únicamente al cliente emisor para que pinte en local
        public const string PedidoActualizado_Receptores = "PedidoActualizado_Receptores";  // notifica únicamente a los receptores, excluyendo al cliente emisor
        public const string PedidoPublicado_Emisor = "PedidoPublicado_Emisor";  // notifica únicamente al cliente emisor para informar que los receptores han recibido

        public const string ProgramacionActualizada_Emisor = "ProgramacionActualizada_Emisor";
        public const string ProgramacionActualizada_Receptores = "ProgramacionActualizada_Receptores";
    }
}
