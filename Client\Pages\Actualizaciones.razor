﻿@page "/actualizaciones"
@using ProgramadorGeneralBLZ.Shared
@using System.Net
@using System.Text.Json
@using Microsoft.Extensions.Configuration
@using ProgramadorGeneralBLZ.Shared.ResponseModels
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.WebAssembly.Hosting

@inject IConfiguration Config
@inject IWebAssemblyHostEnvironment HostEnv
@inject IToastService ToastService
@inject HttpClient Http
@inject IHttpClientFactory ClientFactory
@inject SpinnerService SpinnerService
@inject AuthenticationStateProvider GetAuthenticationStateAsync
@inject DatabaseService DatabaseService


@attribute [Authorize(Roles = $"{Roles.Admin}, {Roles.Programador}")]

<PageTitle>Actualizaciones</PageTitle>

<AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}")">
    <Authorized Context="authContext">
        <DxLayoutBreakpoint DeviceSize="DeviceSize.Large" @bind-IsActive="@_isSmallScreen" />
        <div class="h-100 overflow-auto px-2 py-1">
            <DxFormLayout SizeMode="SizeMode.Small">
                <DxGridLayout CssClass="h-100" ColumnSpacing="1px" RowSpacing="1px">
                    <Rows>
                        <DxGridLayoutRow Areas="CintaBotones" Height="auto" />
                    </Rows>
                    <Items>
                        <DxGridLayoutItem Area="CintaBotones">
                            <Template>
                                <div class="gridlayout-ci gridlayout-item">
                                    @if (HostEnv.IsDevelopment())
                                    {
                                            <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="12">
                                                <DxFormLayoutItem Context="botones" ColSpanLg="3" ColSpanXs="6">
                                                    <DxButton RenderStyle="ButtonRenderStyle.Danger"
                                                    Click="@(e => LanzarActualizacion(Enums.TipoUpdate.Pruebas, ""))"
                                                    Text="PRUEBAS" CssClass="btnSizeXL"/>
                                                </DxFormLayoutItem>
                                            </DxFormLayoutGroup>
                                    }
                                    <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="12">
                                        <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.Card" ColSpanXs="12" ColSpanLg="5" Caption="Procesos de Actualización">
                                            <DxFormLayoutItem Context="botones" ColSpanLg="3" ColSpanXs="6">
                                                <DxButton RenderStyle="ButtonRenderStyle.Primary"
                                                Click="@(e => LanzarActualizacion(Enums.TipoUpdate.CabecerasRapido, ""))"
                                                Text="Actualizar Cabeceras (Rápido)" CssClass="btnSizeXL"/>
                                            </DxFormLayoutItem><DxFormLayoutItem Context="botones" ColSpanLg="3" ColSpanXs="6">
                                                <DxButton RenderStyle="ButtonRenderStyle.Warning"
                                                Click="@(e => LanzarActualizacion(Enums.TipoUpdate.CabecerasRapidoV2, ""))"
                                                Text="Actualizar Cabeceras V2" CssClass="btnSizeXL"/>
                                            </DxFormLayoutItem>
                                            <DxFormLayoutItem Context="botones" ColSpanLg="3" ColSpanXs="6">
                                                <DxButton RenderStyle="ButtonRenderStyle.Warning"
                                                Click="@(e => LanzarActualizacion(Enums.TipoUpdate.Tuberias, ""))"
                                                Text="Actualizar Tuberías" CssClass="btnSizeXL"/>
                                            </DxFormLayoutItem>
                                            <DxFormLayoutItem Context="botones" ColSpanLg="3" ColSpanXs="6">
                                                <DxButton RenderStyle="ButtonRenderStyle.Warning"
                                                Click="@(e => LanzarActualizacion(Enums.TipoUpdate.CorregirMotivos, ""))"
                                                Text="Corregir Motivos" CssClass="btnSizeXL"/>
                                            </DxFormLayoutItem>
                                            <DxFormLayoutItem Context="botones" ColSpanLg="3" ColSpanXs="6" BeginRow="true">
                                                <DxButton RenderStyle="ButtonRenderStyle.Success"
                                                Click="@(e => LanzarActualizacion(Enums.TipoUpdate.Productos, ""))"
                                                Text="Actualizar Productos (Puntual)" CssClass="btnSizeXL"/>
                                            </DxFormLayoutItem>
                                            <DxFormLayoutItem Context="botones" ColSpanLg="3" ColSpanXs="6">
                                                <DxButton RenderStyle="ButtonRenderStyle.Success"
                                                Click="@(e => LanzarActualizacion(Enums.TipoUpdate.CabecerasPuntual, "puntual"))"
                                                Text="Actualizar Cabeceras (Puntual)" CssClass="btnSizeXL"/>
                                            </DxFormLayoutItem>
                                            <DxFormLayoutItem Context="botones" ColSpanLg="3" ColSpanXs="6">
                                                <DxButton RenderStyle="ButtonRenderStyle.Success"
                                                Click="@(e => LanzarActualizacion(Enums.TipoUpdate.HojasPuntual, "puntual"))"
                                                Text="Actualizar Hojas (Puntual)" CssClass="btnSizeXL"/>
                                            </DxFormLayoutItem>
                                            <DxFormLayoutItem Context="botones" ColSpanLg="3" ColSpanXs="6" BeginRow="true">
                                                <DxButton RenderStyle="ButtonRenderStyle.Secondary"
                                                Click="@(e => LanzarActualizacion(Enums.TipoUpdate.CabecerasTotal, "total"))"
                                                Text="Actualizar Cabeceras (Total)" CssClass="btnSizeXL"/>
                                            </DxFormLayoutItem>
                                            <DxFormLayoutItem Context="botones" ColSpanLg="3" ColSpanXs="6">
                                                <DxButton RenderStyle="ButtonRenderStyle.Secondary"
                                                Click="@(e => LanzarActualizacion(Enums.TipoUpdate.HojasTotal, "total"))"
                                                Text="Actualizar Hojas (Total)" CssClass="btnSizeXL"/>
                                            </DxFormLayoutItem>
                                            <DxFormLayoutItem Context="botones" ColSpanLg="3" ColSpanXs="6">
                                                <DxButton RenderStyle="ButtonRenderStyle.Secondary"
                                                Click="@(e => LanzarActualizacion(Enums.TipoUpdate.Productos, ""))"
                                                Text="Actualizar Productos y CodApli" CssClass="btnSizeXL"/>
                                            </DxFormLayoutItem>
                                            <DxFormLayoutItem Context="botones" ColSpanLg="3" ColSpanXs="6" BeginRow="true">
                                                <DxButton RenderStyle="ButtonRenderStyle.Danger"
                                                Click="@BackupDatabase"
                                                Text="BackUp Base de Datos" CssClass="btnSizeXL"/>
                                            </DxFormLayoutItem>
                                            <DxFormLayoutItem Context="botones" ColSpanLg="3" ColSpanXs="6">
                                                <DxButton RenderStyle="ButtonRenderStyle.Danger"
                                                Click="@RestoreDatabase"
                                                Text="Copiar BD a DESARROLLO" CssClass="btnSizeXL"/>
                                            </DxFormLayoutItem>
                                        </DxFormLayoutGroup>
                                    </DxFormLayoutGroup>
                                </div>
                            </Template>
                        </DxGridLayoutItem>
                    </Items>
                </DxGridLayout>
            </DxFormLayout>
        </div>

        <DxWindow HeaderText="Pedidos a revisar" @bind-Visible="@_isPopupVisible" Height="800px" MinWidth="1480" Width="1480px" Scrollable="true"
        ShowCloseButton="true" CloseOnEscape="true" AllowResize="true" AllowDrag="true">
            <BodyContentTemplate>
                <div>
                    <p>@((MarkupString)RespuestaString)</p>
                </div>
            </BodyContentTemplate>
        </DxWindow>

    </Authorized>
    <NotAuthorized>
        <NoPuedesPasar />
    </NotAuthorized>
</AuthorizeView>

@code {
    private bool _isSmallScreen;
    DxWindow _window;
    private string RespuestaString { get; set; }
    private bool _isPopupVisible;

    // protected override async Task OnAfterRenderAsync(bool firstRender)
    // {
    //     if (firstRender)
    //     {
    //     }
    // }
    private async Task LanzarActualizacion(Enums.TipoUpdate tipo, string ambito)
    {
        SpinnerService.Show();
        Console.WriteLine($"url= {Config["API_ActualizarDB"]}");
        var client = ClientFactory.CreateClient();
        client.Timeout = TimeSpan.FromSeconds(400);
        HttpResponseMessage response;
        if (HostEnv.IsDevelopment())
        {
            response = new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.OK
                };
        }
        else
        {
            response = tipo switch
            {
                Enums.TipoUpdate.Pruebas => await client.GetAsync($"{Config["API_ActualizarDB"]}/prueba", CancellationToken.None),
                Enums.TipoUpdate.CabecerasRapido => await client.GetAsync($"{Config["API_ActualizarDB"]}/actualizarcabecerasrapido", CancellationToken.None),
                Enums.TipoUpdate.CabecerasRapidoV2 => await client.GetAsync($"{Config["API_ActualizarDB"]}/actualizarcabecerasrapidov2", CancellationToken.None),
                Enums.TipoUpdate.CabecerasPuntual or Enums.TipoUpdate.CabecerasTotal => await client.GetAsync($"{Config["API_ActualizarDB"]}/actualizarcabecerastotal?ambito={ambito}", CancellationToken.None),
                Enums.TipoUpdate.HojasPuntual or Enums.TipoUpdate.HojasTotal => await client.GetAsync($"{Config["API_ActualizarDB"]}/actualizarhojas?ambito={ambito}", CancellationToken.None),
                Enums.TipoUpdate.Productos => await client.GetAsync($"{Config["API_ActualizarDB"]}/actualizarproductos", CancellationToken.None),
                Enums.TipoUpdate.Tuberias => await client.GetAsync($"{Config["API_ActualizarDB"]}/actualizartuberias", CancellationToken.None),
                Enums.TipoUpdate.CorregirMotivos => await client.GetAsync($"{Config["API_ActualizarDB"]}/corregirerrormotivos", CancellationToken.None),
                _ => throw new ArgumentOutOfRangeException(nameof(tipo), tipo, null)
            };
        }

        if (response.StatusCode != HttpStatusCode.OK)
        {
            var msj = await response.Content.ReadFromJsonAsync<ApiProblem>();
            ToastService.ShowError($"ERROR: Status {msj.Status} - {msj.Detail}");
        }
        else
        {
            if (tipo == Enums.TipoUpdate.CabecerasRapido)
            {
                //TODO: Hay que revisar estos tres métodos y arreglarlos, unificarlos, eliminarlos, corregirlos o algo...
                //Comprobar pedidos con posiciones no informadas
                var resultadoConsultaPedProcesoSinPosicion = await Http.GetFromJsonAsync<SingleResult<string>>("DatosGenerales/getConsultaRevisionProcesos");
                //Volvemos a actualizar pedproceso... No tiene sentido. - Se deja comentado por si acaso
                // var resultadoControl = await Http.GetFromJsonAsync<SingleResult<string>>("DatosGenerales/getControlActualizacionPedProceso");
                var resultadoPedidosModificados = await Http.GetFromJsonAsync<SingleResult<string>>("DatosGenerales/getAuditoriaActualizacionDatosPedido");
                //20040712: Se comprueban directamente los datos de la tabla de datosactualizables con los pedidos y se actualiza donde haga falta.
                var resultadoDatosAct = await Http.GetFromJsonAsync<SingleResult<bool>>("DatosGenerales/getConsultaDatosActualizables");
                if (resultadoConsultaPedProcesoSinPosicion.Errors.Any())
                {
                    ToastService.ShowError($"Error en la llamada de consulta de revisiones de procesos.");
                }
                else if (resultadoPedidosModificados.Errors.Any())
                {
                    ToastService.ShowError($"Error en la llamada de revisión de datos de pedidos modificados.");
                }
                else if (resultadoDatosAct.Errors.Any())
                {
                    ToastService.ShowError(resultadoDatosAct.Errors.FirstOrDefault());
                }
                else
                {
                    RespuestaString = resultadoConsultaPedProcesoSinPosicion.Data + resultadoPedidosModificados.Data;
                    _isPopupVisible = !string.IsNullOrEmpty(RespuestaString);
                }
            }
            else if (tipo is Enums.TipoUpdate.Pruebas or Enums.TipoUpdate.CabecerasRapidoV2)
            {
                //Comprobar pedidos con posiciones no informadas
                var resultadoConsultaPedProcesoSinPosicion = await Http.GetFromJsonAsync<SingleResult<string>>("DatosGenerales/getConsultaRevisionProcesos");
                var resultadoPedidosModificados = await Http.GetFromJsonAsync<SingleResult<string>>("DatosGenerales/getAuditoriaActualizacionDatosPedido");
                //20040712: Se comprueban directamente los datos de la tabla de datosactualizables con los pedidos y se actualiza donde haga falta.
                var resultadoDatosAct = await Http.GetFromJsonAsync<SingleResult<bool>>("DatosGenerales/getConsultaDatosActualizables");
                if (resultadoDatosAct.HasErrors || resultadoPedidosModificados.HasErrors || resultadoConsultaPedProcesoSinPosicion.HasErrors)
                {
                    ToastService.ShowError(
                        resultadoDatosAct.Errors
                            .Concat(resultadoPedidosModificados.Errors)
                            .Concat(resultadoConsultaPedProcesoSinPosicion.Errors).FirstOrDefault() ?? "Error sin descripcion");
                }
                else
                {
                    RespuestaString = resultadoPedidosModificados.Data;
                    _isPopupVisible = !string.IsNullOrEmpty(RespuestaString);
                }
            }
            ToastService.ShowSuccess("Sistema actualizado.");
        }
        SpinnerService.Hide();
    }

    private async Task BackupDatabase()
    {
        SpinnerService.Show();
        // await _window.ShowAsync();
        try
        {
            var message = await DatabaseService.BackupDatabaseAsync();
            ToastService.ShowSuccess(message);
        }
        catch (Exception ex)
        {
            ToastService.ShowError($"Error: {ex.Message}");
        }
        // await _window.CloseAsync();
        SpinnerService.Hide();
    }

    private async Task RestoreDatabase()
    {
        SpinnerService.Show();
        // await _window.ShowAsync();
        try
        {
            var message = await DatabaseService.RestoreDatabaseAsync();
            ToastService.ShowSuccess(message);
        }
        catch (Exception ex)
        {
            ToastService.ShowError($"Error: {ex.Message}");
        }
        // await _window.CloseAsync();
        SpinnerService.Hide();
    }

}