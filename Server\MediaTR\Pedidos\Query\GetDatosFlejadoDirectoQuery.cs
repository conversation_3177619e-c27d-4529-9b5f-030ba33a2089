﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Services.DatabaseManipulation;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Pedidos.Query;

public class GetDatosFlejadoDirectoQuery : IRequest<SingleResult<string>>
{
    public GetDatosFlejadoDirectoQuery(int idPedido)
    {
        IdPedido = idPedido;
    }
    public int IdPedido { get; set; }
}

public class GetDatosFlejadoDirectoQueryHandler : IRequestHandler<GetDatosFlejadoDirectoQuery, SingleResult<string>>
{
    private readonly ProgramadorLitalsaContext _contextProg;
    private readonly IDataManipulationService _dataManipulationService;

    public GetDatosFlejadoDirectoQueryHandler(ProgramadorLitalsaContext contextProg, IDataManipulationService dataManipulationService)
    {
        _contextProg = contextProg;
        _dataManipulationService = dataManipulationService;
    }

    public async Task<SingleResult<string>> Handle(GetDatosFlejadoDirectoQuery request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<string> { Errors = new List<string>(), Data = "" };
        try
        {
            var idPedido = request.IdPedido;
            var datosFlejado = _dataManipulationService.DevuelveObservacionesFlejado(idPedido);
            var datosPedido = _contextProg.PedidoProcesado
                .FirstOrDefaultAsync(o => o.IdPedido.Value == idPedido, cancellationToken)
                .Result.Obsflejado;

            result.Data = $"{datosFlejado}, {datosPedido}";
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: GetDatosFlejadoDirectoQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            //throw new Exception(errorText, e);
            result.Errors.Add(errorText);
        }
        return result;
    }
}