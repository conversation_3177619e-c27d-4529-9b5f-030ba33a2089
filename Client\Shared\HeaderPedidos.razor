﻿@inject NavigationManager Navigation

<DxMenuItem CssClass="search-menu-item" BeginGroup="true" Position="ItemPosition.Start">
    <Template>
        <DxTextBox NullText="Pedido..."
                    SizeMode="SizeMode.Large"
                    @bind-Value="@_pedidoSeleccionado"
                    CssClass="mx-3"/>
    </Template>
</DxMenuItem>


@code {
    string _pedidoSeleccionado { get; set; }
    bool _pedidoBloqueado = false;
    
}
