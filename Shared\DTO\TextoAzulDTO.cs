﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProgramadorGeneralBLZ.Shared.DTO
{
    public class TextoAzulBarnizado
    {
        public float? Solidos { get; set; }
        public double? HTotales { get; set; }
        public double? SupTotal { get; set; }
        public double? BarnizNecesario { get; set; }
        public float? Existencias { get; set; }
    }
    public class TextoAzulFotoLito
    {
        public double? HTotales { get; set; }
        public double? PasesTotales { get; set; }
    }
    public class TextoAzulLitografia
    {
        public double? HTotales { get; set; }
        public double? PasesTotales { get; set; }
        public double? HojasEfectivas { get; set; }
        public double? DiasTrabajo { get; set; }
        public DateTime? PrevisionFinal { get; set; }
    }
}
