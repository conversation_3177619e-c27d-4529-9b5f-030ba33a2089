﻿@using ProgramadorGeneralBLZ.Shared
@using ProgramadorGeneralBLZ.Shared.DTO
@using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa
@using ProgramadorGeneralBLZ.Shared.ResponseModels
@inject HttpClient Http
@inject SpinnerService SpinnerService
@inject IToastService ToastService

<div class="h-100 overflow-auto px-2 py-1">
    <DxGrid Data="@planos" SizeMode="SizeMode.Medium"
            CssClass="ch-320 smallFont progGrid" PageSize="18"
            KeyFieldName="Idplano" Context="GridPlano"
            AllowSort="true" EditNewRowPosition="GridEditNewRowPosition.Top"
            ShowFilterRow="true" ValidationEnabled="true"
            EditMode="GridEditMode.EditRow" @ref="Grid" PagerPosition="GridPagerPosition.TopAndBottom"
            EditorRenderMode="GridEditorRenderMode.Integrated"
            EditModelSaving="GridPlano_EditModelSaving"
            DataItemDeleting="Grid_DataItemDeleting">
        <Columns>
            <DxGridCommandColumn Width="80px">
                <HeaderTemplate>
                    <a class="oi oi-plus" @onclick="@(() => Grid.StartEditNewRowAsync())" style="text-decoration: none;color: lightskyblue;" href="javascript:void(0);"></a>
                </HeaderTemplate>
                <CellDisplayTemplate>
                    <a class="oi oi-pencil" @onclick="@(() => Grid.StartEditRowAsync(context.VisibleIndex))" style="text-decoration: none; padding-right: 15px; color: #c75fff;" href="javascript:void(0);"></a>
                    <a class="oi oi-x" @onclick="@(() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex))" style="text-decoration: none; color: red;" href="javascript:void(0);"></a>
                </CellDisplayTemplate>
                <CellEditTemplate>
                    <a class="oi oi-arrow-thick-bottom" @onclick="@(() => Grid.SaveChangesAsync())" style="text-decoration: none; padding-right: 15px; color: greenyellow; margin-right: 6px; margin-top: 3px;" href="javascript:void(0);"></a>
                    <a class="oi oi-x" @onclick="@(() => Grid.CancelEditAsync())" style="text-decoration: none; color: red;" href="javascript:void(0);"></a>
                </CellEditTemplate>
            </DxGridCommandColumn>
            <DxGridDataColumn FieldName="Idplano" Visible="false" />
            <DxGridDataColumn FieldName="@nameof(PlanoDTO.CodigoPlano)" Caption="Código Plano" Width="180"
                              FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
            <DxGridDataColumn FieldName="@nameof(PlanoDTO.Idcliente)" Caption="Cliente" Width="250">
                <CellDisplayTemplate Context="cellText">
                    @{
                        int? summary = (int?)cellText.Value;
                    }
                    <DxComboBox Data="@clientes" ReadOnly="true"
                                FilteringMode="DataGridFilteringMode.Contains"
                                TextFieldName="Combinado" 
                                ValueFieldName="CodigoCliente"
                                ListRenderMode="ListRenderMode.Virtual"
                    @bind-Value="summary">
                    </DxComboBox>
                </CellDisplayTemplate>
            </DxGridDataColumn>
            <DxGridDataColumn FieldName="@nameof(PlanoDTO.Escuadra)" Caption="Escuadra" TextAlignment="GridTextAlignment.Center"
                              FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="100" />
            <DxGridDataColumn FieldName="@nameof(PlanoDTO.FormaBarnizado)" Caption="F.Barnizado" Width="100" TextAlignment="GridTextAlignment.Center"
                              FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
            <DxGridDataColumn FieldName="@nameof(PlanoDTO.FormaLitografía)" Caption="F.Litografía" Width="100" TextAlignment="GridTextAlignment.Center"
                              FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
        </Columns>
        <DataColumnCellEditTemplate>
            @{
                var plano = (PlanoDTO)GridPlano.EditModel;
            }
            @switch (GridPlano.DataColumn.FieldName)
            {
                case "CodigoPlano":
                    <DxTextBox @bind-Text="@plano.CodigoPlano" maxlength="50"
                               ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                               ShowValidationIcon="true" />
                    break;
                case "Idcliente":
                    <DxComboBox Data="@clientes" ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                FilteringMode="DataGridFilteringMode.Contains"
                                TextFieldName="Combinado"
                                ValueFieldName="CodigoCliente"
                                ListRenderMode="ListRenderMode.Virtual"
                                @bind-Value="plano.Idcliente">
                    </DxComboBox>
                    break;
                case "Escuadra":
                    <DxTextBox @bind-Text="@plano.Escuadra" maxlength="1"
                               ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                               ShowValidationIcon="true" />
                    break;
                case "FormaBarnizado":
                    <DxTextBox @bind-Text="@plano.FormaBarnizado" maxlength="1"
                               ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                               ShowValidationIcon="true" />
                    break;
                case "FormaLitografía":
                    <DxTextBox @bind-Text="@plano.FormaLitografía" maxlength="1"
                               ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                               ShowValidationIcon="true" />
                    break;
            }
        </DataColumnCellEditTemplate>
    </DxGrid>
</div>
@code
{
    DxGrid? Grid;
    List<PlanoDTO> planos = new List<PlanoDTO>();
    List<ClienteDropdownDTO> clientes = new List<ClienteDropdownDTO>();

    protected override async Task OnInitializedAsync()
    {
        SpinnerService.Show();
        await LoadData();
        SpinnerService.Hide();
    }

    async Task LoadData()
    {
        var result = await Http.GetFromJsonAsync<ListResult<ClienteDropdownDTO>>($"DatosGenerales/getclientesdropdown?idpedidocliente=");
        clientes = result.Data;
        planos = await Http.GetFromJsonAsync<List<PlanoDTO>>("GestionTablasV2/Plano");
    }

    async Task GridPlano_EditModelSaving(GridEditModelSavingEventArgs e)
    {
        SpinnerService.Show();
        var dest = (PlanoDTO)e.EditModel;
        var response = e.IsNew == false
            ? await Http.PutAsJsonAsync($"GestionTablasV2/Plano/{dest.Idplano}", dest)
            : await Http.PostAsJsonAsync("GestionTablasV2/Plano", dest);
        if (response.IsSuccessStatusCode)
            await LoadData();
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            ToastService.ShowError(error);
        }
        SpinnerService.Hide();
    }
    async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        SpinnerService.Show();
        var item = (PlanoDTO)e.DataItem;
        var response = await Http.DeleteAsync($"GestionTablasV2/Plano/{item.Idplano}");
        if (response.IsSuccessStatusCode)
            await LoadData();
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            ToastService.ShowError(error);
        }
        SpinnerService.Hide();
    }
    async Task Delete(PlanoDTO item)
    {
        SpinnerService.Show();
        var response = await Http.DeleteAsync($"GestionTablasV2/Plano/{item.Idplano}");
        if (response.IsSuccessStatusCode)
            await LoadData();
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            ToastService.ShowError(error);
        }
        SpinnerService.Hide();
    }
}
