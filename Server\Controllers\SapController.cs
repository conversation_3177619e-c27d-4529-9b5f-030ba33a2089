﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Identity.Web.Resource;
using ProgramadorGeneralBLZ.Server.CustomFilterAttributes;
using ProgramadorGeneralBLZ.Server.Data.InterfaseBD;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.MediaTR.Pedidos.Query;
using ProgramadorGeneralBLZ.Server.MediaTR.Sap.Query;
using ProgramadorGeneralBLZ.Shared;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.DTO.SAP;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.Controllers
{
    [Authorize]
    [ApiController]
    [Route("[controller]")]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAdB2C:Scopes")]
    //[Authorize(Roles = $"{Roles.Programador},{Roles.Admin}")]
    [ClaimRequirementContainRole("extension_Roles", $"{Roles.Programador},{Roles.Admin}")]
    public class SapController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly InterfaseBDContext _context;
        private readonly ProgramadorLitalsaContext _contextProg;

        public SapController(IMediator mediator, InterfaseBDContext context, ProgramadorLitalsaContext contextProg)
        {
            _mediator = mediator;
            _context = context;
            _contextProg = contextProg;
        }

        [HttpGet("getlast")]
        public async Task<SingleResult<ListadoPedidosProcesadosFiltradosDTO>> GetLastPedido()
        {
            var model = await _mediator.Send(new GetPedidoSapProgramadoQuery(
                null, null, null, true));
            return model;
        }

        [HttpGet("getpedido/{idPedido}")]
        public async Task<SingleResult<ListadoPedidosProcesadosFiltradosDTO>> GetLastPedido(int idPedido)
        {
            var model = await _mediator.Send(new GetPedidoProgramadoQuery(idPedido, null));
            return model;
        }
        [HttpGet("getpedidocliente/{idPedido}")]
        public async Task<SingleResult<ListadoPedidosProcesadosFiltradosDTO>> GetPedidoDeCliente(string idPedido, string idCliente)
        {
            var model = await _mediator.Send(new GetPedidoProgramadoQuery(
                null, idCliente, Uri.UnescapeDataString(idPedido)));
            return model;
        }
    }
}
