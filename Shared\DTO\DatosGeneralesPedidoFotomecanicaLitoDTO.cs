﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProgramadorGeneralBLZ.Shared.DTO
{
    public class DatosGeneralesPedidoFotomecanicaLitoDTO
    {
        public int? Id { get; set; }
        public int? IdPedido { get; set; }
        public string? Supedido { get; set; }
        public float? Formato { get; set; }//double
        public int? IdCliente { get; set; }
        public string? DatosCliente { get; set; }
        public DateTime? FechaPedido { get; set; }
        public DateTime? RequeridoEnFecha { get; set; }
        public int? HojasPedido { get; set; }
        public int? HojasTerminadas { get; set; }
        public string? Motivos { get; set; }
        public string? TipoElemento { get; set; }
        public string? Plano { get; set; }
        public bool? PlanchasPedidas { get; set; }
        public int? Pi1ped { get; set; }
        public int? Hojaspi1ped { get; set; }
        public int? Pi2ped { get; set; }
        public int? Hojaspi2ped { get; set; }
        public int? Pi3ped { get; set; }
        public int? Hojaspi3ped { get; set; }
        public int? Pe1ped { get; set; }
        public int? Hojaspe1ped { get; set; }
        public int? Pe2ped { get; set; }
        public int? Hojaspe2ped { get; set; }
        public int? Pe3ped { get; set; }
        public int? Hojaspe3ped { get; set; }
        public bool? URGENTE { get; set; }
        public int? Largo_hjlta { get; set; }
        public int? Ancho_hjlta { get; set; }
        public int? Tipo_hjlta { get; set; }//int???????
        public int? Espesor_hjlta { get; set; }
        public string? Tintas { get; set; }
        public string? TextoTintas { get; set; }
        public bool? Anulado { get; set; }
        public DateTime? FechaPlanchas { get; set; }
        public string? Escuadra { get; set; }
        public string? ObservacionesPedido { get; set; }
        public string? TipoPedido { get; set; }
        public DateTime? FechaFin { get; set; }
        public int? Idcodigoaplicacion { get; set; }
        public string? TextoIdAplicacion { get; set; }
        public string? ObservacionesAplicacion { get; set; }
        public int? LineaTintas { get; set; }
        public DateTime? FechaAsignacionImpresora { get; set; }
        public DateTime? FechaEntregaSolicitada { get; set; }
        public string? EstadoTintas { get; set; }
        //public bool? SINCARPETA { get; set; }
        public int? C01ped { get; set; }
        public int? C02ped { get; set; }
        public int? C03ped { get; set; }
        public int? C04ped { get; set; }
        public int? C05ped { get; set; }
        public int? C06ped { get; set; }
        public int? C07ped { get; set; }
        public int? C08ped { get; set; }
        public int? C09ped { get; set; }
        public int? Co10ped { get; set; }
        public string? Obs1 { get; set; }
        public string? Obs2 { get; set; }
        public string? Obs3 { get; set; }
        public string? ObsProgramacion { get; set; }
        public string? Estado { get; set; }
        public string? Obsflejado { get; set; }
        public bool? SINCARPETA { get; set; }
        public string? Posicion { get; set; }
        [NotMapped]
        public string? DescHojalata { get; set; }
        [NotMapped]
        public string? TextEstado { get; set; }
        [NotMapped]
        public int? PasesTotales { get; set; }
        [NotMapped]
        public int? MultiplicadorHojas { get; set; }
        [NotMapped]
        public int? VelocidadParaPedido { get; set; }
    }
}
