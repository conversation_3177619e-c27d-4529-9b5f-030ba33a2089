﻿using System.Text;
using MediatR;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.DatosGenerales.Query;

public class GetAuditoriaActualizacionDatosPedidoQuery : IRequest<SingleResult<string>>
{
}

internal class GetAuditoriaActualizacionDatosPedidoQueryHandler : IRequestHandler<GetAuditoriaActualizacionDatosPedidoQuery, SingleResult<string>>
{

    private readonly ProgramadorLitalsaContext _contextProg;
    public GetAuditoriaActualizacionDatosPedidoQueryHandler(ProgramadorLitalsaContext contextProg)
    {
        _contextProg = contextProg;
    }

    public async Task<SingleResult<string>> Handle(GetAuditoriaActualizacionDatosPedidoQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var result = new SingleResult<string>
            {
                Data = "",
                Errors = new List<string>()
            };
            var data = await _contextProg.AuditoriaActualizacionDatosPedido
                .Where(a => a.Bloque == _contextProg.AuditoriaActualizacionDatosPedido.Max(x => x.Bloque)
                )
                .OrderByDescending(a => a.Id)
                .ToListAsync(cancellationToken);

            if (data is { Count: 0 })
                return result;

            var pedidosSoloInformados = data
                .Where(o => o.CampoModificado
                    .Contains("informado", StringComparison.OrdinalIgnoreCase))
                .Select(o => o.IdPedido)
                .Distinct()
                .ToHashSet();

            var stringBuilder = new StringBuilder();
            stringBuilder.AppendLine("<style>");
            stringBuilder.AppendLine("table { border-collapse: collapse; }");
            stringBuilder.AppendLine("td, th { border: 1px solid white; padding-left: 8px; padding-right: 8px; }");
            stringBuilder.AppendLine("th, span { font-weight: bold; }");
            stringBuilder.AppendLine("</style>");
            stringBuilder.AppendLine("<h1>PEDIDOS MODIFICADOS</h1></br>");
            stringBuilder.AppendLine("<table border=\"1\">");
            stringBuilder.AppendLine("<thead>");
            stringBuilder.AppendLine("<tr>");
            stringBuilder.AppendLine("<th>Pedido    </th>");
            stringBuilder.AppendLine("<th>Campo   </th>");
            stringBuilder.AppendLine("<th>Valor Antiguo     </th>");
            stringBuilder.AppendLine("<th>Valor Nuevo    </th>");
            stringBuilder.AppendLine("<th>Fecha Registro    </th>");
            stringBuilder.AppendLine("<th>Bloque    </th>");
            stringBuilder.AppendLine("</tr>");
            stringBuilder.AppendLine("</thead>");
            stringBuilder.AppendLine("<tbody>");

            foreach (var item in data.Where(o => !pedidosSoloInformados.Contains(o.IdPedido)))
            {
                stringBuilder.AppendLine("<tr>");
                stringBuilder.AppendLine($"<td>{item.IdPedido}</td>");
                stringBuilder.AppendLine($"<td>{item.CampoModificado}</td>");
                stringBuilder.AppendLine($"<td>{item.ValorAntiguo}</td>");
                stringBuilder.AppendLine($"<td>{item.ValorNuevo}</td>");
                stringBuilder.AppendLine($"<td>{item.FechaRegistro}</td>");
                stringBuilder.AppendLine($"<td>{item.Bloque}</td>");
                stringBuilder.AppendLine("</tr>");
            }

            stringBuilder.AppendLine("</tbody>");
            stringBuilder.AppendLine("</table>");

            stringBuilder.AppendLine("</br>");
            stringBuilder.AppendLine("</br>");
            stringBuilder.AppendLine("<h1>PEDIDOS SOLO INFORMADOS</h1></br>");

            foreach (var pedido in pedidosSoloInformados)
            {
                var cambios = data.Where(o => o.IdPedido == pedido).ToList();

                // 1) Cabecera
                stringBuilder.AppendLine("<br/>");
                stringBuilder.AppendLine($"<p><strong>Pedido {pedido}</strong></p>");

                foreach (var cambio in cambios
                             .Where(c => !c.CampoModificado
                                 .Contains("informado", StringComparison.OrdinalIgnoreCase)))
                {
                    stringBuilder.AppendLine(
                        $"<p>{cambio.CampoModificado}: en Programador {cambio.ValorAntiguo} en IN2 {cambio.ValorNuevo}</p>");
                }
            }
            
            //Borramos los logs de mas de 180 días
            var borrar = await _contextProg.AuditoriaActualizacionDatosPedido
                .Where(o => o.FechaRegistro > DateTime.Now.AddDays(-180)).ToListAsync(cancellationToken);
            _contextProg.AuditoriaActualizacionDatosPedido.RemoveRange(borrar);
            await _contextProg.SaveChangesAsync(cancellationToken);

            result.Data = stringBuilder.ToString();
            return result;
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: GetAuditoriaActualizacionDatosPedidoQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            throw new Exception(errorText, e);
        }
    }
}