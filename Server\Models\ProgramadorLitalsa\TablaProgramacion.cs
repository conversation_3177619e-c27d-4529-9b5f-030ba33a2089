﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa
{
    public partial class TablaProgramacion
    {
        public int Idprogramacion { get; set; }
        public int? Idpedido { get; set; }
        public int? Idaplicacion { get; set; }
        public int? Idlinea { get; set; }
        public int? Posicion { get; set; }
        public int? HojasAprocesar { get; set; }
        public string Producto { get; set; }
        public bool Flejar { get; set; }
        public DateTime? HoraComienzoEstimada { get; set; }
        public DateTime? HoraFinEstimada { get; set; }
        public int? DuracionEstimada { get; set; }
        public TimeSpan? HoraReal { get; set; }
        public DateTime? DiaReal { get; set; }
        public int? VarCambios { get; set; }
        public string TiposCambio { get; set; }
        public int? Orden { get; set; }
        public bool Revisar { get; set; }
        public int? Idproducto { get; set; }
        public float? Peso { get; set; }
        public float? PesoMin { get; set; }
        public float? BarnizNecesario { get; set; }
        public bool Archivado { get; set; }
        public string TipoLavada { get; set; }
        public string CaraAaplicar { get; set; }
        public int? Idaplicacionposterior { get; set; }
        public string Posicionaplicacionposterior { get; set; }
        public int? Idaplicacionanterior { get; set; }
        public string Posicionaplicacionanterior { get; set; }
        public bool Volteado { get; set; }
        public int? AplicacionSimultanea { get; set; }
        public string Observaciones { get; set; }
        public string Obspaseposterior { get; set; }
        public string ObsCalidad { get; set; }
        public string ObsAlmacen { get; set; }
        public int? TemperaturaSecado { get; set; }
        public int? VelocidadMaxima { get; set; }
        public int? OrdenProcesoAplicacion { get; set; }
        public int? PasadasAdicionales { get; set; }
        public string Programador { get; set; }
        public int? TiempoEstimadoCambio { get; set; }
        public int? TiempoEstimadoTirada { get; set; }
        public string DatosPedido { get; set; }
        public bool? ImpresoraComoBarnizadora { get; set; }

        public virtual Codapli IdaplicacionNavigation { get; set; }
        public virtual Maquinas IdlineaNavigation { get; set; }
        public virtual TablaProductos IdproductoNavigation { get; set; }
        public virtual ProgramacionesPantalla ProgramacionesPantalla { get; set; }
        public virtual ICollection<ProgramacionesPantallaLog> ProgramacionesPantallaLog { get; set; } = new List<ProgramacionesPantallaLog>();
    }
}