﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace ProgramadorGeneralBLZ.Server.Models.InterfaseBD;

public partial class Planos
{
    public string IdImposicion { get; set; }

    public string CodigoPlanoAsociado { get; set; }

    public string IdTroquel { get; set; }

    public decimal ArranquePinza { get; set; }

    public decimal ArranqueEscuadra { get; set; }

    public bool HojasScroll { get; set; }

    public decimal NumeroDesarrollos { get; set; }

    public decimal NumeroAlturas { get; set; }

    public decimal NumeroCuerpos { get; set; }

    public decimal AnchoHoja { get; set; }

    public decimal LargoHoja { get; set; }

    public decimal LargoHojaExteriorScroll { get; set; }

    public string SentidoLectura { get; set; }

    public string PosicionesEscuadraExt { get; set; }

    public bool PosicionesEscuadraInv { get; set; }

    public string Nota { get; set; }

    public string SoloParaCodigos { get; set; }

    public string PlanoCliente { get; set; }

    public string Version { get; set; }

    public string TipoElemento { get; set; }

    public bool Activo { get; set; }

    public DateTime FechaCreacion { get; set; }

    public TimeSpan HoraCreacion { get; set; }

    public string UsuarioCreacion { get; set; }

    public DateTime? FechaMod { get; set; }

    public TimeSpan? HoraMod { get; set; }

    public string UsuarioMod { get; set; }

    public DateTime? FechaObsoleto { get; set; }

    public string UsuarioObsoleto { get; set; }

    public virtual Troqueles TroquelNavigation { get; set; }

    public virtual PosicionEscuadra PosicionesEscuadraExtNavigation { get; set; }

    public virtual SentidoLectura SentidoLecturaNavigation { get; set; }

    public virtual TipoElemento TipoElementoNavigation { get; set; }
}