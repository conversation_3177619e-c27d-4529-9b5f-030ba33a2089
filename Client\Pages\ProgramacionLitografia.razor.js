﻿var gridTbodySelector = ".progGrid.dxbl-grid .dxbl-grid-table > tbody";


window.openPdfInNewTab = (pdfUrl) => {
    window.open(pdfUrl, '_blank');
};
window.getIndexYCodigo = () => {
    var dict = {};
    $(gridTbodySelector).find("tr:not(.dxbl-grid-empty-row,.dxbl-grid-header-row)").each(function () {
        var index = $(this).closest("tr").attr("data-visible-index");
        //Atención, hay que indicar la columna del campo "CODIGO"/"POSICION" por lo que si se cambia el grid hay que estar muy atento a que coincida
        //o será necesario indicar la nueva posición
        if (index !== undefined) {
            //var codigo = $(this).closest("tr").find("td:nth-child(3)").text();
            //var posicion = $(this).closest("tr").find("td:nth-child(4)").text();
            //dict[index] = codigo;

            var idprog = $(this).closest("tr").find("td:nth-child(4)").text().trim();
            var codigo = $(this).closest("tr").find("td:nth-child(5)").text().trim();
            dict[index] = codigo + "|" + idprog;
        }
    });
    console.log(dict);
    return dict;
};
