﻿using System.Data;
using System.Data.SqlClient;
using System.Text;
using MediatR;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLital**;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.DatosGenerales.Query;

public class GetControlActualizacionPedProcesoQuery : IRequest<SingleResult<string>>  
{
}

internal class GetControlActualizacionPedProcesoQueryHandler : IRequestHandler<GetControlActualizacionPedProcesoQuery, SingleResult<string>>
{
    
    private readonly ProgramadorLital**Context _contextProg;
    public GetControlActualizacionPedProcesoQueryHandler(ProgramadorLital**Context contextProg)
    {
        _contextProg = contextProg;
    }

    public async Task<SingleResult<string>> Handle(GetControlActualizacionPedProcesoQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var result = new SingleResult<string>
            {
                Data = "",
                Errors = new List<string>()
            };
            this._contextProg.Database.SetCommandTimeout(900);
            var data = await _contextProg.Database.ExecuteSqlAsync($"EXECUTE dbo.Control_ActualizarTablaCodigosPedidos_Desde_PedProceso", cancellationToken: cancellationToken);
            var affectedIds = new List<int>();
            await using (var connection = new SqlConnection("Server=QPLANT1;Database=ProgramadorLital**;  User ID=**;Password=**;MultipleActiveResultSets=True; Encrypt=False; TrustServerCertificate=True; Timeout=0"))
            {
                await connection.OpenAsync(cancellationToken);

                await using (SqlCommand cmd = new SqlCommand("[dbo].[Control_ActualizarTablaCodigosPedidos_Desde_PedProceso]", connection))
                {
                    cmd.CommandType = CommandType.StoredProcedure;

                    cmd.Parameters.Add(new SqlParameter("@idPedido", DBNull.Value));

                    await using (SqlDataReader reader = await cmd.ExecuteReaderAsync(cancellationToken))
                    {
                        while (await reader.ReadAsync(cancellationToken))
                        {
                            affectedIds.Add(reader.GetInt32(0));
                        }
                    }
                }
            }

            if (!affectedIds.Any())
            {
                return result;
            }

            var stringBuilder = new StringBuilder();
            stringBuilder.AppendLine("<style>");
            stringBuilder.AppendLine("table { border-collapse: collapse; }");
            stringBuilder.AppendLine("td, th { border: 1px solid white; padding-left: 8px; padding-right: 8px; }");
            stringBuilder.AppendLine("th { font-weight: bold; }");
            stringBuilder.AppendLine("</style>");
            stringBuilder.AppendLine("</br></br></br><h1>PEDPROCESO PROGRAMADOS - NO ACTUALIZADOS</h1></br>");
            stringBuilder.AppendLine("<table border=\"1\">");
            stringBuilder.AppendLine("<thead>");
            stringBuilder.AppendLine("<tr>");
            for (int i = 0; i < Math.Ceiling(affectedIds.Count / 12.0); i++)
            {
                stringBuilder.AppendLine("<th>Pedido</th>");
            }
            stringBuilder.AppendLine("</tr>");
            stringBuilder.AppendLine("</thead>");
            stringBuilder.AppendLine("<tbody>");

            int counter = 0;
            while (counter < affectedIds.Count)
            {
                stringBuilder.AppendLine("<tr>");
                for (int col = 0; col < Math.Ceiling(affectedIds.Count / 12.0) && counter < affectedIds.Count; col++)
                {
                    stringBuilder.AppendLine($"<td>{affectedIds[counter]}</td>");
                    counter++;
                }
                stringBuilder.AppendLine("</tr>");
            }

            stringBuilder.AppendLine("</tbody>");
            stringBuilder.AppendLine("</table>");

            result.Data = stringBuilder.ToString();
            return result;
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: GetControlActualizacionPedProcesoQuery - {(!string.IsNullOrEmpty(e.InnerException?.Mes**ge) ? e.InnerException?.Mes**ge : e.Mes**ge)}";
            throw new Exception(errorText, e);
        }
    }
}