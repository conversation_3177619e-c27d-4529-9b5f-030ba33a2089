﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Nelibur.ObjectMapper;
using ProgramadorGeneralBLZ.Server.Data.DatoLita01;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Services.DatabaseManipulation;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;
using static DevExpress.Xpo.Helpers.AssociatedCollectionCriteriaHelper;

namespace ProgramadorGeneralBLZ.Server.MediaTR.DatosGenerales.Query;

public class GetDatosConsultaFechasQuery : IRequest<ListResult<PedidoProcesadoDTO>>
{
    public GetDatosConsultaFechasQuery(int idCliente)
    {
        IdCliente = idCliente;
    }

    public int IdCliente { get; set; }
}

internal class GetDatosConsultaFechasQueryHandler : IRequestHandler<GetDatosConsultaFechasQuery, ListResult<PedidoProcesadoDTO>>
{
    private readonly ProgramadorLitalsaContext _programadorLitalsaContext;
    private readonly IDataManipulationService _dataManipulationService;
    private readonly DatoLita01Context _datoLita01Context;
    public GetDatosConsultaFechasQueryHandler(ProgramadorLitalsaContext programadorLitalsaContext, IDataManipulationService dataManipulationService, DatoLita01Context datoLita01Context)
    {
        _programadorLitalsaContext = programadorLitalsaContext;
        _dataManipulationService = dataManipulationService;
        _datoLita01Context = datoLita01Context;
    }

    public async Task<ListResult<PedidoProcesadoDTO>> Handle(GetDatosConsultaFechasQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<PedidoProcesadoDTO>()
        {
            Data = new List<PedidoProcesadoDTO>(),
            Errors = new List<string>()
        };
        try
        {
            var fechaDefaultIN2 = new DateTime(1899, 12, 30);
            var datos = await _programadorLitalsaContext.PedidoProcesado.Where(o =>
                ((o.IdCliente == request.IdCliente &&
                  (o.FechaFin == null || o.FechaFin == fechaDefaultIN2) &&
                  !o.Anulado &&
                  o.HojasTerminadas < (o.HojasPedido * 0.25)) ||
                 (o.IdCliente == request.IdCliente &&
                  o.MantenerAbierto))).ToListAsync(cancellationToken);


            var listaDto = TinyMapper.Map<List<PedidoProcesadoDTO>>(datos);

            foreach (var dto in listaDto)
            {

                dto.TxtEstado = _dataManipulationService.GetTextoEstadoCodigosAplicacion(dto.IdPedido.Value);

                var datosFechas = _datoLita01Context.Fechas.FirstOrDefault(o => o.Codigo == dto.IdPedido.Value);
                if (datosFechas != null)
                {
                    dto.FechaFtp = string.IsNullOrWhiteSpace(datosFechas.Fechaftp) ? null : Convert.ToDateTime(datosFechas.Fechaftp);
                    dto.FechaHojalata = string.IsNullOrWhiteSpace(datosFechas.Fechahojalata) ? null : Convert.ToDateTime(datosFechas.Fechahojalata);
                    dto.FechaLanzamiento = string.IsNullOrWhiteSpace(datosFechas.Fechamaxima) ? null : Convert.ToDateTime(datosFechas.Fechamaxima);
                    dto.FechaContrato = string.IsNullOrWhiteSpace(datosFechas.Fcontrato) ? null : Convert.ToDateTime(datosFechas.Fcontrato);
                    dto.FechaAcordada = string.IsNullOrWhiteSpace(datosFechas.Facordada) ? null : Convert.ToDateTime(datosFechas.Facordada);
                }
            }

            if (listaDto.Any())
            {
                result.Data.AddRange(listaDto);
            }
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: GetDatosProductoQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            throw new Exception(errorText, e);
        }
        return result;
    }
}