﻿using ProgramadorGeneralBLZ.Server.Data.DatoLita01;
using ProgramadorGeneralBLZ.Server.Models.InterfaseBD;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.Services.SAP
{
    public interface ISapService
    {
        Task<SingleResult<bool>> ActualizarDatosDesdeSAP(bool registrosNuevos, CancellationToken ct);
        //Task<SingleResult<bool>> ComprobarRegistrosNuevos(CancellationToken ct);
        //Task<SingleResult<bool>> ComprobarRegistrosModificados(CancellationToken ct);
        Task<SingleResult<bool>> ComprobarRegistrosBorrados(CancellationToken ct);

        Task<List<Matmas>> GetMatmasAll(CancellationToken ct);
        Task<Matmas> GetMatmasById(int id, CancellationToken ct);
        Task<Matmas> GetMatmasByKey(string material,CancellationToken ct);

        Task<List<Batmas>> GetBatmasAll(CancellationToken ct);
        Task<Batmas> GetBatmasById(int id, CancellationToken ct);
        Task<Batmas> GetBatmasByKey(string material, string lote, string centro, CancellationToken ct);

        Task<List<Debmas>> GetDebmasAll(CancellationToken ct);
        Task<Debmas> GetDebmasById(int id, CancellationToken ct);
        Task<Debmas> GetDebmasByKey(string cliente,CancellationToken ct);

        Task<List<CodigosAplicacion>> GetCodigosAplicacionAll(CancellationToken ct);
        Task<CodigosAplicacion> GetCodigoAplicacionByKey(string codApli, CancellationToken ct);

        Task<List<Colores>> GetColoresAll(CancellationToken ct);
        Task<Colores> GetColorByKey(string idColor, CancellationToken ct);

        Task<List<Motivos>> GetMotivosAll(CancellationToken ct);
        Task<Motivos> GetMotivoByKey(string idMotivo, CancellationToken ct);

        Task<List<Planos>> GetPlanosAll(CancellationToken ct);
        Task<Planos> GetPlanoByKey(string idPlano, CancellationToken ct);

        Task<List<TratamientosCabecera>> GetTratamientosCabeceraAll(CancellationToken ct);
        Task<TratamientosCabecera> GetTratamientoCabeceraByKey(string idTratamiento, CancellationToken ct);

        Task<List<TratamientosDetalle>> GetTratamientosDetalleAll(CancellationToken ct);
        Task<TratamientosDetalle> GetTratamientoDetalleByKey(string idTratamiento, string numOperacion, CancellationToken ct);

        Task<List<MotivosColores>> GetMotivosColoresAll(CancellationToken ct);
        Task<MotivosColores> GetMotivoColorByKey(string idMotivo, decimal contador, CancellationToken ct);
    }
}
