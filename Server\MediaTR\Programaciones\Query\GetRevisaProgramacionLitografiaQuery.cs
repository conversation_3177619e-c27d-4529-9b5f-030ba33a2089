﻿using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using MediatR;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Services.DatabaseManipulation;
using ProgramadorGeneralBLZ.Shared;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Programaciones.Query;

public class GetRevisaProgramacionLitografiaQuery : IRequest<SingleResult<int>>
{
    /// <summary>
    /// Adaptación de Public Sub actualiza_tiempos() para Barnizado, combinando en una unica funcion:
    /// Call recorre_programacion_barnizado(Me.txt_linea, Me.txt_posicion_desde, 9999999)
    /// Call revisa_programacion_barnizado(Me.txt_linea, Me.txt_posicion_desde)
    /// </summary>
    /// <param name="tipo"></param>
    /// <param name="maquina"></param>
    public GetRevisaProgramacionLitografiaQuery(Enums.TipoPedido tipo, int maquina)
    {
        Tipo = tipo;
        Maquina = maquina;
    }
    public Enums.TipoPedido Tipo;
    public int Maquina;
}

internal class GetRevisaProgramacionLitografiaQueryHandler : IRequestHandler<GetRevisaProgramacionLitografiaQuery, SingleResult<int>>
{
    private readonly IDbContextFactory<ProgramadorLitalsaContext> _programadorLitalsaContextFactory;
    private readonly IDataManipulationService _dataManipulationService;
    public GetRevisaProgramacionLitografiaQueryHandler(IDataManipulationService dataManipulationService, IDbContextFactory<ProgramadorLitalsaContext> contextProg)
    {
        _dataManipulationService = dataManipulationService;
        _programadorLitalsaContextFactory = contextProg;
    }

    public async Task<SingleResult<int>> Handle(GetRevisaProgramacionLitografiaQuery request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<int>
        {
            Data = 0,
            Errors = new List<string>()
        };

        try
        {
            DateTime hora2 = default;
            int taux;
            string descCambio = "",
                tipoElemento0 = "",
                escuadra0 = "";
            int hojasYaTiradas = 0,
                tamaño0 = 0,
                idproducto0 = 0,
                numTintas0 = 0;

            int[] tintas0 = new int[12];
            Single formato0 = 0;
            //string result;

            var _contextProg = await _programadorLitalsaContextFactory.CreateDbContextAsync(cancellationToken);
            
            //var tiemposMaquina = DevuelveTiemposMaquina(idmaquina, ttamaño, velocidadst, trodillo, tguias);
            var datosMaquina = await _contextProg.Maquinas.FirstOrDefaultAsync(o => o.Idmaquina == request.Maquina,
                    cancellationToken);

            //DevuelveTiemposMaquina(idmaquina, ttamaño, velocidadst, trodillo, tguias);
            var velocidadAlu = datosMaquina.VelocidadAlu.Value;
            var velocidadscroll = datosMaquina.VelocidadScroll.Value;

            var idmaquina = request.Maquina;
            var posicionHasta = 9999999; // Puedes cambiar este valor si necesitas 

            var pedidoMinimo = int.Parse($"{DateTime.Now.AddYears(-1):yy}00000");

            var desdePosicionTiempo = _contextProg.TablaProgramacion
                .Where(x => x.Posicion <= posicionHasta &&
                            x.Idpedido >= pedidoMinimo && //Intenta hacer la consulta más rápida al sólo considerar los pedidos desde el año anterior
                            x.Idlinea == idmaquina &&
                            x.DiaReal != null &&
                            x.HoraReal != null)
                .Max(x => (int?)x.Posicion) ?? 0;


            var tablaProg = await _contextProg.TablaProgramacion
                .Where(x => x.Idlinea == idmaquina && x.Posicion >= desdePosicionTiempo)
                .OrderBy(x => x.Posicion)
                .ToListAsync(cancellationToken);

            if (tablaProg.Any()) // Comprobamos si el conjunto de registros tiene algún registro
            {
                var firstRecord = tablaProg.First(); // Tomamos el primer registro

                // Verificamos si los campos HoraReal y DiaReal no son nulos
                if (firstRecord.HoraReal.HasValue && firstRecord.DiaReal.HasValue)
                {
                    hora2 = firstRecord.DiaReal.Value.Add(firstRecord.HoraReal.Value);
                }
                else if (firstRecord.HoraComienzoEstimada.HasValue) // Verificamos si el campo HoraComienzoEstimada no es nulo
                {
                    hora2 = firstRecord.HoraComienzoEstimada.Value;
                }
                else // En caso de que todas las comprobaciones sean falsas
                {
                    result.Errors.Add("NO Hay referencia de comienzo");
                    return result; // Salimos de la función/subrutina
                }
            }


            foreach (var (itemProg, index) in tablaProg.Select((value, i) => (value, i)))
            {
                //28-08-2023: Venimos de ProgramarPedidosBarnizadoCommandHandler con el campo ImpresoraComoBarnizadora
                //A veces se usan las impresoras como barnizadoras (donde la máquina lo permite) y hay que diferenciar estos pedidos
                //ya que si a continuación, en la misma máquina pero usándola como impresora, se programa un pedido nuevo de sólo LITO
                //hay que vigilar que al dar a calcular tiempos, no se cargue los tiempos que se han calculado desde la vista de barnizado
                //Por tanto, nos vamos quedando con el tiempo final de cada pedido, pero los saltamos sin hacer nada con ellos.
                if (datosMaquina.TipoMaquina == Enums.TipoMaquina.Impresora.ToString() && (itemProg.ImpresoraComoBarnizadora.HasValue && itemProg.ImpresoraComoBarnizadora.Value))
                {
                    hora2 = itemProg.HoraFinEstimada.Value;
                    continue;
                }
                //añadimos el tiempo de ejecución del pedido al tiempo de comienzo del pedido i-1
                //y obtenemos el tiempo de comienzo del pedido i
                //la hora de fin del pedido anterior es el comienzo del siguiente
                var hora1 = hora2;

                //calculamos el tiempo de ejecución del pedido i, incluyendo el cambio del pedido i-1 al i
                var aux = 0;
                int numTintas = 0;
                int numTintaMate = 0;
                int tamaño = 0;
                string tipoElemento = string.Empty;
                string escuadra = string.Empty;
                float formato = 0;

                int[] tintas = new int[12];
                //calculo del tiempo de cambio de formato
                var datosPedido =
                    await _contextProg.PedidoProcesado.FirstOrDefaultAsync(o => o.IdPedido == itemProg.Idpedido,
                        cancellationToken);
                numTintas = itemProg.Idaplicacion switch
                {
                    // solo tinta mate
                    997 => _dataManipulationService.GetNumeroTintas(datosPedido.IdPedido.Value, numTintaMate, true),
                    // solo tinta en la pinza
                    996 => 1,
                    //Restamos en la función directamente el numero de tintas mates, para no hacer las mierdas de pasar por referencia ni nada.
                    _ => _dataManipulationService.GetNumeroTintas(datosPedido.IdPedido.Value, numTintaMate, false, true)
                };
                tamaño = datosPedido.AnchoHjlta.Value + datosPedido.LargoHjlta.Value;
                CheckAndAddToAux(ref aux, ref descCambio, tamaño != tamaño0, datosMaquina.CambioFormato.Value, "Tamaño");

                escuadra = _contextProg.Plano
                    .FirstOrDefault(o => o.Idcliente == datosPedido.IdCliente && o.CodigoPlano == datosPedido.Plano)?.Escuadra ?? string.Empty;
                CheckAndAddToAux(ref aux, ref descCambio, escuadra != escuadra0, datosMaquina.CambioEscuadra.Value, "Cambio Escuadra");
                tipoElemento = datosPedido.TipoElemento;
                formato = datosPedido.Formato.Value;
                if (tipoElemento0 != tipoElemento) // si los elementos son distintos
                {
                    // hay cambio de rodillo excepto si son fondos o tapas que mantendríamos el rodillo
                    CheckAndAddToAux(ref aux, ref descCambio, !(tipoElemento0 is "F" or "T" && datosPedido.TipoElemento is "F" or "T"), datosMaquina.CambioRodillo.Value, "Rodillo");
                }
                else // si los elementos son iguales
                {
                    // si elementos iguales pero formatos distintos

                    CheckAndAddToAux(ref aux, ref descCambio, formato != formato0, datosMaquina.CambioRodillo.Value, "Rodillo");
                }


                tintas = _dataManipulationService.VolcaTintas2Vector(datosPedido.IdPedido.Value);
                var minCambiosPorTintas = 0;
                var maxColoresMaquina = (int)datosMaquina.NumeroCuerpos;//Cuerpos = Cantidad de tintas por pase.
                if (numTintas0 >= 1)
                {
                    var numTintasComunes = 0;
                    bool[] comparado = new bool[12]; // Array para marcar los elementos ya comparados

                    for (int i2 = 0; i2 < 12; i2++)
                    {
                        for (int i3 = 0; i3 < 12; i3++)
                        {
                            if (tintas[i2] == tintas0[i3] && tintas[i2] != 0 && !comparado[i3])
                            {
                                numTintasComunes++;
                                comparado[i3] = true; // Marca el elemento en tintas0 como ya comparado
                                break; // Rompe el bucle interior ya que encontró una coincidencia
                            }
                        }
                    }

                    // Calcula la base de las limpiezas necesarias
                    var limpiezasBase = numTintas0 - Math.Min(numTintasComunes, numTintas);
                    // Calcula la penalización por exceder el número máximo de colores de la máquina
                    var penalizacionExceso = Math.Max(numTintas - maxColoresMaquina, 0);
                    // Suma la base y la penalización para obtener el número total de limpiezas
                    var numLimpiezas = limpiezasBase + penalizacionExceso; 

                    taux = numLimpiezas * 20;//Es un valor por defecto. TODO Moverlo a la tabla Maquinas o algun sitio de configuración
                                             //aux += taux;
                                             //descCambio += $" Tintas: {taux}";

                    CheckAndAddToAux(ref aux, ref descCambio, true, numLimpiezas * 20, "Tintas");
                }
                if (index == 0)
                {
                    aux = 0;
                    descCambio = string.Empty;
                }

                //Tiempo cambio planchas
                CheckAndAddToAux(ref aux, ref descCambio, true, numTintas * datosMaquina.CambioPlanchas.Value, "Planchas");


                CheckAndAddToAux(ref aux, ref descCambio,
                    datosPedido.TipoPedido == "L" && datosPedido.TipoEnvase.ToUpperInvariant().Contains("ALIME"),
                    20 * (int)(numTintas / (datosMaquina.NumeroCuerpos + 0.1) + 1),
                    "PREP.LECHES");

                //si hay variaciones incluidas manualmente las añadimos y las metemos en descripción de cambios
                CheckAndAddToAux(ref aux, ref descCambio, (itemProg.VarCambios ?? 0) != 0, itemProg.VarCambios ?? 0, "Desvios");


                var datosProducto =
                    await _contextProg.TablaProductos.FirstOrDefaultAsync(o => o.Idproducto == itemProg.Idproducto, cancellationToken);

                var idproducto = datosProducto?.Idproducto ?? 0;

                //si el producto del que venimos es distinto al que vamos entonces habría que entrar
                //además tiene que ser distinto de 0

                CheckAndAddToAux(ref aux, ref descCambio, idproducto != idproducto0 && idproducto0 != 0 && idproducto != 0, 60, "Lavada");
                //if (idproducto != idproducto0 && idproducto0 != 0 && idproducto != 0)
                //{
                //}
                //else
                //{
                //    itemProg.TipoLavada = "";
                //}
                idproducto0 = idproducto;
                //ALMACENAMOS EL TIEMPO DE LOS CAMBIOS REQUERIDOS
                var tcambios = aux;

                //calculamos el número de pasadas por máquina en función del número de cuerpos
                var numPasadas = (int)((numTintas / (datosMaquina.NumeroCuerpos + 0.1)) + 1);

                //***********AJUSTAMOS EL NÚMERO REAL DE HOJAS***************************
                //si hay hojas ya tiradas tenemos que analizar el campo observaciones donde aparezca
                //el texto YA TIRADAS ..... HOJAS
                //ese tiempo habrá que restarlo
                if (itemProg.Observaciones.ToUpperInvariant().Contains("YA TIRADAS"))
                {
                    //debemos localizar el texto donde aparecen el número de hojas tiradas...
                    hojasYaTiradas = Convert.ToInt32(_dataManipulationService.DevuelveString(itemProg.Observaciones, "#", "#").Replace(".", ""));
                }
                if (Regex.IsMatch(itemProg.Observaciones.ToUpperInvariant(), @"SON SOLO.*HOJAS.*"))
                {
                    hojasYaTiradas = datosPedido.HojasPedido.Value -
                                     Convert.ToInt32(_dataManipulationService.DevuelveString(itemProg.Observaciones, "#", "#").Replace(".", ""));
                }
                // SE ESTABLECE LA VELOCIDAD POR DEFECTO = A LA STANDARD
                var velocidad = datosMaquina.Velocidadmd.Value;
                if (numPasadas > 1)
                {
                    velocidad = Math.Min(datosMaquina.Velocidad2pasadas.Value, velocidad);
                }
                // definimos VELOCIDAD si es scroll
                if (_dataManipulationService.GetDatosPedido(itemProg.Idpedido.Value, "DevuelveCaracteristicasHojalata").Text.ToUpperInvariant().Contains("SCROLL"))
                {
                    velocidad = Math.Min(velocidadscroll, velocidad);
                }

                // definimos VELOCIDAD si es aluminio
                if (_dataManipulationService.GetDatosPedido(itemProg.Idpedido.Value, "DevuelveCaracteristicasHojalata").Text.ToUpperInvariant().Contains("ALU"))
                {
                    velocidad = Math.Min(velocidadAlu, velocidad);
                }

                //taux = (int)Math.Ceiling((double)((numPasadas * 60 * (datosPedido.HojasPedido.Value - hojasYaTiradas)) / velocidad));
                taux = (int)Math.Ceiling((numPasadas * 60 * (datosPedido.HojasPedido.Value - hojasYaTiradas) / (float)velocidad));

                // ALMACENAMOS EL TIEMPO DE TIRADA EFECTIVA
                var ttirada = taux;

                aux += taux;

                descCambio = $"{descCambio} Tirada: {(numPasadas > 1 ? $"({numPasadas})" : "")}{taux:#}(V:{velocidad} h/h)";
                descCambio = $"{descCambio} // Ttotal= {aux:#}";

                // calculamos la hora de final del pedido (la columna cambios se tiene en cuenta más arriba)
                hora2 = hora1.AddMinutes(aux);

                // ***********CALCULO DE LOS BARNICES NECESARIOS ***************************
                float kgNecesarios = 0;
                if (itemProg.AplicacionSimultanea != null && itemProg.AplicacionSimultanea.Value != 0)
                {
                    // calculamos el consumo de barniz para ese producto mediante la función devuelve_barniz_necesario
                    //La función devuelve_barniz_necesario se combina con otra llamada interna que tiene para la superficie, ya que solo se usa aqui y para litgrafía
                    kgNecesarios = _dataManipulationService.DevuelveBarnizNecesarioConCalculoDePeso(
                        itemProg.Idpedido.Value, itemProg.Idproducto.Value, itemProg.Peso.Value, hojasYaTiradas);

                    if (kgNecesarios > (datosProducto.Existencias ?? 0 - datosProducto.CantidadRechazada ?? 0))
                    {
                        result.Info.Add(
                            $"Línea: {itemProg.Posicion}{Environment.NewLine}" +
                            $"No hay suficiente {datosProducto.Denominacion}{Environment.NewLine}" +
                            $"Existencias: {datosProducto.Existencias ?? 0 - datosProducto.CantidadRechazada ?? 0:0} kg{Environment.NewLine}" +
                            $"Necesarios: {kgNecesarios:0} kg");
                    }
                }
                // ***********FIN DE LOS CALCULO DE LOS BARNICES NECESARIOS ***************************

                //actualizamos el tiempo de comienzo estimado del pedido i
                itemProg.HoraComienzoEstimada = hora1;
                //actualizamos el tiempo de fin estimado del pedido i
                itemProg.HoraFinEstimada = hora2;
                itemProg.TiposCambio = descCambio;
                itemProg.BarnizNecesario = kgNecesarios;
                itemProg.TiempoEstimadoCambio = tcambios;
                itemProg.TiempoEstimadoTirada = ttirada;
                //12/10/2020: CAMBIOS PROTEO
                itemProg.PasadasAdicionales = numPasadas - 1;

                //*****ACTUALIZACIÓN DE LAS OBSERVACIONES DE CALIDAD********
                //actualización 28/03/2023 se elimina el primero y se deja el 2º
                //cadena_calidad = devuelve_obs_calidad(!IdPedido, !Idaplicacion, idmaquina, !Idprogramacion)
                var cadenaCalidad = await _dataManipulationService.GetObservacionesCalidadLite(itemProg.Idpedido.Value, itemProg.Idaplicacion);

                if (!string.IsNullOrEmpty(cadenaCalidad))
                {
                    itemProg.ObsCalidad = cadenaCalidad.Length > 255 ? cadenaCalidad.Substring(0, 255) : cadenaCalidad;
                }
                //*****FIN DE LA ACTUALIZACIÓN DE LAS OBSERVACIONES DE CALIDAD********

                numTintas0 = numTintas;
                for (int i2 = 0; i2 < 11; i2++)
                {
                    tintas0[i2] = tintas[i2];
                }
                tamaño0 = tamaño;
                formato0 = formato;
                tipoElemento0 = tipoElemento;
                escuadra0 = escuadra;
                idproducto0 = idproducto;
                descCambio = "";
                hojasYaTiradas = 0;

                //Actualizamos los datos
                _contextProg.Update(itemProg);
            }
            //await CompruebaExistenciasBarnices(datosMaquina, result, cancellationToken);
            await _contextProg.SaveChangesAsync(cancellationToken);
            await _contextProg.DisposeAsync();
        }
        catch (Exception e)
        {
            var error = $"ERROR: GetCalcularHorariosQuery - {e.Message}--{(!string.IsNullOrWhiteSpace(e.InnerException?.Message) ? e.InnerException : string.Empty)}";
            result.Errors.Add(error);
        }
        return result;
    }

    private void CheckAndAddToAux(ref int aux, ref string descCambio, bool condition, int changeValue, string description)
    {
        if (condition)
        {
            aux += changeValue;
            descCambio += $" {description}: {changeValue}";
        }
    }



    /// <summary>
    /// Adaptación de Public Sub comprueba_existencias_barnices(idmaquina As Integer, Desde As Long, hasta As Long)
    /// este procedimiento determina para una máquina (idmaquina)a partir de una posición de la programación (desde)
    /// los consumos estimados de barnices y avisa si no hay existencias suficientes, dando aviso a través
    /// del procedimiento de avisos.
    /// </summary>
    /// <param name="maquina"></param>
    /// <param name="singleResult"></param>
    /// <param name="ct"></param>
    /// <returns></returns>
    public async Task CompruebaExistenciasBarnices(Models.ProgramadorLitalsa.Maquinas maquina, SingleResult<int> result, CancellationToken ct)
    {

        var parameters = new[]
        {
            new SqlParameter("@IdMaquina", maquina.Idmaquina),
            new SqlParameter("@Desde", maquina.PosicionDesde.Value)
        };
        var programador = await _programadorLitalsaContextFactory.CreateDbContextAsync(ct);
        var datosBarnices = await programador.Set<CompruebaExistenciasBarnices>().FromSqlRaw("[dbo].[GetDatosBarnices] @IdMaquina, @Desde", parameters)
            .ToListAsync(ct);


        foreach (var barniz in datosBarnices)
        {
            //si las existencias-cantidadrechazada es menor que la cantidad necesaria
            //entramos en el if de aviso de barniz insuficiente.
            if ((barniz.Existencias - barniz.CantidadRechazada) < barniz.TotBarn)
            {
                var datosProducto =
                    await programador.TablaProductos.FirstOrDefaultAsync(o => o.Idproducto == barniz.Idproducto, ct);


                //en el caso que se haya avisado en días anteriores o que nunca se haya dado aviso,
                //se avisa y se actualiza el campo UltAviso
                if (datosProducto.UltAvisoInsuficiente < DateTime.Today ||
                    datosProducto.UltAvisoInsuficiente == DateTime.MinValue ||
                    datosProducto.UltAvisoInsuficiente == null)
                {
                    result.Info.Add($"********** BARNIZ INSUFICIENTE ************");
                    result.Info.Add($"Idproducto {barniz.Idproducto} : {datosProducto.Denominacion}");
                    result.Info.Add($"Necesarios: {barniz.TotBarn:0} kg");
                    result.Info.Add($"Existencias: {barniz.Existencias:0} kg");
                    result.Info.Add($"Rechazados: {barniz.CantidadRechazada:0} kg");
                    result.Info.Add($"Fecha Entrega: {barniz.FechaEntrega:dd/MM}");
                    result.Info.Add($"Días hasta entrega: {(barniz.FechaEntrega != null
                                                                    ? (barniz.FechaEntrega - DateTime.Today).Value.TotalDays
                                                                    : "N/A")}");
                    //actualizamos el momento en el que se ha dado el aviso
                    datosProducto.UltAvisoInsuficiente = DateTime.Now;

                }
            }
        }

        await programador.DisposeAsync();
    }
}