﻿using System.Data;
using System.Text.RegularExpressions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.Identity.Web.Resource;
using ProgramadorGeneralBLZ.Server.CustomFilterAttributes;
using ProgramadorGeneralBLZ.Shared;

namespace ProgramadorGeneralBLZ.Server.Controllers
{
    [Authorize]
    [ApiController]
    [Route("[controller]")]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAdB2C:Scopes")]
    [ClaimRequirementContainRole("extension_Roles", $"{Roles.Programador},{Roles.Admin}")]
    public class DatabaseController : ControllerBase
    {
        private readonly string _connectionString;
        // Rutas base (sin escape extra)
        private const string CarpetaBackup = @"C:\Program Files\Microsoft SQL Server\MSSQL12.MSSQLSERVER\MSSQL\Backup\";
        private const string CarpetaData = @"C:\Program Files\Microsoft SQL Server\MSSQL12.MSSQLSERVER\MSSQL\DATA\";

        // Nombres fijos en este ejemplo (podrías parametrizar también si fuera nece**rio)
        private const string BaseOriginal = "ProgramadorLital**";
        private const string BaseDestino = "ProgramadorLital**_DES2";
        private const string NombreBackUp = "PROGRAMADOR_AUTO_BACKUP.bak";

        public DatabaseController(IConfiguration configuration)
        {
            //_connectionString = configuration.GetConnectionString("DefaultConnectionProgramador");
            _connectionString = "Server=QPLANT1;Database=master;User ID=**;Password=**;MultipleActiveResultSets=True; Encrypt=False; TrustServerCertificate=True;";

        }

        [HttpPost("backup")]
        public async Task<IActionResult> BackupDatabase()
        {
            try
            {
                string rutaBackup = Path.Combine(CarpetaBackup, NombreBackUp);
                await ExecuteBackup(BaseOriginal, rutaBackup);
                return Ok("Backup completado correctamente.");
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Error durante el backup: {ex.Mes**ge}");
            }
        }

        [HttpPost("restore")]
        public async Task<IActionResult> RestoreDatabase()
        {
            try
            {
                string rutaBackup = Path.Combine(CarpetaBackup, NombreBackUp);
                await ExecuteRestore(BaseDestino, rutaBackup);
                return Ok("Restore completado correctamente.");
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Error durante el restore: {ex.Mes**ge}");
            }
        }

        private void ValidarNombreBaseDatos(string nombre)
        {
            // Solo letras, dígitos y guiones bajos
            if (!Regex.IsMatch(nombre, @"^[A-Za-z0-9_]+$"))
                throw new ArgumentException("Nombre de base de datos no válido.");
        }

        private async Task ExecuteBackup(string databaseName, string backupFilePath)
        {
            ValidarNombreBaseDatos(databaseName);
            // El usuario/rol en la cadena de conexión debe tener permiso de BACKUP DATABASE
            using SqlConnection connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            string backupSql = $"BACKUP DATABASE [{databaseName}] TO DISK = @backupPath WITH INIT";
            using SqlCommand cmd = new SqlCommand(backupSql, connection);
            cmd.Parameters.Add(new SqlParameter("@backupPath", SqlDbType.NVarChar) { Value = backupFilePath });
            await cmd.ExecuteNonQueryAsync();
        }

        private async Task ExecuteRestore(string targetDatabase, string backupFilePath)
        {
            ValidarNombreBaseDatos(targetDatabase);
            using SqlConnection connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // 1) SINGLE_USER
            string singleSql = $"ALTER DATABASE [{targetDatabase}] SET SINGLE_USER WITH ROLLBACK IMMEDIATE";
            using (SqlCommand cmd1 = new SqlCommand(singleSql, connection))
            {
                await cmd1.ExecuteNonQueryAsync();
            }

            // 2) Saber nombres lógicos del backup: 
            //    Opcional: podrías ejecutar RESTORE FILELISTONLY aquí y leer resultados,
            //    pero en este ejemplo asumo que fueron "P" y "P_log".
            //    Si vas a restaurar otra base, ajusta estos nombres según tu .bak.

            string logicalDataName = BaseOriginal;      // Nombre lógico en el .bak (ejemplo: "P")
            string logicalLogName = BaseOriginal + "_log"; // Nombre lógico del log

            // Rutas físicas destino
            string rutaMdf = Path.Combine(CarpetaData, $"{targetDatabase}_Data.mdf");
            string rutaLdf = Path.Combine(CarpetaData, $"{targetDatabase}_Log.ldf");

            string restoreSql = $@"
                RESTORE DATABASE [{targetDatabase}]
                  FROM DISK = @backupPath
                  WITH REPLACE,
                       MOVE '{logicalDataName}' TO @rutaMdf,
                       MOVE '{logicalLogName}'  TO @rutaLdf";

            using (SqlCommand cmd2 = new SqlCommand(restoreSql, connection))
            {
                cmd2.Parameters.Add(new SqlParameter("@backupPath", SqlDbType.NVarChar) { Value = backupFilePath });
                cmd2.Parameters.Add(new SqlParameter("@rutaMdf", SqlDbType.NVarChar) { Value = rutaMdf });
                cmd2.Parameters.Add(new SqlParameter("@rutaLdf", SqlDbType.NVarChar) { Value = rutaLdf });
                await cmd2.ExecuteNonQueryAsync();
            }

            // 3) MULTI_USER
            string multiSql = $"ALTER DATABASE [{targetDatabase}] SET MULTI_USER";
            using (SqlCommand cmd3 = new SqlCommand(multiSql, connection))
            {
                await cmd3.ExecuteNonQueryAsync();
            }
        }
    }
}
