﻿using System.Text.Json.Serialization;

namespace ProgramadorGeneralBLZ.Shared.DTO
{
    public class SAP_FasePedidoDTO
    {
        public int Idproceso { get; set; }
        public string Idpedido { get; set; }
        public string Idcodigoaplicacion { get; set; }
        public string? Posicion { get; set; }
        public int? Hojasaplicadas { get; set; }
        public string? Idproductoprioritario { get; set; }
        public double? Gramajeprioritario { get; set; }
        public string? ObservacionesAplicacion { get; set; }
        public int? Idpedproceso { get; set; }
        public bool CreaccionManual { get; set; }
        public int? Fase { get; set; }
        public string Idbarniz { get; set; }
        public bool ImpresionDia { get; set; }
        public string LineaTintas { get; set; }
        public string? EstadoTintas { get; set; }
        public DateTime? FechaAsignacionImpresora { get; set; }
        public bool Sincarpeta { get; set; }
        public string? Idstrpedproceso { get; set; }

        //[JsonIgnore]
        //public string? TextoIdCodigoAplicacion { get; set; }
        //[JsonIgnore]
        //public string? TextoIdbarniz { get; set; }
    }
}