﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Data.ProteoLitalsa;
using ProgramadorGeneralBLZ.Shared;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Programaciones.Command;

public class BorrarProgramacionesCommand : IRequest<SingleResult<int>>
{

    public BorrarProgramacionesCommand(Dictionary<int, int> dict, Enums.TipoPedido tp)
    {
        Tp = tp;
        Dict = dict;
    }

    public Dictionary<int, int> Dict { get; set; }
    public Enums.TipoPedido Tp;
}

public class BorrarProgramacionesCommandHandler : IRequestHandler<BorrarProgramacionesCommand, SingleResult<int>>
{
    private readonly ProgramadorLitalsaContext _context;
    private readonly ProteoLitalsaContext _proteoContext;
    private readonly IWebHostEnvironment _env;
    private readonly IConfiguration _configuration;
    public BorrarProgramacionesCommandHandler(ProgramadorLitalsaContext context, ProteoLitalsaContext proteoContext, IWebHostEnvironment env, IConfiguration configuration)
    {
        _context = context;
        _proteoContext = proteoContext;
        _env = env;
        _configuration = configuration;
    }

    public async Task<SingleResult<int>> Handle(BorrarProgramacionesCommand request, CancellationToken cancellationToken)
    {
        var esDesarrollo = _configuration.GetConnectionString("DefaultConnectionProgramador").Contains("_DES", StringComparison.OrdinalIgnoreCase);

        var result = new SingleResult<int> { Errors = new List<string>(), Data = 0 };

        using var transaction = await _context.Database.BeginTransactionAsync(cancellationToken);
        try
        {
            var progs = request.Dict.Keys.ToArray();
            var pedidos = request.Dict.Values.ToArray();

            // Obtener información de las programaciones que se van a borrar para eliminar del historial
            var programacionesABorrar = await _context.TablaProgramacion
                .Where(o => progs.Contains(o.Idprogramacion))
                .Select(o => new { o.Idprogramacion })
                .ToListAsync(cancellationToken);

            // Eliminar registros de las nuevas tablas para las programaciones que se van a borrar
            foreach (var prog in programacionesABorrar)
            {
                // Eliminar logs relacionados con esta programación
                await _context.ProgramacionesPantallaLog
                    .Where(l => l.IdProgramacion == prog.Idprogramacion)
                    .ExecuteDeleteAsync(cancellationToken);

                // Eliminar registro principal de ProgramacionesPantalla
                await _context.ProgramacionesPantalla
                    .Where(p => p.IdProgramacion == prog.Idprogramacion)
                    .ExecuteDeleteAsync(cancellationToken);
            }

            if (request.Tp == Enums.TipoPedido.Litografia)
            {
                var registroLitoIds = await _context.TablaProgramacion
                    .Where(o => progs.Contains(o.Idprogramacion))
                    .Select(o => new { o.Idpedido, o.Idaplicacion })
                    .ToListAsync(cancellationToken);

                foreach (var litoId in registroLitoIds)
                {
                    var res = await _context.TablaCodigosPedido
                        .Where(o => o.Idpedido == litoId.Idpedido && o.Idcodigoaplicacion == litoId.Idaplicacion)
                        .ToListAsync(cancellationToken);

                    res.ForEach(o => o.EstadoTintas = "Sin Aplicar");
                }
                await _context.SaveChangesAsync(cancellationToken);
            }

            // ExecuteDeleteAsync NO NECESITA SaveChangesAsync
            var progsBorrar = await _context.TablaProgramacion.Where(o => progs.Contains(o.Idprogramacion))
                .ExecuteDeleteAsync(cancellationToken);
            //BORRADO EN PROTEO V2
            //Analizar el dejar marcadas como DESCARTAR en lugar de borrar. Atención: Tiene implicaciones con volver a programar un pedido que 
            //ha sido "borrado"/descartado y como lo reconoce/ignora el programador
            if (_env.IsProduction() && !esDesarrollo)
            {
                foreach (var (prog, pedido) in request.Dict)
                {
                    //0 Si no existe en PROTEO, saltamos.
                    var enProteo = await _proteoContext.AccMesExportPhasesIdprog
                        .AnyAsync(o => prog == o.Idprogramacion && pedido == o.WorkOrder, cancellationToken);
                    if (!enProteo)
                    {
                        continue;
                    }
                    //1 Borrar la posición/phase.
                    var posProteo = await _proteoContext.AccMesExportPhasesIdprog
                        .Where(o => prog == o.Idprogramacion && pedido == o.WorkOrder)
                        .ExecuteDeleteAsync(cancellationToken);
                    //2 Comprobar si hay más posiciones para el pedido.
                    var posicionesRestantes =
                        await _proteoContext.AccMesExportPhasesIdprog
                            .AnyAsync(o => o.WorkOrder == pedido,
                                cancellationToken);
                    //3 Si no hay más posiciones, borrar la cabecera/workorder
                    if (!posicionesRestantes)
                    {
                        var cabProteo = await _proteoContext.AccMesExportWorkOrders
                            .Where(o => pedido == o.WorkOrder)
                            .ExecuteDeleteAsync(cancellationToken);
                    }
                }
            }

            await transaction.CommitAsync(cancellationToken);
            result.Data = 1;
        }
        catch (Exception e)
        {
            await transaction.RollbackAsync(cancellationToken);
            var errorText = $"ERROR: BorrarProgramacionesCommand - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }
        return result;
    }
}