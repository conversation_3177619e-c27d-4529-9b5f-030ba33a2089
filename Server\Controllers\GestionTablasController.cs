﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Identity.Web.Resource;
using Nelibur.ObjectMapper;
using ProgramadorGeneralBLZ.Server.CustomFilterAttributes;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared;
using ProgramadorGeneralBLZ.Shared.DTO;


namespace ProgramadorGeneralBLZ.Server.Controllers
{
    [Authorize]
    [ApiController]
    [Route("[controller]")]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAdB2C:Scopes")]
    //[Authorize(Roles = $"{Roles.Programador},{Roles.Admin}")]
    [ClaimRequirementContainRole("extension_Roles", $"{Roles.Programador},{Roles.Admin}")]
    public class GestionTablasController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<GestionTablasController> _logger;

        private readonly ProgramadorLitalsaContext _context;

        public GestionTablasController(IMediator mediator,
            ILogger<GestionTablasController> logger, ProgramadorLitalsaContext programadorLitalsaContext)
        {
            _mediator = mediator;
            _logger = logger;
            _context = programadorLitalsaContext;
        }

        // GET: api/EnvioProgramaciones/GrupoNotificaciones
        [HttpGet("GrupoNotificaciones")]
        public async Task<ActionResult<List<GrupoNotificacionesDTO>>> GetGruposNotificaciones()
        {
            var datos = await _context.GrupoNotificaciones.ToListAsync();
            var result = TinyMapper.Map<List<GrupoNotificacionesDTO>>(datos);
            return result;
        }

        // POST: api/EnvioProgramaciones/GrupoNotificaciones
        [HttpPost("GrupoNotificaciones")]
        public async Task<ActionResult<GrupoNotificacionesDTO>> PostGrupoNotificaciones(GrupoNotificacionesDTO grupo)
        {
            _context.GrupoNotificaciones.Add(TinyMapper.Map<GrupoNotificaciones>(grupo));
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetGruposNotificaciones), new { id = grupo.Id }, grupo);
        }

        // PUT: api/EnvioProgramaciones/GrupoNotificaciones/5
        [HttpPut("GrupoNotificaciones/{id}")]
        public async Task<IActionResult> PutGrupoNotificaciones(int id, GrupoNotificacionesDTO grupo)
        {
            if (id != grupo.Id)
            {
                return BadRequest();
            }

            var currentGrupo = await _context.GrupoNotificaciones.FirstOrDefaultAsync(o => o.Id == id);
            _context.Entry(currentGrupo).CurrentValues.SetValues(grupo);

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!_context.GrupoNotificaciones.Any(e => e.Id == id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // DELETE: api/EnvioProgramaciones/GrupoNotificaciones/5
        [HttpDelete("GrupoNotificaciones/{id}")]
        public async Task<IActionResult> DeleteGrupoNotificaciones(int id)
        {
            var grupo = await _context.GrupoNotificaciones.FindAsync(id);
            if (grupo == null)
            {
                return NotFound();
            }

            _context.GrupoNotificaciones.Remove(grupo);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        // GET: api/EnvioProgramaciones/DestinatarioProgramaciones
        [HttpGet("DestinatarioProgramaciones")]
        public async Task<ActionResult<List<DestinatarioProgramacionesDTO>>> GetDestinatarioProgramaciones()
        {
            var datos = await _context.DestinatarioProgramaciones.ToListAsync();
            var result = TinyMapper.Map<List<DestinatarioProgramacionesDTO>>(datos);
            return result;
        }

        // POST: api/EnvioProgramaciones/DestinatarioProgramaciones
        [HttpPost("DestinatarioProgramaciones")]
        public async Task<ActionResult<DestinatarioProgramacionesDTO>> PostDestinatarioProgramaciones(
            DestinatarioProgramacionesDTO destinatario)
        {
            _context.DestinatarioProgramaciones.Add(TinyMapper.Map<DestinatarioProgramaciones>(destinatario));
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetDestinatarioProgramaciones), new { id = destinatario.Id }, destinatario);
        }

        // PUT: api/EnvioProgramaciones/DestinatarioProgramaciones/5
        [HttpPut("DestinatarioProgramaciones/{id}")]
        public async Task<IActionResult> PutDestinatarioProgramaciones(int id, DestinatarioProgramacionesDTO destinatario)
        {
            if (id != destinatario.Id)
            {
                return BadRequest();
            }

            var currentGrupo = await _context.DestinatarioProgramaciones.FirstOrDefaultAsync(o => o.Id == id);
            _context.Entry(currentGrupo).CurrentValues.SetValues(destinatario);

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!_context.DestinatarioProgramaciones.Any(e => e.Id == id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // DELETE: api/EnvioProgramaciones/DestinatarioProgramaciones/5
        [HttpDelete("DestinatarioProgramaciones/{id}")]
        public async Task<IActionResult> DeleteDestinatarioProgramaciones(int id)
        {
            var destinatario = await _context.DestinatarioProgramaciones.FindAsync(id);
            if (destinatario == null)
            {
                return NotFound();
            }

            _context.DestinatarioProgramaciones.Remove(destinatario);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        // GET: api/EnvioProgramaciones/Comentarios
        [HttpGet("Comentarios")]
        public async Task<ActionResult<List<ComentariosDTO>>> GetComentarios()
        {
            var datos = await _context.TablaComentarios.ToListAsync();
            var result = TinyMapper.Map<List<ComentariosDTO>>(datos);
            return result;
        }

        // POST: api/EnvioProgramaciones/Comentarios
        [HttpPost("Comentarios")]
        public async Task<ActionResult<ComentariosDTO>> PostComentarios(
            ComentariosDTO comm)
        {
            _context.TablaComentarios.Add(TinyMapper.Map<TablaComentarios>(comm));
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetComentarios), new { id = comm.Id }, comm);
        }

        // PUT: api/EnvioProgramaciones/Comentarios/5
        [HttpPut("Comentarios/{id}")]
        public async Task<IActionResult> PutComentarios(int id, ComentariosDTO destinatario)
        {
            if (id != destinatario.Id)
            {
                return BadRequest();
            }
            
            var currentGrupo = await _context.TablaComentarios.FirstOrDefaultAsync(o => o.Id == id);
            _context.Entry(currentGrupo).CurrentValues.SetValues(destinatario);

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!_context.TablaComentarios.Any(e => e.Id == id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // DELETE: api/EnvioProgramaciones/Comentarios/5
        [HttpDelete("Comentarios/{id}")]
        public async Task<IActionResult> DeleteComentario(int id)
        {
            var comm = await _context.TablaComentarios.FindAsync(id);
            if (comm == null)
            {
                return NotFound();
            }

            _context.TablaComentarios.Remove(comm);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        // GET: api/EnvioProgramaciones/TablaCfg
        [HttpGet("TablaCfg")]
        public async Task<ActionResult<List<TablaCfgDTO>>> GetTablaCfg()
        {
            var datos = await _context.TablaCfg.ToListAsync();
            var result = TinyMapper.Map<List<TablaCfgDTO>>(datos);
            return result;
        }

        // POST: api/EnvioProgramaciones/TablaCfg
        [HttpPost("TablaCfg")]
        public async Task<ActionResult<TablaCfgDTO>> PostTablaCfg(TablaCfgDTO comm)
        {
            _context.TablaCfg.Add(TinyMapper.Map<TablaCfg>(comm));
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetTablaCfg), new { id = comm.Id }, comm);
        }

        // PUT: api/EnvioProgramaciones/TablaCfg/5
        [HttpPut("TablaCfg/{id}")]
        public async Task<IActionResult> PutTablaCfg(int id, TablaCfgDTO cfg)
        {
            if (id != cfg.Id)
            {
                return BadRequest();
            }

            var currentGrupo = await _context.TablaCfg.FirstOrDefaultAsync(o => o.Id == id);
            _context.Entry(currentGrupo).CurrentValues.SetValues(cfg);

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!_context.TablaCfg.Any(e => e.Id == id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // DELETE: api/EnvioProgramaciones/TablaCfg/5
        [HttpDelete("TablaCfg/{id}")]
        public async Task<IActionResult> DeleteTablaCfg(int id)
        {
            var cfg = await _context.TablaCfg.FindAsync(id);
            if (cfg == null)
            {
                return NotFound();
            }

            _context.TablaCfg.Remove(cfg);
            await _context.SaveChangesAsync();

            return NoContent();
        }


        // GET: api/EnvioProgramaciones/Plano
        [HttpGet("Plano")]
        public async Task<ActionResult<List<PlanoDTO>>> GetPlano()
        {
            var datos = await _context.Plano.ToListAsync();
            var result = TinyMapper.Map<List<PlanoDTO>>(datos);
            return result;
        }

        // POST: api/EnvioProgramaciones/Plano
        [HttpPost("Plano")]
        public async Task<ActionResult<TablaCfgDTO>> PostPlano(PlanoDTO comm)
        {
            _context.Plano.Add(TinyMapper.Map<Plano>(comm));
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetPlano), new { id = comm.Idplano }, comm);
        }

        // PUT: api/EnvioProgramaciones/Plano/5
        [HttpPut("Plano/{id}")]
        public async Task<IActionResult> PutPlano(int id, PlanoDTO p)
        {
            if (id != p.Idplano)
            {
                return BadRequest();
            }

            var currentPlano = await _context.Plano.FirstOrDefaultAsync(o => o.Idplano == id);
            _context.Entry(currentPlano).CurrentValues.SetValues(p);

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!_context.Plano.Any(e => e.Idplano == id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // DELETE: api/EnvioProgramaciones/Plano/5
        [HttpDelete("Plano/{id}")]
        public async Task<IActionResult> DeletePlano(int id)
        {
            var p = await _context.Plano.FindAsync(id);
            if (p == null)
            {
                return NotFound();
            }

            _context.Plano.Remove(p);
            await _context.SaveChangesAsync();

            return NoContent();
        }
    }
}