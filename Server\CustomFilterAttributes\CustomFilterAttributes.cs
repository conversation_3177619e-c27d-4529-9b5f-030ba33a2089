﻿using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using Microsoft.AspNetCore.Mvc.ApplicationModels;

namespace ProgramadorGeneralBLZ.Server.CustomFilterAttributes
{
    public class ClaimRequirementContainRoleAttribute : TypeFilterAttribute
    {
        public ClaimRequirementContainRoleAttribute(string claimType, string claimValue) : base(typeof(ClaimRequirementContainRoleFilter))
        {
            Arguments = new object[] { new Claim(claimType, claimValue) };
        }
    }

    public class ClaimRequirementContainRoleFilter : IAuthorizationFilter
    {
        readonly Claim _claim;

        public ClaimRequirementContainRoleFilter(Claim claim)
        {
            _claim = claim;
        }

        public void OnAuthorization(AuthorizationFilterContext context)
        {
            var claimValueList = _claim.Value.Contains(',')
                ? _claim.Value.Split(",").ToList()
                : new List<string>{_claim.Value};

            var hasClaim = context.HttpContext.User.Claims.Any(c => c.Type == _claim.Type && claimValueList.Any(cv=>c.Value.Contains(cv)));
            if (!hasClaim)
            {
                context.Result = new ForbidResult();
            }
        }
    }

}
