﻿@using ProgramadorGeneralBLZ.Shared
@using ProgramadorGeneralBLZ.Shared.DTO
@using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa
@using ProgramadorGeneralBLZ.Shared.ResponseModels
@inject HttpClient Http
@inject SpinnerService SpinnerService
@inject IToastService ToastService
@inject IJSRuntime Js

<div class="h-100 overflow-auto px-2 py-1">
    <DxGrid Data="@productos" SizeMode="SizeMode.Medium"
            CssClass="ch-320 smallFont progGrid" PageSize="18"
            KeyFieldName="Idproducto" Context="GridTablaProductos"
            UnboundColumnData="Grid_UnboundColumnData"
            AllowSort="true" EditNewRowPosition="GridEditNewRowPosition.Top"
            ShowFilterRow="true" ValidationEnabled="true" FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always"
            EditMode="GridEditMode.EditRow" @ref="Grid" PagerPosition="GridPagerPosition.TopAndBottom"
            EditorRenderMode="GridEditorRenderMode.Integrated"
            EditModelSaving="GridPlano_EditModelSaving"
            DataItemDeleting="Grid_DataItemDeleting">
        <Columns>
            <DxGridCommandColumn Width="80px">
                <HeaderTemplate>
                    <a class="oi oi-plus" @onclick="@(() => Grid.StartEditNewRowAsync())" style="text-decoration: none;color: lightskyblue;" href="javascript:void(0);"></a>
                </HeaderTemplate>
                <CellDisplayTemplate>
                    <a class="oi oi-pencil" @onclick="@(() => Grid.StartEditRowAsync(context.VisibleIndex))" style="text-decoration: none; padding-right: 15px; color: #c75fff;" href="javascript:void(0);"></a>
                    <a class="oi oi-x" @onclick="@(() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex))" style="text-decoration: none; color: red;" href="javascript:void(0);"></a>
                </CellDisplayTemplate>
                <CellEditTemplate>
                    <a class="oi oi-arrow-thick-bottom" @onclick="@(() => Grid.SaveChangesAsync())" style="text-decoration: none; padding-right: 15px; color: greenyellow; margin-right: 6px; margin-top: 3px;" href="javascript:void(0);"></a>
                    <a class="oi oi-x" @onclick="@(() => Grid.CancelEditAsync())" style="text-decoration: none; color: red;" href="javascript:void(0);"></a>
                </CellEditTemplate>
            </DxGridCommandColumn>
            <DxGridDataColumn FieldName="@nameof(ProductoDTO.Idproducto)" Width="80" DisplayFormat="F0" SortIndex="0"
                              SortOrder="GridColumnSortOrder.Ascending"
                              FilterRowOperatorType="GridFilterRowOperatorType.Contains">
                <CellDisplayTemplate Context="cellText">
                    <span class="p-1 d-block text-left" style="cursor: pointer"
                          @ondblclick="() => AbrirFichaTecnica(cellText.DisplayText)">
                        @cellText.DisplayText
                    </span>
                </CellDisplayTemplate>
            </DxGridDataColumn>
            <DxGridDataColumn FieldName="@nameof(ProductoDTO.NombreProducto)" FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
            <DxGridDataColumn FieldName="@nameof(ProductoDTO.Denominacion)" FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
            <DxGridDataColumn FieldName="@nameof(ProductoDTO.Existencias)" Width="100" TextAlignment="GridTextAlignment.Center" />
            <DxGridDataColumn FieldName="@nameof(ProductoDTO.Solidos)" Width="100" DisplayFormat="N2" TextAlignment="GridTextAlignment.Center"
                              FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
            <DxGridDataColumn FieldName="@nameof(ProductoDTO.CantidadPedida)" Width="100" DisplayFormat="N0"
                              FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
            <DxGridDataColumn FieldName="@nameof(ProductoDTO.FechaEntrega)" Width="100" />
            <DxGridDataColumn FieldName="@nameof(ProductoDTO.CalleApq)" Width="100" FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
            <DxGridDataColumn FieldName="@nameof(ProductoDTO.Idficha)" Caption="Ficha" Width="100" DisplayFormat="N0"
                              FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
            <DxGridDataColumn FieldName="@nameof(ProductoDTO.Observaciones)" FilterRowOperatorType="GridFilterRowOperatorType.Contains" />
            <DxGridDataColumn FieldName="@nameof(ProductoDTO.MarcadoVerde)" Width="100" FilterRowOperatorType="GridFilterRowOperatorType.Equal">
                <CellDisplayTemplate Context="checkbox">
                    <DxCheckBox Checked="@((bool)checkbox.Value)" ReadOnly="true" CssClass="checkVerde" />
                </CellDisplayTemplate>
            </DxGridDataColumn>

        </Columns>
        <DataColumnCellEditTemplate>
            @{
                var producto = (ProductoDTO)GridTablaProductos.EditModel;
            }
            @switch (GridTablaProductos.DataColumn.FieldName)
            {
                case "Idproducto":
                    <DxSpinEdit @bind-Value="@producto.Idproducto"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "NombreProducto":
                    <DxTextBox @bind-Text="@producto.NombreProducto"
                               ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                               ShowValidationIcon="true" />
                    break;
                case "Denominacion":
                    <DxTextBox @bind-Text="@producto.Denominacion"
                               ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                               ShowValidationIcon="true" />
                    break;
                case "Existencias":
                    <DxSpinEdit @bind-Value="@producto.Existencias"
                                ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "Solidos":
                    <DxSpinEdit @bind-Value="@producto.Solidos"
                                ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "CantidadPedida":
                    <DxSpinEdit @bind-Value="@producto.CantidadPedida"
                                ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "FechaEntrega":
                    <DxDateEdit @bind-Date="@producto.FechaEntrega"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "CalleApq":
                    <DxTextBox @bind-Text="@producto.CalleApq"
                               ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                               ShowValidationIcon="true" />
                    break;
                case "Idficha":
                    <DxSpinEdit @bind-Value="@producto.Idficha"
                                ShowSpinButtons="false"
                                ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                ShowValidationIcon="true" />
                    break;
                case "Observaciones":
                    <DxTextBox @bind-Text="@producto.Observaciones"
                               ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                               ShowValidationIcon="true" />
                    break;
                case "MarcadoVerde":
                    <DxCheckBox CssClass="d-inline-block checkVerde" @bind-Checked="@producto.MarcadoVerde" ValueChecked=true ValueUnchecked=false />
                    break;
            }
        </DataColumnCellEditTemplate>
    </DxGrid>
</div>
@code
{
    DxGrid? Grid;
    List<ProductoDTO> productos = new List<ProductoDTO>();
    IJSObjectReference _jsModule { get; set; }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        //await base.OnAfterRenderAsync(firstRender);

        if (firstRender)
        {
            _jsModule = await Js.InvokeAsync<IJSObjectReference>("import", $"./scripts/extraScripts.js?v={DateTime.Now}");

            //        _dotNetHelper = DotNetObjectReference.Create(this);
            ////await JS.InvokeVoidAsync( "jsFunctions.addKeyboardListenerEvent");
            //        await _jsModule.InvokeVoidAsync("setDotNetHelper", _dotNetHelper);
            //        await _jsModule.InvokeVoidAsync("initialize");
        }


    }
    protected override async Task OnInitializedAsync()
    {
        SpinnerService.Show();
        await LoadData();
        SpinnerService.Hide();
    }

    async Task LoadData()
    {
        productos = await Http.GetFromJsonAsync<List<ProductoDTO>>("GestionTablasV2/Producto");
    }

    void Grid_UnboundColumnData(GridUnboundColumnDataEventArgs e)
    {
        if (e.FieldName == "FT")
        {
            var idProducto = Convert.ToDouble(e.GetRowValue("Idproducto"));
            e.Value = $"\\\\*************\\in2$\\datos\\dato01LITA\\DOCS\\ARTICULOS\\999985FTECNICA1152__________27-03-14__________15-12-14.pdf";
        }
    }
    async Task GridPlano_EditModelSaving(GridEditModelSavingEventArgs e)
    {
        SpinnerService.Show();
        var dest = (ProductoDTO)e.EditModel;
        var response = e.IsNew == false
            ? await Http.PutAsJsonAsync($"GestionTablasV2/Producto/{dest.Idproducto}", dest)
            : await Http.PostAsJsonAsync("GestionTablasV2/Producto", dest);
        if (response.IsSuccessStatusCode)
            await LoadData();
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            ToastService.ShowError(error);
        }
        SpinnerService.Hide();
    }
    async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        SpinnerService.Show();
        var item = (ProductoDTO)e.DataItem;
        var response = await Http.DeleteAsync($"GestionTablasV2/Producto/{item.Idproducto}");
        if (response.IsSuccessStatusCode)
            await LoadData();
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            ToastService.ShowError(error);
        }
        SpinnerService.Hide();
    }
    async Task Delete(ProductoDTO item)
    {
        SpinnerService.Show();
        var response = await Http.DeleteAsync($"GestionTablasV2/Producto/{item.Idproducto}");
        if (response.IsSuccessStatusCode)
            await LoadData();
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            ToastService.ShowError(error);
        }
        SpinnerService.Hide();
    }

    private async Task AbrirFichaTecnica(string cellTextDisplayText)
    {
        SpinnerService.Show();
        var response = await Http.GetFromJsonAsync<ListResult<string>>($"Pedido/{cellTextDisplayText}/getFileByType?tipo={Enums.TipoFicheros.FichaTecnica}");
        if (response.Errors.Any())
        {
            ToastService.ShowInfo($"{response.Errors.First()}");
        }
        else
        {
            foreach (var filePath in response.Data)
            {
                await Js.InvokeVoidAsync("openPdfInNewTab", filePath);
            }
        }
        SpinnerService.Hide();
    }
}
