﻿@inject NavigationManager Navigation

<nav class="navbar header-navbar p-0">
    <div class="w-100">
        <div class="p-0">
            <DxMenu Orientation="Orientation.Horizontal"
                    ItemsPosition="ItemPosition.Center"
                    ItemsStretched="false"
                    DropDownActionMode="MenuDropDownActionMode.Click"
                    CollapseItemToIconMode="MenuCollapseItemToIconMode.Sequentially"
                    CollapseItemsToHamburgerMenu="false">
                <Items>
                    <DxMenuItem CssClass="search-menu-item" Position="ItemPosition.Start">
                        <Template>
                            <DxComboBox NullText="Litografías..."
                                        SizeMode="SizeMode.Large"
                                        Data="@Cities"
                                        @bind-Value="@_maquinaSeleccionada"
                                        CssClass="mx-3"
                                        Enabled="_seleccionarMaquina" />
                        </Template>
                    </DxMenuItem>
                    <DxMenuItem CssClass="search-menu-item" BeginGroup="true" Position="ItemPosition.Start">
                        <Template>
                            <DxButton RenderStyle="ButtonRenderStyle.Warning"
                                      Text="Bloquear Máquina" SizeMode="SizeMode.Large"
                                      Attributes="@(new Dictionary<string, object> { ["title"] = "Bloquear máquina seleccionada" })"
                                      IconCssClass="oi oi-lock-locked iconInButton"
                                      CssClass="mx-3"
                                      Enabled="@(!string.IsNullOrEmpty(_maquinaSeleccionada) && !_maquinaBloqueada)"
                                      Click="@BloquearMaquina" />
                        </Template>
                    </DxMenuItem>
                    <DxMenuItem CssClass="search-menu-item" Position="ItemPosition.Start">
                        <Template>
                            <DxButton RenderStyle="ButtonRenderStyle.Success"
                                      Text="Desbloquear Máquina" SizeMode="SizeMode.Large"
                                      Attributes="@(new Dictionary<string, object> { ["title"] = "Desbloquear máquina seleccionada" })"
                                      IconCssClass="oi oi-lock-unlocked iconInButton"
                                      CssClass="ms-1 ajusteBtn"
                                      Enabled="@(!string.IsNullOrEmpty(_maquinaSeleccionada) && _maquinaBloqueada)"
                                      Click="@DesbloquearMaquina" />
                        </Template>
                    </DxMenuItem>
                    <DxMenuItem CssClass="search-menu-item" BeginGroup="true">
                        <Template>
                            <DxButton RenderStyle="ButtonRenderStyle.Danger"
                                      Text="Forzar Desbloqueo" SizeMode="SizeMode.Large"
                                      Attributes="@(new Dictionary<string, object> { ["title"] = "Forzar desbloqueo máquina seleccionada" })"
                                      IconCssClass="oi oi-lock-unlocked iconInButton"
                                      CssClass="mx-3 ajusteBtn"
                                      Enabled="@(!string.IsNullOrEmpty(_maquinaSeleccionada) && _maquinaBloqueada)" />
                        </Template>
                    </DxMenuItem>
                </Items>
            </DxMenu>
        </div>
    </div>
</nav>

@code {
    string _maquinaSeleccionada { get; set; }
    bool _maquinaBloqueada = false;
    bool _seleccionarMaquina = true;

    IEnumerable<string> Cities = new List<string>() {
        "London",
        "Berlin",
        "Paris",
    };

    async void DesbloquearMaquina()
    {
        //Llamadas al API


        //Lo ultimo, limpiar la máquina seleccionada.

        _seleccionarMaquina = true;
        _maquinaSeleccionada = null;
        _maquinaBloqueada = false;
        await InvokeAsync(StateHasChanged);
    }
    async void BloquearMaquina()
    {
        //Llamadas al API


        //Lo ultimo, limpiar la máquina seleccionada.
        _seleccionarMaquina = false;
        _maquinaBloqueada = true;
        //await InvokeAsync(StateHasChanged);
    }
}
