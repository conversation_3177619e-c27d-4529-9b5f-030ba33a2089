﻿{
   "CodeGenerationMode": 2,
   "ContextClassName": "LitalsaDataWarehouseContext",
   "ContextNamespace": null,
   "DefaultDacpacSchema": null,
   "FilterSchemas": false,
   "IncludeConnectionString": true,
   "ModelNamespace": null,
   "OutputContextPath": "Data\\LitalsaDataWarehouse",
   "OutputPath": "Models\\LitalsaDataWarehouse",
   "PreserveCasingWithRegex": true,
   "ProjectRootNamespace": "ProgramadorGeneralBLZ.Server",
   "Schemas": null,
   "SelectedHandlebarsLanguage": 0,
   "SelectedToBeGenerated": 0,
   "Tables": [
      {
         "Name": "[calidad].[NotasProgramacion]",
         "ObjectType": 3
      }
   ],
   "UiHint": "QPLANT1.LitalsaDataWarehouse",
   "UncountableWords": null,
   "UseBoolPropertiesWithoutDefaultSql": false,
   "UseDatabaseNames": false,
   "UseDbContextSplitting": false,
   "UseFluentApiOnly": true,
   "UseHandleBars": false,
   "UseHierarchyId": false,
   "UseInflector": false,
   "UseLegacyPluralizer": false,
   "UseManyToManyEntity": false,
   "UseNoDefaultConstructor": false,
   "UseNoObjectFilter": false,
   "UseNodaTime": false,
   "UseNullableReferences": false,
   "UseSchemaFolders": false,
   "UseSpatial": false,
   "UseT4": false
}