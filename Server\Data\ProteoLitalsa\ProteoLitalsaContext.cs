﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using ProgramadorGeneralBLZ.Server.Models.ProteoLitalsa;

namespace ProgramadorGeneralBLZ.Server.Data.ProteoLitalsa
{
    public partial class ProteoLitalsaContext : DbContext
    {
        public ProteoLitalsaContext()
        {
        }

        public ProteoLitalsaContext(DbContextOptions<ProteoLitalsaContext> options)
            : base(options)
        {
        }

        public virtual DbSet<AccMesExportPhasesIdprog> AccMesExportPhasesIdprog { get; set; }
        public virtual DbSet<AccMesExportWorkOrders> AccMesExportWorkOrders { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
#warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see http://go.microsoft.com/fwlink/?LinkId=723263.
                optionsBuilder.UseSqlServer("Data Source=QPLANT2\\PROTEO;Initial Catalog=proteo_litalsadb;User ID=sa;Password=*********");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<AccMesExportPhasesIdprog>(entity =>
            {
                entity.HasNoKey();

                entity.ToTable("ACC_MES_EXPORT_PHASES_IDPROG");

                entity.Property(e => e.BarnizNecesario).HasColumnName("BARNIZ_NECESARIO");

                entity.Property(e => e.Bloqueada).HasColumnName("BLOQUEADA");

                entity.Property(e => e.CodigoAplicacionAnterior).HasColumnName("CODIGO_APLICACION_ANTERIOR");

                entity.Property(e => e.CodigoAplicacionPosterior).HasColumnName("CODIGO_APLICACION_POSTERIOR");

                entity.Property(e => e.CodigoAplicacionWet).HasColumnName("CODIGO_APLICACION_WET");

                entity.Property(e => e.CodigoBarniz).HasColumnName("CODIGO_BARNIZ");

                entity.Property(e => e.CogerHojasMuestra).HasColumnName("COGER_HOJAS_MUESTRA");

                entity.Property(e => e.CycleTime).HasColumnName("CYCLE_TIME");

                entity.Property(e => e.DateTimeDownload)
                    .HasColumnType("datetime")
                    .HasColumnName("DATE_TIME_DOWNLOAD");

                entity.Property(e => e.DateTimeModif)
                    .HasColumnType("datetime")
                    .HasColumnName("DATE_TIME_MODIF");

                entity.Property(e => e.Descartar).HasColumnName("DESCARTAR");

                entity.Property(e => e.DescripPhase).HasColumnName("DESCRIP_PHASE");

                entity.Property(e => e.DescripcionBarniz)
                    .HasMaxLength(500)
                    .IsUnicode(false)
                    .HasColumnName("DESCRIPCION_BARNIZ");

                entity.Property(e => e.DurationPlanned).HasColumnName("DURATION_PLANNED");

                entity.Property(e => e.EmpezadaProteo).HasColumnName("EMPEZADA_PROTEO");

                entity.Property(e => e.FechaEmpezadaProteo).HasColumnName("FECHA_EMPEZADA_PROTEO");

                entity.Property(e => e.FechaTerminadaProteo).HasColumnName("FECHA_TERMINADA_PROTEO");

                entity.Property(e => e.Flip).HasColumnName("FLIP");

                entity.Property(e => e.Fraction)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("FRACTION");

                entity.Property(e => e.Free1).HasColumnName("FREE1");

                entity.Property(e => e.Free2).HasColumnName("FREE2");

                entity.Property(e => e.Free3).HasColumnName("FREE3");

                entity.Property(e => e.Free4).HasColumnName("FREE4");

                entity.Property(e => e.GramajeMaximo).HasColumnName("GRAMAJE_MAXIMO");

                entity.Property(e => e.GramajeMinimo).HasColumnName("GRAMAJE_MINIMO");

                entity.Property(e => e.HojasProcesadas).HasColumnName("HOJAS_PROCESADAS");

                entity.Property(e => e.Idprogramacion).HasColumnName("IDPROGRAMACION");

                entity.Property(e => e.MachinePlanned).HasColumnName("MACHINE_PLANNED");

                entity.Property(e => e.MaxWorkersOnWorkOrder).HasColumnName("MAX_WORKERS_ON_WORK_ORDER");

                entity.Property(e => e.ObservacionesCalidad)
                    .HasMaxLength(500)
                    .IsUnicode(false)
                    .HasColumnName("OBSERVACIONES_CALIDAD");

                entity.Property(e => e.ObservacionesGenerales)
                    .HasMaxLength(500)
                    .IsUnicode(false)
                    .HasColumnName("OBSERVACIONES_GENERALES");

                entity.Property(e => e.ObservacionesHojasMuestra)
                    .HasMaxLength(500)
                    .IsUnicode(false)
                    .HasColumnName("OBSERVACIONES_HOJAS_MUESTRA");

                entity.Property(e => e.PasadasAdicionales).HasColumnName("PASADAS_ADICIONALES");

                entity.Property(e => e.Phase).HasColumnName("PHASE");

                entity.Property(e => e.PhaseErased).HasColumnName("PHASE_ERASED");

                entity.Property(e => e.PhaseStatus)
                    .HasMaxLength(255)
                    .HasColumnName("PHASE_STATUS");

                entity.Property(e => e.Posicion)
                    .HasColumnName("POSICION")
                    .HasDefaultValueSql("((-1))");

                entity.Property(e => e.PostPhase).HasColumnName("POST_PHASE");

                entity.Property(e => e.PreviousPhase).HasColumnName("PREVIOUS_PHASE");

                entity.Property(e => e.QtyPrevPhase).HasColumnName("QTY_PREV_PHASE");

                entity.Property(e => e.RecUpdated).HasColumnName("REC_UPDATED");

                entity.Property(e => e.Revisado)
                    .HasColumnName("REVISADO")
                    .HasDefaultValueSql("((0))");

                entity.Property(e => e.StartPlanned)
                    .HasColumnType("datetime")
                    .HasColumnName("START_PLANNED");

                entity.Property(e => e.TemperaturaHorno).HasColumnName("TEMPERATURA_HORNO");

                entity.Property(e => e.TipoLimpieza)
                    .HasMaxLength(500)
                    .IsUnicode(false)
                    .HasColumnName("TIPO_LIMPIEZA");

                entity.Property(e => e.VelocidadMaxima).HasColumnName("VELOCIDAD_MAXIMA");

                entity.Property(e => e.Wet).HasColumnName("WET");

                entity.Property(e => e.WorkOrder).HasColumnName("WORK_ORDER");
            });

            modelBuilder.Entity<AccMesExportWorkOrders>(entity =>
            {
                entity.HasKey(e => e.WorkOrder);

                entity.ToTable("ACC_MES_EXPORT_WORK_ORDERS");

                entity.Property(e => e.WorkOrder).HasColumnName("WORK_ORDER");

                entity.Property(e => e.Client).HasColumnName("CLIENT");

                entity.Property(e => e.DateTimeDownload)
                    .HasColumnType("datetime")
                    .HasColumnName("DATE_TIME_DOWNLOAD");

                entity.Property(e => e.DateTimeModif)
                    .HasColumnType("datetime")
                    .HasColumnName("DATE_TIME_MODIF");

                entity.Property(e => e.DateTimeUpdate)
                    .HasColumnType("datetime")
                    .HasColumnName("DATE_TIME_UPDATE");

                entity.Property(e => e.DescripWorkOrder).HasColumnName("DESCRIP_WORK_ORDER");

                entity.Property(e => e.EstimatedDuration).HasColumnName("ESTIMATED_DURATION");

                entity.Property(e => e.Metalwork).HasColumnName("METALWORK");

                entity.Property(e => e.Observations).HasColumnName("OBSERVATIONS");

                entity.Property(e => e.ProcessType).HasColumnName("PROCESS_TYPE");

                entity.Property(e => e.QtyPlanned).HasColumnName("QTY_PLANNED");

                entity.Property(e => e.RecUpdated).HasColumnName("REC_UPDATED");

                entity.Property(e => e.Routing).HasColumnName("ROUTING");

                entity.Property(e => e.StartPlanned)
                    .HasColumnType("datetime")
                    .HasColumnName("START_PLANNED");

                entity.Property(e => e.Varnish).HasColumnName("VARNISH");

                entity.Property(e => e.WorkOrderErased).HasColumnName("WORK_ORDER_ERASED");

                entity.Property(e => e.WorkOrderStatus)
                    .IsRequired()
                    .HasColumnName("WORK_ORDER_STATUS");
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}