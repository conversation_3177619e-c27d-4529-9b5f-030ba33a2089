﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Nelibur.ObjectMapper;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.DatosGenerales.Query;

public class GetDatosProductoQuery : IRequest<ListResult<ProductoDTO>>
{
}

internal class GetDatosProductoQueryHandler : IRequestHandler<GetDatosProductoQuery, ListResult<ProductoDTO>>
{
    private readonly ProgramadorLitalsaContext _programadorLitalsaContext;
    public GetDatosProductoQueryHandler(ProgramadorLitalsaContext programadorLitalsaContext)
    {
        _programadorLitalsaContext = programadorLitalsaContext;
    }

    public async Task<ListResult<ProductoDTO>> Handle(GetDatosProductoQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var result = new ListResult<ProductoDTO>()
            {
                Data = new List<ProductoDTO>(),
                Errors = new List<string>()
            };

            var datosProd = await _programadorLitalsaContext.ViewCodApliTablaProductos.ToListAsync(cancellationToken);
            result.Data = TinyMapper.Map<List<ProductoDTO>>(datosProd);
            return result;
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: GetDatosProductoQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            throw new Exception(errorText, e);
        }
    }
}