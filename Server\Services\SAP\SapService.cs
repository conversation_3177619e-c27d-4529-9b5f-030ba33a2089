﻿using System.Diagnostics;
using DevExpress.XtraRichEdit.Import.Html;
using System.Drawing;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using ProgramadorGeneralBLZ.Server.Data.InterfaseBD;
using ProgramadorGeneralBLZ.Server.Data.LitalsaDataWarehouse;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.DatoLita01;
using ProgramadorGeneralBLZ.Server.Models.InterfaseBD;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared.ResponseModels;
using Motivos = ProgramadorGeneralBLZ.Server.Models.InterfaseBD.Motivos;
using System.Linq.Expressions;
using MimeKit;
using System.Reflection;
using ProgramadorGeneralBLZ.Shared;
using ProgramadorGeneralBLZ.Shared.DTO;

namespace ProgramadorGeneralBLZ.Server.Services.SAP;

public class SapService : ISapService
{
    private readonly IEmailService _emailService;
    private readonly InterfaseBDContext _contextSAP;
    private readonly ProgramadorLitalsaContext _programadorLitalsaContext;
    private readonly LitalsaDataWarehouseContext _litalsaDataWarehouseContext;

    public SapService(InterfaseBDContext contextSAP, ProgramadorLitalsaContext programadorLitalsaContext,
        LitalsaDataWarehouseContext litalsaDataWarehouseContext, IEmailService emailService)
    {
        _contextSAP = contextSAP;
        _programadorLitalsaContext = programadorLitalsaContext;
        _litalsaDataWarehouseContext = litalsaDataWarehouseContext;
        _emailService = emailService;
    }

    private async void EnviarMensajeAvisoBorrarPedidosProgramados(string mensaje)
    {
        var body =
            $"{mensaje} <br /> " +
            $"<b>Fecha y Hora</b>: {DateTime.Now:yyyy-MM-dd -- HH:mm:ss:ffff}";
        await _emailService.SendAsync("<EMAIL>", "ORDENES PARA BORRAR DESDE SAP", body,
            MessagePriority.Urgent);
    }

    public async Task<SingleResult<bool>> ActualizarDatosDesdeSAP(bool registrosNuevos, CancellationToken ct)
    {
        var result = new SingleResult<bool> { Errors = new List<string>(), Data = false };
        try
        {
            // Obtener todas las órdenes y operaciones según la condición
            var conditionOrden = registrosNuevos
                ? (Expression<Func<Orden, bool>>)(o => o.Nuevo && !o.Borrado && !o.Transformado)
                : (o => o.Modificado && !o.Borrado && !o.Transformado);
            var ordenes = await ObtenerOrdenes(conditionOrden, ct);

            // Si no hay registros que cumplan la condición, no necesitamos continuar
            if (!ordenes.Any())
            {
                result.Data = false;
                return result;
            }

            var conditionOperaciones = registrosNuevos
                ? (Expression<Func<Operaciones, bool>>)(o => o.Nuevo && !o.Borrado && !o.Transformado)
                : (o => o.Modificado && !o.Borrado && !o.Transformado);
            var operaciones = await ObtenerOperaciones(conditionOperaciones, ct);

            // Procesar las nuevas órdenes y operaciones
            List<PedidoProcesado> listaPP = await TratarOrdenes(ordenes, registrosNuevos, operaciones, ct);
            await _programadorLitalsaContext.PedidoProcesado.AddRangeAsync(listaPP, ct);
            //No se hace el AddRange porque cada tabla se trata independientemente en el método
            await TratarOperaciones(operaciones, registrosNuevos, ct);

            await _programadorLitalsaContext.SaveChangesAsync(ct);

            MarcarComoTransformadas(ordenes, registrosNuevos ? "Nuevo" : "Modificado");
            MarcarComoTransformadas(operaciones, registrosNuevos ? "Nuevo" : "Modificado");
            await _contextSAP.SaveChangesAsync(ct);

            result.Data = true;
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: ComprobarRegistros{(registrosNuevos ? "Nuevos" : "Modificados")} - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }


    public async Task<SingleResult<bool>> ComprobarRegistrosBorrados(CancellationToken ct)
    {

        var result = new SingleResult<bool> { Errors = new List<string>(), Data = false };
        try
        {
            var ordenes = await _contextSAP.Orden
                .Where(o => o.Modificado && o.Borrado)
                .ToListAsync(ct);

            // Si no hay registros que cumplan la condición, no necesitamos continuar
            if (!ordenes.Any())
            {
                result.Data = false;
                return result;
            }

            var listadoOrdenes = ordenes.Select(o => o.NumOrden);


            if (_programadorLitalsaContext.TablaProgramacion.Any(o => listadoOrdenes.Contains(o.Idpedido.ToString())))
            {
                var mensaje =
                    $"Se han detectado los siguientes pedidos que se han actualizado desde SAP como BORRADOS: {string.Join(", ", listadoOrdenes)}";
                EnviarMensajeAvisoBorrarPedidosProgramados(mensaje);
            }
            else
            {
                foreach (var orden in ordenes)
                {
                    var pp = await _programadorLitalsaContext.PedidoProcesado
                        .Where(o => o.IdPedido == int.Parse(orden.NumOrden))
                        .FirstOrDefaultAsync(ct);

                    pp.Anulado = true;
                    orden.Modificado = false;
                }

                await _programadorLitalsaContext.SaveChangesAsync(ct);
                await _contextSAP.SaveChangesAsync(ct);

                //Borrar también de tablas auxiliares del programador
                await _programadorLitalsaContext.DboPedproceso
                    .Where(o => listadoOrdenes.Contains(o.Pedido.ToString()))
                    .ExecuteDeleteAsync(ct);
                await _programadorLitalsaContext.TablaCodigosPedido
                    .Where(o => listadoOrdenes.Contains(o.Idpedido.ToString()))
                    .ExecuteDeleteAsync(ct);
                await _programadorLitalsaContext.TablaProgramacion
                    .Where(o => listadoOrdenes.Contains(o.Idpedido.ToString()))
                    .ExecuteDeleteAsync(ct);
            }

            result.Data = true;
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: ComprobarRegistrosBorrados - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }

        return result;
    }


    private async Task<List<PedidoProcesado>> TratarOrdenes(List<Orden> ordenes, bool esNueva,
        List<Operaciones> operacionesList, CancellationToken ct)
    {
        var result = new List<PedidoProcesado>();

        var campoC0_PEDNames = Enumerable.Range(1, 11).Select(i => i < 10 ? $"C0{i}ped" : $"Co{i}ped").ToList();
        foreach (var or in ordenes)
        {
            var material = await _contextSAP.Matmas.Where(o => o.Material == or.Material).FirstOrDefaultAsync(ct);
            var loteMaterial = await _contextSAP.Batmas
                .Where(o => o.Material == material.Material && o.Centro == "4610" && o.Lote == "?????????????")
                .FirstOrDefaultAsync(ct);
            //Del lote habria que filtrar tambien por LOTE y CENTRO, pero no sé dónde están en la ORDEN
            var datosLote = await _contextSAP.Batmas.Where(o => o.Material == or.Material).ToListAsync(ct);
            var pp = esNueva
                ? new PedidoProcesado()
                : await _programadorLitalsaContext.PedidoProcesado.FirstOrDefaultAsync(
                    o => o.IdPedido == int.Parse(or.NumOrden), ct);
            var ops = esNueva
                ? operacionesList.Where(o => o.NumOrden == or.NumOrden)
                : new List<Operaciones>();
            int? valorCodAPli = null;
            //Hay que añadir columna nueva para el status (B-L-C, etc)
            //Status = or.Status 
            pp.IdPedido = int.Parse(or.NumOrden);
            pp.Supedido = or.NumPedidoCliente;
            pp.IdCliente = int.Parse(or.IdCliente);
            pp.RequeridoEnFecha = pp.RequeridoEnFecha;
            pp.ActualizacionFechaRequerida = pp.ActualizacionFechaRequerida;
            pp.HojasPedido = (int?)or.CantidadOrden;
            pp.HojasTerminadas = pp.HojasTerminadas;
            pp.HojasLlevadas = pp.HojasLlevadas;
            pp.Motivos = or.IdMotivo;
            pp.TipoElemento = or.MotivoNavigation.TroquelNavigation.TipoElementoNavigation.GetDescripcion("TipoElemento1", "TextoTipoElemento");
            pp.Formato = float.Parse(or.MotivoNavigation.TroquelNavigation.DiametroEnvase);// Este o FormatoEnvase o FormatoIN2
            //Siempre se obtiene el valor de las operaciones porque si se ha modificado en el programador debe viajar a SAP
            //y este actualizará el registro y lo devolverá actualizado y se "re-actualizará"
            //INTERIOR

            pp.Pi1ped = int.Parse(operacionesList
                .FirstOrDefault(o => o.NumOrden == or.NumOrden && o.Cara == "I" && o.NumOperacion == "10")
                    ?.CodigoAplicacion ?? string.Empty);
            pp.Pi2ped = int.Parse(operacionesList
                .FirstOrDefault(o => o.NumOrden == or.NumOrden && o.Cara == "I" && o.NumOperacion == "20")
                    ?.CodigoAplicacion ?? string.Empty);
            pp.Pi3ped = int.Parse(operacionesList
                .FirstOrDefault(o => o.NumOrden == or.NumOrden && o.Cara == "I" && o.NumOperacion == "30")
                    ?.CodigoAplicacion ?? string.Empty);
            //Estos tienen que ser las hojas terminadas, pero por ahora lo dejamos montado usando las hojas de la operación por tener código ya adelantado
            pp.Hojaspi1ped = (int?)operacionesList
                .FirstOrDefault(o => o.NumOrden == or.NumOrden && o.Cara == "I" && o.NumOperacion == "10")?
                .CantidadOperacion;
            pp.Hojaspi2ped = (int?)operacionesList
                .FirstOrDefault(o => o.NumOrden == or.NumOrden && o.Cara == "I" && o.NumOperacion == "20")?
                .CantidadOperacion;
            pp.Hojaspi3ped = (int?)operacionesList
                .FirstOrDefault(o => o.NumOrden == or.NumOrden && o.Cara == "I" && o.NumOperacion == "30")?
                .CantidadOperacion;

            //EXTERIOR
            pp.Pe1ped = int.Parse(operacionesList
                .FirstOrDefault(o => o.NumOrden == or.NumOrden && o.Cara == "E" && o.NumOperacion == "10")
                            ?.CodigoAplicacion ?? string.Empty);
            pp.Pe2ped = int.Parse(operacionesList
                .FirstOrDefault(o => o.NumOrden == or.NumOrden && o.Cara == "E" && o.NumOperacion == "20")
                            ?.CodigoAplicacion ?? string.Empty);
            pp.Pe3ped = int.Parse(operacionesList.
                FirstOrDefault(o => o.NumOrden == or.NumOrden && o.Cara == "E" && o.NumOperacion == "30")
                            ?.CodigoAplicacion ?? string.Empty);
            //Estos tienen que ser las hojas terminadas, pero por ahora lo dejamos montado usando las hojas de la operación por tener código ya adelantado
            pp.Hojaspe1ped = (int?)operacionesList
                .FirstOrDefault(o => o.NumOrden == or.NumOrden && o.Cara == "E" && o.NumOperacion == "10")?
                .CantidadOperacion;
            pp.Hojaspe2ped = (int?)operacionesList
                .FirstOrDefault(o => o.NumOrden == or.NumOrden && o.Cara == "E" && o.NumOperacion == "20")?
                .CantidadOperacion;
            pp.Hojaspe3ped = (int?)operacionesList
                .FirstOrDefault(o => o.NumOrden == or.NumOrden && o.Cara == "E" && o.NumOperacion == "30")?
                .CantidadOperacion;


            var tintas = or.MotivoNavigation.MotivosColores
                .Select(o => o.CodigoColorNavigation.IdCodigoColor);
            var ppdto = new PedidoProcesadoDTO();
            //var type = ppdto.GetType();
            foreach (var (item, index) in tintas.Select((value, i) => (value, i+1)))//+1 para empezar en 1, no en 0
            {
                var indexConCero = index.ToString("00");
                var campo = campoC0_PEDNames.FirstOrDefault(o => o.Contains(indexConCero))??string.Empty;
                if (!string.IsNullOrEmpty(campo))
                {
                    var propertyInfo = pp.GetType().GetProperty(campo);

                    if (propertyInfo != null)
                    {
                        propertyInfo.SetValue(pp, Convert.ChangeType(item, propertyInfo.PropertyType), null);
                    }
                }
            }



            pp.Hojasco1ped = null;
            pp.Hojasco2ped = null;
            pp.Hojasco3ped = null;
            pp.Hojasco4ped = null;
            pp.Hojasco5ped = null;
            pp.Hojasco6ped = null;
            pp.Hojasco7ped = null;
            pp.Hojasco8ped = null;
            pp.Hojasco9ped = null;
            pp.Hojasco10ped = null;
            pp.Hojasco11ped = null;

            pp.Cd1ped = null; // TINTA INTERIOR
            pp.Hojascd1ped = null;
            pp.Cd2ped = null; // TINTA INTERIOR
            pp.Hojascd2ped = null;
            pp.Cd3ped = null; // TINTA INTERIOR
            pp.Hojascd3ped = null;
            pp.Cd4ped = null; // TINTA INTERIOR
            pp.Hojascd4ped = null;

            pp.IdPri1 = null; //Sin uso
            pp.Capai1 = null; //Sin uso
            pp.IdPri2 = null; //Sin uso
            pp.Capai2 = null; //Sin uso
            pp.IdPri3 = null; //Sin uso
            pp.Capai3 = null; //Sin uso
            pp.IdPre1 = null; //Sin uso
            pp.Capae1 = null; //Sin uso
            pp.IdPre2 = null; //Sin uso
            pp.Capae2 = null; //Sin uso
            pp.IdPre3 = null; //Sin uso
            pp.Capae3 = null; //Sin uso
            pp.AnchoHjlta = (int?)loteMaterial.Ancho; //De MATMAS?
            pp.LargoHjlta = (int?)loteMaterial.Largo; //De MATMAS?
            pp.EspesorHjlta = (int?)loteMaterial.Espesor; //De MATMAS?
            pp.TipoHjlta = null; //De MATMAS?
            pp.ClaseSustrato = null; //SIN USO
            pp.Corte2 = null; //SIN USO
            pp.Diferencial = null; //SIN USO
            pp.Obs1 = or.Observaciones;
            pp.Obs2 = null;
            pp.Obs3 = null;
            //pp.ObsCalidad = or.ObsCalidad; - HAY QUE CREARLO EN LA BD
            pp.Obsrayas = null; //PENDIENTE - Por codigo?
            pp.Obsflejado = null; //PENDIENTE - Por codigo?
            pp.Obsarticulo = or.ObsArticulo; //PENDIENTE - Por codigo?
            pp.Wo = null; //????????????????????????????????????????????????//PENDIENTE
            pp.EstadoTintas = pp.EstadoTintas; //SI ESTÁ PROGRAMADO/SIN APLICAR
            pp.LineaTintas = pp.LineaTintas; //MAQUINA ASIGNADA
            pp.PlanchasPedidas = false; //FIJO
            pp.FechaPlanchas = null; //FIJO
            pp.Terminado = false; //FIJO... Será según el status
            pp.ObservacionesPedido = null; //FIJO
            pp.TipoPedido = or.MotivoNavigation.TratamientoNavigation.TipoTratamiento;//CREO QUE ESTO...  Lito/Barni 
            pp.Anulado = false; //FIJO
            pp.PlanchasPasadas = false; //FIJO
            pp.MantenerAbierto = false; //FIJO
            pp.ObsACliente = null; //FIJO
            pp.TipoBarnizado = or.MotivoNavigation.TratamientoNavigation.TipoTratamientoNavigation.GetDescripcion("TipoTratamiento1", "TextoTipoTratamiento");//CREO... //L-N-A... ???
            pp.TipoEnvase = or.MotivoNavigation.TipoProducto; //PLANO.TIPOELEMENTO falta la tabla de descipcion para poder invocar GetDescripcion()
            pp.Embuticion = or.MotivoNavigation.Embuticion ? "S" : "N";
            pp.Sincarpeta = false; //NO HAY CAMPO QUE ME DIGA ESTO???????;
            pp.ObsProgramacion = pp.ObsProgramacion;
            pp.VistoObsProgramacion = false; //FIJO
            pp.Triptico = false; //FIJO
            pp.FechaEnvioTriptico = null; //FIJO
            pp.FechaOktriptico = null; //FIJO
            pp.ObsTriptico = null; //FIJO
            pp.NoIncluirEnListado = false; //FIJO
            pp.IdpedidoInformix = null; //FIJO
            pp.UltimaModificacion = null; //??
            pp.Estado = null; //FACTURADO, PENDIENTE HOJALATA...?¿?¿? - Va a ser ESTATUS_USUARIO
            pp.Revisado = false; //FIJO
            pp.FechaUltRevision = pp.FechaUltRevision; //????
            pp.AlturaCuerpos = (double)or.MotivoNavigation.TroquelNavigation.AlturaElemento;
            pp.HojalataYaSacada = pp.HojalataYaSacada; //FIJO
            pp.Urgente = or.Urgente; //PENDIENTE
            pp.BarnizadoWoW = false; //FIJO
            pp.PrecioHoja = null; //NO SE USA
            //pp.FechaFin = or.FechaFin; - FALTA
            pp.FechaEntregaSolicitada = or.FechaEntrega;
            pp.FechaPedido = or.FechaPedido;
            //HAY QUE CREAR LOS CAMPOS DE FECHAS EN PedidoProcesado para obtenerlos de la Orden
            //y así ahorrarnos una consulta tonta a la hora de montar el DTO

            result.Add(pp);

        }
        return result;

    }

    private async Task TratarOperaciones(List<Operaciones> operaciones, bool esNueva, CancellationToken ct)
    {
        foreach (var op in operaciones)
        {
            //Del lote habria que filtrar tambien por LOTE y CENTRO, pero no sé dónde están en la OPERACION
            var datosLote = await _contextSAP.Batmas.Where(o => o.Material == op.CodigoAplicacion).ToListAsync(ct);
            var codApli = await _contextSAP.CodigosAplicacion.Where(o => o.IdCodigoAplicacion == op.CodigoAplicacion).FirstOrDefaultAsync(ct);
            var material = await _contextSAP.Matmas.Where(o => o.Material == codApli.IdBarniz).FirstOrDefaultAsync(ct);

            var pedProceso = await RegistrarPedProceso(esNueva, op, material, codApli, datosLote, ct);
            await RegistrarTablaCodigosPedido(esNueva, op, material, codApli, datosLote, pedProceso.PedprocesoCopiaId, ct);
        }
    }

    private async Task<DboPedproceso> RegistrarPedProceso(bool esNueva, Operaciones op, Matmas material, CodigosAplicacion codApli,
        List<Batmas> datosLote, CancellationToken ct)
    {
        var dpp = esNueva
            ? new DboPedproceso()
            : await _programadorLitalsaContext.DboPedproceso.FirstOrDefaultAsync(
                o => o.Pedido == int.Parse(op.NumOrden) && o.Posicion == op.NumOperacion, ct);

        dpp.Pedido = int.Parse(op.NumOrden);
        dpp.Proceso = op.CodigoAplicacion;
        dpp.Cantidad = (double?)op.CantidadOperacion;
        dpp.Lugar = op.Cara;
        dpp.Barniz = codApli.IdBarniz;
        dpp.Txtbarniz = material.TextoMaterial; // ?????????????
        dpp.Impdia = null;
        dpp.Posicion = op.NumOperacion; //PedprocesoCopiaId = 0, ES ID autoincremental >.<
        dpp.CalculatedId = op.CodigoAplicacion switch
        {
            "998" when op.Cara == "ip" => string.Concat(op.CodigoAplicacion, "i4"),
            "999" when op.Cara == "ep" => string.Concat(op.CodigoAplicacion, "e4"),
            "996" when op.Cara == "ip" => string.Concat(op.CodigoAplicacion, "i6"),
            "996" when op.Cara == "ep" => string.Concat(op.CodigoAplicacion, "e6"),
            "997" when op.Cara == "ep" => string.Concat(op.CodigoAplicacion, "e5"),
            "997" when op.Cara == "ip" => string.Concat(op.CodigoAplicacion, "i5"),
            _ => string.Concat(op.CodigoAplicacion, op.Cara)
        };

        var item = await _programadorLitalsaContext.DboPedproceso.AddAsync(dpp, ct);
        return item.Entity;
    }
    private async Task RegistrarTablaCodigosPedido(bool esNueva, Operaciones op, Matmas material, CodigosAplicacion codApli,
        List<Batmas> datosLote, int pedProcesoCopiaId, CancellationToken ct)
    {
        var tcp = esNueva
            ? new TablaCodigosPedido()
            : await _programadorLitalsaContext.TablaCodigosPedido.FirstOrDefaultAsync(
                o => o.Idpedido == int.Parse(op.NumOrden) && o.Fase == int.Parse(op.NumOperacion), ct);

        tcp.Idpedido = int.Parse(op.NumOrden);
        tcp.Idcodigoaplicacion = int.Parse(op.CodigoAplicacion);
        tcp.Posicion = op.Cara; //EP,E1,E2,I1,etc
        tcp.Hojasaplicadas = null; //Nos sirve la cantidad del pedido sin mas?
        tcp.Idproductoprioritario = tcp.Idproductoprioritario;
        tcp.Gramajeprioritario = tcp.Gramajeprioritario;
        tcp.ObservacionesAplicacion = null; // ??
        tcp.Idpedproceso = pedProcesoCopiaId; //PEDPROCESO igual puede morir con la migración
        tcp.CreaccionManual = tcp.CreaccionManual;
        tcp.Fase = int.Parse(op.NumOperacion);
        tcp.Idbarniz = int.Parse(codApli.IdBarniz); //A sacar desde codapli y matmas
        tcp.ImpresionDia = false;
        tcp.LineaTintas = tcp.LineaTintas;
        tcp.EstadoTintas = tcp.EstadoTintas;
        tcp.FechaAsignacionImpresora = tcp.FechaAsignacionImpresora;
        tcp.Sincarpeta = false;
        tcp.Idstrpedproceso = op.CodigoAplicacion switch
        {
            "998" when op.Cara == "ip" => string.Concat(op.CodigoAplicacion, "i4"),
            "999" when op.Cara == "ep" => string.Concat(op.CodigoAplicacion, "e4"),
            "996" when op.Cara == "ip" => string.Concat(op.CodigoAplicacion, "i6"),
            "996" when op.Cara == "ep" => string.Concat(op.CodigoAplicacion, "e6"),
            "997" when op.Cara == "ep" => string.Concat(op.CodigoAplicacion, "e5"),
            "997" when op.Cara == "ip" => string.Concat(op.CodigoAplicacion, "i5"),
            _ => string.Concat(op.CodigoAplicacion, op.Cara)
        };
        await _programadorLitalsaContext.TablaCodigosPedido.AddAsync(tcp, ct);
    }


    private async Task<List<Orden>> ObtenerOrdenes(Expression<Func<Orden, bool>> condition, CancellationToken ct)
    {
        return await _contextSAP.Orden
            .Where(condition).ToListAsync(ct);
    }

    private async Task<List<Operaciones>> ObtenerOperaciones(Expression<Func<Operaciones, bool>> condition, CancellationToken ct)
    {
        return await _contextSAP.Operaciones
            .Where(condition).ToListAsync(ct);
    }

    public async Task<List<Matmas>> GetMatmasAll(CancellationToken ct)
    {
        return await _contextSAP.Matmas.ToListAsync(ct);
    }

    private void MarcarComoTransformadas<T>(IEnumerable<T> items, string campo) where T : class
    {
        foreach (var item in items)
        {
            var type = item.GetType();
            var transformadoProperty = type.GetProperty("Transformado");
            var propertyToSet = type.GetProperty(campo);

            transformadoProperty?.SetValue(item, true);

            propertyToSet?.SetValue(item, false);
        }
    }



    public async Task<Matmas> GetMatmasById(int id, CancellationToken ct)
    {
        return await _contextSAP.Matmas
            .Where(o => o.Id == id)
            .FirstOrDefaultAsync(ct);
    }

    public async Task<Matmas> GetMatmasByKey(string material, CancellationToken ct)
    {
        return await _contextSAP.Matmas
            .Where(o => o.Material == material)
            .FirstOrDefaultAsync(ct);
    }

    public async Task<List<Batmas>> GetBatmasAll(CancellationToken ct)
    {
        return await _contextSAP.Batmas.ToListAsync(ct);
    }

    public async Task<Batmas> GetBatmasById(int id, CancellationToken ct)
    {
        return await _contextSAP.Batmas
            .Where(o => o.Id == id)
            .FirstOrDefaultAsync(ct);
    }

    public async Task<Batmas> GetBatmasByKey(string material, string lote, string centro, CancellationToken ct)
    {
        return await _contextSAP.Batmas
            .Where(o => o.Material == material && o.Lote == lote && o.Centro == centro)
            .FirstOrDefaultAsync(ct);
    }

    public async Task<List<Debmas>> GetDebmasAll(CancellationToken ct)
    {
        return await _contextSAP.Debmas.ToListAsync(ct);
    }

    public async Task<Debmas> GetDebmasById(int id, CancellationToken ct)
    {
        return await _contextSAP.Debmas
            .Where(o => o.Id == id)
            .FirstOrDefaultAsync(ct);
    }

    public async Task<Debmas> GetDebmasByKey(string cliente, CancellationToken ct)
    {
        return await _contextSAP.Debmas
            .Where(o => o.Cliente == cliente)
            .FirstOrDefaultAsync(ct);
    }



    public async Task<List<CodigosAplicacion>> GetCodigosAplicacionAll(CancellationToken ct)
    {
        return await _contextSAP.CodigosAplicacion.ToListAsync(ct);
    }

    public async Task<CodigosAplicacion> GetCodigoAplicacionByKey(string codApli, CancellationToken ct)
    {
        return await _contextSAP.CodigosAplicacion
            .Where(o => o.IdCodigoAplicacion == codApli)
            .FirstOrDefaultAsync(ct);
    }


    public async Task<List<Colores>> GetColoresAll(CancellationToken ct)
    {
        return await _contextSAP.Colores.ToListAsync(ct);
    }

    public async Task<Colores> GetColorByKey(string idColor, CancellationToken ct)
    {
        return await _contextSAP.Colores
            .Where(o => o.IdCodigoColor == idColor)
            .FirstOrDefaultAsync(ct);
    }


    public async Task<List<Motivos>> GetMotivosAll(CancellationToken ct)
    {
        return await _contextSAP.Motivos.ToListAsync(ct);
    }

    public async Task<Motivos> GetMotivoByKey(string idMotivo, CancellationToken ct)
    {
        return await _contextSAP.Motivos
            .Where(o => o.IdMotivo == idMotivo)
            .FirstOrDefaultAsync(ct);
    }


    public async Task<List<Planos>> GetPlanosAll(CancellationToken ct)
    {
        return await _contextSAP.Planos.ToListAsync(ct);
    }

    public async Task<Planos> GetPlanoByKey(string idPlano, CancellationToken ct)
    {
        return await _contextSAP.Planos
            .Where(o => o.IdImposicion == idPlano)
            .FirstOrDefaultAsync(ct);
    }


    public async Task<List<TratamientosCabecera>> GetTratamientosCabeceraAll(CancellationToken ct)
    {
        return await _contextSAP.TratamientosCabecera.ToListAsync(ct);
    }

    public async Task<TratamientosCabecera> GetTratamientoCabeceraByKey(string idTratamiento, CancellationToken ct)
    {
        return await _contextSAP.TratamientosCabecera
            .Where(o => o.IdTratamiento == idTratamiento)
            .FirstOrDefaultAsync(ct);
    }

    public async Task<List<TratamientosDetalle>> GetTratamientosDetalleAll(CancellationToken ct)
    {
        return await _contextSAP.TratamientosDetalle.ToListAsync(ct);
    }

    public async Task<TratamientosDetalle> GetTratamientoDetalleByKey(string idTratamiento, string numOperacion, CancellationToken ct)
    {
        return await _contextSAP.TratamientosDetalle
            .Where(o => o.IdTratamiento == idTratamiento && o.Fase == numOperacion)
            .FirstOrDefaultAsync(ct);
    }

    public async Task<List<MotivosColores>> GetMotivosColoresAll(CancellationToken ct)
    {
        return await _contextSAP.MotivosColores.ToListAsync(ct);
    }

    public async Task<MotivosColores> GetMotivoColorByKey(string idMotivo, decimal contador, CancellationToken ct)
    {
        return await _contextSAP.MotivosColores
            .Where(o => o.IdMotivo == idMotivo && o.Contador == contador)
            .FirstOrDefaultAsync(ct);
    }

}