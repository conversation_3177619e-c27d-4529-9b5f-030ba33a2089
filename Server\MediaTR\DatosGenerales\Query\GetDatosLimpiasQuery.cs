﻿using System.Globalization;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Nelibur.ObjectMapper;
using ProgramadorGeneralBLZ.Server.Data.DatoLita01;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.DatosGenerales.Query;

public class GetDatosLimpiasQuery : IRequest<ListResult<TblLimpiezasDTO>>
{
    public GetDatosLimpiasQuery(int codBarniz)
    {
        CodBarniz = codBarniz;
    }
    public int CodBarniz { get; set; }
}

internal class GetDatosLimpiasQueryHandler : IRequestHandler<GetDatosLimpiasQuery, ListResult<TblLimpiezasDTO>>
{
    private readonly DatoLita01Context _datoLita01Context;
    private readonly ProgramadorLitalsaContext _programadorLitalsaContext;
    public GetDatosLimpiasQueryHandler(DatoLita01Context datoLita01Context, ProgramadorLitalsaContext programadorLitalsaContext)
    {
        _datoLita01Context = datoLita01Context;
        _programadorLitalsaContext = programadorLitalsaContext;
    }

    public async Task<ListResult<TblLimpiezasDTO>> Handle(GetDatosLimpiasQuery request, CancellationToken cancellationToken)
    {
        var result = new ListResult<TblLimpiezasDTO>()
        {
            Data = new List<TblLimpiezasDTO>(),
            Errors = new List<string>()
        };
        try
        {
            var datosLimpias =
                await _programadorLitalsaContext.TblLimpiezas.Where(o => o.DeIdProducto == request.CodBarniz || o.AidProducto == request.CodBarniz)
                    .ToListAsync(cancellationToken);
            if (!datosLimpias.Any())
            {
                result.Errors.Add($"No hay datos de limpias para el producto {request.CodBarniz}");
                return result;
            }

            result.Data = TinyMapper.Map<List<TblLimpiezasDTO>>(datosLimpias);
            return result;
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: GetDatosLimpiasQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            throw new Exception(errorText, e);
        }
    }
}