﻿/*::deep .gridlayout-item {*/
    /*font-size: 1.2em;*/
    /*font-weight: 500;*/
    /*text-align: left;
    height: 100%;
    padding: 0.4rem;*/
    /*padding: 0.5rem;*/
    /*text-align: center;*/
    /*position: relative;
    z-index: 0;*/
    /*display: flex;*/
    /*align-items: center;
    justify-content: center;
}

    ::deep .gridlayout-item:before {
        content: " ";
        position: absolute;
        z-index: -1;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        opacity: 0.4;
    }*/
::deep .dxbl-checkbox.dxbl-checkbox-switch.dxbl-readonly > .dxbl-checkbox-check-element {
    -ms-opacity: 100;
    opacity: 100;
}
::deep .dxbl-fl .dxbl-fl-cpt {
    color: white;
}
::deep .dxbl-checkbox.dxbl-checkbox-switch.dxbl-checkbox-checked .dxbl-checkbox-check-element {
    background-color: #1381f3;
}
::deep .dxbl-row {
    --dxbl-row-item-spacing-y: 0.25rem !important;
}

::deep .dxbl-col {
    /*padding-left: 5px !important;*/
}

::deep .alTecho {
    margin-top: -65px;
}

::deep .dxbl-grid {
    padding: 0;
}

::deep .checkBoxVerticalAlign {
    align-self: center !important;
}

::deep .dxbl-grid .dxbl-btn {
    padding: 0 1px !important;
}

::deep .dxbl-checkbox.dxbl-checkbox-switch .dxbl-checkbox-check-element {
    margin: 0;
}

::deep .width40 {
    width: 40px !important;
}
::deep .btnSizeL {
    width: 100px !important;
    height: 45px !important;
}

::deep .btnDoble {
    --dxbl-btn-padding-x: 0.65rem !important;
}

    ::deep .btnDoble.btnPrimeroDeDos {
        border-right: 1px whitesmoke solid !important;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }

    ::deep .btnDoble.btnSegundoDeDos {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }

::deep .azulGradiente1 {
    background-color: red !important;
    min-width: 80px !important;
}
::deep .azulGradiente1 {
    background: radial-gradient(circle at 12.3% 19.3%, rgb(85, 88, 218) 0%, rgb(95, 209, 249) 100.2%);
}

::deep .inputDobleFecha {
    /*margin-top: 0;*/
}
::deep .smallFont {
    --dxbl-grid-font-size: 0.65rem !important;
}
::deep .inputDobleFecha.primeraFecha {
    margin-right: 0 !important;
    padding-right: 0 !important;
}

    ::deep .inputDobleFecha.segundaFecha {
        margin-left: 0 !important;
        padding-left: 0 !important;
        margin-top: 0 !important; /*Necesario al no tener Caption*/
    }

@media (max-width: 575.98px) {
    ::deep .gridlayout-item {
        font-size: 0.9em;
    }
}

::deep .colorNaranja {
    color: orange !important;
}