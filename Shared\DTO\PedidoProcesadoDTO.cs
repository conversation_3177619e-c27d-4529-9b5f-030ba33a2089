﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace ProgramadorGeneralBLZ.Shared.DTO
{
    public class PedidoProcesadoDTO
    {

        #region CamposAdicionalesDelDTO

        public string? NombreCliente { get; set; }
        public int? PesoTotalHojas { get; set; }
        public double? PesoHoja { get; set; }
        public double? Mermas { get; set; }
        public string? TxtEstado { get; set; }
        public DateTime? FechaFtp { get; set; }
        public DateTime? FechaHojalata { get; set; }
        public DateTime? FechaLanzamiento { get; set; }
        public DateTime? FechaMaxima { get; set; }
        public DateTime? FechaContrato { get; set; }
        public DateTime? FechaAcordada { get; set; }
        public bool TieneReproceso { get; set; }
        public Enums.TipoReproceso? TipoReproceso { get; set; }
        public int? IdPedidoReproceso { get; set; }
        public string? CaracteristicasHojalata { get; set; }
        public string? Esc { get; set; }
        public string? C01pedText { get; set; }
        public string? C02pedText { get; set; }
        public string? C03pedText { get; set; }
        public string? C04pedText { get; set; }
        public string? C05pedText { get; set; }
        public string? C06pedText { get; set; }
        public string? C07pedText { get; set; }
        public string? C08pedText { get; set; }
        public string? C09pedText { get; set; }
        public string? Co10pedText { get; set; }
        public string? Co11pedText { get; set; }
        public string? Cd1pedText { get; set; }
        public string? Cd2pedText { get; set; }
        public string? Cd3pedText { get; set; }
        public string? Cd4pedText { get; set; }
        public string? ObsAlmacen { get; set; }
        public string? ObsCalidad { get; set; }
        public List<FasePedidoDTO>? FasesPedido { get; set; }
        
        public bool ExisteFicheroFotomecanica { get; set; }
        [NotMapped]
        public List<string>? Carpetas { get; set; }

        #endregion

        #region CamposOriginalesModel

        public int Id { get; set; }
        public int? IdPedido { get; set; }
        public string? Supedido { get; set; }
        public int? IdCliente { get; set; }
        public DateTime? FechaPedido { get; set; }
        public DateTime? RequeridoEnFecha { get; set; }
        public DateTime? ActualizacionFechaRequerida { get; set; }
        public int? HojasPedido { get; set; }
        public int? HojasTerminadas { get; set; }
        public int? HojasLlevadas { get; set; }
        public string? Motivos { get; set; }
        public string? TipoElemento { get; set; }
        public float? Formato { get; set; }
        public string? Plano { get; set; }
        public int? Pi1ped { get; set; }
        public int? Hojaspi1ped { get; set; }
        public int? Pi2ped { get; set; }
        public int? Hojaspi2ped { get; set; }
        public int? Pi3ped { get; set; }
        public int? Hojaspi3ped { get; set; }
        public int? Pe1ped { get; set; }
        public int? Hojaspe1ped { get; set; }
        public int? Pe2ped { get; set; }
        public int? Hojaspe2ped { get; set; }
        public int? Pe3ped { get; set; }
        public int? Hojaspe3ped { get; set; }
        public int? C01ped { get; set; }
        public int? Hojasco1ped { get; set; }
        public int? C02ped { get; set; }
        public int? Hojasco2ped { get; set; }
        public int? C03ped { get; set; }
        public int? Hojasco3ped { get; set; }
        public int? C04ped { get; set; }
        public int? Hojasco4ped { get; set; }
        public int? C05ped { get; set; }
        public int? Hojasco5ped { get; set; }
        public int? C06ped { get; set; }
        public int? Hojasco6ped { get; set; }
        public int? C07ped { get; set; }
        public int? Hojasco7ped { get; set; }
        public int? C08ped { get; set; }
        public int? Hojasco8ped { get; set; }
        public int? C09ped { get; set; }
        public int? Hojasco9ped { get; set; }
        public int? Co10ped { get; set; }
        public int? Hojasco10ped { get; set; }
        public int? Co11ped { get; set; }
        public int? Hojasco11ped { get; set; }
        public int? Cd1ped { get; set; }
        public int? Hojascd1ped { get; set; }
        public int? Cd2ped { get; set; }
        public int? Hojascd2ped { get; set; }
        public int? Cd3ped { get; set; }
        public int? Hojascd3ped { get; set; }
        public int? Cd4ped { get; set; }
        public int? Hojascd4ped { get; set; }
        public int? IdPri1 { get; set; }
        public float? Capai1 { get; set; }
        public int? IdPri2 { get; set; }
        public float? Capai2 { get; set; }
        public int? IdPri3 { get; set; }
        public float? Capai3 { get; set; }
        public int? IdPre1 { get; set; }
        public float? Capae1 { get; set; }
        public int? IdPre2 { get; set; }
        public float? Capae2 { get; set; }
        public int? IdPre3 { get; set; }
        public float? Capae3 { get; set; }
        public int? AnchoHjlta { get; set; }
        public int? LargoHjlta { get; set; }
        public int? EspesorHjlta { get; set; }
        public int? TipoHjlta { get; set; }
        public string? ClaseSustrato { get; set; }
        public string? Corte2 { get; set; }
        public string? Diferencial { get; set; }
        public string? Obs1 { get; set; }
        public string? Obs2 { get; set; }
        public string? Obs3 { get; set; }
        public string? Obsrayas { get; set; }
        public string? Obsflejado { get; set; }
        public string? Obsarticulo { get; set; }
        public string? Wo { get; set; }
        public string? EstadoTintas { get; set; }
        public int? LineaTintas { get; set; }
        public bool PlanchasPedidas { get; set; }
        public DateTime? FechaPlanchas { get; set; }
        public bool Terminado { get; set; }
        public string? ObservacionesPedido { get; set; }
        public string? TipoPedido { get; set; }
        public bool Anulado { get; set; }
        public bool PlanchasPasadas { get; set; }
        public bool MantenerAbierto { get; set; }
        public string? ObsACliente { get; set; }
        public string? TipoBarnizado { get; set; }
        public string? TipoEnvase { get; set; }
        public string? Embuticion { get; set; }
        public bool Sincarpeta { get; set; }
        public string? ObsProgramacion { get; set; }
        public bool VistoObsProgramacion { get; set; }
        public bool Triptico { get; set; }
        public DateTime? FechaEnvioTriptico { get; set; }
        public DateTime? FechaOktriptico { get; set; }
        public string? ObsTriptico { get; set; }
        public bool NoIncluirEnListado { get; set; }
        public int? IdpedidoInformix { get; set; }
        public DateTime? UltimaModificacion { get; set; }
        public string? Estado { get; set; }
        public bool Revisado { get; set; }
        public DateTime? FechaUltRevision { get; set; }
        public double? AlturaCuerpos { get; set; }
        public bool HojalataYaSacada { get; set; }
        public bool Urgente { get; set; }
        public bool BarnizadoWoW { get; set; }
        public decimal? PrecioHoja { get; set; }
        public DateTime? FechaFin { get; set; }
        public DateTime? FechaEntregaSolicitada { get; set; }

        #endregion
    }
}