﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace ProgramadorGeneralBLZ.Server.Models.InterfaseBD;

public partial class Matmas
{
    public int Id { get; set; }

    public string Material { get; set; }

    public string TipoMaterial { get; set; }

    public string Ramo { get; set; }

    public string GrupoArticulos { get; set; }

    public string TextoMaterial { get; set; }

    public string CodigoAntiguo { get; set; }

    public bool Borrado { get; set; }

    public DateTime? FechaRegistro { get; set; }

    public DateTime? FechaModificacion { get; set; }

    public DateTime? FechaBorrado { get; set; }

    public virtual ICollection<Batmas> Batmas { get; set; } = new List<Batmas>();

    public virtual ICollection<CodigosAplicacion> CodigosAplicacion { get; set; } = new List<CodigosAplicacion>();

    public virtual GrupoArticulos GrupoArticulosNavigation { get; set; }

    public virtual ICollection<Orden> Orden { get; set; } = new List<Orden>();

    public virtual Ramo RamoNavigation { get; set; }

    public virtual ICollection<Reservas> Reservas { get; set; } = new List<Reservas>();

    public virtual TipoMaterial TipoMaterialNavigation { get; set; }
}