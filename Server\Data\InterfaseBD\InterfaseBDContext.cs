﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Models.InterfaseBD;

namespace ProgramadorGeneralBLZ.Server.Data.InterfaseBD;

public partial class InterfaseBDContext : DbContext
{
    public InterfaseBDContext()
    {
    }

    public InterfaseBDContext(DbContextOptions<InterfaseBDContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Batmas> Batmas { get; set; }

    public virtual DbSet<CaraAplicar> CaraAplicar { get; set; }

    public virtual DbSet<ClaseMovimiento> ClaseMovimiento { get; set; }

    public virtual DbSet<CodigosAplicacion> CodigosAplicacion { get; set; }

    public virtual DbSet<Colores> Colores { get; set; }

    public virtual DbSet<ConsumoAlta> ConsumoAlta { get; set; }

    public virtual DbSet<Debmas> Debmas { get; set; }

    public virtual DbSet<Familia> Familia { get; set; }

    public virtual DbSet<GrupoArticulos> GrupoArticulos { get; set; }

    public virtual DbSet<Horas> Horas { get; set; }

    public virtual DbSet<Matmas> Matmas { get; set; }

    public virtual DbSet<MensajeOperaciones> MensajeOperaciones { get; set; }

    public virtual DbSet<Motivos> Motivos { get; set; }

    public virtual DbSet<MotivosColores> MotivosColores { get; set; }

    public virtual DbSet<Operaciones> Operaciones { get; set; }

    public virtual DbSet<Orden> Orden { get; set; }

    public virtual DbSet<Planos> Planos { get; set; }

    public virtual DbSet<PosicionEscuadra> PosicionEscuadra { get; set; }

    public virtual DbSet<Ramo> Ramo { get; set; }

    public virtual DbSet<Reservas> Reservas { get; set; }

    public virtual DbSet<Responsable> Responsable { get; set; }

    public virtual DbSet<SentidoLectura> SentidoLectura { get; set; }

    public virtual DbSet<Stock> Stock { get; set; }

    public virtual DbSet<Subfamilia> Subfamilia { get; set; }

    public virtual DbSet<TipoElemento> TipoElemento { get; set; }

    public virtual DbSet<TipoMaterial> TipoMaterial { get; set; }

    public virtual DbSet<TipoPosicion> TipoPosicion { get; set; }

    public virtual DbSet<TipoTratamiento> TipoTratamiento { get; set; }

    public virtual DbSet<TratamientosCabecera> TratamientosCabecera { get; set; }

    public virtual DbSet<TratamientosDetalle> TratamientosDetalle { get; set; }

    public virtual DbSet<Troqueles> Troqueles { get; set; }

    public virtual DbSet<UnidadMedida> UnidadMedida { get; set; }

    public virtual DbSet<UsoTintaMotivo> UsoTintaMotivo { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
#warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see http://go.microsoft.com/fwlink/?LinkId=723263.
        => optionsBuilder.UseSqlServer("Data Source=LITAENT;Initial Catalog=Prueba;User ID=sa;Password=***********");

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Batmas>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__BATMAS");

            entity.ToTable("BATMAS", tb =>
                {
                    tb.HasTrigger("tr_BATMAS_Insert");
                    tb.HasTrigger("tr_BATMAS_Update");
                });

            entity.HasIndex(e => new { e.Material, e.Lote, e.Centro }, "IX_BATMAS").IsUnique();

            entity.Property(e => e.Ancho).HasColumnType("decimal(7, 2)");
            entity.Property(e => e.AnchoBobina).HasColumnType("decimal(7, 2)");
            entity.Property(e => e.Centro)
                .IsRequired()
                .HasMaxLength(4);
            entity.Property(e => e.Clase)
                .IsRequired()
                .HasMaxLength(18);
            entity.Property(e => e.Espesor).HasColumnType("decimal(5, 3)");
            entity.Property(e => e.FechaBorrado).HasColumnType("datetime");
            entity.Property(e => e.FechaModificacion).HasColumnType("datetime");
            entity.Property(e => e.FechaRegistro).HasColumnType("datetime");
            entity.Property(e => e.IdCliente)
                .IsRequired()
                .HasMaxLength(6);
            entity.Property(e => e.Largo).HasColumnType("decimal(7, 2)");
            entity.Property(e => e.Lote)
                .IsRequired()
                .HasMaxLength(10);
            entity.Property(e => e.Material)
                .IsRequired()
                .HasMaxLength(40);
            entity.Property(e => e.NumBobina)
                .IsRequired()
                .HasMaxLength(30);
            entity.Property(e => e.Siderurgia)
                .IsRequired()
                .HasMaxLength(20);
            entity.Property(e => e.TempSecado1).HasColumnType("decimal(3, 0)");
            entity.Property(e => e.TempSecado2).HasColumnType("decimal(3, 0)");
            entity.Property(e => e.TempSecado3).HasColumnType("decimal(3, 0)");
            entity.Property(e => e.TempSecado4).HasColumnType("decimal(3, 0)");
            entity.Property(e => e.TipoCorte)
                .IsRequired()
                .HasMaxLength(8);

            entity.HasOne(d => d.MaterialNavigation).WithMany(p => p.Batmas)
                .HasPrincipalKey(p => p.Material)
                .HasForeignKey(d => d.Material)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_BATMAS_MATMAS");
        });

        modelBuilder.Entity<CaraAplicar>(entity =>
        {
            entity.HasKey(e => e.CaraAplicar1);

            entity.ToTable("CARA_APLICAR");

            entity.HasIndex(e => e.CaraAplicar1, "UQ__CARA_APL__ECA60D3D080C538D").IsUnique();

            entity.Property(e => e.CaraAplicar1)
                .HasMaxLength(1)
                .HasColumnName("CaraAplicar");
            entity.Property(e => e.TextoCaraAplicar)
                .IsRequired()
                .HasMaxLength(40);
        });

        modelBuilder.Entity<ClaseMovimiento>(entity =>
        {
            entity.HasKey(e => e.Bwart);

            entity.ToTable("CLASE_MOVIMIENTO");

            entity.HasIndex(e => e.Bwart, "UQ__CLASE_MO__766ED6BDFEB075FE").IsUnique();

            entity.Property(e => e.Bwart).HasMaxLength(3);
            entity.Property(e => e.Btext)
                .IsRequired()
                .HasMaxLength(20);
        });

        modelBuilder.Entity<CodigosAplicacion>(entity =>
        {
            entity.HasKey(e => e.IdCodigoAplicacion);

            entity.ToTable("CODIGOS_APLICACION");

            entity.Property(e => e.IdCodigoAplicacion).HasMaxLength(9);
            entity.Property(e => e.DescripcionCodigo1)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.DescripcionCodigo2)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.DescripcionCodigo3)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.FechaCreacion).HasColumnType("date");
            entity.Property(e => e.FechaMod).HasColumnType("date");
            entity.Property(e => e.FechaObsoleto).HasColumnType("date");
            entity.Property(e => e.GramajeMax).HasColumnType("decimal(3, 1)");
            entity.Property(e => e.GramajeMin).HasColumnType("decimal(3, 1)");
            entity.Property(e => e.GramajeNominal).HasColumnType("decimal(3, 1)");
            entity.Property(e => e.HoraCreacion).HasPrecision(0);
            entity.Property(e => e.HoraMod).HasPrecision(0);
            entity.Property(e => e.IdBarniz)
                .IsRequired()
                .HasMaxLength(40);
            entity.Property(e => e.TemperaturaSecado).HasColumnType("decimal(3, 0)");
            entity.Property(e => e.UsuarioCreacion)
                .IsRequired()
                .HasMaxLength(12);
            entity.Property(e => e.UsuarioMod).HasMaxLength(12);
            entity.Property(e => e.UsuarioObsoleto).HasMaxLength(12);

            entity.HasOne(d => d.BarnizNavigation).WithMany(p => p.CodigosAplicacion)
                .HasPrincipalKey(p => p.Material)
                .HasForeignKey(d => d.IdBarniz)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CODIGOS_APLICACION_MATMAS");
        });

        modelBuilder.Entity<Colores>(entity =>
        {
            entity.HasKey(e => e.IdCodigoColor).HasName("PK_Colores");

            entity.ToTable("COLORES");

            entity.Property(e => e.IdCodigoColor).HasMaxLength(8);
            entity.Property(e => e.DescripcionColor)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.FechaCreacion).HasColumnType("date");
            entity.Property(e => e.FechaMod).HasColumnType("date");
            entity.Property(e => e.FechaObsoleto).HasColumnType("date");
            entity.Property(e => e.HoraCreacion).HasPrecision(0);
            entity.Property(e => e.HoraMod).HasPrecision(0);
            entity.Property(e => e.UsuarioCreacion)
                .IsRequired()
                .HasMaxLength(12);
            entity.Property(e => e.UsuarioMod).HasMaxLength(12);
            entity.Property(e => e.UsuarioObsoleto).HasMaxLength(12);
        });

        modelBuilder.Entity<ConsumoAlta>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__ConsumoA__3214EC07ADE22369");

            entity.ToTable("CONSUMO_ALTA");

            entity.Property(e => e.Almacen)
                .HasMaxLength(4)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.CantidadConsumirAlta).HasColumnType("decimal(13, 3)");
            entity.Property(e => e.CantidadScrap).HasColumnType("decimal(13, 3)");
            entity.Property(e => e.Centro)
                .HasMaxLength(4)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.Fecha).HasColumnType("date");
            entity.Property(e => e.LoteConsumir)
                .HasMaxLength(10)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.MaterialConsumir)
                .HasMaxLength(18)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.Movimiento)
                .HasMaxLength(3)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.OperacionNotificar)
                .HasMaxLength(4)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.Orden)
                .HasMaxLength(12)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.Pedido)
                .HasMaxLength(10)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.StockBloqueado)
                .HasMaxLength(1)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.StockPedido)
                .HasMaxLength(1)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.UnidadMedida)
                .HasMaxLength(3)
                .IsUnicode(false)
                .IsFixedLength();
        });

        modelBuilder.Entity<Debmas>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__DEBMAS");

            entity.ToTable("DEBMAS", tb =>
                {
                    tb.HasTrigger("tr_DEBMAS_Insert");
                    tb.HasTrigger("tr_DEBMAS_Update");
                });

            entity.HasIndex(e => e.Cliente, "IX_DEBMAS_CLIENTE").IsUnique();

            entity.Property(e => e.Cliente)
                .IsRequired()
                .HasMaxLength(10);
            entity.Property(e => e.CodigoAntiguo).IsRequired();
            entity.Property(e => e.FechaBorrado).HasColumnType("datetime");
            entity.Property(e => e.FechaModificacion).HasColumnType("datetime");
            entity.Property(e => e.FechaRegistro).HasColumnType("datetime");
            entity.Property(e => e.Nombre)
                .IsRequired()
                .HasMaxLength(40);
        });

        modelBuilder.Entity<Familia>(entity =>
        {
            entity.HasKey(e => e.Familia1);

            entity.ToTable("FAMILIA");

            entity.Property(e => e.Familia1)
                .HasMaxLength(1)
                .HasColumnName("Familia");
            entity.Property(e => e.TextoFamilia)
                .IsRequired()
                .HasMaxLength(40);
        });

        modelBuilder.Entity<GrupoArticulos>(entity =>
        {
            entity.HasKey(e => e.Matkl).HasName("PK_CLASE_MATERIAL");

            entity.ToTable("GRUPO_ARTICULOS");

            entity.HasIndex(e => e.Matkl, "UQ__CLASE_MA__294F54A3F3B6F4A4").IsUnique();

            entity.Property(e => e.Matkl).HasMaxLength(9);
            entity.Property(e => e.Wgbez)
                .IsRequired()
                .HasMaxLength(20);
        });

        modelBuilder.Entity<Horas>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Horas__3214EC0739229EF4");

            entity.ToTable("HORAS");

            entity.Property(e => e.ActividadMaq).HasColumnType("decimal(13, 3)");
            entity.Property(e => e.ActividadMod).HasColumnType("decimal(13, 3)");
            entity.Property(e => e.ActividadPrep).HasColumnType("decimal(13, 3)");
            entity.Property(e => e.Centro)
                .HasMaxLength(4)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.Fecha).HasColumnType("date");
            entity.Property(e => e.OperacionNotificar)
                .HasMaxLength(4)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.Orden)
                .HasMaxLength(12)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.Uomactividad1)
                .HasMaxLength(3)
                .IsUnicode(false)
                .IsFixedLength()
                .HasColumnName("UOMActividad1");
            entity.Property(e => e.Uomactividad2)
                .HasMaxLength(3)
                .IsUnicode(false)
                .IsFixedLength()
                .HasColumnName("UOMActividad2");
            entity.Property(e => e.Uomactividad3)
                .HasMaxLength(3)
                .IsUnicode(false)
                .IsFixedLength()
                .HasColumnName("UOMActividad3");
        });

        modelBuilder.Entity<Matmas>(entity =>
        {
            entity.ToTable("MATMAS", tb =>
                {
                    tb.HasTrigger("tr_MATMAS_Insert");
                    tb.HasTrigger("tr_MATMAS_Update");
                });

            entity.HasIndex(e => e.Material, "IX_MATMAS").IsUnique();

            entity.Property(e => e.CodigoAntiguo).IsRequired();
            entity.Property(e => e.FechaBorrado).HasColumnType("datetime");
            entity.Property(e => e.FechaModificacion).HasColumnType("datetime");
            entity.Property(e => e.FechaRegistro).HasColumnType("datetime");
            entity.Property(e => e.GrupoArticulos)
                .IsRequired()
                .HasMaxLength(9);
            entity.Property(e => e.Material)
                .IsRequired()
                .HasMaxLength(40);
            entity.Property(e => e.Ramo)
                .IsRequired()
                .HasMaxLength(1);
            entity.Property(e => e.TextoMaterial)
                .IsRequired()
                .HasMaxLength(40);
            entity.Property(e => e.TipoMaterial)
                .IsRequired()
                .HasMaxLength(4);

            entity.HasOne(d => d.GrupoArticulosNavigation).WithMany(p => p.Matmas)
                .HasForeignKey(d => d.GrupoArticulos)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_MATMAS_GRUPO_ARTICULOS");

            entity.HasOne(d => d.RamoNavigation).WithMany(p => p.Matmas)
                .HasForeignKey(d => d.Ramo)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_MATMAS_RAMO");

            entity.HasOne(d => d.TipoMaterialNavigation).WithMany(p => p.Matmas)
                .HasForeignKey(d => d.TipoMaterial)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_MATMAS_TIPO_MATERIAL");
        });

        modelBuilder.Entity<MensajeOperaciones>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__MensajeO__3214EC07A8EF74CA");

            entity.ToTable("MENSAJE_OPERACIONES");

            entity.Property(e => e.Cara)
                .HasMaxLength(10)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.CodigoAplicacion)
                .HasMaxLength(10)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.FinEjecucionFecha).HasColumnType("datetime");
            entity.Property(e => e.InicioEjecucionFecha).HasColumnType("datetime");
            entity.Property(e => e.MaterialComponente)
                .HasMaxLength(40)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.NumeroLote)
                .HasMaxLength(10)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.Operacion)
                .HasMaxLength(4)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.PuestoTrabajo)
                .HasMaxLength(8)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.StatusUsuario)
                .HasMaxLength(4)
                .IsUnicode(false)
                .IsFixedLength();
        });

        modelBuilder.Entity<Motivos>(entity =>
        {
            entity.HasKey(e => e.IdMotivo).HasName("PK_Motivos");

            entity.ToTable("MOTIVOS");

            entity.Property(e => e.IdMotivo).HasMaxLength(8);
            entity.Property(e => e.CodigoArchivo)
                .IsRequired()
                .HasMaxLength(13);
            entity.Property(e => e.DescripcionComercial)
                .IsRequired()
                .HasMaxLength(30);
            entity.Property(e => e.FechaCreacion).HasColumnType("date");
            entity.Property(e => e.FechaMod).HasColumnType("date");
            entity.Property(e => e.FechaObsoleto).HasColumnType("date");
            entity.Property(e => e.HoraCreacion).HasPrecision(0);
            entity.Property(e => e.HoraMod).HasPrecision(0);
            entity.Property(e => e.IdCliente)
                .IsRequired()
                .HasMaxLength(10);
            entity.Property(e => e.IdMotivoPrecedente)
                .IsRequired()
                .HasMaxLength(8);
            entity.Property(e => e.IdTratamiento)
                .IsRequired()
                .HasMaxLength(8);
            entity.Property(e => e.IdTroquel)
                .IsRequired()
                .HasMaxLength(8);
            entity.Property(e => e.Marca)
                .IsRequired()
                .HasMaxLength(30);
            entity.Property(e => e.MuestraFisica)
                .IsRequired()
                .HasMaxLength(30);
            entity.Property(e => e.RefMotivoCliente)
                .IsRequired()
                .HasMaxLength(30);
            entity.Property(e => e.ReferenciaColor)
                .IsRequired()
                .HasMaxLength(30);
            entity.Property(e => e.TipoProducto)
                .IsRequired()
                .HasMaxLength(3);
            entity.Property(e => e.UsuarioCreacion)
                .IsRequired()
                .HasMaxLength(12);
            entity.Property(e => e.UsuarioMod)
                .IsRequired()
                .HasMaxLength(12);
            entity.Property(e => e.UsuarioObsoleto)
                .IsRequired()
                .HasMaxLength(12);

            entity.HasOne(d => d.ClienteNavigation).WithMany(p => p.Motivos)
                .HasPrincipalKey(p => p.Cliente)
                .HasForeignKey(d => d.IdCliente)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_MOTIVOS_DEBMAS");

            entity.HasOne(d => d.TratamientoNavigation).WithMany(p => p.Motivos)
                .HasForeignKey(d => d.IdTratamiento)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_MOTIVOS_TRATAMIENTOS_CABECERA");

            entity.HasOne(d => d.TroquelNavigation).WithMany(p => p.Motivos)
                .HasForeignKey(d => d.IdTroquel)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_MOTIVOS_TROQUELES");
        });

        modelBuilder.Entity<MotivosColores>(entity =>
        {
            entity.HasKey(e => new { e.IdMotivo, e.Contador });

            entity.ToTable("MOTIVOS_COLORES");

            entity.Property(e => e.IdMotivo).HasMaxLength(8);
            entity.Property(e => e.Contador).HasColumnType("numeric(2, 0)");
            entity.Property(e => e.CodigoTintaConv)
                .IsRequired()
                .HasMaxLength(8);
            entity.Property(e => e.CodigoTintaUv)
                .IsRequired()
                .HasMaxLength(8);
            entity.Property(e => e.FechaCreacion).HasColumnType("date");
            entity.Property(e => e.FechaMod).HasColumnType("date");
            entity.Property(e => e.FechaObsoleto).HasColumnType("date");
            entity.Property(e => e.HoraCreacion).HasPrecision(0);
            entity.Property(e => e.HoraMod).HasPrecision(0);
            entity.Property(e => e.IdCodigoAplicacion)
                .IsRequired()
                .HasMaxLength(9);
            entity.Property(e => e.IdCodigoColor)
                .IsRequired()
                .HasMaxLength(8);
            entity.Property(e => e.Superficie).HasColumnType("decimal(7, 0)");
            entity.Property(e => e.UsoMotivoTinta)
                .IsRequired()
                .HasMaxLength(2);
            entity.Property(e => e.UsuarioCreacion)
                .IsRequired()
                .HasMaxLength(12);
            entity.Property(e => e.UsuarioMod).HasMaxLength(12);
            entity.Property(e => e.UsuarioObsoleto).HasMaxLength(12);

            entity.HasOne(d => d.CodigoAplicacionNavigation).WithMany(p => p.MotivosColores)
                .HasForeignKey(d => d.IdCodigoAplicacion)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_MOTIVOS_COLORES_CODIGOS_APLICACION");

            entity.HasOne(d => d.CodigoColorNavigation).WithMany(p => p.MotivosColores)
                .HasForeignKey(d => d.IdCodigoColor)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_MOTIVOS_COLORES_Colores");

            entity.HasOne(d => d.MotivoNavigation).WithMany(p => p.MotivosColores)
                .HasForeignKey(d => d.IdMotivo)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_MOTIVOS_COLORES_Motivos");

            entity.HasOne(d => d.UsoMotivoTintaNavigation).WithMany(p => p.MotivosColores)
                .HasForeignKey(d => d.UsoMotivoTinta)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_MOTIVOS_COLORES_USO_TINTA_MOTIVO");
        });

        modelBuilder.Entity<Operaciones>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__OPERACIONES");

            entity.ToTable("OPERACIONES", tb =>
                {
                    tb.HasTrigger("tr_OPERACIONES_Insert");
                    tb.HasTrigger("tr_OPERACIONES_Update");
                });

            entity.HasIndex(e => new { e.NumOrden, e.NumOperacion }, "UC_OPERACIONES").IsUnique();

            entity.Property(e => e.CantidadOperacion).HasColumnType("decimal(13, 3)");
            entity.Property(e => e.Cara)
                .IsRequired()
                .HasMaxLength(10);
            entity.Property(e => e.CodigoAplicacion)
                .IsRequired()
                .HasMaxLength(10);
            entity.Property(e => e.EstatusUsuario)
                .IsRequired()
                .HasMaxLength(4);
            entity.Property(e => e.FechaBorrado).HasColumnType("datetime");
            entity.Property(e => e.FechaFin).HasColumnType("date");
            entity.Property(e => e.FechaInicio).HasColumnType("date");
            entity.Property(e => e.FechaModificacion).HasColumnType("datetime");
            entity.Property(e => e.FechaRegistro).HasColumnType("datetime");
            entity.Property(e => e.Gramaje).HasColumnType("decimal(5, 2)");
            entity.Property(e => e.GramajeMinimo).HasColumnType("decimal(5, 2)");
            entity.Property(e => e.IdRecurso)
                .IsRequired()
                .HasMaxLength(8);
            entity.Property(e => e.NumOperacion)
                .IsRequired()
                .HasMaxLength(4);
            entity.Property(e => e.NumOrden)
                .IsRequired()
                .HasMaxLength(12);
            entity.Property(e => e.PuestoTrabajo)
                .IsRequired()
                .HasMaxLength(8);
            entity.Property(e => e.Temperatura).HasColumnType("decimal(5, 2)");
            entity.Property(e => e.TextoBreveOp)
                .IsRequired()
                .HasMaxLength(40);
            entity.Property(e => e.UnidadMedida)
                .IsRequired()
                .HasMaxLength(3);

            entity.HasOne(d => d.NumOrdenNavigation).WithMany(p => p.Operaciones)
                .HasPrincipalKey(p => p.NumOrden)
                .HasForeignKey(d => d.NumOrden)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_OPERACIONES_ORDEN");

            entity.HasOne(d => d.UnidadMedidaNavigation).WithMany(p => p.Operaciones)
                .HasForeignKey(d => d.UnidadMedida)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_OPERACIONES_UNIDAD_MEDIDA");
        });

        modelBuilder.Entity<Orden>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__ORDEN");

            entity.ToTable("ORDEN", tb =>
                {
                    tb.HasTrigger("tr_ORDEN_Insert");
                    tb.HasTrigger("tr_ORDEN_Update");
                });

            entity.HasIndex(e => e.NumOrden, "UC_ORDEN").IsUnique();

            entity.Property(e => e.CantidadOrden).HasColumnType("decimal(13, 3)");
            entity.Property(e => e.Centro)
                .IsRequired()
                .HasMaxLength(4);
            entity.Property(e => e.ClaseOrden)
                .IsRequired()
                .HasMaxLength(4);
            entity.Property(e => e.FechaAcordada).HasColumnType("date");
            entity.Property(e => e.FechaBorrado).HasColumnType("datetime");
            entity.Property(e => e.FechaContrato).HasColumnType("date");
            entity.Property(e => e.FechaEntrega).HasColumnType("date");
            entity.Property(e => e.FechaFtp)
                .HasColumnType("date")
                .HasColumnName("FechaFTP");
            entity.Property(e => e.FechaHojalata).HasColumnType("date");
            entity.Property(e => e.FechaLanzamiento).HasColumnType("date");
            entity.Property(e => e.FechaModificacion).HasColumnType("datetime");
            entity.Property(e => e.FechaPedido).HasColumnType("date");
            entity.Property(e => e.FechaPedidoCliente).HasColumnType("date");
            entity.Property(e => e.FechaRegistro).HasColumnType("datetime");
            entity.Property(e => e.IdCliente)
                .IsRequired()
                .HasMaxLength(10);
            entity.Property(e => e.IdMotivo).HasMaxLength(8);
            entity.Property(e => e.IdPlano).HasMaxLength(30);
            entity.Property(e => e.Material)
                .IsRequired()
                .HasMaxLength(40);
            entity.Property(e => e.NumOrden)
                .IsRequired()
                .HasMaxLength(12);
            entity.Property(e => e.NumPedidoCliente)
                .IsRequired()
                .HasMaxLength(10);
            entity.Property(e => e.NumPosicionPedidoCliente)
                .IsRequired()
                .HasMaxLength(6);
            entity.Property(e => e.ResponsableProduccion)
                .IsRequired()
                .HasMaxLength(3);
            entity.Property(e => e.Status)
                .IsRequired()
                .HasMaxLength(5);
            entity.Property(e => e.UnidadMedida)
                .IsRequired()
                .HasMaxLength(3);

            entity.HasOne(d => d.MotivoNavigation).WithMany(p => p.Orden)
                .HasForeignKey(d => d.IdMotivo)
                .HasConstraintName("FK_ORDEN_MOTIVOS");

            entity.HasOne(d => d.MaterialNavigation).WithMany(p => p.Orden)
                .HasPrincipalKey(p => p.Material)
                .HasForeignKey(d => d.Material)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ORDEN_MATMAS");

            entity.HasOne(d => d.ResponsableProduccionNavigation).WithMany(p => p.Orden)
                .HasForeignKey(d => d.ResponsableProduccion)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ORDEN_RESPONSABLE");

            entity.HasOne(d => d.UnidadMedidaNavigation).WithMany(p => p.Orden)
                .HasForeignKey(d => d.UnidadMedida)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ORDEN_UNIDAD_MEDIDA");
        });

        modelBuilder.Entity<Planos>(entity =>
        {
            entity.HasKey(e => e.IdImposicion).HasName("PK_PLANOS_1");

            entity.ToTable("PLANOS");

            entity.Property(e => e.IdImposicion).HasMaxLength(8);
            entity.Property(e => e.AnchoHoja).HasColumnType("decimal(6, 2)");
            entity.Property(e => e.ArranqueEscuadra).HasColumnType("decimal(5, 2)");
            entity.Property(e => e.ArranquePinza).HasColumnType("decimal(5, 2)");
            entity.Property(e => e.CodigoPlanoAsociado)
                .IsRequired()
                .HasMaxLength(30);
            entity.Property(e => e.FechaCreacion).HasColumnType("date");
            entity.Property(e => e.FechaMod).HasColumnType("date");
            entity.Property(e => e.FechaObsoleto).HasColumnType("date");
            entity.Property(e => e.HoraCreacion).HasPrecision(0);
            entity.Property(e => e.HoraMod).HasPrecision(0);
            entity.Property(e => e.IdTroquel)
                .IsRequired()
                .HasMaxLength(8);
            entity.Property(e => e.LargoHoja).HasColumnType("decimal(6, 2)");
            entity.Property(e => e.LargoHojaExteriorScroll).HasColumnType("decimal(6, 2)");
            entity.Property(e => e.Nota)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.NumeroAlturas).HasColumnType("decimal(3, 0)");
            entity.Property(e => e.NumeroCuerpos).HasColumnType("decimal(4, 0)");
            entity.Property(e => e.NumeroDesarrollos).HasColumnType("decimal(3, 0)");
            entity.Property(e => e.PlanoCliente)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.PosicionesEscuadraExt)
                .IsRequired()
                .HasMaxLength(1);
            entity.Property(e => e.SentidoLectura)
                .IsRequired()
                .HasMaxLength(2);
            entity.Property(e => e.SoloParaCodigos)
                .IsRequired()
                .HasMaxLength(9)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.TipoElemento)
                .IsRequired()
                .HasMaxLength(3);
            entity.Property(e => e.UsuarioCreacion)
                .IsRequired()
                .HasMaxLength(12);
            entity.Property(e => e.UsuarioMod).HasMaxLength(12);
            entity.Property(e => e.UsuarioObsoleto).HasMaxLength(12);
            entity.Property(e => e.Version)
                .IsRequired()
                .HasMaxLength(2)
                .IsUnicode(false)
                .IsFixedLength();

            entity.HasOne(d => d.TroquelNavigation).WithMany(p => p.Planos)
                .HasForeignKey(d => d.IdTroquel)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PLANOS_TROQUELES");

            entity.HasOne(d => d.PosicionesEscuadraExtNavigation).WithMany(p => p.Planos)
                .HasForeignKey(d => d.PosicionesEscuadraExt)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PLANOS_POSICION_ESCUADRA");

            entity.HasOne(d => d.SentidoLecturaNavigation).WithMany(p => p.Planos)
                .HasForeignKey(d => d.SentidoLectura)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PLANOS_SENTIDO_LECTURA");

            entity.HasOne(d => d.TipoElementoNavigation).WithMany(p => p.Planos)
                .HasForeignKey(d => d.TipoElemento)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PLANOS_TIPO_ELEMENTO");
        });

        modelBuilder.Entity<PosicionEscuadra>(entity =>
        {
            entity.HasKey(e => e.PosicionEscuadraExt);

            entity.ToTable("POSICION_ESCUADRA");

            entity.Property(e => e.PosicionEscuadraExt).HasMaxLength(1);
            entity.Property(e => e.TextoPosicionEscuadraExt)
                .IsRequired()
                .HasMaxLength(40);
        });

        modelBuilder.Entity<Ramo>(entity =>
        {
            entity.HasKey(e => e.Mbrsh);

            entity.ToTable("RAMO");

            entity.Property(e => e.Mbrsh).HasMaxLength(1);
            entity.Property(e => e.Mbbez)
                .IsRequired()
                .HasMaxLength(25);
        });

        modelBuilder.Entity<Reservas>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__RESERVAS");

            entity.ToTable("RESERVAS", tb =>
                {
                    tb.HasTrigger("tr_RESERVAS_Insert");
                    tb.HasTrigger("tr_RESERVAS_Update");
                });

            entity.HasIndex(e => new { e.NumOrden, e.NumOperacion, e.NumReservaSec, e.NumPosReservaSec }, "UC_RESERVAS").IsUnique();

            entity.Property(e => e.Almacen)
                .IsRequired()
                .HasMaxLength(4);
            entity.Property(e => e.CantidadNecesaria).HasColumnType("decimal(13, 3)");
            entity.Property(e => e.Centro)
                .IsRequired()
                .HasMaxLength(4);
            entity.Property(e => e.ClaseMovimientoGestionStocks)
                .IsRequired()
                .HasMaxLength(3);
            entity.Property(e => e.ClaseNecesidad)
                .IsRequired()
                .HasMaxLength(2);
            entity.Property(e => e.FechaBorrado).HasColumnType("datetime");
            entity.Property(e => e.FechaModificacion).HasColumnType("datetime");
            entity.Property(e => e.FechaNecesidad)
                .IsRequired()
                .HasMaxLength(8);
            entity.Property(e => e.FechaRegistro).HasColumnType("datetime");
            entity.Property(e => e.Material)
                .IsRequired()
                .HasMaxLength(40);
            entity.Property(e => e.NumOperacion)
                .IsRequired()
                .HasMaxLength(4);
            entity.Property(e => e.NumOrden)
                .IsRequired()
                .HasMaxLength(12);
            entity.Property(e => e.NumPosReservaSec)
                .IsRequired()
                .HasMaxLength(4);
            entity.Property(e => e.NumReservaSec)
                .IsRequired()
                .HasMaxLength(10);
            entity.Property(e => e.NumeroLote)
                .IsRequired()
                .HasMaxLength(10);
            entity.Property(e => e.NumeroPosicionListaMateriales)
                .IsRequired()
                .HasMaxLength(4);
            entity.Property(e => e.TipoPosicionListaMateriales)
                .IsRequired()
                .HasMaxLength(1);
            entity.Property(e => e.Unidad)
                .IsRequired()
                .HasMaxLength(3);

            entity.HasOne(d => d.ClaseMovimientoGestionStocksNavigation).WithMany(p => p.Reservas)
                .HasForeignKey(d => d.ClaseMovimientoGestionStocks)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RESERVAS_CLASE_MOVIMIENTO");

            entity.HasOne(d => d.MaterialNavigation).WithMany(p => p.Reservas)
                .HasPrincipalKey(p => p.Material)
                .HasForeignKey(d => d.Material)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RESERVAS_MATMAS");

            entity.HasOne(d => d.NumOrdenNavigation).WithMany(p => p.Reservas)
                .HasPrincipalKey(p => p.NumOrden)
                .HasForeignKey(d => d.NumOrden)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RESERVAS_ORDEN");

            entity.HasOne(d => d.TipoPosicionListaMaterialesNavigation).WithMany(p => p.Reservas)
                .HasForeignKey(d => d.TipoPosicionListaMateriales)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RESERVAS_TIPO_POSICION");

            entity.HasOne(d => d.UnidadNavigation).WithMany(p => p.Reservas)
                .HasForeignKey(d => d.Unidad)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RESERVAS_UNIDAD_MEDIDA");
        });

        modelBuilder.Entity<Responsable>(entity =>
        {
            entity.HasKey(e => e.Fevor);

            entity.ToTable("RESPONSABLE");

            entity.Property(e => e.Fevor).HasMaxLength(3);
            entity.Property(e => e.Txt)
                .IsRequired()
                .HasMaxLength(30);
        });

        modelBuilder.Entity<SentidoLectura>(entity =>
        {
            entity.HasKey(e => e.SentidoLectura1);

            entity.ToTable("SENTIDO_LECTURA");

            entity.Property(e => e.SentidoLectura1)
                .HasMaxLength(2)
                .HasColumnName("SentidoLectura");
            entity.Property(e => e.TextoSentidoLectura)
                .IsRequired()
                .HasMaxLength(40);
        });

        modelBuilder.Entity<Stock>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__STOCK__3214EC07801BAB79");

            entity.ToTable("STOCK");

            entity.Property(e => e.AreaAlmacenamiento)
                .IsRequired()
                .HasMaxLength(4);
            entity.Property(e => e.Ctd).HasColumnType("decimal(13, 3)");
            entity.Property(e => e.FechaEntradaMercancias).HasColumnType("decimal(15, 0)");
            entity.Property(e => e.IdUdManipulacion)
                .IsRequired()
                .HasMaxLength(20);
            entity.Property(e => e.Lote)
                .IsRequired()
                .HasMaxLength(10);
            entity.Property(e => e.Material)
                .IsRequired()
                .HasMaxLength(40);
            entity.Property(e => e.NumeroAlmacen)
                .IsRequired()
                .HasMaxLength(4);
            entity.Property(e => e.Propietario)
                .IsRequired()
                .HasMaxLength(10);
            entity.Property(e => e.TipoAlmacen)
                .IsRequired()
                .HasMaxLength(4);
            entity.Property(e => e.TipoStockPedidoCliente)
                .IsRequired()
                .HasMaxLength(3);
            entity.Property(e => e.TipoStocks)
                .IsRequired()
                .HasMaxLength(2);
            entity.Property(e => e.Ubicacion)
                .IsRequired()
                .HasMaxLength(18);
            entity.Property(e => e.UnidadMedidaBase)
                .IsRequired()
                .HasMaxLength(3);

            entity.HasOne(d => d.UnidadMedidaBaseNavigation).WithMany(p => p.Stock)
                .HasForeignKey(d => d.UnidadMedidaBase)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_STOCK_UNIDAD_MEDIDA");
        });

        modelBuilder.Entity<Subfamilia>(entity =>
        {
            entity.HasKey(e => e.Subfamilia1);

            entity.ToTable("SUBFAMILIA");

            entity.Property(e => e.Subfamilia1)
                .HasMaxLength(2)
                .HasColumnName("Subfamilia");
            entity.Property(e => e.TextoSubfamilia)
                .IsRequired()
                .HasMaxLength(40);
        });

        modelBuilder.Entity<TipoElemento>(entity =>
        {
            entity.HasKey(e => e.TipoElemento1);

            entity.ToTable("TIPO_ELEMENTO");

            entity.Property(e => e.TipoElemento1)
                .HasMaxLength(3)
                .HasColumnName("TipoElemento");
            entity.Property(e => e.TextoTipoElemento)
                .IsRequired()
                .HasMaxLength(40);
        });

        modelBuilder.Entity<TipoMaterial>(entity =>
        {
            entity.HasKey(e => e.Mtart);

            entity.ToTable("TIPO_MATERIAL");

            entity.Property(e => e.Mtart).HasMaxLength(4);
            entity.Property(e => e.Wgbez)
                .IsRequired()
                .HasMaxLength(25);
        });

        modelBuilder.Entity<TipoPosicion>(entity =>
        {
            entity.HasKey(e => e.Postp);

            entity.ToTable("TIPO_POSICION");

            entity.Property(e => e.Postp).HasMaxLength(1);
            entity.Property(e => e.Ptext)
                .IsRequired()
                .HasMaxLength(30);
        });

        modelBuilder.Entity<TipoTratamiento>(entity =>
        {
            entity.HasKey(e => e.TipoTratamiento1);

            entity.ToTable("TIPO_TRATAMIENTO");

            entity.Property(e => e.TipoTratamiento1)
                .HasMaxLength(1)
                .HasColumnName("TipoTratamiento");
            entity.Property(e => e.TextoTipoTratamiento)
                .IsRequired()
                .HasMaxLength(40);
        });

        modelBuilder.Entity<TratamientosCabecera>(entity =>
        {
            entity.HasKey(e => e.IdTratamiento);

            entity.ToTable("TRATAMIENTOS_CABECERA");

            entity.Property(e => e.IdTratamiento).HasMaxLength(8);
            entity.Property(e => e.Descripcion)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.FechaCreacion).HasColumnType("date");
            entity.Property(e => e.FechaMod).HasColumnType("date");
            entity.Property(e => e.FechaObsoleto).HasColumnType("date");
            entity.Property(e => e.HoraCreacion).HasPrecision(0);
            entity.Property(e => e.HoraMod).HasPrecision(0);
            entity.Property(e => e.IdTratamientoPrecedente)
                .IsRequired()
                .HasMaxLength(8);
            entity.Property(e => e.TipoTratamiento)
                .IsRequired()
                .HasMaxLength(1);
            entity.Property(e => e.UsuarioCreacion)
                .IsRequired()
                .HasMaxLength(12);
            entity.Property(e => e.UsuarioMod).HasMaxLength(12);
            entity.Property(e => e.UsuarioObsoleto).HasMaxLength(12);

            entity.HasOne(d => d.TipoTratamientoNavigation).WithMany(p => p.TratamientosCabecera)
                .HasForeignKey(d => d.TipoTratamiento)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_TRATAMIENTOS_CABECERA_TIPO_TRATAMIENTO");
        });

        modelBuilder.Entity<TratamientosDetalle>(entity =>
        {
            entity.HasKey(e => new { e.IdTratamiento, e.Fase });

            entity.ToTable("TRATAMIENTOS_DETALLE");

            entity.Property(e => e.IdTratamiento).HasMaxLength(8);
            entity.Property(e => e.Fase).HasMaxLength(4);
            entity.Property(e => e.CaraAplicar)
                .IsRequired()
                .HasMaxLength(1);
            entity.Property(e => e.FechaCreacion).HasColumnType("date");
            entity.Property(e => e.FechaMod).HasColumnType("date");
            entity.Property(e => e.FechaObsoleto).HasColumnType("date");
            entity.Property(e => e.HoraCreacion).HasPrecision(0);
            entity.Property(e => e.HoraMod).HasPrecision(0);
            entity.Property(e => e.IdCodigoAplicacion)
                .IsRequired()
                .HasMaxLength(9);
            entity.Property(e => e.MaquinaPorDefecto)
                .IsRequired()
                .HasMaxLength(8);
            entity.Property(e => e.TemperaturaSecado).HasColumnType("decimal(3, 0)");
            entity.Property(e => e.UsuarioCreacion)
                .IsRequired()
                .HasMaxLength(12);
            entity.Property(e => e.UsuarioMod).HasMaxLength(12);
            entity.Property(e => e.UsuarioObsoleto).HasMaxLength(12);

            entity.HasOne(d => d.CaraAplicarNavigation).WithMany(p => p.TratamientosDetalle)
                .HasForeignKey(d => d.CaraAplicar)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_TRATAMIENTOS_DETALLE_CARA_APLICAR");

            entity.HasOne(d => d.CodigoAplicacionNavigation).WithMany(p => p.TratamientosDetalle)
                .HasForeignKey(d => d.IdCodigoAplicacion)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_TRATAMIENTOS_DETALLE_CODIGOS_APLICACION");

            entity.HasOne(d => d.TratamientoNavigation).WithMany(p => p.TratamientosDetalle)
                .HasForeignKey(d => d.IdTratamiento)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_TRATAMIENTOS_DETALLE_TRATAMIENTOS_CABECERA");
        });

        modelBuilder.Entity<Troqueles>(entity =>
        {
            entity.HasKey(e => e.IdTroquel);

            entity.ToTable("TROQUELES");

            entity.Property(e => e.IdTroquel).HasMaxLength(8);
            entity.Property(e => e.AlturaElemento).HasColumnType("decimal(5, 2)");
            entity.Property(e => e.AlturaEnvase)
                .IsRequired()
                .HasMaxLength(10);
            entity.Property(e => e.Desarrollo).HasColumnType("decimal(6, 2)");
            entity.Property(e => e.Descripcion)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.DiametroEnvase)
                .IsRequired()
                .HasMaxLength(10);
            entity.Property(e => e.DiametroReal).HasColumnType("decimal(6, 3)");
            entity.Property(e => e.EjeMayor).HasColumnType("decimal(6, 3)");
            entity.Property(e => e.EjeMenor).HasColumnType("decimal(6, 3)");
            entity.Property(e => e.Familia)
                .IsRequired()
                .HasMaxLength(1);
            entity.Property(e => e.FechaCreacion).HasColumnType("date");
            entity.Property(e => e.FechaMod).HasColumnType("date");
            entity.Property(e => e.FechaObsoleto).HasColumnType("date");
            entity.Property(e => e.FormatoEnvase)
                .IsRequired()
                .HasMaxLength(10)
                .IsUnicode(false)
                .IsFixedLength();
            entity.Property(e => e.FormatoIn2)
                .IsRequired()
                .HasMaxLength(30);
            entity.Property(e => e.HoraCreacion).HasPrecision(0);
            entity.Property(e => e.HoraMod).HasPrecision(0);
            entity.Property(e => e.IdCliente)
                .IsRequired()
                .HasMaxLength(10);
            entity.Property(e => e.Observaciones)
                .IsRequired()
                .HasMaxLength(150);
            entity.Property(e => e.RlDer).HasColumnType("decimal(4, 2)");
            entity.Property(e => e.RlIzq).HasColumnType("decimal(4, 2)");
            entity.Property(e => e.RtInferior).HasColumnType("decimal(4, 2)");
            entity.Property(e => e.RtSuperior).HasColumnType("decimal(4, 2)");
            entity.Property(e => e.SubFamilia)
                .IsRequired()
                .HasMaxLength(2);
            entity.Property(e => e.Superficie).HasColumnType("decimal(7, 0)");
            entity.Property(e => e.TipoElemento)
                .IsRequired()
                .HasMaxLength(3);
            entity.Property(e => e.UsuarioCreacion)
                .IsRequired()
                .HasMaxLength(12);
            entity.Property(e => e.UsuarioMod).HasMaxLength(12);
            entity.Property(e => e.UsuarioObsoleto).HasMaxLength(12);

            entity.HasOne(d => d.FamiliaNavigation).WithMany(p => p.Troqueles)
                .HasForeignKey(d => d.Familia)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_TROQUELES_FAMILIA");

            entity.HasOne(d => d.ClienteNavigation).WithMany(p => p.Troqueles)
                .HasPrincipalKey(p => p.Cliente)
                .HasForeignKey(d => d.IdCliente)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_TROQUELES_DEBMAS");

            entity.HasOne(d => d.SubFamiliaNavigation).WithMany(p => p.Troqueles)
                .HasForeignKey(d => d.SubFamilia)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_TROQUELES_SUBFAMILIA");

            entity.HasOne(d => d.TipoElementoNavigation).WithMany(p => p.Troqueles)
                .HasForeignKey(d => d.TipoElemento)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_TROQUELES_TIPO_ELEMENTO");
        });

        modelBuilder.Entity<UnidadMedida>(entity =>
        {
            entity.HasKey(e => e.Meins);

            entity.ToTable("UNIDAD_MEDIDA");

            entity.Property(e => e.Meins).HasMaxLength(3);
            entity.Property(e => e.Mseh6)
                .IsRequired()
                .HasMaxLength(6);
        });

        modelBuilder.Entity<UsoTintaMotivo>(entity =>
        {
            entity.HasKey(e => e.UsoMotivoTinta);

            entity.ToTable("USO_TINTA_MOTIVO");

            entity.Property(e => e.UsoMotivoTinta).HasMaxLength(2);
            entity.Property(e => e.TextoUsoMotivoTinta)
                .IsRequired()
                .HasMaxLength(40);
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}