﻿@page "/ProgramacionPedidos"
@page "/ProgramacionPedidos/{idPedido:int}"

@using ProgramadorGeneralBLZ.Shared
@using ProgramadorGeneralBLZ.Shared.DTO
@using ProgramadorGeneralBLZ.Shared.ResponseModels
@using System.Text.Json
@using System.Text.Json.Serialization
@using DevExpress.Blazor.Internal
@using Microsoft.AspNetCore.Authorization
@using System.ComponentModel.DataAnnotations
@using System.Net
@using ProgramadorGeneralBLZ.Client.Pages.Components
@using Microsoft.Extensions.Configuration

@implements IDisposable

@inject AuthenticationStateProvider AuthenticationStateProvider
@inject Blazored.LocalStorage.ILocalStorageService LocalStorage
@inject IJSRuntime Js
@inject IConfiguration Config
@inject IToastService ToastService
@inject HttpClient Http
@inject SpinnerService SpinnerService
@inject NavigationManager NavManager
@inject FiltroService FiltroService

@attribute [Authorize(Roles = $"{Roles.Admin}, {Roles.Programador}, {Roles.Consulta}")]

<PageTitle>Pedidos</PageTitle>

<AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}, {Roles.Consulta}")">
    <Authorized>
        <DxLayoutBreakpoint DeviceSize="DeviceSize.Large" @bind-IsActive="@_isSmallScreen" />
        <div class="h-100 overflow-auto px-2 py-1">
            <EditForm Model="@_datosPedido"
                      OnValidSubmit="@OnSubmitAsync" OnInvalidSubmit="@HandleInvalidSubmit"
                      Context="editFormContext">
                <DxFormLayout Data="@_datosPedido" SizeMode="SizeMode.Small" ReadOnly="true" Id="Pedidos">
                    <DxGridLayout CssClass="h-100" ColumnSpacing="1px" RowSpacing="1px">
                        <Rows>
                            <DxGridLayoutRow Areas="cabeceraPedido" Height="auto" />
                            <DxGridLayoutRow Areas="cabecera" Height="auto" />
                        </Rows>
                        <Items>
                            <DxGridLayoutItem Area="cabeceraPedido">
                                <Template>
                                    <div class="gridlayout-headerPedido gridlayout-item">
                                        @*START ROW*@
                                        <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" BeginRow="true" CssClass="pt-1">
                                            <DxFormLayoutItem Caption="Pedidos Filtrados" Context="campoDropdown" ColSpanLg="2">
                                                <DxComboBox Data="@_listadoIdPedidosFiltrados"
                                                            AllowUserInput="true" @ref="@_component"
                                                            FilteringMode="DataGridFilteringMode.Contains"
                                                            DropDownWidthMode="DropDownWidthMode.EditorWidth"
                                                            @bind-Value="@PedidoFiltrado" InputId="inputPedido"
                                                            TextChanged="@OnListadoPedidosTextChanged" />
                                            </DxFormLayoutItem>
                                            <DxFormLayoutItem Context="botonReproceso" ColSpanLg="1">
                                                <DxButton RenderStyle="ButtonRenderStyle.Danger" Click="@((e) => OnPedidoValueChanged(_datosPedido.IdPedidoReproceso.ToString()))"
                                                          Text="@_txtBtnReproceso" Visible="@_datosPedido.IdPedidoReproceso.HasValue"
                                                          CssClass="" />
                                            </DxFormLayoutItem>
                                            <DxFormLayoutItem Context="textField" Caption="SU Pedido" CssClass="" ColSpanLg="2">
                                                <DxTextBox Text="@IdPedidoCliente" TextExpression="@(() => IdPedidoCliente)"
                                                           TextChanged="@(async value => await OnPedidoClienteValueChanged(value))" />
                                            </DxFormLayoutItem>
                                            <DxFormLayoutItem Caption="Cliente" Context="campoDropdown" ColSpanLg="2">
                                                <DxComboBox Data="@DatosClientes"
                                                            FilteringMode="DataGridFilteringMode.Contains"
                                                            DropDownWidthMode="DropDownWidthMode.ContentOrEditorWidth"
                                                            TextFieldName="Combinado" ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                                            ValueFieldName="CodigoCliente"
                                                            @bind-Value="IdCliente"
                                                            TextChanged="@OnDatosClientesTextChanged">
                                                </DxComboBox>
                                            </DxFormLayoutItem>
                                            <DxFormLayoutItem Context="botonLimpiarFiltros" ColSpanLg="1">
                                                <DxButton RenderStyle="ButtonRenderStyle.Info"
                                                          Click="@LimpiarFiltros"
                                                          Text="Limpiar Filtros" CssClass="" />
                                            </DxFormLayoutItem>
                                            <DxFormLayoutItem Caption="Línea Tintas" Context="campoDropdown" ColSpanLg="2">
                                                <DxComboBox Data="@DatosMaquinas" CssClass="claseLineaTintas"
                                                            FilteringMode="DataGridFilteringMode.Contains"
                                                            TextFieldName="Nombremaquina"
                                                            ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                                            ValueFieldName="Idmaquina"
                                                            SelectedItemChanged="@((MaquinaDTO m) => OnUpdateMaquina(m))"
                                                            ListRenderMode="ListRenderMode.Entire"
                                                            DropDownWidthMode="DropDownWidthMode.EditorWidth"
                                                            @bind-Value="LineaTintasCabecera"
                                                            NullText="Máquina...">
                                                </DxComboBox>
                                            </DxFormLayoutItem>
                                            <DxFormLayoutItem Context="botonReproceso" ColSpanLg="1">
                                                <DxButton RenderStyle="ButtonRenderStyle.Secondary" Enabled="@(_pedidoModoEdicion)"
                                                          Text="Limpiar Linea Tintas" Click="@LimpiarTintas" />
                                            </DxFormLayoutItem>
                                            <AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}")" Context="especial">
                                                <DxFormLayoutItem ColSpanLg="1" Context="botonEditar">
                                                    <DxCheckBox Checked="@_pedidoModoEdicion"
                                                                CheckedExpression="@(() => _pedidoModoEdicion)"
                                                                CheckedChanged="@((bool value) => CheckedChanged(value))"
                                                                Alignment="CheckBoxContentAlignment.Right">
                                                        BLOQUEAR Y EDITAR
                                                    </DxCheckBox>
                                                </DxFormLayoutItem>
                                            </AuthorizeView>
                                        </DxFormLayoutGroup>
                                    </div>
                                </Template>
                            </DxGridLayoutItem>

                            <DxGridLayoutItem Area="cabecera">
                                <Template>
                                    <div class="gridlayout-header gridlayout-item">
                                        <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="12">
                                            <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="12">
                                                <DxFormLayoutItem Field="@nameof(_datosPedido.IdPedido)" Context="spinField"
                                                                  Caption="NºPedido" CssClass="" ColSpanLg="2">
                                                    <DxMaskedInput @bind-Value="@_datosPedido.IdPedido" ReadOnly="true"
                                                                   Mask="@NumericMask.WholeNumber" />
                                                </DxFormLayoutItem>
                                            </DxFormLayoutGroup>
                                            <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="3">
                                                @*DATOS CLIENTE Y NUMERO DE HOJAS*@
                                                <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" BeginRow="true">
                                                    <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="6">
                                                        <DxFormLayoutItem Field="@nameof(_datosPedido.Supedido)"
                                                                          Caption="SU Pedido" CssClass="" ColSpanLg="12" />
                                                        <DxFormLayoutItem Field="@nameof(_datosPedido.NombreCliente)"
                                                                          Caption="Cliente" CssClass="" ColSpanLg="12" />
                                                        <DxFormLayoutItem Caption="Estado" ColSpanLg="12" Context="texto">
                                                            <DxTextBox @ondblclick="VerDatosPedido" ReadOnly="true" @bind-Text="@_datosPedido.Estado"
                                                                       CssClass="@(_datosPedido.Estado=="PDTE HJLTA" ? "colorNaranja":"")" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Field="@nameof(_datosPedido.Mermas)" Context="spinField"
                                                                          Caption="Mermas" CssClass="" ColSpanLg="12">
                                                            <DxSpinEdit @bind-Value="@_datosPedido.Mermas" ShowSpinButtons="false"
                                                                        ReadOnly="true"
                                                                        Mask="@NumericMask.Percentage" />
                                                        </DxFormLayoutItem>

                                                    </DxFormLayoutGroup>
                                                    <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="6">
                                                        <DxFormLayoutItem Field="@nameof(_datosPedido.HojasPedido)" Context="spinField"
                                                                          Caption="H. Pedido" CssClass="" ColSpanLg="12">
                                                            <DxSpinEdit @bind-Value="@_datosPedido.HojasPedido" ShowSpinButtons="false" Mask="n0"
                                                                        ReadOnly="true" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Field="@nameof(_datosPedido.HojasTerminadas)" Context="spinField"
                                                                          Caption="H. Terminadas" CssClass="" ColSpanLg="12">
                                                            <DxSpinEdit @bind-Value="@_datosPedido.HojasTerminadas" ShowSpinButtons="false" Mask="n0"
                                                                        ReadOnly="true" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Field="@nameof(_datosPedido.HojasLlevadas)" Context="spinField"
                                                                          Caption="H. Llevadas" CssClass="" ColSpanLg="12">
                                                            <DxSpinEdit @bind-Value="@_datosPedido.HojasLlevadas" ShowSpinButtons="false" Mask="n0"
                                                                        ReadOnly="true" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Field="@nameof(_datosPedido.PesoTotalHojas)" Context="spinField"
                                                                          Caption="Peso Total" CssClass="" ColSpanLg="12">
                                                            <DxSpinEdit @bind-Value="@_datosPedido.PesoTotalHojas" ShowSpinButtons="false" Mask="n2"
                                                                        ReadOnly="true" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Field="@nameof(_datosPedido.PesoHoja)" Context="spinField"
                                                                          Caption="Peso por Hoja" CssClass="" ColSpanLg="12">
                                                            <DxSpinEdit @bind-Value="@_datosPedido.PesoHoja" ShowSpinButtons="false" Mask="n4"
                                                                        ReadOnly="true" />
                                                        </DxFormLayoutItem>
                                                    </DxFormLayoutGroup>
                                                </DxFormLayoutGroup>
                                                @*ESTADO TINTAS Y MOTIVOS*@
                                                <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" BeginRow="true">
                                                </DxFormLayoutGroup>
                                                <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" BeginRow="true">
                                                    <DxFormLayoutItem Caption="Estado Tintas" CssClass="my-3" ColSpanLg="12" Context="texto">
                                                        <DxTextBox @ondblclick="VerDatosPedido" ReadOnly="true" title="@_datosPedido.TxtEstado"
                                                                   @bind-Text="@_datosPedido.TxtEstado"
                                                                   ShowValidationIcon="true" />
                                                    </DxFormLayoutItem>
                                                    <DxFormLayoutItem Field="@nameof(_datosPedido.Motivos)"
                                                                      Caption="Motivos" CssClass="" ColSpanLg="12" />
                                                </DxFormLayoutGroup>
                                                @*MULTIPLES CHECKS*@
                                                <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" BeginRow="true">
                                                    <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="12">
                                                        <DxFormLayoutItem Caption="Revisado" CssClass="" ColSpanLg="5" Context="checkBox">
                                                            <DxCheckBox CheckType="CheckType.Switch" SizeMode="SizeMode.Medium"
                                                                        @bind-Checked="@_datosPedido.Revisado" ReadOnly="true"
                                                                        Alignment="CheckBoxContentAlignment.SpaceAround">
                                                            </DxCheckBox>
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="Ult.Revision" Context="campoFecha" ColSpanLg="7">
                                                            <DxDateEdit @bind-Date="@_datosPedido.FechaUltRevision"
                                                                        ReadOnly="true" DisplayFormat="dd/MM/yyyy">
                                                            </DxDateEdit>
                                                        </DxFormLayoutItem>
                                                    </DxFormLayoutGroup>
                                                </DxFormLayoutGroup>
                                                @*DESC HOJALATA*@
                                                <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" BeginRow="true">
                                                    <DxFormLayoutItem Field="@nameof(_datosPedido.CaracteristicasHojalata)"
                                                                      Caption="Hojalata" CssClass="" Context="campo"
                                                                      ReadOnly="true" ColSpanLg="10" />
                                                    <DxFormLayoutItem ColSpanLg="1" Context="campo43">
                                                        <DxButton RenderStyle="ButtonRenderStyle.Secondary"
                                                                  Context="btn2Icono2s" Click="@((e) => MostrarPedidosFiltrados(Enums.TipoFiltroPedidos.Hojalata))"
                                                                  Attributes="@(new Dictionary<string, object> { ["title"] = "Filtro Características Hojalata" })"
                                                                  CssClass="btnDoble">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-funnel-fill" viewBox="0 0 16 16">
                                                                <path d="M1.5 1.5A.5.5 0 0 1 2 1h12a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-.128.334L10 8.692V13.5a.5.5 0 0 1-.342.474l-3 1A.5.5 0 0 1 6 14.5V8.692L1.628 3.834A.5.5 0 0 1 1.5 3.5v-2z" />
                                                            </svg>
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-lightning-fill" viewBox="0 0 16 16">
                                                                <path d="M5.52.359A.5.5 0 0 1 6 0h4a.5.5 0 0 1 .474.658L8.694 6H12.5a.5.5 0 0 1 .395.807l-7 9a.5.5 0 0 1-.873-.454L6.823 9.5H3.5a.5.5 0 0 1-.48-.641l2.5-8.5z" />
                                                            </svg>
                                                        </DxButton>
                                                    </DxFormLayoutItem>
                                                </DxFormLayoutGroup>
                                                @*CAMPOS FORMATO y FECHAS*@
                                                <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" BeginRow="true">
                                                    @*CAMPOS FORMATO*@
                                                    <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="6">
                                                        <DxFormLayoutItem Caption="Plano" CssClass="" ColSpanLg="12" Context="texto">
                                                            <DxTextBox @ondblclick="@((e) => OpenPdfInNewTabAsync(Enums.TipoFicheros.Plano))" ReadOnly="true" title="@_datosPedido.Plano"
                                                                       @bind-Text="@_datosPedido.Plano"
                                                                       ShowValidationIcon="true" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Field="@nameof(_datosPedido.Esc)"
                                                                          Caption="ESC" CssClass="" ColSpanLg="12" />
                                                        <DxFormLayoutItem Caption="Formato" CssClass="" Context="spinField" ColSpanLg="12">
                                                            <DxSpinEdit @bind-Value="@_datosPedido.Formato" ShowSpinButtons="false"
                                                                        ReadOnly="true" Mask="n2" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Field="@nameof(_datosPedido.TipoElemento)"
                                                                          Caption="Tipo" CssClass="" ColSpanLg="12" />
                                                        <DxFormLayoutItem Caption="Altura Cuerpo" CssClass="" Context="spinField" ColSpanLg="12">
                                                            <DxSpinEdit @bind-Value="@_datosPedido.AlturaCuerpos" ShowSpinButtons="false"
                                                                        ReadOnly="true" Mask="n0" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Field="@nameof(_datosPedido.TipoEnvase)"
                                                                          Caption="Envase" CssClass=""
                                                                          ColSpanLg="12" />
                                                        <DxFormLayoutItem Field="@nameof(_datosPedido.Embuticion)"
                                                                          Caption="Embuticion" CssClass=""
                                                                          ColSpanLg="12" />
                                                        <DxFormLayoutItem Field="@nameof(_datosPedido.TipoBarnizado)"
                                                                          Caption="Reservas" CssClass=""
                                                                          ColSpanLg="12" />


                                                    </DxFormLayoutGroup>
                                                    @*CAMPOS FECHAS*@
                                                    <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="6">
                                                        <DxFormLayoutItem Caption="F.Pedido" Context="campoFecha" ColSpanLg="12">
                                                            <DxDateEdit @bind-Date="@_datosPedido.FechaPedido"
                                                                        ReadOnly="true" DisplayFormat="dd/MM/yyyy">
                                                            </DxDateEdit>
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="Ultima Modif." Context="campoFecha" ColSpanLg="12">
                                                            <DxDateEdit @bind-Date="@_datosPedido.UltimaModificacion"
                                                                        ReadOnly="true" DisplayFormat="dd/MM/yyyy">
                                                            </DxDateEdit>
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="Fecha FTP" Context="campoFecha" ColSpanLg="12">
                                                            <DxDateEdit @bind-Date="@_datosPedido.FechaFtp"
                                                                        ReadOnly="true" DisplayFormat="dd/MM/yyyy">
                                                            </DxDateEdit>
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="F.Hojalata" Context="campoFecha" ColSpanLg="12">
                                                            <DxDateEdit @bind-Date="@_datosPedido.FechaHojalata"
                                                                        ReadOnly="true" DisplayFormat="dd/MM/yyyy">
                                                            </DxDateEdit>
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="F.Lanzamiento" Context="campoFecha" ColSpanLg="12">
                                                            <DxDateEdit @bind-Date="@_datosPedido.FechaLanzamiento"
                                                                        ReadOnly="true" DisplayFormat="dd/MM/yyyy">
                                                            </DxDateEdit>
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="F.Contrato" CssClass="" Context="campoFecha" ColSpanLg="12">
                                                            <DxDateEdit @bind-Date="@_datosPedido.FechaContrato"
                                                                        ReadOnly="true" DisplayFormat="dd/MM/yyyy">
                                                            </DxDateEdit>
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="F.Acordada" CssClass="" Context="campoFecha" ColSpanLg="12">
                                                            <DxDateEdit @bind-Date="@_datosPedido.FechaAcordada"
                                                                        ReadOnly="true" DisplayFormat="dd/MM/yyyy">
                                                            </DxDateEdit>
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="Fecha Fin" CssClass="" Context="campoFecha" ColSpanLg="12">
                                                            <DxDateEdit @bind-Date="@_datosPedido.FechaFin"
                                                                        ReadOnly="true" DisplayFormat="dd/MM/yyyy">
                                                            </DxDateEdit>
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="F.Sol. Pedido" CssClass="" Context="campoFecha" ColSpanLg="12">
                                                            <DxDateEdit @bind-Date="@_datosPedido.FechaEntregaSolicitada"
                                                                        ReadOnly="true" DisplayFormat="dd/MM/yyyy">
                                                            </DxDateEdit>
                                                        </DxFormLayoutItem>
                                                    </DxFormLayoutGroup>
                                                </DxFormLayoutGroup>

                                                <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="12">
                                                    @if (_datosPedido.Carpetas != null)
                                                    {
                                                        for (var i = 0; i < _datosPedido.Carpetas.Count; i++)
                                                        {
                                                            var carpeta = _datosPedido.Carpetas[i];
                                                            <DxFormLayoutItem Caption="Carpeta" CssClass="" ColSpanLg="12" Context="carp">
                                                                <Template>
                                                                    <DxTextBox ReadOnly="true" @bind-Text="@carpeta">
                                                                    </DxTextBox>
                                                                </Template>
                                                            </DxFormLayoutItem>
                                                        }
                                                    }
                                                </DxFormLayoutGroup>
                                            </DxFormLayoutGroup>
                                            <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="9">
                                                @*GRID*@
                                                <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="12" BeginRow="true">
                                                    <DxGrid Data="@GridFasesPedido" EditMode="GridEditMode.EditRow" SizeMode="SizeMode.Small"
                                                            Context="ContextGridFases" ValidationEnabled="false" CssClass="w-100 smallFont"
                                                            CustomizeCellDisplayText="Grid_CustomizeCellDisplayText"
                                                            EditorRenderMode="GridEditorRenderMode.Integrated"
                                                            EditModelSaving="Grid_EditModelSaving" @ref="_myGrid"
                                                            DataItemDeleting="Grid_DataItemDeleting"
                                                            PageSize="7">
                                                        <Columns>
                                                            <DxGridCommandColumn Width="80px" Visible="_pedidoModoEdicion" />
                                                            <DxGridDataColumn FieldName="Idpedido" Visible="false" />
                                                            <DxGridDataColumn FieldName="Fase" Width="60px"
                                                                              TextAlignment="GridTextAlignment.Center"
                                                                              SortIndex="0"
                                                                              SortOrder="GridColumnSortOrder.Ascending" />
                                                            <DxGridDataColumn FieldName="Idcodigoaplicacion"
                                                                              TextAlignment="GridTextAlignment.Left"
                                                                              Caption="Cod. Apli" Width="260px">
                                                                <CellDisplayTemplate Context="cellText">
                                                                    @{
                                                                        string summary = DatosCodsAplicacion.FirstOrDefault(o => o.Idcodigoaplicacion == int.Parse(cellText.Value?.ToString()))?.CombinadoSimple;
                                                                        <div class="d-block text-truncate" title="@summary" style="max-width: 255px">
                                                                            @summary
                                                                        </div>
                                                                    }
                                                                </CellDisplayTemplate>
                                                            </DxGridDataColumn>
                                                            <DxGridDataColumn FieldName="Idbarniz"
                                                                              TextAlignment="GridTextAlignment.Left"
                                                                              Caption="Cod. Barniz" Width="260px">
                                                                <CellDisplayTemplate Context="cellText">
                                                                    @{
                                                                        string summary = DatosCodsBarniz.FirstOrDefault(o => o.Idcodigoaplicacion == int.Parse(cellText.Value?.ToString()))?.CombinadoSimple;
                                                                        <div class="d-block text-truncate" title="@summary" style="max-width: 255px">
                                                                            @summary
                                                                        </div>
                                                                    }
                                                                </CellDisplayTemplate>
                                                            </DxGridDataColumn>
                                                            <DxGridDataColumn FieldName="Posicion" Width="60px"
                                                                              TextAlignment="GridTextAlignment.Left" />
                                                            <DxGridDataColumn FieldName="Hojasaplicadas" DisplayFormat="D"
                                                                              TextAlignment="GridTextAlignment.Left"
                                                                              Caption="Hojas Apli" Width="80px" />
                                                            <DxGridDataColumn FieldName="Idproductoprioritario"
                                                                              TextAlignment="GridTextAlignment.Left"
                                                                              Caption="Prod. Prioritario" Width="120px" />
                                                            <DxGridDataColumn FieldName="Gramajeprioritario"
                                                                              TextAlignment="GridTextAlignment.Left"
                                                                              Caption="Gram. Prioritario" Width="80px" />
                                                            <DxGridDataColumn FieldName="ObservacionesAplicacion"
                                                                              TextAlignment="GridTextAlignment.Left"
                                                                              Caption="Obs. Apli" Width="160px" />
                                                            <DxGridDataColumn FieldName="LineaTintas" Width="120px"
                                                                              TextAlignment="GridTextAlignment.Left" />
                                                            <DxGridDataColumn FieldName="EstadoTintas" Width="120px"
                                                                              TextAlignment="GridTextAlignment.Left" />
                                                            <DxGridDataColumn FieldName="FechaAsignacionImpresora"
                                                                              Caption="F.Asig.Imp" Width="80px"
                                                                              TextAlignment="GridTextAlignment.Left" />
                                                        </Columns>
                                                        <DataColumnCellEditTemplate>
                                                            @{
                                                                var fasePedido = (FasePedidoDTO)ContextGridFases.EditModel;
                                                            }
                                                            @switch (ContextGridFases.DataColumn.FieldName)
                                                            {
                                                                case "Fase":
                                                                    <DxSpinEdit @bind-Value="@fasePedido.Fase" ShowValidationIcon="true" ShowSpinButtons="false" />
                                                                    break;
                                                                case "Idcodigoaplicacion":
                                                                    <DxComboBox Data="@DatosCodsAplicacion"
                                                                                FilteringMode="DataGridFilteringMode.Contains"
                                                                                TextFieldName="CombinadoSimple"
                                                                                ValueFieldName="Idcodigoaplicacion"
                                                                                ListRenderMode="ListRenderMode.Virtual"
                                                                                @bind-Value="fasePedido.Idcodigoaplicacion">
                                                                    </DxComboBox>
                                                                    break;
                                                                case "Idbarniz":
                                                                    <DxComboBox Data="@DatosCodsBarniz"
                                                                                FilteringMode="DataGridFilteringMode.Contains"
                                                                                TextFieldName="CombinadoSimple"
                                                                                ValueFieldName="Idcodigoaplicacion"
                                                                                ListRenderMode="ListRenderMode.Virtual"
                                                                                @bind-Value="fasePedido.Idbarniz">
                                                                    </DxComboBox>
                                                                    break;
                                                                case "Posicion":
                                                                    <DxTextBox @bind-Text="@fasePedido.Posicion" ShowValidationIcon="true" />
                                                                    break;
                                                                case "Hojasaplicadas":
                                                                    <DxSpinEdit @bind-Value="@fasePedido.Hojasaplicadas" ShowValidationIcon="true" ShowSpinButtons="false" />
                                                                    break;
                                                                case "Idproductoprioritario":
                                                                    <DxSpinEdit @bind-Value="@fasePedido.Idproductoprioritario" ShowValidationIcon="true" ShowSpinButtons="false" />
                                                                    //
                                                                    break;
                                                                case "Gramajeprioritario":
                                                                    <DxSpinEdit @bind-Value="@fasePedido.Gramajeprioritario" DisplayFormat="#,#" ShowValidationIcon="true" ShowSpinButtons="false" />
                                                                    break;
                                                                case "ObservacionesAplicacion":
                                                                    <DxTextBox @bind-Text="@fasePedido.ObservacionesAplicacion" ShowValidationIcon="true" />
                                                                    break;
                                                                case "LineaTintas":
                                                                    <DxComboBox Data="@DatosMaquinas"
                                                                                FilteringMode="DataGridFilteringMode.Contains"
                                                                                TextFieldName="Nombremaquina"
                                                                                ValueFieldName="Idmaquina"
                                                                                ListRenderMode="ListRenderMode.Virtual"
                                                                                @bind-Value="fasePedido.LineaTintas">
                                                                    </DxComboBox>
                                                                    break;
                                                                case "EstadoTintas":
                                                                    <DxComboBox Data="@EstadoTintas"
                                                                                FilteringMode="DataGridFilteringMode.Contains"
                                                                                TextFieldName="DisplayName"
                                                                                ValueFieldName="Value"
                                                                                ListRenderMode="ListRenderMode.Virtual"
                                                                                @bind-Value="fasePedido.EstadoTintas">
                                                                    </DxComboBox>
                                                                    break;
                                                                case "FechaAsignacionImpresora":
                                                                    <DxDateEdit @bind-Date="@fasePedido.FechaAsignacionImpresora" ShowValidationIcon="true" />
                                                                    break;
                                                                default:
                                                                    throw new NotImplementedException();
                                                            }
                                                        </DataColumnCellEditTemplate>
                                                    </DxGrid>
                                                </DxFormLayoutGroup>
                                                @*OBSERVACIONES*@
                                                <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="7">
                                                    <DxFormLayoutItem Context="campoTextAreaObs"
                                                                      Caption="Observaciones" CssClass=""
                                                                      ColSpanLg="12">
                                                        <DxMemo @bind-Text="@_datosPedido.Obs1" ResizeMode="MemoResizeMode.Disabled"
                                                                ReadOnly="true" Rows="3" />
                                                    </DxFormLayoutItem>
                                                    <DxFormLayoutItem Field="@nameof(_datosPedido.Wo)" Caption="WO"
                                                                      CssClass="" ColSpanLg="12" />
                                                    <DxFormLayoutItem Field="@nameof(_datosPedido.Obsarticulo)" Caption="Obs. Articulo"
                                                                      CssClass="" ColSpanLg="12" />
                                                    <DxFormLayoutItem Field="@nameof(_datosPedido.Obsrayas)" Caption="Obs. Rayas"
                                                                      CssClass="" ColSpanLg="12" />
                                                    <DxFormLayoutItem Field="@nameof(_datosPedido.Obsflejado)" Caption="Obs. Flejado"
                                                                      CssClass="" ColSpanLg="12" />
                                                    <DxFormLayoutItem Context="campoTextAreaObsCal"
                                                                      Caption="Obs. Calidad" CssClass="" ColSpanLg="12">
                                                        <DxMemo @bind-Text="@_datosPedido.ObsCalidad" ResizeMode="MemoResizeMode.Disabled"
                                                                ReadOnly="true" Rows="3" />
                                                    </DxFormLayoutItem>
                                                    <DxFormLayoutItem Context="campoTextAreaObsAlm"
                                                                      Caption="Obs. Almacén" CssClass=""
                                                                      ColSpanLg="12">
                                                        <DxMemo @bind-Text="@_datosPedido.ObsAlmacen" ResizeMode="MemoResizeMode.Disabled"
                                                                ReadOnly="true" Rows="3" />
                                                    </DxFormLayoutItem>
                                                    <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.Card"
                                                                       BeginRow="true" Caption="Datos Actualizables">
                                                        <AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}")" Context="especial">
                                                            <DxFormLayoutItem ColSpanLg="10" Context="checkEditar">
                                                                <DxCheckBox Checked="@_pedidoModoEdicion"
                                                                            CheckedExpression="@(() => _pedidoModoEdicion)"
                                                                            CheckedChanged="@((bool value) => CheckedChanged(value))"
                                                                            Alignment="CheckBoxContentAlignment.Left">
                                                                    BLOQUEAR Y EDITAR
                                                                </DxCheckBox>
                                                            </DxFormLayoutItem>
                                                            <DxFormLayoutItem ColSpanLg="2" Context="botonEditar">
                                                                <DxButton RenderStyle="(_pedidoModoEdicion ? ButtonRenderStyle.Success : ButtonRenderStyle.Secondary) " Enabled="@(_pedidoModoEdicion)"
                                                                          Text="Guardar Datos" SubmitFormOnClick="true"
                                                                          CssClass="" />
                                                            </DxFormLayoutItem>
                                                        </AuthorizeView>
                                                        <DxFormLayoutItem Caption="Fecha Entrega" BeginRow="true" Context="campoFecha"
                                                                          ColSpanLg="6" CssClass="inputDobleFecha primeraFecha">
                                                            <DxDateEdit @bind-Date="@_datosPedido.RequeridoEnFecha"
                                                                        ReadOnly="@(!_pedidoModoEdicion)"
                                                                        DisplayFormat="dd/MM/yyyy">
                                                            </DxDateEdit>
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="Cerrado" BeginRow="true" CssClass="" ColSpanLg="5" Context="checkBox">
                                                            <DxCheckBox CheckType="CheckType.Switch" SizeMode="SizeMode.Medium"
                                                                        @bind-Checked="@_datosPedido.Anulado"
                                                                        ReadOnly="@(!_pedidoModoEdicion)"
                                                                        Alignment="CheckBoxContentAlignment.SpaceAround">
                                                            </DxCheckBox>
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="Mantener Abto" CssClass="" ColSpanLg="5" Context="checkBox">
                                                            <DxCheckBox CheckType="CheckType.Switch" SizeMode="SizeMode.Medium"
                                                                        @bind-Checked="@_datosPedido.MantenerAbierto"
                                                                        ReadOnly="@(!_pedidoModoEdicion)"
                                                                        Alignment="CheckBoxContentAlignment.SpaceAround">
                                                            </DxCheckBox>
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="NoIncluirListado" CssClass="" ColSpanLg="5" Context="checkBox">
                                                            <DxCheckBox CheckType="CheckType.Switch" SizeMode="SizeMode.Medium"
                                                                        @bind-Checked="@_datosPedido.NoIncluirEnListado"
                                                                        ReadOnly="@(!_pedidoModoEdicion)"
                                                                        Alignment="CheckBoxContentAlignment.SpaceAround">
                                                            </DxCheckBox>
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="URGENTE" CssClass="" ColSpanLg="5" Context="checkBox">
                                                            <DxCheckBox CheckType="CheckType.Switch" SizeMode="SizeMode.Medium"
                                                                        @bind-Checked="@_datosPedido.Urgente"
                                                                        ReadOnly="@(!_pedidoModoEdicion)"
                                                                        Alignment="CheckBoxContentAlignment.SpaceAround">
                                                            </DxCheckBox>
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Context="campoTextAreaProg" BeginRow="true"
                                                                          Caption="Obs. Programación" CssClass="" ColSpanLg="11">
                                                            <DxMemo @bind-Text="@_datosPedido.ObsProgramacion" ResizeMode="MemoResizeMode.Disabled"
                                                                    ReadOnly="@(!_pedidoModoEdicion)" Rows="4" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem CssClass="" ColSpanLg="1" Context="checkBox">
                                                            <DxCheckBox CheckType="CheckType.Checkbox" SizeMode="SizeMode.Medium" Id="checkBoxObs"
                                                                        @bind-Checked="@_datosPedido.VistoObsProgramacion" CssClass="alTecho"
                                                                        ReadOnly="@(!_pedidoModoEdicion)"
                                                                        Alignment="CheckBoxContentAlignment.SpaceAround">
                                                            </DxCheckBox>
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Context="campoTextAreaObsCliente"
                                                                          Caption="Obs. Cliente" CssClass="" ColSpanLg="12">
                                                            <DxMemo @bind-Text="@_datosPedido.ObsACliente" ResizeMode="MemoResizeMode.Disabled"
                                                                    ReadOnly="@(!_pedidoModoEdicion)" Rows="1" />
                                                        </DxFormLayoutItem>
                                                    </DxFormLayoutGroup>
                                                </DxFormLayoutGroup>
                                                @*TINTAS Y HOJAS TINTAS y BOTONES*@
                                                <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="5">
                                                    @*TINTAS*@
                                                    <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="5">
                                                        <DxFormLayoutItem Field="@nameof(_datosPedido.C01pedText)" CssClass="" ColSpanLg="12" />
                                                        <DxFormLayoutItem Field="@nameof(_datosPedido.C02pedText)" CssClass="" ColSpanLg="12" />
                                                        <DxFormLayoutItem Field="@nameof(_datosPedido.C03pedText)" CssClass="" ColSpanLg="12" />
                                                        <DxFormLayoutItem Field="@nameof(_datosPedido.C04pedText)" CssClass="" ColSpanLg="12" />
                                                        <DxFormLayoutItem Field="@nameof(_datosPedido.C05pedText)" CssClass="" ColSpanLg="12" />
                                                        <DxFormLayoutItem Field="@nameof(_datosPedido.C06pedText)" CssClass="" ColSpanLg="12" />
                                                        <DxFormLayoutItem Field="@nameof(_datosPedido.C07pedText)" CssClass="" ColSpanLg="12" />
                                                        <DxFormLayoutItem Field="@nameof(_datosPedido.C08pedText)" CssClass="" ColSpanLg="12" />
                                                        <DxFormLayoutItem Field="@nameof(_datosPedido.C09pedText)" CssClass="" ColSpanLg="12" />
                                                        <DxFormLayoutItem Field="@nameof(_datosPedido.Co10pedText)" CssClass="" ColSpanLg="12" />
                                                        <DxFormLayoutItem Field="@nameof(_datosPedido.Co11pedText)" CssClass="" ColSpanLg="12" />
                                                        <DxFormLayoutItem Caption="" CssClass="" ColSpanLg="12" Context="saltoLinea">
                                                            <Template>
                                                                <br />
                                                            </Template>
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Field="@nameof(_datosPedido.Cd1pedText)" CssClass="" ColSpanLg="12" />
                                                        <DxFormLayoutItem Field="@nameof(_datosPedido.Cd2pedText)" CssClass="" ColSpanLg="12" />
                                                        <DxFormLayoutItem Field="@nameof(_datosPedido.Cd3pedText)" CssClass="" ColSpanLg="12" />
                                                        <DxFormLayoutItem Field="@nameof(_datosPedido.Cd4pedText)" CssClass="" ColSpanLg="12" />
                                                    </DxFormLayoutGroup>
                                                    @*HOJAS TINTAS*@
                                                    <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="4">
                                                        <DxFormLayoutItem CssClass="" ColSpanLg="12" Context="spinField">
                                                            <DxSpinEdit @bind-Value="@_datosPedido.Hojasco1ped" ShowSpinButtons="false"
                                                                        ReadOnly="true" Mask="n0" NullText="" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem CssClass="" ColSpanLg="12" Context="spinField">
                                                            <DxSpinEdit @bind-Value="@_datosPedido.Hojasco2ped" ShowSpinButtons="false"
                                                                        ReadOnly="true" Mask="n0" NullText="" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem CssClass="" ColSpanLg="12" Context="spinField">
                                                            <DxSpinEdit @bind-Value="@_datosPedido.Hojasco3ped" ShowSpinButtons="false"
                                                                        ReadOnly="true" Mask="n0" NullText="" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem CssClass="" ColSpanLg="12" Context="spinField">
                                                            <DxSpinEdit @bind-Value="@_datosPedido.Hojasco4ped" ShowSpinButtons="false"
                                                                        ReadOnly="true" Mask="n0" NullText="" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem CssClass="" ColSpanLg="12" Context="spinField">
                                                            <DxSpinEdit @bind-Value="@_datosPedido.Hojasco5ped" ShowSpinButtons="false"
                                                                        ReadOnly="true" Mask="n0" NullText="" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem CssClass="" ColSpanLg="12" Context="spinField">
                                                            <DxSpinEdit @bind-Value="@_datosPedido.Hojasco6ped" ShowSpinButtons="false"
                                                                        ReadOnly="true" Mask="n0" NullText="" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem CssClass="" ColSpanLg="12" Context="spinField">
                                                            <DxSpinEdit @bind-Value="@_datosPedido.Hojasco7ped" ShowSpinButtons="false"
                                                                        ReadOnly="true" Mask="n0" NullText="" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem CssClass="" ColSpanLg="12" Context="spinField">
                                                            <DxSpinEdit @bind-Value="@_datosPedido.Hojasco8ped" ShowSpinButtons="false"
                                                                        ReadOnly="true" Mask="n0" NullText="" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem CssClass="" ColSpanLg="12" Context="spinField">
                                                            <DxSpinEdit @bind-Value="@_datosPedido.Hojasco9ped" ShowSpinButtons="false"
                                                                        ReadOnly="true" Mask="n0" NullText="" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem CssClass="" ColSpanLg="12" Context="spinField">
                                                            <DxSpinEdit @bind-Value="@_datosPedido.Hojasco10ped" ShowSpinButtons="false"
                                                                        ReadOnly="true" Mask="n0" NullText="" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem CssClass="" ColSpanLg="12" Context="spinField">
                                                            <DxSpinEdit @bind-Value="@_datosPedido.Hojasco11ped" ShowSpinButtons="false"
                                                                        ReadOnly="true" Mask="n0" NullText="" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Caption="" CssClass="" ColSpanLg="12" Context="saltoLinea">
                                                            <Template>
                                                                <br />
                                                            </Template>
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem CssClass="" ColSpanLg="12" Context="spinField">
                                                            <DxSpinEdit @bind-Value="@_datosPedido.Hojascd1ped" ShowSpinButtons="false"
                                                                        ReadOnly="true" Mask="n0" NullText="" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem CssClass="" ColSpanLg="12" Context="spinField">
                                                            <DxSpinEdit @bind-Value="@_datosPedido.Hojascd2ped" ShowSpinButtons="false"
                                                                        ReadOnly="true" Mask="n0" NullText="" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem CssClass="" ColSpanLg="12" Context="spinField">
                                                            <DxSpinEdit @bind-Value="@_datosPedido.Hojascd3ped" ShowSpinButtons="false"
                                                                        ReadOnly="true" Mask="n0" NullText="" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem CssClass="" ColSpanLg="12" Context="spinField">
                                                            <DxSpinEdit @bind-Value="@_datosPedido.Hojascd4ped" ShowSpinButtons="false"
                                                                        ReadOnly="true" Mask="n0" NullText="" />
                                                        </DxFormLayoutItem>
                                                    </DxFormLayoutGroup>
                                                    @*BOTONES*@
                                                    <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="3">
                                                        <DxFormLayoutItem Context="botones" ColSpanLg="6" ColSpanXs="4">
                                                            <DxButton RenderStyle="ButtonRenderStyle.Secondary"
                                                                      Context="btn2Iconos" Click="@((e) => MostrarPedidosFiltrados(Enums.TipoFiltroPedidos.Procesos))"
                                                                      Attributes="@(new Dictionary<string, object> { ["title"] = "Filtro códigos Cliente" })"
                                                                      CssClass="btnDoble btnPrimeroDeDos">
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-funnel-fill" viewBox="0 0 16 16">
                                                                    <path d="M1.5 1.5A.5.5 0 0 1 2 1h12a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-.128.334L10 8.692V13.5a.5.5 0 0 1-.342.474l-3 1A.5.5 0 0 1 6 14.5V8.692L1.628 3.834A.5.5 0 0 1 1.5 3.5v-2z" />
                                                                </svg>
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-lightning-fill" viewBox="0 0 16 16">
                                                                    <path d="M5.52.359A.5.5 0 0 1 6 0h4a.5.5 0 0 1 .474.658L8.694 6H12.5a.5.5 0 0 1 .395.807l-7 9a.5.5 0 0 1-.873-.454L6.823 9.5H3.5a.5.5 0 0 1-.48-.641l2.5-8.5z" />
                                                                </svg>
                                                            </DxButton>
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Context="botones" ColSpanLg="6" ColSpanXs="4" CssClass="ps-0">
                                                            <DxButton RenderStyle="ButtonRenderStyle.Secondary"
                                                                      Context="btnIcono" Click="@((e) => MostrarPedidosFiltrados(Enums.TipoFiltroPedidos.Blanco))"
                                                                      Attributes="@(new Dictionary<string, object> { ["title"] = "Filtro Pedidos" })"
                                                                      CssClass="btnDoble btnSegundoDeDos">
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-funnel-fill" viewBox="0 0 16 16">
                                                                    <path d="M1.5 1.5A.5.5 0 0 1 2 1h12a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-.128.334L10 8.692V13.5a.5.5 0 0 1-.342.474l-3 1A.5.5 0 0 1 6 14.5V8.692L1.628 3.834A.5.5 0 0 1 1.5 3.5v-2z" />
                                                                </svg>
                                                            </DxButton>
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Context="botones" ColSpanLg="12" ColSpanXs="12">
                                                            <DxButton RenderStyle="(_pedidoModoEdicion ? ButtonRenderStyle.Success : ButtonRenderStyle.Secondary) " Enabled="@(_pedidoModoEdicion)"
                                                                      Text="Actualiza Pedido"
                                                                      Click="@((e) => ActualizarDatosPedido())"
                                                                      CssClass="btnSizeL" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Context="botones" ColSpanLg="12" ColSpanXs="12">
                                                            <DxButton RenderStyle="(_pedidoModoEdicion ? ButtonRenderStyle.Danger : ButtonRenderStyle.Secondary) " Enabled="@(_pedidoModoEdicion)"
                                                                      Text="Actualiza Pedido V2"
                                                                      Click="@((e) => ActualizarDatosPedido(true))"
                                                                      CssClass="btnSizeL" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutGroup ColSpanLg="12" ColSpanXs="12" Decoration="FormLayoutGroupDecoration.None">
                                                            <DxFormLayoutItem Context="botones" ColSpanLg="6" ColSpanXs="6" CssClass="pe-0">
                                                                <DxButton RenderStyle="ButtonRenderStyle.Warning"
                                                                          Text="Planchas"
                                                                          Click="@((e) => ComprobarPlanchas())"
                                                                          CssClass="" />
                                                            </DxFormLayoutItem>
                                                            <DxFormLayoutItem Context="botones" ColSpanLg="4" ColSpanXs="4" CssClass="ps-0">
                                                                <DxSpinEdit @bind-Value="@_minutosPlanchas" ShowSpinButtons="false"
                                                                            Mask="n0" NullText="" />
                                                            </DxFormLayoutItem>

                                                        </DxFormLayoutGroup>
                                                        <DxFormLayoutItem Context="botones" ColSpanLg="12" ColSpanXs="12">
                                                            <DxButton RenderStyle="ButtonRenderStyle.Info"
                                                                      Text="Partes Asoc."
                                                                      Click="@((e) => ObtenerPartes())"
                                                                      CssClass="btnSizeL" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Context="botones" ColSpanLg="12" ColSpanXs="12">
                                                            @*Enabled="@(datosPedido.ExisteFicheroFotomecanica)"*@
                                                            <DxButton RenderStyle="ButtonRenderStyle.Primary"
                                                                      Click="@((e) => OpenPdfInNewTabAsync(Enums.TipoFicheros.Fotolito))"
                                                                      Text="PDF Fichero"
                                                                      CssClass="btnSizeL" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Context="botones" ColSpanLg="12" ColSpanXs="12">
                                                            <DxButton RenderStyle="ButtonRenderStyle.Primary"
                                                                      Enabled="@(!string.IsNullOrEmpty(_datosPedido.Motivos))"
                                                                      Click="@((e) => OpenPdfInNewTabAsync(Enums.TipoFicheros.Motivo))"
                                                                      Text="Motivos"
                                                                      CssClass="btnSizeL" />
                                                        </DxFormLayoutItem>
                                                        <DxFormLayoutItem Context="botones" ColSpanLg="12" ColSpanXs="12">
                                                            <DxButton RenderStyle="(_pedidoModoEdicion ? ButtonRenderStyle.Success : ButtonRenderStyle.Secondary) " Enabled="@(_pedidoModoEdicion)"
                                                                      Text="Guardar Datos" SubmitFormOnClick="true"
                                                                      CssClass="btnSizeL" />
                                                        </DxFormLayoutItem>
                                                    </DxFormLayoutGroup>
                                                </DxFormLayoutGroup>
                                            </DxFormLayoutGroup>
                                        </DxFormLayoutGroup>
                                    </div>
                                </Template>
                            </DxGridLayoutItem>
                        </Items>
                    </DxGridLayout>
                </DxFormLayout>
            </EditForm>
        </div>
    </Authorized>
    <NotAuthorized>
        <NoPuedesPasar />
    </NotAuthorized>
</AuthorizeView>
<DxWindow HeaderText=@_cabeceraVentanaAdicional @bind-Visible="@_isPopupVisible" Width="1400px" Height="800px" Scrollable="true"
          Closed="CerrarYRecargar" ShowCloseButton="true" CloseOnEscape="true" AllowResize="true" AllowDrag="true">
    <BodyContentTemplate>
        <div>
            <p>@((MarkupString)_respuestaString)</p>
        </div>
    </BodyContentTemplate>
</DxWindow>
<BasicPopUp PopUpVisibleEventCallback="PopUpVisibleHandler" PopupVisible="PopupVisible" Codigo="@_datosPedido?.IdPedido" Contenido="@_contenidoPopUp"></BasicPopUp>
@code
{
    #region VARIABLES

    public BasicPopUp Bpp;
    private string _respuestaString { get; set; }
    private string _cabeceraVentanaAdicional { get; set; }
    private bool _isPopupVisible;

    [CascadingParameter]
    private Task<AuthenticationState> AuthenticationStateTask { get; set; }

    [Parameter]
    public int? IdPedido { get; set; }

    public string? IdPedidoCliente { get; set; }
    public int? IdCliente { get; set; }
    //public string? _lineaTintas { get; set; }
    public int? LineaTintasCabecera { get; set; }

    private bool _modoConsulta = false;
    private bool _pedidoModoEdicion = false;
    private int? PedidoFiltrado { get; set; }
    private string _contenidoPopUp = string.Empty;
    private string _txtBtnReproceso = string.Empty;
    private string _txtBtnEditar = "Editar y Bloquear";
    private bool _isSmallScreen = false;
    private bool PopupVisible { get; set; } = false;

    SingleResult<ListadoPedidosProcesadosFiltradosDTO> Result { get; set; }
    SingleResult<int> ResultEditFases { get; set; }
    ListResult<MaquinaDTO> ResultMaquinas { get; set; }
    ListResult<ClienteDropdownDTO> ResultClientes { get; set; }
    SingleResult<DatosGeneralesDTO> ResultDatosGenerales { get; set; }

    PedidoProcesadoDTO _datosPedido = new();
    List<MaquinaDTO> DatosMaquinas { get; set; }
    MaquinaDTO CurrentMaquina { get; set; }
    List<CodigoAplicacionDTO> DatosCodsAplicacion { get; set; }
    List<CodigoAplicacionDTO> DatosCodsBarniz { get; set; }
    List<FasePedidoDTO> GridFasesPedido { get; set; }
    List<ClienteDropdownDTO> DatosClientes { get; set; }
    List<PedidoProcesadoDTO> _listadoPedidosFiltrados = new();
    List<PedidoProcesadoDTO> _listadoOriginalPedidosFiltrados = new();
    List<int?> _listadoIdPedidosFiltrados = new();
    List<int?> _listadoOriginalIdPedidosFiltrados = new();
    int _minutosPlanchas = 60;
    DxComboBox<int?, int?> _component;
    DxGrid? _myGrid;
    private ButtonRenderStyle _btnButtonRenderStyle;

    DotNetObjectReference<ProgramacionPedidos> DotNetHelper { get; set; }
    private IJSObjectReference _jsModule;

    public class EstatoTintasWrapper
    {
        // Specifies an enumeration value
        public string? Value { get; set; }
        // Specifies text to display in the ComboBox
        public string? DisplayName { get; set; }
    }

    public List<EstatoTintasWrapper> EstadoTintas { get; set; } =
        Enum.GetValues(typeof(Enums.EstadoTintas))
            .OfType<Enums.EstadoTintas>()
            .Select(t => new EstatoTintasWrapper()
                {
                    Value = t.GetAttribute<DisplayAttribute>().Name,
                    DisplayName = t.GetAttribute<DisplayAttribute>().Name
                }).ToList();

    JsonSerializerOptions _options = new JsonSerializerOptions
        {
            ReferenceHandler = ReferenceHandler.Preserve,
            PropertyNameCaseInsensitive = true
        };
    #endregion


    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await LocalStorage.RemoveItemAsync("maquinaIdProgPedido");
        await base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            await _component.FocusAsync();
            DotNetHelper = DotNetObjectReference.Create(this);
            _jsModule = await Js.InvokeAsync<IJSObjectReference>("import", $"./scripts/extraScripts.js?v={DateTime.Now}");
            await Js.InvokeVoidAsync("jsFunctions.addKeyboardListenerEvent", DotNetHelper);
        }
    }


    private void PopUpVisibleHandler(bool cerrar)
    {
        PopupVisible = false;
    }

    private void ObtenerPartes()
    {
        _contenidoPopUp = "Partes";
        PopupVisible = true;
    }
    private async Task ComprobarPlanchas()
    {
        SpinnerService.Show();
        var resultado = await Http.GetFromJsonAsync<SingleResult<string>>($"DatosGenerales/getConsultaPlanchas/{_minutosPlanchas}");
        if (string.IsNullOrEmpty(resultado.Data))
        {
            ToastService.ShowSuccess($"No se han encontrado pedidos.");
        }
        else
        {
            _respuestaString = resultado.Data;
            _cabeceraVentanaAdicional = "Listado Pedidos - Planchas Asignadas";
            _isPopupVisible = !string.IsNullOrEmpty(_respuestaString);
        }
        SpinnerService.Hide();
    }
    private async Task ActualizarDatosPedido(bool forzar = false)
    {
        SpinnerService.Show();
        var responseC = forzar
            ? await Http.GetAsync($"{Config["API_ActualizarDB"]}/actualizarpedidov2?idPedido={_datosPedido.IdPedido.Value}", CancellationToken.None)
            : await Http.GetAsync($"{Config["API_ActualizarDB"]}/actualizarpedido?idPedido={_datosPedido.IdPedido.Value}", CancellationToken.None);

        // var responseC = await Http.GetAsync($"http://qplant1:61216/actualizarpedido?idPedido={_datosPedido.IdPedido.Value}", CancellationToken.None);
        if (responseC.StatusCode != HttpStatusCode.OK)
        {
            if (forzar)
            {
                var msj = await responseC.Content.ReadFromJsonAsync<ApiProblem>();
                ToastService.ShowError($"ERROR: Status {msj.Status} - {msj.Detail}");
            }
            else
            {
                ToastService.ShowError($"Error al actualizar los datos del pedido actual.");
            }
        }
        else
        {
	        await GetDatosPedido(false, _datosPedido.IdPedido.Value.ToString()); // para que recargue los datos de la página

	        // 10/06/25. Javi.
	        // Lo dejo comentado. Creo que es de Alejandro y está a medias, porque no encuentro getConsultaPedidosDatosModificados en el proyecto.
	        // var resultadoPedidosModificados = await Http.GetFromJsonAsync<SingleResult<string>>("DatosGenerales/getConsultaPedidosDatosModificados");
	        // if (string.IsNullOrEmpty(resultadoPedidosModificados.Data))
	        // {
	        //     ToastService.ShowSuccess($"No se han encontrado modificaciones.");
	        // }
	        // else
	        // {
	        //     _respuestaString = resultadoPedidosModificados.Data;
	        //     _cabeceraVentanaAdicional = "Datos modificados";
	        //     _isPopupVisible = !string.IsNullOrEmpty(_respuestaString);
	        //     await GetDatosPedido(false, _datosPedido.IdPedido.Value.ToString());
	        // }
	        // await Js.InvokeVoidAsync("open", $"{NavManager.BaseUri}ProgramacionPedidos/{_datosPedido.IdPedido.Value}", "_self");
        }
        await Task.Delay(100);
        StateHasChanged();
        SpinnerService.Hide();
    }

    async Task CerrarYRecargar(WindowClosedEventArgs args)
    {
        if (_datosPedido != null)
        {
            SpinnerService.Show();
            await Task.Delay(100);
            StateHasChanged();
            await Task.Delay(100);
            await GetDatosPedido(false, _datosPedido.IdPedido.Value.ToString());
            await Task.Delay(100);
            StateHasChanged();
            SpinnerService.Hide();
        }
    }

    [JSInvokable]
    public static async void JsKeyDown(string e)
    {
        Console.WriteLine(e);
    }
    protected override async Task OnInitializedAsync()
    {
        SpinnerService.Show();

        var f = await AuthenticationStateTask;
        var authstate = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user1 = authstate.User;
        var listaRoles = new List<string>();
        foreach (var claim in user1.Claims)
        {
            if (claim.Type == "role")
            {
                listaRoles.Add(claim.Value);
            }

        }


        ResultMaquinas = await Http.GetFromJsonAsync<ListResult<MaquinaDTO>>("Maquinas/dropdownbytipo/Impresora");
        ///TODO: Refactorizar las llamadas para obtener los codigos de aplicación, en Pedidos y en Prog.Barni
        ResultDatosGenerales = await Http.GetFromJsonAsync<SingleResult<DatosGeneralesDTO>>("DatosGenerales");
        if (ResultDatosGenerales.Errors.Any())
        {
            ToastService.ShowInfo(ResultDatosGenerales.Errors.First());
        }
        else if (ResultMaquinas.Errors.Any())
        {
            ToastService.ShowInfo($"{ResultMaquinas.Errors.First()}");
        }
        else
        {
            LineaTintasCabecera = await LocalStorage.GetItemAsync<int?>("maquinaIdProgPedido");
            DatosMaquinas = ResultMaquinas.Data;
            CurrentMaquina = DatosMaquinas.FirstOrDefault(o => o.Idmaquina == LineaTintasCabecera);
            DatosCodsAplicacion = ResultDatosGenerales.Data.CodsApli;
            DatosCodsBarniz = ResultDatosGenerales.Data.Barnices;
            DatosClientes = ResultDatosGenerales.Data.Clientes;
            if (IdPedido == null)
            {
                //await GetDatosPedido(true);
            }
            else
            {
                await GetDatosPedido(false, IdPedido.ToString());
                await CheckBloqueoPedido();
            }
        }

        //StateHasChanged();
        SpinnerService.Hide();
    }

    private async Task CheckBloqueoPedido()
    {
        var responseModoPedido = await Http.GetFromJsonAsync<SingleResult<bool>>($"Seguridad/getbloqueopedido/{_datosPedido.IdPedido}");
        if (responseModoPedido.Errors.Any())
        {
            ToastService.ShowInfo(responseModoPedido.Errors.First());
            _pedidoModoEdicion = false;
        }
        await Js.InvokeAsync<string>("focusEditor", "inputPedido");
    }

    private async Task OnListadoPedidosTextChanged(string idPedido)
    {
        //idPedido = idPedido.TrimStart('0');

        SpinnerService.Show();

        _pedidoModoEdicion = false;
        if (string.IsNullOrEmpty(idPedido))
        {
            SpinnerService.Hide();
            return;
        }
        if (_listadoIdPedidosFiltrados.Any(o => o == int.Parse(idPedido)))
        {
            _datosPedido = _listadoPedidosFiltrados.FirstOrDefault(o => o.IdPedido == int.Parse(idPedido));
            GridFasesPedido = _datosPedido.FasesPedido ?? new List<FasePedidoDTO>();
            ComprobarReproceso(_datosPedido.IdPedidoReproceso);
        }
        else
        {
            //Al buscar de nuevo por un idPedido que no está filtrado, recupero todos los clientes para "resetear" los desplegables.
            DatosClientes = ResultDatosGenerales.Data.Clientes;
            await GetDatosPedido(false, idPedido);
        }
        await CheckBloqueoPedido();
        SpinnerService.Hide();
    }

    private async Task OnDatosClientesTextChanged(string datoCliente)
    {
        if (IdPedidoCliente == null && !_listadoIdPedidosFiltrados.Any())
        {
            return;
        }
        SpinnerService.Show();
        _pedidoModoEdicion = false;

        if (string.IsNullOrEmpty(datoCliente))
        {
            _listadoPedidosFiltrados = _listadoOriginalPedidosFiltrados;
            _listadoIdPedidosFiltrados = _listadoOriginalIdPedidosFiltrados;
            _datosPedido = _listadoOriginalPedidosFiltrados.First();
        }
        else
        {
            var idCliente = datoCliente.Split(",")[0];
            _listadoPedidosFiltrados = _listadoOriginalPedidosFiltrados.Where(o => o.IdCliente == int.Parse(idCliente)).ToList();
            _listadoIdPedidosFiltrados = _listadoPedidosFiltrados.Select(o => o.IdPedido).ToList();
            _datosPedido = _listadoPedidosFiltrados.FirstOrDefault(o => o.IdCliente == int.Parse(idCliente)) ?? Result.Data.Pedidos.FirstOrDefault();
            IdCliente = int.Parse(idCliente);
        }
        GridFasesPedido = _datosPedido?.FasesPedido ?? new List<FasePedidoDTO>();
        ComprobarReproceso(_datosPedido.IdPedidoReproceso);
        PedidoFiltrado = _listadoIdPedidosFiltrados.First();
        await CheckBloqueoPedido();
        SpinnerService.Hide();
    }

    private async Task OnPedidoValueChanged(string idPedido)
    {
        idPedido = idPedido.TrimStart('0');

        SpinnerService.Show();

        _pedidoModoEdicion = false;
        if (_listadoIdPedidosFiltrados.Any(o => o == int.Parse(idPedido)))
        {
            _datosPedido = _listadoPedidosFiltrados.FirstOrDefault(o => o.IdPedido == int.Parse(idPedido));
        }
        else
        {
            await GetDatosPedido(false, idPedido);
        }
        await CheckBloqueoPedido();
        SpinnerService.Hide();
    }

    private async Task OnPedidoClienteValueChanged(string idPedidoCliente)
    {
        SpinnerService.Show();

        _pedidoModoEdicion = false;
        await GetDatosPedido(false, null, idPedidoCliente);
        ComprobarReproceso(_datosPedido.IdPedidoReproceso);
        await CheckBloqueoPedido();
        SpinnerService.Hide();
    }

    private async Task GetDatosPedido(bool ultimo, string? idPedido = null, string? idPedidoCliente = null, int? idPedidoReproceso = null)
    {
        if (ultimo)
        {
            Result = await Http.GetFromJsonAsync<SingleResult<ListadoPedidosProcesadosFiltradosDTO>>("Pedido/getlast");
        }
        else if (!string.IsNullOrEmpty(idPedido))
        {
            var checkError = idPedido.Any(c => c is < '0' or > '9');
            if (checkError)
            {
                ToastService.ShowError("Carácter incorrecto en el código del pedido.");
                return;
            }
            idPedido = idPedido.TrimStart('0');
            Result = await Http.GetFromJsonAsync<SingleResult<ListadoPedidosProcesadosFiltradosDTO>>($"Pedido/getpedido/{idPedido}");
        }
        else if (!string.IsNullOrEmpty(idPedidoCliente))
        {
            if (idPedidoCliente.Contains('/') || idPedidoCliente.Contains("\\"))
            {
                idPedidoCliente = Uri.EscapeDataString(idPedidoCliente);
            }
            Result = await Http.GetFromJsonAsync<SingleResult<ListadoPedidosProcesadosFiltradosDTO>>($"Pedido/getpedidocliente/{idPedidoCliente}?idcliente={IdCliente.ToString()}");
            ResultClientes = await Http.GetFromJsonAsync<ListResult<ClienteDropdownDTO>>($"DatosGenerales/getclientesdropdown?idpedidocliente={idPedidoCliente}");
        }
        if (Result.Errors.Any())
        {
            ToastService.ShowError($"{Result.Errors.First()}");
        }
        else
        {
            _listadoPedidosFiltrados = Result.Data.Pedidos;
            _listadoOriginalPedidosFiltrados = Result.Data.Pedidos;
            _datosPedido = _listadoPedidosFiltrados.FirstOrDefault();
            GridFasesPedido = _datosPedido.FasesPedido ?? new List<FasePedidoDTO>();
            ComprobarReproceso(_datosPedido.IdPedidoReproceso);
            if (!string.IsNullOrEmpty(idPedidoCliente))
                DatosClientes = ResultClientes.Data;

            if (Result.Data.ListadoIdPedidos.Count > 1)
            {
                _listadoIdPedidosFiltrados = Result.Data.ListadoIdPedidos;
                _listadoOriginalIdPedidosFiltrados = Result.Data.ListadoIdPedidos;
                PedidoFiltrado = _listadoIdPedidosFiltrados.First();
            }
            else
            {
                _listadoIdPedidosFiltrados = new List<int?>();
                PedidoFiltrado = null;
            }

            //StateHasChanged();
        }
    }

    public async Task OpenPdfInNewTabAsync(Enums.TipoFicheros tipo)
    {
        var response = await Http.GetFromJsonAsync<ListResult<string>>($"Pedido/{_datosPedido.IdPedido.ToString()}/getFileByType?tipo={tipo}");
        if (response.Errors.Any())
        {
            ToastService.ShowInfo($"{response.Errors.First()}");
        }
        else
        {
            foreach (var filePath in response.Data)
            {
                switch (tipo)
                {
                    case Enums.TipoFicheros.Motivo:
                    case Enums.TipoFicheros.Fotolito:
                    case Enums.TipoFicheros.Plano:
                        await Js.InvokeVoidAsync("jsFunctions.openPdfInNewTab", filePath);
                        //await JS.InvokeVoidAsync("downloadPdf", $"{filePath}");
                        break;
                    case Enums.TipoFicheros.Otro:
                        break;
                    case Enums.TipoFicheros.FichaTecnica:
                        break;
                    default:
                        throw new ArgumentOutOfRangeException(nameof(tipo), tipo, null);
                }
            }
        }

    }

    private void ComprobarReproceso(int? idPedidoReproceso)
    {
        if (idPedidoReproceso == null)
            return;

        var idRepro = idPedidoReproceso.Value.ToString();
        _txtBtnReproceso = idRepro.Substring(2, 1) == "0"
            ? $"Pedido {idPedidoReproceso}"
            : $"Reproceso {idPedidoReproceso}";
    }

    private async Task OnSubmitAsync(EditContext editContext)
    {
        SpinnerService.Show();
        if (editContext.Validate())
        {
            var response = await Http.PostAsJsonAsync($"Pedido/actualizardatos", _datosPedido);
            var datos = await response.Content.ReadFromJsonAsync<SingleResult<int>>(_options);
            if (datos.Errors.Any())
            {
                ToastService.ShowError(datos.Errors.FirstOrDefault());
            }
            else
            {
                ToastService.ShowSuccess("Datos del pedido actualizados.");
                _pedidoModoEdicion = false;
                StateHasChanged();
            }
        }
        await CheckBloqueoPedido();
        await GetDatosPedido(false, _datosPedido.IdPedido.Value.ToString());
        SpinnerService.Hide();
    }
    private void HandleInvalidSubmit()
    {
        ToastService.ShowError("HandleInvalidSubmit");
    }

    #region GRID_FUNCTIONS
    async Task Grid_EditModelSaving(GridEditModelSavingEventArgs e)
    {
        SpinnerService.Show();
        var editableFasesPedido = (FasePedidoDTO)e.EditModel;
        var currentFasePedido = (FasePedidoDTO)e.DataItem;

        if (e.IsNew)
        {
            var response = await Http.PostAsJsonAsync($"Pedido/{_datosPedido.IdPedido}/createfasepedido", editableFasesPedido);
            ResultEditFases = await response.Content.ReadFromJsonAsync<SingleResult<int>>(_options);
        }
        else
        {
            //await http.PutAsJsonAsync($"Pedido/{editableFasesPedido.Idpedido}/editfasepedido", jsonObject);
            var response = await Http.PutAsJsonAsync($"Pedido/{editableFasesPedido.Idpedido}/editfasepedido/{currentFasePedido.Fase}", editableFasesPedido);
            ResultEditFases = await response.Content.ReadFromJsonAsync<SingleResult<int>>(_options);
        }
        if (ResultEditFases.Errors.Any())
        {
            ToastService.ShowError("Error al editar/crear las fases del pedido");
        }
        await UpdateDataAsync();

    }
    async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        SpinnerService.Show();
        var fasePedido = (FasePedidoDTO)e.DataItem;
        var response = await Http.DeleteAsync($"Pedido/{fasePedido.Idpedido}/deletefasepedido/{fasePedido.Idproceso}");
        var responseValue = await response.Content.ReadFromJsonAsync<SingleResult<int>>();
        if (responseValue.Errors.Any())
        {
            ToastService.ShowError("Error al eliminar la fase del pedido");
        }
        await UpdateDataAsync();
    }

    async Task UpdateDataAsync()
    {
        var response = await Http.GetFromJsonAsync<ListResult<FasePedidoDTO>>($"Pedido/getfasespedido/{_datosPedido.IdPedido}");
        GridFasesPedido = response.Data;
        SpinnerService.Hide();
    }

    void Grid_CustomizeCellDisplayText(GridCustomizeCellDisplayTextEventArgs e)
    {
        switch (e.FieldName)
        {
            //case "Idbarniz":
            //    {
            //        var id = Convert.ToInt64(e.Value);
            //        e.DisplayText = DatosCodsBarniz.FirstOrDefault(o => o.Idcodigoaplicacion == id)?.CombinadoSimple ?? "N/A";
            //        break;
            //    }
            //case "Idcodigoaplicacion":
            //    {
            //        var id = Convert.ToInt64(e.Value);
            //        e.DisplayText = DatosCodsAplicacion.FirstOrDefault(o => o.Idcodigoaplicacion == id)?.CombinadoSimple ?? "N/A";
            //        break;
            //    }
            case "LineaTintas" when e.Value != null:
                {
                    var id = Convert.ToInt32(e.Value);
                    e.DisplayText = DatosMaquinas.FirstOrDefault(o => o.Idmaquina == id)?.Nombremaquina ?? "N/A";
                    break;
                }
        }
    }

    #endregion
    async Task CheckedChanged(bool value)
    {
        SpinnerService.Show();
        var response = await Http.PostAsJsonAsync($"Seguridad/bloquearpedido/{_datosPedido.IdPedido}", value);
        var result = await response.Content.ReadFromJsonAsync<SingleResult<bool>>(_options);
        if (result.Errors.Any())
        {
            ToastService.ShowError(result.Errors.First());
            _pedidoModoEdicion = false;
        }
        else
        {
            _pedidoModoEdicion = value;
        }
        SpinnerService.Hide();

    }
    public void Dispose()
    {
        GC.SuppressFinalize(this);

        if (DotNetHelper != null)
        {
            //Now dispose our object reference so our component can be garbage collected
            DotNetHelper.Dispose();
        }
    }
    private async Task OnUpdateMaquina(MaquinaDTO m)
    {
        if (m != CurrentMaquina)
        {
            CurrentMaquina = m;
            await LocalStorage.SetItemAsync("maquinaIdProgPedido", CurrentMaquina.Idmaquina);
        }
        await Js.InvokeAsync<string>("focusEditor", "inputPedido");
    }
    [JSInvokable]
    public async Task OnPlusKeyPressed()
    {
        SpinnerService.Show();
        var response = await Http.PutAsJsonAsync($"Pedido/{_datosPedido.IdPedido}/updatelineatintas", CurrentMaquina.Idmaquina);
        var result = await response.Content.ReadFromJsonAsync<SingleResult<int>>(_options);
        if (result.Errors.Any())
        {
            ToastService.ShowInfo(result.Errors.First());
        }
        else
        {
            ToastService.ShowSuccess("Todo chupi");
        }
        await Js.InvokeAsync<string>("limpiarCampo", "inputPedido");
        await Js.InvokeAsync<string>("focusEditor", "inputPedido");
        SpinnerService.Hide();
    }
    [JSInvokable]
    public async Task OnF10KeyPressed()
    {
        SpinnerService.Show();
        //_pedidoModoEdicion = value;
        var response = await Http.PostAsJsonAsync($"Seguridad/forzardesbloqueo", _datosPedido.IdPedido);
        var result = await response.Content.ReadFromJsonAsync<SingleResult<bool>>(_options);
        if (result.Errors.Any())
        {
            ToastService.ShowError(result.Errors.First());
        }
        SpinnerService.Hide();
    }
    async Task LimpiarTintas()
    {
        SpinnerService.Show();
        var response = await Http.PutAsJsonAsync($"Pedido/{_datosPedido.IdPedido}/updatelineatintas", 0);
        var result = await response.Content.ReadFromJsonAsync<SingleResult<int>>(_options);
        if (result.Errors.Any())
        {
            ToastService.ShowError(result.Errors.First());
        }
        else
        {
            ToastService.ShowSuccess("Linea Tintas eliminada.");
            await UpdateDataAsync();
        }
        SpinnerService.Hide();
    }

    private async Task VerDatosPedido()
    {

        _contenidoPopUp = "TablaProgramacion";
        PopupVisible = true;
    }

    private async Task MostrarPedidosFiltrados(Enums.TipoFiltroPedidos tfp)
    {
        var filtro = new FiltroDTO();
        switch (tfp)
        {
            case Enums.TipoFiltroPedidos.Blanco:
                filtro.VerHojalata = false;
                filtro.DatosCliente = _datosPedido.NombreCliente;
                filtro.IdCliente = _datosPedido.IdCliente.Value;
                break;
            case Enums.TipoFiltroPedidos.Procesos:
                filtro.VerHojalata = false;
                filtro.DatosCliente = _datosPedido.NombreCliente;
                filtro.IdCliente = _datosPedido.IdCliente.Value;
                filtro.Pe1ped = _datosPedido.Pe1ped;
                filtro.Pe2ped = _datosPedido.Pe2ped;
                filtro.Pe3ped = _datosPedido.Pe3ped;
                filtro.Pi1ped = _datosPedido.Pi1ped;
                filtro.Pi2ped = _datosPedido.Pi2ped;
                filtro.Pi3ped = _datosPedido.Pi3ped;
                break;
            case Enums.TipoFiltroPedidos.Hojalata:
                filtro.VerHojalata = true;
                filtro.LargoHjlta = _datosPedido.LargoHjlta;
                filtro.AnchoHjlta = _datosPedido.AnchoHjlta;
                filtro.EspesorHjlta = _datosPedido.EspesorHjlta;
                filtro.IdCliente = _datosPedido.IdCliente.Value;
                filtro.DatosCliente = _datosPedido.NombreCliente;
                break;
        }
        await FiltroService.SetFiltroAsync(filtro);
        //NavManager.NavigateTo("/pedidosfiltrados");
        await Js.InvokeVoidAsync("open", "pedidosfiltrados", "_blank");
    }

    private void LimpiarFiltros()
    {
        DatosClientes = ResultDatosGenerales.Data.Clientes;
        PedidoFiltrado = null;
        IdPedidoCliente = null;
        IdCliente = null;
        _listadoPedidosFiltrados = new List<PedidoProcesadoDTO>();
        _listadoOriginalPedidosFiltrados = new List<PedidoProcesadoDTO>();
        _listadoIdPedidosFiltrados = new List<int?>();
        _listadoOriginalIdPedidosFiltrados = new List<int?>();
    }
}


