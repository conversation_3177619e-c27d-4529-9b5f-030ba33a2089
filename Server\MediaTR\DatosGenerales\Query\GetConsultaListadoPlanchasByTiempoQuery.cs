﻿using System.Diagnostics;
using System.Text;
using DevExpress.DataProcessing.InMemoryDataProcessor;
using MediatR;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared.ResponseModels;
using ProgramadorGeneralBLZ.Shared;

namespace ProgramadorGeneralBLZ.Server.MediaTR.DatosGenerales.Query;

public class GetConsultaListadoPlanchasByTiempoQuery : IRequest<SingleResult<string>>
{
    public int Tiempo { get; }

    public GetConsultaListadoPlanchasByTiempoQuery(int tiempo)
    {
        Tiempo = tiempo;
    }
}

internal class GetConsultaListadoPlanchasByTiempoQueryHandler : IRequestHandler<GetConsultaListadoPlanchasByTiempoQuery, SingleResult<string>>
{

    private readonly ProgramadorLitalsaContext _contextProg;
    public GetConsultaListadoPlanchasByTiempoQueryHandler(ProgramadorLitalsaContext contextProg)
    {
        _contextProg = contextProg;
    }

    public async Task<SingleResult<string>> Handle(GetConsultaListadoPlanchasByTiempoQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var result = new SingleResult<string>
            {
                Data = "",
                Errors = new List<string>()
            };
            var fechaFiltro = DateTime.Now.AddMinutes(-request.Tiempo);

            var groupedData = await _contextProg.TablaCodigosPedido
                .Where(o => o.FechaAsignacionImpresora.HasValue && o.FechaAsignacionImpresora >= fechaFiltro)
                .GroupBy(s => s.LineaTintas)
                .Select(g => new
                {
                    Maquina = g.Key,
                    Pedidos = g.Select(x => x.Idpedido).ToList()
                })
                .ToListAsync(cancellationToken);
            if (!groupedData.Any())
                return result;

            var maquinas = await _contextProg.Maquinas.ToListAsync(cancellationToken);
            int maxPedidos = groupedData.Max(g => g.Pedidos.Count);

            var stringBuilder = new StringBuilder();
            stringBuilder.AppendLine("<style>");
            stringBuilder.AppendLine("table { border-collapse: collapse; width: 100%; }");
            stringBuilder.AppendLine("td, th { border: 1px solid #ddd; padding: 2px; text-align: center; }");
            stringBuilder.AppendLine("th { font-weight: bold;}");
            stringBuilder.AppendLine(".summary { font-weight: bold; }"); // Class for summary row cells
            stringBuilder.AppendLine("</style>");
            stringBuilder.AppendLine($"<h1>Listado Planchas desde {fechaFiltro.ToString("dd/MM/yyyy - HH:mm:ss")}</h1></br>");
            stringBuilder.AppendLine("<table border=\"1\">");
            stringBuilder.AppendLine("<thead>");
            stringBuilder.AppendLine("<tr>");

            // Dynamically generate table headers based on unique Maquinas
            foreach (var group in groupedData)
            {
                var nombreMaquina = maquinas.FirstOrDefault(o => o.Idmaquina == group.Maquina)?.IdmaquinaG21 ?? "N/A";
                stringBuilder.AppendLine($"<th>{nombreMaquina}</th>");
            }

            stringBuilder.AppendLine("</tr>");
            stringBuilder.AppendLine("</thead>");
            stringBuilder.AppendLine("<tbody>");

            // Populate rows, ensuring each column has the same number of rows
            for (int i = 0; i < maxPedidos; i++)
            {
                stringBuilder.AppendLine("<tr>");
                foreach (var group in groupedData)
                {
                    if (i < group.Pedidos.Count)
                    {
                        stringBuilder.AppendLine($"<td>{group.Pedidos[i]}</td>");
                    }
                    else
                    {
                        // Placeholder for missing "Pedidos" to maintain uniform row count across columns
                        stringBuilder.AppendLine("<td></td>");
                    }
                }
                stringBuilder.AppendLine("</tr>");
            }

            // Add summary row for the sum of Pedidos for each Maquina
            stringBuilder.AppendLine("<tr>");
            foreach (var group in groupedData)
            {
                stringBuilder.AppendLine($"<td class='summary'>{group.Pedidos.Count}</td>");
            }
            stringBuilder.AppendLine("</tr>");

            stringBuilder.AppendLine("</tbody>");
            stringBuilder.AppendLine("</table>");

            result.Data = stringBuilder.ToString();
            return result;
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: GetConsultaListadoPlanchasByTiempoQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            throw new Exception(errorText, e);
        }
    }
}