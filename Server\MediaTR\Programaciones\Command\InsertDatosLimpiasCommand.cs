﻿using MediatR;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Programaciones.Command;

public class InsertDatosLimpiasCommand : IRequest<SingleResult<int>>
{
    public InsertDatosLimpiasCommand(TblLimpiezasDTO limpias)
    {
        Limpias = limpias;
    }

    public TblLimpiezasDTO Limpias{ get; set; }
}

public class InsertDatosLimpiasCommandHandler : IRequestHandler<InsertDatosLimpiasCommand, SingleResult<int>>
{
    private readonly ProgramadorLitalsaContext _contextProg;
    public InsertDatosLimpiasCommandHandler(ProgramadorLitalsaContext contextProg)
    {
        _contextProg = contextProg;
    }

    public async Task<SingleResult<int>> Handle(InsertDatosLimpiasCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<int>{ Errors = new List<string>(), Data = 0 };
        try
        {
            var limpia = request.Limpias.TipoLimpieza switch
            {
                "8" => "Lavar BPA NI",
                "7" => "Lavar+Lavar BPA NI",
                "L" or "l" => "Lavar",
                "E" or "e" => "Encima",
                "D" or "d" => "Descargar",
                _ => string.Empty
            };
            var item = new ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa.TblLimpiezas
            {
                TipoLimpieza = limpia,
                DeIdProducto = request.Limpias.DeIdProducto,
                DeProductoxparatirar = request.Limpias.DeProductoxparatirar,
                AidProducto = request.Limpias.AidProducto,
                Aproductoxparatirar = request.Limpias.Aproductoxparatirar
            };

            _contextProg.TblLimpiezas.Add(item);
            var dbResult = await _contextProg.SaveChangesAsync(cancellationToken);
            result.Data = dbResult;
        }
        catch (Exception e)
        {
            result.Errors.Add($"Se ha producido un error al insertar nuevos datos de limpias");
        }
        return result;
    }
}