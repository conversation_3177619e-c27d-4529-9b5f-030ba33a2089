﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="customSqlQuery1.Sql" xml:space="preserve">
    <value>select "PedidoProgramadoEnviadoImprimirCombinado".*
  from "dbo"."PedidoProgramadoEnviadoImprimirCombinado"
       "PedidoProgramadoEnviadoImprimirCombinado"
order by "PedidoProgramadoEnviadoImprimirCombinado"."Posicion" asc</value>
  </data>
  <data name="sqlDataSource1.ResultSchemaSerializable" xml:space="preserve">
    <value>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</value>
  </data>
</root>