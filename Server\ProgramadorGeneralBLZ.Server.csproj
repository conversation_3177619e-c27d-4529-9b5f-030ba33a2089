﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>disable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<UserSecretsId>ProgramadorGeneralBLZ.Server-be9a647b-333a-492c-802c-0c480e8d7262</UserSecretsId>
		<ErrorReport>prompt</ErrorReport>
	</PropertyGroup>

	<!-- Adds EnvironmentName variable during publish -->
	<PropertyGroup Condition="'$(Configuration)' == 'Debug'">
		<EnvironmentName>Development</EnvironmentName>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)' == 'Release'">
		<EnvironmentName>Production</EnvironmentName>
	</PropertyGroup>

	<!--ATENCION!!!!!!
	Se ha tenido que hacer downgrade de los paquetes SqlServer y Tool de EntityFrameworkCore porque la versión 7 da problemas
	de connecionstring porque requiere añadir los parametros Encript=false y otros de TrustedConnection=true.
	Sobretodo da problema el Trusted ya que con ese parametro se ignora el user e id del connectionstring y coge el de windows
	Por ahora no he conseguido otra solución.-->
	<ItemGroup>
		<PackageReference Include="MailKit" Version="4.11.0" />
		<PackageReference Include="DevExpress.AspNetCore.Reporting" Version="24.2.8" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="7.0.2" NoWarn="NU1605" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="7.0.2" NoWarn="NU1605" />
		<PackageReference Include="Microsoft.AspNetCore.Http" Version="2.2.0" />
		<PackageReference Include="Microsoft.CodeAnalysis" Version="4.7.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="7.0.2" />
		<PackageReference Include="Microsoft.Identity.Web" Version="1.16.0" />
		<PackageReference Include="Microsoft.Identity.Web.UI" Version="1.16.0" />
		<PackageReference Include="Serilog" Version="2.12.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="6.1.0" />
		<PackageReference Include="Serilog.Enrichers.AspNetCore" Version="1.0.0" />
		<PackageReference Include="Serilog.Enrichers.AspNetCore.HttpContext" Version="1.0.1" />
		<PackageReference Include="Serilog.Enrichers.Thread" Version="3.1.0" />
		<PackageReference Include="Serilog.Exceptions.EntityFrameworkCore" Version="8.4.0" />
		<PackageReference Include="System.Data.Odbc" Version="8.0.2" />
		<PackageReference Include="System.Data.OleDb" Version="8.0.2" />
		<PackageReference Include="TinyMapper" Version="3.0.3" />
		<PackageReference Include="DevExpress.Blazor" Version="24.2.8" />
		<PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="11.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="8.0.14" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="7.0.2" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="7.0.2">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Client\ProgramadorGeneralBLZ.Client.csproj" />
		<ProjectReference Include="..\Shared\ProgramadorGeneralBLZ.Shared.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Files\Pdf\" />
		<Folder Include="Files\Images\" />
	</ItemGroup>

	<ItemGroup>
	  <None Include="Data\DatoLita01\efpt.config.json.user" />
	  <None Include="Data\ProgramadorLitalsa\efpt.config.json.user" />
	</ItemGroup>

	
	<ItemGroup>
	  <None Update="Properties\PublishProfiles\IISProfile.pubxml">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
	  <None Update="Properties\PublishProfiles\IISProfile_MIRROR.pubxml">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
	</ItemGroup>

</Project>
