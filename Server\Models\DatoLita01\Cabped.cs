﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace ProgramadorGeneralBLZ.Server.Models.DatoLita01
{
    public partial class Cabped
    {
        public int? Numped { get; set; }
        public int? Cliped { get; set; }
        public DateTime? Fecped { get; set; }
        public double? Hojped { get; set; }
        public string Ob1ped { get; set; }
        public string Ob2ped { get; set; }
        public string Ob3ped { get; set; }
        public int? Pe1ped { get; set; }
        public int? Pe2ped { get; set; }
        public int? Pe3ped { get; set; }
        public int? Pi1ped { get; set; }
        public int? Pi2ped { get; set; }
        public int? Pi3ped { get; set; }
        public int? C01ped { get; set; }
        public int? C02ped { get; set; }
        public int? C03ped { get; set; }
        public int? C04ped { get; set; }
        public int? C05ped { get; set; }
        public int? C06ped { get; set; }
        public int? C07ped { get; set; }
        public int? C08ped { get; set; }
        public int? C09ped { get; set; }
        public int? C10ped { get; set; }
        public int? C11ped { get; set; }
        public int? C12ped { get; set; }
        public int? Cd1ped { get; set; }
        public int? Cd2ped { get; set; }
        public int? Cd3ped { get; set; }
        public int? Cd4ped { get; set; }
        public string Plaped { get; set; }
        public string Treped { get; set; }
        public string Embped { get; set; }
        public int? Hteped { get; set; }
        public int? Hpfped { get; set; }
        public int Hfaped { get; set; }
        public int Stped { get; set; }
        public int Bulped { get; set; }
        public string Supped { get; set; }
        public string Tenlped { get; set; }
        public string Rayasped { get; set; }
        public string Flejaped { get; set; }
        public string Codartped { get; set; }
        public string Eleped { get; set; }
        public double? Diaped { get; set; }
        public string Tipped { get; set; }
        public int? Codmot1 { get; set; }
        public int? Codmot2 { get; set; }
        public int? Codmot3 { get; set; }
        public int? Codmot4 { get; set; }
        public int? Codmot5 { get; set; }
        public int? Codmot6 { get; set; }
        public int? Codmot7 { get; set; }
        public int? Codmot8 { get; set; }
        public int? Codmot9 { get; set; }
        public int? Codmot10 { get; set; }
        public int? Codmot11 { get; set; }
        public int? Codmot12 { get; set; }
        public string Horped { get; set; }
        public string Fechaped { get; set; }
        public string Codigo { get; set; }
        public string Txttrat1 { get; set; }
        public string Txttrat2 { get; set; }
        public string Txttrat3 { get; set; }
        public string Txttrat4 { get; set; }
        public string Txttrat5 { get; set; }
        public string Txttrat6 { get; set; }
        public string Situacion { get; set; }
        public string Hojalata { get; set; }
        public string DescripHojalata { get; set; }
        public string Largo { get; set; }
        public string Ancho { get; set; }
        public string Espesor { get; set; }
        public double? Altura { get; set; }
        public string Codarticulo { get; set; }
        public string Estado { get; set; }
        public string Tipo { get; set; }
        public DateTime? FechaFinal { get; set; }
        public double? Preciohoja { get; set; }
        public DateTime? Fecentrega { get; set; }
        public string Fortintaext1 { get; set; }
        public string Fortintaext2 { get; set; }
        public string Fortintaext3 { get; set; }
        public string Fortintaext4 { get; set; }
        public string Fortintaext5 { get; set; }
        public string Fortintaext6 { get; set; }
        public string Fortintaext7 { get; set; }
        public string Fortintaext8 { get; set; }
        public string Fortintaext9 { get; set; }
        public string Fortintaext10 { get; set; }
        public string Fortintaext11 { get; set; }
        public string Fortintaext12 { get; set; }
        public string Fortintaint1 { get; set; }
        public string Fortintaint2 { get; set; }
        public string Fortintaint3 { get; set; }
        public string Fortintaint4 { get; set; }
        public string Fortintaint5 { get; set; }
        public string Fortintaint6 { get; set; }
        public string Numtinta { get; set; }
    }
}