﻿using System.Data;
using System.Security.Claims;
using System.Text.Json;
using Microsoft.AspNetCore.Components.WebAssembly.Authentication;
using Microsoft.AspNetCore.Components.WebAssembly.Authentication.Internal;

namespace ProgramadorGeneralBLZ.Client
{
    public class CustomUserFactory
        : AccountClaimsPrincipalFactory<RemoteUserAccount>
    {
        public CustomUserFactory(IAccessTokenProviderAccessor accessor)
            : base(accessor)
        {
        }

        public override async ValueTask<ClaimsPrincipal> CreateUserAsync(
            RemoteUserAccount account,
            RemoteAuthenticationUserOptions options)
        {
            var user = await base.CreateUserAsync(account, options);

            if (user.Identity.IsAuthenticated)
            {
                var identity = (ClaimsIdentity)user.Identity;
                var roleClaims = identity.FindAll(identity.RoleClaimType).ToArray();
                //var roleClaims = identity.FindAll("extension_Roles").ToArray();

                if (roleClaims != null && roleClaims.Any())
                {
                    foreach (var existingClaim in roleClaims)
                    {
                        identity.RemoveClaim(existingClaim);
                    }
                    //Adaptación para trabajar con "extension_Roles" en lugar de roles standard.
                    //Necesario porque Azure Active Directory B2C no tiene (aún?) implementados roles y no hay más remedio
                    //que usar roles creados como custom attributes.
                    //Llegará el día que se pueda hacer como esto: (No se puede por ahora porque necesita Graph API y esta no funciona para AAD B2C
                    //https://learn.microsoft.com/en-us/aspnet/core/blazor/security/webassembly/azure-active-directory-groups-and-roles?view=aspnetcore-7.0&viewFallbackFrom=aspnetcore-3.1
                    //Otra opción es hacerlo con Custom Policy:
                    //https://learn.microsoft.com/en-us/azure/active-directory-b2c/add-api-connector-token-enrichment?pivots=b2c-custom-policy

                    var rolesElem = account.AdditionalProperties[identity.RoleClaimType];
                    //var rolesElem = account.AdditionalProperties["extension_Roles"];

                    string[] rolesArray = rolesElem.ToString().Split(',');
                    
                    foreach (var role in rolesArray)
                    {
                        identity.AddClaim(new Claim(options.RoleClaim, role));
                        //identity.AddClaim(new Claim("role", role));
                    }
                }
            }

            return user;
        }
    }
}
