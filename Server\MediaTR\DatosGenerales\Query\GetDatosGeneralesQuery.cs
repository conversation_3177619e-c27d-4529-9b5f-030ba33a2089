﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.DatosGenerales.Query
{
    public class GetDatosGeneralesQuery : IRequest<SingleResult<DatosGeneralesDTO>>
    {
        public GetDatosGeneralesQuery()
        {

        }
    }
    public class GetDatosGeneralesQueryHandler : IRequestHandler<GetDatosGeneralesQuery, SingleResult<DatosGeneralesDTO>>
    {
        private readonly ProgramadorLitalsaContext _contextProg;
        public GetDatosGeneralesQueryHandler(ProgramadorLitalsaContext contextProg)
        {
            _contextProg = contextProg;
        }
        public async Task<SingleResult<DatosGeneralesDTO>> Handle(GetDatosGeneralesQuery request, CancellationToken cancellationToken)
        {
            var result = new SingleResult<DatosGeneralesDTO>()
            {
                Data = new DatosGeneralesDTO(),
                Errors = new List<string>()
            };
            try
            {

                var queryClientes = await _contextProg.Clientes.ToListAsync(cancellationToken);
                if (!queryClientes.Any())
                {
                    result.Errors.Add("No se han encontrado los datos de Clientes");
                    return result;
                }
                result.Data.Clientes = queryClientes.Select(s => new ClienteDropdownDTO
                {
                    CodigoCliente = s.CodigoCliente,
                    NombreCliente = s.NombreCliente
                }).ToList();

                var queryBarnices = await _contextProg.TablaProductos.ToListAsync(cancellationToken);
                if (!queryBarnices.Any())
                {
                    result.Errors.Add("No se han encontrado los datos de TablaProductos");
                    return result;
                }
                result.Data.Barnices = queryBarnices.Select(s => new CodigoAplicacionDTO
                {
                    Idcodigoaplicacion = s.Idproducto,
                    Descripcion = s.Denominacion
                }).ToList();

                //var queryCodsApli = await _contextProg.CodApliAll.ToListAsync(cancellationToken);
                var queryCodsApli = await _contextProg.Codapli.ToListAsync(cancellationToken);
                if (!queryCodsApli.Any())
                {
                    result.Errors.Add("No se han encontrado los datos de CodApli");
                    return result;
                }
                result.Data.CodsApli = queryCodsApli.Select(s => new CodigoAplicacionDTO
                {
                    Idcodigoaplicacion = s.Codbaz,
                    Descripcion = s.Txtbaz
                }).ToList();
            }
            catch (Exception e)
            {
                var errorText = $"ERROR: GetDatosGeneralesQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
                result.Errors.Add(errorText);
            }
            return result;

        }
    }
}
