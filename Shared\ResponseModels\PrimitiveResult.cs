﻿using System.Text.Json.Serialization;

namespace ProgramadorGeneralBLZ.Shared.ResponseModels
{
    // Las propiedades sin un setter no se serializan
    // https://stackoverflow.com/questions/13401192/why-are-properties-without-a-setter-not-serialized
    [Serializable]
    public class PrimitiveResult
    {
        [JsonPropertyName("errors")]
        public IList<string> Errors { get; set; }
        [JsonPropertyName("info")]
        public IList<string> Info { get; set; }
        public PrimitiveResult()
        {
            Errors = new List<string>();
            Info = new List<string>();
        }

        [JsonPropertyName("hasErrors")]
        public bool HasErrors => Errors.Any();
        [JsonPropertyName("hasInfo")]
        public bool HasInfo => Info.Any();
    }
}
