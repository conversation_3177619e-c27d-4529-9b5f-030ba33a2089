﻿using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.DatosGenerales.Command;

public class UpdateDatosMaquinaCommand : IRequest<SingleResult<int>>
{
    public UpdateDatosMaquinaCommand(MaquinaDTO maquina)
    {
        Maquina = maquina;
    }
    public MaquinaDTO Maquina { get; set; }

}

public class UpdateDatosMaquinaCommandHandler : IRequestHandler<UpdateDatosMaquinaCommand, SingleResult<int>>
{
    private readonly ProgramadorLitalsaContext _programadorLitalsaContext ;
    public UpdateDatosMaquinaCommandHandler(ProgramadorLitalsaContext programadorLitalsaContext)
    {
        _programadorLitalsaContext = programadorLitalsaContext;
    }

    public async Task<SingleResult<int>> Handle(UpdateDatosMaquinaCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<int>
        {
            Errors = new List<string>(),
            Data = 0
        };
        try
        {
            var currentMaquina =
                await _programadorLitalsaContext.Maquinas.SingleOrDefaultAsync(
                    o => o.Idmaquina == request.Maquina.Idmaquina, cancellationToken);

            _programadorLitalsaContext.Entry(currentMaquina).CurrentValues.SetValues(request.Maquina);
            await _programadorLitalsaContext.SaveChangesAsync(cancellationToken);
            result.Data = 1;
        }
        catch (Exception e)
        {
            var errorText = $"ERROR: UpdateDatosMaquinaCommand - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }
        return result;
    }
}