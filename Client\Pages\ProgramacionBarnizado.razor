﻿@page "/ProgramacionBarnizado"
@using Microsoft.AspNetCore.SignalR.Client
@using ProgramadorGeneralBLZ.Shared
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Authorization
@using ProgramadorGeneralBLZ.Shared.DTO
@using ProgramadorGeneralBLZ.Shared.ResponseModels
@using ProgramadorGeneralBLZ.Client.Pages.Components
@using System.Text.Json;
@using System.Text;
@using System.Text.Json.Serialization
@using System.Collections.ObjectModel
@using System.Threading;
@using System.Diagnostics;
@using System.Security.Claims

@implements IAsyncDisposable

@inject Blazored.LocalStorage.ILocalStorageService localStorage
@inject IJSRuntime Js
@inject IToastService ToastService
@inject HttpClient Http
@inject SpinnerService SpinnerService
@inject AuthenticationStateProvider GetAuthenticationStateAsync
@inject NavigationManager Navigation
@inject IHttpClientFactory HttpClientFactory
@inject AuthenticationStateProvider AuthState

@attribute [Authorize(Roles = Roles.Programador)]

<PageTitle>Barnizado</PageTitle>

<AuthorizeView Roles="@($"{Roles.Admin}, {Roles.Programador}")">
	<Authorized Context="authContext">
		<DxLayoutBreakpoint DeviceSize="DeviceSize.Large" @bind-IsActive="@_isSmallScreen" />
		<div class="h-100 overflow-auto px-2 py-1">
			<DxFormLayout SizeMode="SizeMode.Small">
				<DxGridLayout CssClass="h-100" ColumnSpacing="1px" RowSpacing="1px">
					<Rows>
						<DxGridLayoutRow Areas="CintaSuperior" Height="auto" />
						<DxGridLayoutRow Areas="GridSuperior" Height="auto" />
						<DxGridLayoutRow Areas="CintaIntermedia" Height="auto" />
						<DxGridLayoutRow Areas="GridInferior" Height="auto" />
						<DxGridLayoutRow Areas="CintaInferior" Height="auto" />
					</Rows>
					<Items>
						<DxGridLayoutItem Area="CintaSuperior">
							<Template>
								<div class="gridlayout-cs gridlayout-item">
									<DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="12">
										<DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanLg="9">
											<DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanLg="2">
												<DxFormLayoutItem Caption="Máquina:" Context="ddMaquina" ColSpanLg="12" ColSpanXs="6">
													<span @ref="@_popupTarget"></span>
													<DxComboBox Data="@DatosMaquinas" @ref="@_componentMaquina"
																TextFieldName="@nameof(MaquinaDTO.IdmaquinaG21)"
																Value="@CurrentMaquina"
																SelectedItemChanged="@((MaquinaDTO m) => OnUpdateMaquina(m))"
																NullText="Máquina...">
													</DxComboBox>
												</DxFormLayoutItem>
												<DxFormLayoutItem Context="botones" ColSpanLg="12" ColSpanXs="6">
													<DxButtonGroup RenderStyle="ButtonRenderStyle.Info">
														<Items>
															<DxButtonGroupItem CssClass="movMaq" IconCssClass="oi oi-arrow-thick-left" Click="@(() => OnButtonGroupClick("PrevMaquina"))" />
															<DxButtonGroupItem CssClass="movMaq" IconCssClass="oi oi-arrow-thick-right" Click="@(() => OnButtonGroupClick("NextMaquina"))" />
														</Items>
													</DxButtonGroup>
													@* <DxToolbar ItemClick="OnItemClick" CssClass="ajusteToolbar" AdaptivityMinRootItemCount="2">
                                                    <Items>
                                                    <DxToolbarItem CssClass="toolbarItem1" Alignment="ToolbarItemAlignment.Right"
                                                    IconCssClass="oi oi-arrow-thick-left" Name="PrevMaquina"
                                                    RenderStyle="ButtonRenderStyle.Info" />
                                                    <DxToolbarItem CssClass="toolbarItem2" Alignment="ToolbarItemAlignment.Right"
                                                    IconCssClass="oi oi-arrow-thick-right" Name="NextMaquina"
                                                    RenderStyle="ButtonRenderStyle.Info" />
                                                    </Items>
                                                    </DxToolbar> *@
												</DxFormLayoutItem>
											</DxFormLayoutGroup>
											<DxFormLayoutItem Caption="Aplicación:" Context="ddCodApli" ColSpanLg="4" ColSpanXs="6">
												<DxComboBox Data="@DatosCodsApliBarniz" @ref="@_componentCodApli"
															DropDownWidthMode="DropDownWidthMode.ContentOrEditorWidth"
															TextFieldName="@nameof(CodigoAplicacionDTO.Combinado)"
															ListRenderMode="ListRenderMode.Virtual"
															ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
															Value="@CurrentCodApli" InputCssClass="codApliInput"
															SelectedItemChanged="@((CodigoAplicacionDTO? ca) => OnUpdateCodApli(ca))"
															AllowUserInput="true"
															NullText="Código aplicación..."
															SearchMode="ListSearchMode.AutoSearch"
															SearchFilterCondition="ListSearchFilterCondition.StartsWith">
													<EditBoxDisplayTemplate Context="id_cod_apl_cs">
														<DxInputBox />
													</EditBoxDisplayTemplate>
												</DxComboBox>
											</DxFormLayoutItem>
											<DxFormLayoutItem Caption="Barniz:" Context="ddBarniz" ColSpanLg="4" ColSpanXs="6">
												<DxComboBox Data="@DatosBarniz" @ref="@_componentBarniz"
															DropDownWidthMode="DropDownWidthMode.EditorWidth"
															TextFieldName="@nameof(ProductoDTO.DenominacionCompleta)"
															ListRenderMode="ListRenderMode.Virtual"
															ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
															Value="@CurrentBarniz" InputCssClass="barnizInput"
															SelectedItemChanged="@((ProductoDTO? p) => OnUpdateProducto(p))"
															AllowUserInput="true"
															NullText="Barniz..."
															SearchMode="ListSearchMode.AutoSearch"
															SearchFilterCondition="ListSearchFilterCondition.StartsWith">
													<EditBoxDisplayTemplate Context="id_cod_bar_cs">
														<DxInputBox />
													</EditBoxDisplayTemplate>
												</DxComboBox>
											</DxFormLayoutItem>
											<DxFormLayoutItem ColSpanLg="1" ColSpanXs="6" BeginRow="false" CssClass="ToTop" Context="textosAzules">
												<DxSpinEdit @bind-Value="@_currentTextoAzul.Existencias" ShowSpinButtons="false"
															DisplayFormat="{0:N0} [Kg]" CssClass="textoAzul" ReadOnly="true" />
											</DxFormLayoutItem>
											<DxFormLayoutItem Context="botones" ColSpanLg="1" ColSpanXs="6" BeginRow="false" CssClass="ToTop">
												<DxButton RenderStyle="ButtonRenderStyle.Info" Text="FT" CssClass=""
														  Enabled="@((CurrentBarniz?.Idproducto ?? 0) > 0)"
														  Click="@((e) => OpenPdfInNewTabAsync(Enums.TipoFicheros.FichaTecnica))" />
												<DxButton RenderStyle="ButtonRenderStyle.Warning" Text="Limpias" CssClass=""
														  Click="@((e) => MostrarDatosLimpias())"
														  Enabled="@((CurrentBarniz?.Idproducto ?? 0) > 0)" />
											</DxFormLayoutItem>
										</DxFormLayoutGroup>
										<DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanLg="3">
											<DxFormLayoutItem CssClass="textoBlanco" Caption="Uso Barniz" Context="textosAzules"
															  ColSpanLg="6" ColSpanXs="6">
												<DxSpinEdit @bind-Value="@_currentTextoAzul.BarnizNecesario" Mask="@NumericMask.Percentage"
															DisplayFormat="{0:N0} [Kg]" CssClass="textoAzul" ShowSpinButtons="false" ReadOnly="true" />
											</DxFormLayoutItem>
											<DxFormLayoutItem CssClass="textoBlanco" Caption="Solidos" Context="textosAzules"
															  ColSpanLg="6" ColSpanXs="6">
												<DxSpinEdit @bind-Value="@_currentTextoAzul.Solidos" ShowSpinButtons="false"
															DisplayFormat="P2" CssClass="textoAzul" ReadOnly="true" />
											</DxFormLayoutItem>
											<DxFormLayoutItem CssClass="textoBlanco" Caption="Hojas Tot" Context="textosAzules"
															  BeginRow="true" ColSpanLg="6" ColSpanXs="6">
												<DxSpinEdit @bind-Value="@_currentTextoAzul.HTotales" ShowSpinButtons="false"
															DisplayFormat="{0:N0} [hojas]" CssClass="textoAzul" ReadOnly="true" />
											</DxFormLayoutItem>
											<DxFormLayoutItem CssClass="textoBlanco" Caption=" Sup. Tot" Context="textosAzules"
															  ColSpanLg="6" ColSpanXs="6">
												<DxSpinEdit @bind-Value="@_currentTextoAzul.SupTotal" ShowSpinButtons="false"
															DisplayFormat="{0:N2} [m2]" CssClass="textoAzul" ReadOnly="true" />
											</DxFormLayoutItem>
										</DxFormLayoutGroup>
									</DxFormLayoutGroup>
								</div>
							</Template>
						</DxGridLayoutItem>
						<DxGridLayoutItem Area="GridSuperior">
							<Template>
								<div class="gridlayout-gs gridlayout-item">
									<DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="12">
										<DxGrid Data="@DatosPedidos" SizeMode="SizeMode.Small" CssClass="ch-320 smallFont"
												ShowFilterRow="true" ColumnResizeMode="GridColumnResizeMode.ColumnsContainer"
												SelectionMode="GridSelectionMode.Multiple" PageSize="100"
												SelectAllCheckboxMode="GridSelectAllCheckboxMode.Page" @ref="GridBarnizado"
												EditorRenderMode="GridEditorRenderMode.Integrated"
												KeyFieldNames="KeyFieldNames"
												FilterCriteriaChanged="GridDatosPedidos_FilterCriteriaChanged"
												FilterMenuButtonDisplayMode="GridFilterMenuButtonDisplayMode.Always"
												@bind-SelectedDataItems="_selectedPedidosBarnizDataItems">
											<Columns>
												<DxGridSelectionColumn Width="50">
												</DxGridSelectionColumn>
												<DxGridDataColumn FieldName="IdPedido" Caption="Pedido" TextAlignment="GridTextAlignment.Center"
																  Width="70px" DisplayFormat="F0" SortIndex="2" SortOrder="GridColumnSortOrder.Ascending"
																  FilterRowOperatorType="GridFilterRowOperatorType.Contains">
													<CellDisplayTemplate Context="cellText">
														<span class="p-1 d-block text-left" style="cursor: pointer"
															  @ondblclick="() => VerDatosPedido(cellText.DisplayText)">
															@cellText.DisplayText
														</span>
													</CellDisplayTemplate>
												</DxGridDataColumn>
												<DxGridDataColumn FieldName="DatosCliente" Caption="Cliente" Width="100px"
																  SortIndex="1" SortOrder="GridColumnSortOrder.Ascending">
													<CellDisplayTemplate Context="cellText">
														@{
															string summary = cellText.Value?.ToString() ?? string.Empty;
															<div class="d-block text-truncate" title="@summary" style="max-width: 100px">
																@summary
															</div>
														}
													</CellDisplayTemplate>
												</DxGridDataColumn>
												<DxGridDataColumn FieldName="BarnizNecesario" Caption="Barniz"
																  TextAlignment="GridTextAlignment.Center" DisplayFormat="N0" />
												<DxGridDataColumn FieldName="HojasPedido" Caption="Nº Hojas" TextAlignment="GridTextAlignment.Center">
												</DxGridDataColumn>
												<DxGridDataColumn FieldName="HojasTerminadas" Caption="Hojas" TextAlignment="GridTextAlignment.Center" />
												<DxGridDataColumn FieldName="Motivos" Width="205px">
													<CellDisplayTemplate Context="cellText">
														@{
															string summary = cellText.Value?.ToString() ?? string.Empty;
															<div class="d-block text-truncate" title="@summary" style="max-width: 200px">
																@summary
															</div>
														}
													</CellDisplayTemplate>
												</DxGridDataColumn>
												<DxGridDataColumn FieldName="TipoElemento" Caption="T.Ele" TextAlignment="GridTextAlignment.Center" />
												<DxGridDataColumn FieldName="TipoBarnizado" Caption="T.Bar" TextAlignment="GridTextAlignment.Center" />
												<DxGridDataColumn FieldName="Estado" Width="90px" />
												<DxGridDataColumn FieldName="DescHojalata" Width="160px" />
												<DxGridDataColumn FieldName="Plano" Width="80px" TextAlignment="GridTextAlignment.Center">
													<CellDisplayTemplate Context="cellText">
														@{
															string summary = cellText.Value?.ToString() ?? string.Empty;
															<div class="d-block text-truncate" title="@summary" style="max-width: 80px">
																@summary
															</div>
														}
													</CellDisplayTemplate>
												</DxGridDataColumn>
												<DxGridDataColumn FieldName="Formato" DisplayFormat="N2" Width="55px" TextAlignment="GridTextAlignment.Center"
																  SortIndex="0" SortOrder="GridColumnSortOrder.Ascending" />
												<DxGridDataColumn FieldName="GRMBAZMIN" Width="60px" Caption="GrMinBar" TextAlignment="GridTextAlignment.Center" />
												<DxGridDataColumn FieldName="GRMBAZ" Width="40px" Caption="GrBar" TextAlignment="GridTextAlignment.Center" />
												<DxGridDataColumn FieldName="Posicion" Width="30px" Caption="Pos." TextAlignment="GridTextAlignment.Center" />
												<DxGridDataColumn FieldName="Idcodigoaplicacion" Caption="Cod.Apli" DisplayFormat="D"
																  Width="80px" TextAlignment="GridTextAlignment.Center" />
												<DxGridDataColumn FieldName="TextEstado" Caption=" TextoEstado" Width="300px" />
												@* <DxGridDataColumn FieldName="TextEstado" Caption=" TextoEstado" Width="205px">
                                                <CellDisplayTemplate Context="cellText">
                                                @{
                                                string summary = cellText.Value?.ToString() ?? string.Empty;
                                                <div class="d-block text-truncate" title="@summary" style="max-width: 200px">
                                                @summary
                                                </div>
                                                }
                                                </CellDisplayTemplate>
                                                </DxGridDataColumn>*@
												<DxGridDataColumn FieldName="URGENTE">
													<CellDisplayTemplate Context="checkbox">
														<DxCheckBox Checked="@((bool)checkbox.Value)" />
													</CellDisplayTemplate>
												</DxGridDataColumn>
												<DxGridDataColumn FieldName="RequeridoEnFecha" Caption="Req. F" />
											</Columns>
											<TotalSummary>
												<DxGridSummaryItem FieldName="IdPedido" SummaryType="GridSummaryItemType.Count" Visible="true" />
											</TotalSummary>
										</DxGrid>
									</DxFormLayoutGroup>
								</div>
							</Template>
						</DxGridLayoutItem>
						<DxGridLayoutItem Area="CintaIntermedia">
							<Template>
								<div class="gridlayout-cm gridlayout-item">
									<DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="12">
										<DxFormLayoutItem Context="botones" ColSpanLg="2" ColSpanXs="6">
											<DxButton RenderStyle="ButtonRenderStyle.Primary" CssClass="btnWide"
													  Enabled="@(_selectedPedidosBarnizDataItems.Any())"
													  IconCssClass="oi oi-arrow-thick-bottom"
													  Click="@((e) => MoverEntreGrids(true))" />
											<DxButton RenderStyle="ButtonRenderStyle.Primary" CssClass="btnWide"
													  Enabled="@(_selectedAImprimirDataItems.Any())"
													  IconCssClass="oi oi-arrow-thick-top"
													  Click="@((e) => MoverEntreGrids(false))" />
											<DxButton RenderStyle="ButtonRenderStyle.Warning" CssClass="btnWide"
													  Enabled="@(_selectedAImprimirDataItems.Any())"
													  IconCssClass="oi oi-aperture"
													  @onclick="@(() => _isCambiosMasivosPopUpVisible = true)" />
										</DxFormLayoutItem>
									</DxFormLayoutGroup>
								</div>
							</Template>
						</DxGridLayoutItem>
						<DxGridLayoutItem Area="GridInferior">
							<Template>
								<div class="gridlayout-gi gridlayout-item">
									<DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="12">
										<DxLoadingPanel @bind-Visible="PanelVisible"
														IsContentBlocked="true"
														ApplyBackgroundShading="true"
														IndicatorAreaVisible="false"
														IndicatorAnimationType="WaitIndicatorAnimationType.Spin"
														Text="Cargando..." ZIndex="100"
														TextAlignment="LoadingPanelTextAlignment.Right">
											<DxGrid Data="@DatosProgramacion" SizeMode="SizeMode.Small" @key="@myKey" id="idProgBarni"
													CssClass="ch-390 smallFont progGrid" PageSize="100" EditMode="GridEditMode.PopupEditForm"
													PopupEditFormCssClass="popupEditor"
													KeyFieldName="Idprogramacion" Context="GridProgramacion"
													EditNewRowPosition="GridEditNewRowPosition.Top"
													ShowFilterRow="true" ColumnResizeMode="GridColumnResizeMode.ColumnsContainer"
													SelectionMode="GridSelectionMode.Multiple" ValidationEnabled="false"
													SelectAllCheckboxMode="GridSelectAllCheckboxMode.Page"
													EditModelSaving="Grid_EditModelSaving" @ref="GridProg"
													CustomizeElement="Grid_CustomizeElement"
													@bind-SelectedDataItems="_selectedAImprimirDataItems"
													AllowSort="false"
													AllowSelectRowByClick="true"
													AllowDragRows="true"
													ItemsDropped="Grid_ItemsDropped">
												<Columns>
													<DxGridSelectionColumn Width="50" />

													<DxGridCommandColumn NewButtonVisible="false" DeleteButtonVisible="false" Width="50px">
														<CellDisplayTemplate>
															<a class="oi oi-pencil" @onclick="@(() => StartEditing(context.VisibleIndex))" style="text-decoration: none; color: #c75fff;" href="javascript:void(0);"></a>
														</CellDisplayTemplate>
														<CellEditTemplate>
															<a class="oi oi-arrow-thick-bottom" @onclick="@(() => GridProg.SaveChangesAsync())" style="text-decoration: none; color: greenyellow; margin-right: 6px; margin-top: 3px;" href="javascript:void(0);"></a>
															<a class="oi oi-x" @onclick="@(() => GridProg.CancelEditAsync())" style="text-decoration: none; color: red;" href="javascript:void(0);"></a>
														</CellEditTemplate>
													</DxGridCommandColumn>
													<DxGridDataColumn FieldName="Id" Visible="false" />
													<DxGridDataColumn FieldName="Idprogramacion" Width="80px" DisplayFormat="F0" Visible="false" />
													<DxGridDataColumn FieldName="Idpedido" Caption="Pedido" TextAlignment="GridTextAlignment.Center"
																	  Width="70px" DisplayFormat="F0">
														<CellDisplayTemplate Context="cellText">
															<span class="p-1 d-block text-left" style="cursor: pointer" @ondblclick="() => VerDatosPedido(cellText.DisplayText)">
																@cellText.DisplayText
															</span>
														</CellDisplayTemplate>
													</DxGridDataColumn>
													<DxGridDataColumn FieldName="Posicion" DisplayFormat="F0" Width="50px">
														<CellDisplayTemplate Context="cellText">
															<span class="p-1 d-block text-left posi" style="cursor: pointer">
																@cellText.DisplayText
															</span>
														</CellDisplayTemplate>
													</DxGridDataColumn>
													@*<DxGridDataColumn FieldName="DatosPedido" Width="125px" />*@
													<DxGridDataColumn FieldName="DatosPedido" Width="185px">
														<CellDisplayTemplate Context="cellText">
															<CellDisplay Value="@(cellText.Value?.ToString() ?? string.Empty)" />
														</CellDisplayTemplate>
													</DxGridDataColumn>
													@*<DxGridDataColumn FieldName="Idaplicacion" DisplayFormat="F0" Width="70px" />*@
													<DxGridDataColumn FieldName="Idaplicacion" Context="combo" Caption="Id Aplicación" Width="80px">
														<CellDisplayTemplate>
															@{
																var item = (TablaProgramacionDTO)combo.DataItem;
															}
															<span class="p-1 d-block text-left" style="cursor: pointer; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"
																  @ondblclick="() => UpdateCodApliByDblClick(item.Idaplicacion)">
																@($"{item.Idaplicacion}, {item.TextoIdAplicacion}")
															</span>
														</CellDisplayTemplate>
													</DxGridDataColumn>
													<DxGridDataColumn FieldName="Producto" Width="205px">
														<CellDisplayTemplate Context="cellText">
															@{
																var texto = AjustarTextoProductoSinPeso(cellText.Value?.ToString() ?? string.Empty);
															}
															<CellDisplay Value="@texto" />
														</CellDisplayTemplate>
													</DxGridDataColumn>
													<DxGridDataColumn FieldName="Idproducto" Context="combo" Caption="Id Producto" Width="70px">
														<CellDisplayTemplate>
															@{
																var item = (TablaProgramacionDTO)combo.DataItem;
															}
															<span class="p-1 d-block text-left" style="cursor: pointer; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"
																  @ondblclick="() => UpdateCodBarnizByDblClick(item.Idproducto)">
																@(item.Idproducto.HasValue ? $"{item.Idproducto}" : string.Empty)
															</span>
														</CellDisplayTemplate>
													</DxGridDataColumn>
													<DxGridDataColumn FieldName="PesoMin" Caption="Peso M" TextAlignment="GridTextAlignment.Center" />
													<DxGridDataColumn FieldName="Peso" TextAlignment="GridTextAlignment.Center" />
													<DxGridDataColumn FieldName="Idlinea" Caption="Máquina" Context="combo" TextAlignment="GridTextAlignment.Center">
														<CellDisplayTemplate>
															@{
																var item = (TablaProgramacionDTO)combo.DataItem;
															}
															<CellDisplay Value="@($"{item.NombreG21}")" />
															@*<p>@($"{item.Idproducto}, {item.TextoIdProducto}")</p>*@
														</CellDisplayTemplate>
													</DxGridDataColumn>
													<DxGridDataColumn FieldName="HoraComienzoEstimada" Caption="Hora Ini Est" DisplayFormat="dd/MM HH:mm"
																	  Width="80px" TextAlignment="GridTextAlignment.Center" />
													<DxGridDataColumn FieldName="HoraFinEstimada" Caption="Hora Fin Est" DisplayFormat="dd/MM HH:mm"
																	  Width="80px" TextAlignment="GridTextAlignment.Center" />
													<DxGridDataColumn FieldName="VarCambios" Caption="Cambios" TextAlignment="GridTextAlignment.Center" />
													<DxGridDataColumn FieldName="DiaReal" Width="80px" TextAlignment="GridTextAlignment.Center" />
													<DxGridDataColumn FieldName="HoraReal" DisplayFormat="t" Width="80px" TextAlignment="GridTextAlignment.Center" />
													<DxGridDataColumn FieldName="Observaciones" Width="205px">
														<CellDisplayTemplate Context="cellText">
															<CellDisplay Value="@(cellText.Value?.ToString() ?? string.Empty)" />
														</CellDisplayTemplate>
													</DxGridDataColumn>
													<DxGridDataColumn FieldName="Flejar">
														<CellDisplayTemplate Context="checkbox">
															<DxCheckBox Checked="@((bool)checkbox.Value)" CssClass="noElegir" />
														</CellDisplayTemplate>
													</DxGridDataColumn>
													<DxGridDataColumn FieldName="ObsAlmacen" Width="140px" Context="columnAlmacen">
														<CellDisplayTemplate>
															@{
																var item = (TablaProgramacionDTO)columnAlmacen.DataItem;
															}
															<span class="p-1 d-block text-left" style="cursor: pointer; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"
																  @ondblclick="() => GetObsAlmacenByDblClick(item)">
																@item.ObsAlmacen
															</span>
														</CellDisplayTemplate>
													</DxGridDataColumn>
													<DxGridDataColumn FieldName="Idaplicacionposterior" DisplayFormat="F0" Width="120px" TextAlignment="GridTextAlignment.Center" />
													<DxGridDataColumn FieldName="Posicionaplicacionposterior" TextAlignment="GridTextAlignment.Center" />
													<DxGridDataColumn FieldName="ObsCalidad" Width="205px">
														<CellDisplayTemplate Context="cellText">
															<CellDisplay Value="@(cellText.Value?.ToString() ?? string.Empty)" />
														</CellDisplayTemplate>
													</DxGridDataColumn>
													<DxGridDataColumn FieldName="Obspaseposterior" Width="205px">
														<CellDisplayTemplate Context="cellText">
															<CellDisplay Value="@(cellText.Value?.ToString() ?? string.Empty)" />
														</CellDisplayTemplate>
													</DxGridDataColumn>
													<DxGridDataColumn FieldName="TemperaturaSecado" Caption="T.Secado" Width="100px" />
												</Columns>
												<EditFormTemplate>
													@{
														var prog = (TablaProgramacionDTO)GridProgramacion.EditModel;
													}

													<DxFormLayout CssClass="w-100">
														@*<DxFormLayoutItem Caption="Id programacion:" ColSpanLg="4">
                                                        <DxSpinEdit @bind-Value="@prog.Idprogramacion" ShowSpinButtons="false" ShowValidationIcon="true" ReadOnly="true" />
                                                        </DxFormLayoutItem>*@
														<DxFormLayoutItem Caption="Id pedido:" ColSpanLg="4">
															<DxSpinEdit @bind-Value="@prog.Idpedido" ShowSpinButtons="false" ShowValidationIcon="true" ReadOnly="true" />
														</DxFormLayoutItem>
														<DxFormLayoutItem Caption="Id aplicacion:" ColSpanLg="4">
															<DxComboBox Data="@DatosCodsApliBarniz"
																		FilteringMode="DataGridFilteringMode.Contains"
																		TextFieldName="Combinado"
																		ValueFieldName="Idcodigoaplicacion"
																		ListRenderMode="ListRenderMode.Virtual"
																		@bind-Value="prog.Idaplicacion">
															</DxComboBox>
														</DxFormLayoutItem>
														<DxFormLayoutItem Caption="Datos Pedido:" ColSpanLg="4">
															<DxTextBox @bind-Text="@prog.DatosPedido" ShowValidationIcon="true" ReadOnly="true" />
														</DxFormLayoutItem>
														<DxFormLayoutItem Caption="Posicion:" ColSpanLg="4">
															<DxSpinEdit @bind-Value="@prog.Posicion" ShowSpinButtons="false" ShowValidationIcon="true" />
														</DxFormLayoutItem>
														<DxFormLayoutItem Caption="Id producto:" ColSpanLg="4">
															<DxComboBox Data="@DatosBarniz"
																		ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
																		TextFieldName="DenominacionCompleta"
																		ValueFieldName="Idproducto"
																		ListRenderMode="ListRenderMode.Virtual"
																		@bind-Value="prog.Idproducto"
																		AllowUserInput="true"
																		SearchMode="ListSearchMode.AutoSearch"
																		SearchFilterCondition="ListSearchFilterCondition.Contains">
																<EditBoxDisplayTemplate Context="id_producto_edit_form">
																	<DxInputBox />
																</EditBoxDisplayTemplate>
															</DxComboBox>
														</DxFormLayoutItem>
														<DxFormLayoutItem Caption="Id Maquina:" ColSpanLg="4">
															<DxComboBox Data="@DatosMaquinas"
																		FilteringMode="DataGridFilteringMode.Contains"
																		TextFieldName="IdmaquinaG21"
																		ValueFieldName="Idmaquina"
																		ListRenderMode="ListRenderMode.Virtual"
																		@bind-Value="prog.Idlinea">
															</DxComboBox>
														</DxFormLayoutItem>
														<DxFormLayoutItem Caption="Dia Real:" ColSpanLg="4">
															<DxDateEdit @bind-Date="@prog.DiaReal" ShowValidationIcon="true"
																		ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
																		@ondblclick="() => FechaAutomatica(prog)" />
														</DxFormLayoutItem>
														<DxFormLayoutItem Caption="Peso Min:" ColSpanLg="4">
															<DxSpinEdit ShowSpinButtons="false" MinValue="0" @bind-Value="@prog.PesoMin" ShowValidationIcon="true" />
														</DxFormLayoutItem>
														<DxFormLayoutItem Caption="Temp Secado:" ColSpanLg="4">
															<DxSpinEdit ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" ShowSpinButtons="false"
																		MinValue="0" @bind-Value="@prog.TemperaturaSecado" ShowValidationIcon="true" />
														</DxFormLayoutItem>
														<DxFormLayoutItem Caption="Hora Real:" ColSpanLg="4">
															<DxTimeEdit @bind-Time="@prog.HoraReal" ShowValidationIcon="true"
																		ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
																		Mask="@DateTimeMask.ShortTime" @ondblclick="() => HoraAutomatica(prog)" />
														</DxFormLayoutItem>
														<DxFormLayoutItem Caption="Peso:" ColSpanLg="4">
															<DxSpinEdit ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" ShowSpinButtons="false"
																		MinValue="0" @bind-Value="@prog.Peso" ShowValidationIcon="true" />
														</DxFormLayoutItem>
														<DxFormLayoutItem Caption="Hora Comienzo Estimada:" ColSpanLg="4">
															@{
																if (prog.VarCambios != null)
																{
																	prog.HoraComienzoEstimada = prog.HoraComienzoEstimada?.AddMinutes(prog.VarCambios.Value);
																}
															}
															<DxTimeEdit @bind-Time="@prog.HoraComienzoEstimada" ReadOnly="true"
																		ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
																		ShowValidationIcon="true" Mask="@DateTimeMask.ShortTime" />
														</DxFormLayoutItem>
														<DxFormLayoutItem Caption="Cambios:" ColSpanLg="4">
															<DxSpinEdit ShowSpinButtons="false" @bind-Value="@prog.VarCambios" ShowValidationIcon="true" />
														</DxFormLayoutItem>
														<DxFormLayoutItem Caption="Producto:" ColSpanLg="4">
															@{
																var texto = AjustarTextoProductoSinPeso(@prog.Producto);
															}
															<DxTextBox @bind-Text="@texto" ShowValidationIcon="true" ReadOnly="true" />
														</DxFormLayoutItem>
														<DxFormLayoutItem Caption="Hora Fin Estimada:" ColSpanLg="4">
															<DxTimeEdit @bind-Time="@prog.HoraFinEstimada" ReadOnly="true"
																		ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
																		ShowValidationIcon="true" Mask="@DateTimeMask.ShortTime" />
														</DxFormLayoutItem>
														<DxFormLayoutItem Caption="Obs Almacen:" ColSpanLg="4">
															<DxMemo @bind-Text="@prog.ObsAlmacen" ShowValidationIcon="true" />
														</DxFormLayoutItem>
														<DxFormLayoutItem Caption="Id Aplicación Posterior:" ColSpanLg="4">
															<DxComboBox Data="@prog.DatosPedProceso" AllowUserInput="true"
																		FilteringMode="DataGridFilteringMode.Contains"
																		ListRenderMode="ListRenderMode.Virtual"
																		ValueChanged="@((int? newValue) => OnValueChanged(newValue, prog))"
																		Value="prog.Idaplicacionposterior" ValueExpression="@(() => prog.Idaplicacionposterior)"
																		TextFieldName="Combinado"
																		ValueFieldName="Proceso">
															</DxComboBox>
														</DxFormLayoutItem>
														<DxFormLayoutItem Caption="Flejar:" ColSpanLg="4">
															<DxCheckBox CssClass="d-inline-block" @bind-Checked="@prog.Flejar"
																		ValueChecked="true" ValueUnchecked="false"
																		@onclick="@(e => OnFlejarChanged(e, prog))" />
															@*CheckedChanged="@((bool newValue) => OnFlejarChanged(newValue, prog))"/>*@
														</DxFormLayoutItem>

														<DxFormLayoutItem ColSpanLg="4">
														</DxFormLayoutItem>
														<DxFormLayoutItem Caption="Posicion aplicacion posterior:" ColSpanLg="4">
															<DxTextBox @bind-Text="@prog.Posicionaplicacionposterior" ShowValidationIcon="true" />
														</DxFormLayoutItem>
														<DxFormLayoutItem ColSpanLg="4">
														</DxFormLayoutItem>

														<DxFormLayoutItem Caption="Observaciones:" ColSpanLg="4" BeginRow="true" CaptionPosition="CaptionPosition.Vertical">
															<DxMemo @bind-Text="@prog.Observaciones" ShowValidationIcon="true" Rows="5" />
														</DxFormLayoutItem>
														<DxFormLayoutItem Caption="Obs pase posterior:" ColSpanLg="4" CaptionPosition="CaptionPosition.Vertical">
															<DxMemo @bind-Text="@prog.Obspaseposterior" ShowValidationIcon="true" Rows="5" />
														</DxFormLayoutItem>
														<DxFormLayoutItem Caption="Obs Calidad:" ColSpanLg="4" CaptionPosition="CaptionPosition.Vertical">
															<DxMemo @bind-Text="@prog.ObsCalidad" ShowValidationIcon="true" Rows="5" />
														</DxFormLayoutItem>
														<DxFormLayoutItem BeginRow="true" ColSpanMd="12">
															<DxButton Context="btn" CssClass="btnSizeM izq" Click="@(() => EditarRegistro("anterior", prog))"
																	  IconCssClass="oi oi-arrow-thick-left" RenderStyle="ButtonRenderStyle.Info" />
															<DxButton Context="btn" CssClass="btnSizeM der" Click="@(() => EditarRegistro("siguiente", prog))"
																	  IconCssClass="oi oi-arrow-thick-right" RenderStyle="ButtonRenderStyle.Info" />
														</DxFormLayoutItem>
													</DxFormLayout>

												</EditFormTemplate>
												<DragHintTextTemplate>
													@{
														if (GridProgramacion.DataItems.Count > 1)
														{
															var texto = string.Empty;
															for (var i = 0; i < GridProgramacion.DataItems.Count; i++)
															{
																var item = GridProgramacion.DataItems[i];
																var dataItem = (TablaProgramacionDTO)item;
																texto += $" {(i != 0 ? " || " : "")}{dataItem.Idpedido}";
															}
															<span class="dxbl-text">@texto</span>
														}
														else
														{
															var dataItem = (TablaProgramacionDTO)GridProgramacion.DataItems[0];
															<span class="dxbl-text">@dataItem.Idpedido</span>
															<span class="dxbl-text">@dataItem.Posicion</span>
															<span class="dxbl-text">@dataItem.Idaplicacion, @dataItem.TextoIdAplicacion</span>
															<span class="dxbl-text">@dataItem.Producto</span>
														}
													}
												</DragHintTextTemplate>
											</DxGrid>
										</DxLoadingPanel>
									</DxFormLayoutGroup>
								</div>
							</Template>
						</DxGridLayoutItem>
						<DxGridLayoutItem Area="CintaInferior">
							<Template>
								<div class="gridlayout-ci gridlayout-item">
									<DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="12">
										<DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="4">
											<DxFormLayoutItem Caption="Desde:" Context="ddPos1" ColSpanLg="6" ColSpanXs="6">
												@{
													var value = CurrentMaquina?.PosicionDesde ?? 0;
													var hasta = CurrentMaquina?.PosicionHasta ?? 0;
												}
												<DxSpinEdit Value="@value" Increment="10" MaxValue="@hasta" ValueChanged="@((int value) => OnUpdateDesde(value))" />
											</DxFormLayoutItem>
											<DxFormLayoutItem Context="botones" ColSpanLg="3" ColSpanXs="6">
												<DxButton RenderStyle="ButtonRenderStyle.Light" Click="@SetUltimaPosicion"
														  Text="Ultima Posición" CssClass="" />
											</DxFormLayoutItem>
											<DxFormLayoutItem Context="btnImpProg" ColSpanLg="3" ColSpanXs="6" CssClass="px-0">
												<DxButton Text="Imprimir Programación" CssClass=""
														  Click="@(() => ProcesoImpresion(false, false))"></DxButton>
											</DxFormLayoutItem>
											<DxFormLayoutItem Caption="Hasta:" Context="ddPos2" BeginRow="true" ColSpanLg="6" ColSpanXs="6">
												@{
													var value = CurrentMaquina?.PosicionHasta ?? 0;
													var desde = CurrentMaquina?.PosicionDesde ?? 0;
												}
												<DxSpinEdit Value="@value" Increment="10" MinValue="@desde"
															@ondblclick="@(SetMaximaPosicion)"
															ValueChanged="@((int value) => OnUpdateHasta(value))" />
											</DxFormLayoutItem>
											<DxFormLayoutItem Context="check" ColSpanLg="6" ColSpanXs="6">
												<DxCheckBox Alignment="CheckBoxContentAlignment.Right" LabelPosition="LabelPosition.Left"
															@bind-Checked="@CheckDesgloseHorarios">Imprimir Desglose Horarios</DxCheckBox>
											</DxFormLayoutItem>
										</DxFormLayoutGroup>
										<DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" ColSpanXs="12" ColSpanLg="8" CssClass="">
											<DxFormLayoutItem ColSpanXs="12">
												<Template Context="layoutContext">
													<div style="display: flex; flex-wrap: wrap; gap: 8px; align-items: flex-start;">
														<div style="display: flex; flex-wrap: wrap; gap: 8px;">
															<DxButton RenderStyle="ButtonRenderStyle.Primary" Text="Calcular Horarios" CssClass=""
																	  Click="@((e) => CalcularHorarios())" />
															<DxButton RenderStyle="ButtonRenderStyle.Info" Text="Comprueba Programaciones" CssClass=""
																	  Click="@ComprobarProgramacion" />
															<DxButton RenderStyle="ButtonRenderStyle.Warning" Text="Renumerar" CssClass=""
																	  Click="@Renumerar" />
														</div>

														<div style="display: flex; flex-direction: column; gap: 8px;">
															<DxButton RenderStyle="ButtonRenderStyle.Success" Text="Enviar a PROTEO" CssClass=""
																	  Click="@EnviarProteo" Enabled="@(!esDesarrollo)" />
															<DxButton Text="Publicar" CssClass=""
																	  Click="@(() => ProcesoImpresion(false, true))" />
														</div>

														<div style="display: flex; flex-wrap: wrap; gap: 8px;">
															<DxButton RenderStyle="ButtonRenderStyle.Warning" Text="Recalcular Posiciones" CssClass=""
																	  Click="@RecalcularPosiciones" />
														</div>
														<div style="display: flex; flex-direction: column; gap: 8px;">
															<DxButton RenderStyle="ButtonRenderStyle.Danger" Text="Combinado (Imp)" CssClass=""
																	  Click="@(() => ProcesoImpresion(true, false))"></DxButton>
															<DxButton RenderStyle="ButtonRenderStyle.Danger" Text="Combinado (Publi)" CssClass=""
																	  Click="@(() => ProcesoImpresion(true, true))"></DxButton>
														</div>
														<div style="display: flex; flex-wrap: wrap; gap: 8px;">
															<DxButton RenderStyle="ButtonRenderStyle.Info" Click="@(GridBarnizado_ExportXlsx)" CssClass="">
																Exportar Grid Barnizado
															</DxButton>
															<DxButton RenderStyle="ButtonRenderStyle.Info" Click="@(GridProg_ExportXlsx)" CssClass="">
																Exportar Grid Programación
															</DxButton>
														</div>
													</div>
												</Template>
											</DxFormLayoutItem>
										</DxFormLayoutGroup>
									</DxFormLayoutGroup>
								</div>
							</Template>
						</DxGridLayoutItem>
					</Items>
				</DxGridLayout>
			</DxFormLayout>
		</div>
	</Authorized>
	<NotAuthorized>
		<NoPuedesPasar />
	</NotAuthorized>
</AuthorizeView>

<BasicPopUp PopUpVisibleEventCallback="PopUpVisibleHandler"
			ListaMensajes="@ListaMensajes"
			WidthSize="1200"
			PopupVisible="PopupBasicoVisible"
			Codigo="@CurrentBarniz?.Idproducto"
			Contenido="@_contenido">

</BasicPopUp>

<DxPopup HeaderText="Observaciones de Programación" ZIndex="2000" MinWidth="1000"
		 @bind-Visible="@_isObsProgPopupVisible" AllowDrag="true"
		 CloseOnEscape="false" ShowFooter="true"
		 CloseOnOutsideClick="false">
	<BodyContentTemplate>
		<div>
			<p>@((MarkupString)_listadoNotificaciones.ToString())</p>
		</div>
	</BodyContentTemplate>
	<FooterContentTemplate Context="footer">
		<DxButton Click="@(() => NotificacionesRevisadas(true))" RenderStyle="ButtonRenderStyle.Success" CssClass="float-right me-2" accept-button>Aceptar</DxButton>
		<DxButton Click="@(() => NotificacionesRevisadas(false))" RenderStyle="ButtonRenderStyle.Danger" CssClass="float-right cancel-button">Cancelar</DxButton>
	</FooterContentTemplate>
</DxPopup>

@*Popup para registrar datos de Tabla Limpiezas*@
<DxPopup HeaderText="Limpias entre Productos" AllowDrag="true" ZIndex="2000"
		 @bind-Visible="@_isLimpiasPopupVisible"
		 CloseOnEscape="false" ShowFooter="true"
		 CloseOnOutsideClick="false" @ref="PopupLimpias">
	<BodyContentTemplate>
		<div>
			<p>@(_textoLimpias)</p>
			<p>(L)avada</p>
			<p>(E)ncima</p>
			<p>(D)escarga</p>
			<p>(8) Lavada entre BPA NI</p>
			<p>(7) Lavada de BPA a BPA NI</p>
			<DxTextBox Text="@_valorLimpia" BindValueMode="BindValueMode.OnInput" TextExpression="@(() => _valorLimpia)" TextChanged="@(value => AsignarTextoLimpias(value))" />
		</div>
	</BodyContentTemplate>
	<FooterContentTemplate Context="footer">
		<DxButton Click="@(() => LimpiasRevisadas(true))" RenderStyle="ButtonRenderStyle.Success" CssClass="float-right me-2" accept-button>Guardar</DxButton>
		<DxButton Click="@(() => LimpiasRevisadas(false))" RenderStyle="ButtonRenderStyle.Danger" CssClass="float-right cancel-button">Cancelar</DxButton>
	</FooterContentTemplate>
</DxPopup>

<DxWindow @ref="_windowRef"
		  AllowResize="true"
		  AllowDrag="true"
		  ShowCloseButton="true"
		  CloseOnEscape="true"
		  MinHeight="calc(100vh - 100px)" MinWidth="calc(100vw - 100px)"
		  @bind-Visible="_windowVisible">
	<BodyTextTemplate>
		<ReportViewer ParamNombreReport="@ParameterNombreReport" />
	</BodyTextTemplate>
</DxWindow>

<BulkChangesPopUp PopUpBulkVisibleEventCallback="PopUpBulkVisibleHandler"
				  AplicarCambiosEventCallback="AplicarCambiosMasivos"
				  Maquinas="@DatosMaquinas"
				  Barnices="@DatosBarniz"
				  CambiosMasivosPopUpVisible="_isCambiosMasivosPopUpVisible" />
@code {
	Guid myKey { get; set; } = Guid.NewGuid();

	[Parameter]
	public string Maquina { get; set; }

	private HubConnection _hubConnection;
	private ClaimsPrincipal User { get; set; }
	DxWindow _windowRef;
	bool _windowVisible;
	public string ParameterNombreReport { get; set; }
	ElementReference _popupTarget;
	private int _visibleIndex { get; set; }
	private bool _isPopupVisible = false;
	private bool _notificacionesAceptadas;
	private bool _limpiasRegistradas;

	DxComboBox<CodigoAplicacionDTO, CodigoAplicacionDTO> _componentCodApli;
	DxComboBox<ProductoDTO, ProductoDTO> _componentBarniz;
	DxComboBox<MaquinaDTO, MaquinaDTO> _componentMaquina;
	private bool _isSmallScreen;
	private bool PopupBasicoVisible { get; set; }
	public string ValorCalculado { get; set; } = "Porcentaje";

	[CascadingParameter]
	private Task<AuthenticationState> AuthenticationStateTask { get; set; }

	private bool CheckDesgloseHorarios { get; set; }
	IReadOnlyList<object> _selectedPedidosBarnizDataItems = new List<object>();
	IReadOnlyList<object> _selectedAImprimirDataItems = new List<object>();

	ListResult<MaquinaDTO> ResultMaquinas { get; set; }
	ListResult<CodigoAplicacionDTO> ResultCodsApliBarniz { get; set; }
	ListResult<ProductoDTO> ResultBarniz { get; set; }
	ListResult<DatosGeneralesPedidoDTO> ResultPedidos { get; set; }
	ListResult<TablaProgramacionDTO> ResultProgramacion { get; set; }

	List<MaquinaDTO> DatosMaquinas { get; set; }
	List<CodigoAplicacionDTO> DatosCodsApliBarniz { get; set; }
	List<ProductoDTO> DatosBarniz { get; set; }
	List<DatosGeneralesPedidoDTO> DatosPedidos { get; set; }
	List<DatosGeneralesPedidoDTO> DatosPedidosFiltrados = new();
	ObservableCollection<TablaProgramacionDTO> DatosProgramacion { get; set; }

	CodigoAplicacionDTO? CurrentCodApli { get; set; }
	ProductoDTO? CurrentBarniz { get; set; }
	MaquinaDTO CurrentMaquina { get; set; }
	DxGrid? GridProg;
	DxGrid? GridBarnizado;
	DxPopup PopupLimpias;
	readonly TextoAzulBarnizado _currentTextoAzul = new();

	private bool _isObsProgPopupVisible;
	private bool _isLimpiasPopupVisible;
	private bool _isCambiosMasivosPopUpVisible;
	private bool _checkUltimo;
	private bool _isConfirmationPopupVisible = false;
	private StringBuilder _listadoNotificaciones = new();
	private string _textoLimpias;
	private string _valorLimpia;
	private string _contenido;
	private int _deIdProducto;
	private int _aIdProducto;
	private string _deProducto;
	private string _aProducto;
	private List<string> ListaMensajes = new();
	private bool PanelVisible { get; set; }
	DotNetObjectReference<ProgramacionBarnizado> DotNetHelper { get; set; }
	IJSObjectReference _jsModule { get; set; }
	private bool esDesarrollo { get; set; }
	IReadOnlyList<string> KeyFieldNames = new[] {
		"Id",
		"IdPedido",
		"Idcodigoaplicacion",
		"Posicion"
	};
	JsonSerializerOptions jsonSerializerOptions = new JsonSerializerOptions
	{
		DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
	};
	public async void OnValueChanged(int? newValue, TablaProgramacionDTO model)
	{
		model.Idaplicacionposterior = newValue;
		switch (newValue)
		{
			case -1:
				model.Flejar = true;
				//Obtenemos los datos de flejado
				var datosFlejado = await Http.GetFromJsonAsync<SingleResult<string>>($"Pedido/{model.Idpedido.Value}/getdatosflejadodirecto");
				if (!datosFlejado.HasErrors)
				{
					model.Obspaseposterior = datosFlejado.Data;
				}
				model.Posicionaplicacionposterior = string.Empty;
				break;
			case 0:
				model.Obspaseposterior = "PASE POSTERIOR INDETERMINADO";
				break;
			default:
				model.Flejar = false;
				model.Obspaseposterior = model.DatosPedProceso.FirstOrDefault(o => o.Proceso == model.Idaplicacionposterior)?.TextoCaraSiguiente ?? "";
				model.Posicionaplicacionposterior = model.DatosPedProceso.FirstOrDefault(o => o.Proceso == model.Idaplicacionposterior)?.Lugar ?? "";
				break;
		}
	}
	protected override async Task OnAfterRenderAsync(bool firstRender)
	{
		if (firstRender)
		{
			await _componentCodApli.FocusAsync();
			_jsModule = await Js.InvokeAsync<IJSObjectReference>("import", $"./Pages/ProgramacionBarnizado.razor.js?v={DateTime.Now}");
			var client = HttpClientFactory.CreateClient("DatabaseAPI");
			esDesarrollo = Convert.ToBoolean(await client.GetStringAsync("ConsultaDB/current-database"));
		}
	}

	protected override async Task OnInitializedAsync()
	{
		SpinnerService.Show();

		var authstate = await AuthState.GetAuthenticationStateAsync();
		User = authstate.User;
		_hubConnection = new HubConnectionBuilder()
			.WithUrl(Navigation.ToAbsoluteUri("/imprimirHub"))
			.Build();

		await _hubConnection.StartAsync();
		_hubConnection.On<SingleResultHub<int>>(TiposMensajesImprimirHub.ProgramacionActualizada_Emisor, (resultHub) =>
		{
			if (resultHub.Errors.Any())
				ToastService.ShowError(resultHub.Errors.First().Mensaje);
			else if (resultHub.Info.Any())
				ToastService.ShowSuccess(resultHub.Info.First().Mensaje);

			SpinnerService.Hide();
		});

		ResultMaquinas = await Http.GetFromJsonAsync<ListResult<MaquinaDTO>>($"Maquinas/dropdownbytipo/{Enums.TipoMaquina.Barnizadora}");
		//TODO: Refactorizar las llamadas para obtener los codigos de aplicación, en Pedidos y en Prog.Barni
		ResultCodsApliBarniz = await Http.GetFromJsonAsync<ListResult<CodigoAplicacionDTO>>("DatosGenerales/getcodsaplicaciondropdown?tipo=B");
		ResultBarniz = await Http.GetFromJsonAsync<ListResult<ProductoDTO>>("DatosGenerales/getdatosproductos");
		//Cargamos los pedidos que cumplen el filtro
		//var filterSerialized = JsonSerializer.Serialize(filtro);

		if (ResultMaquinas.Errors.Any() || ResultBarniz.Errors.Any() || ResultCodsApliBarniz.Errors.Any())
		{
			var errors = ResultMaquinas.Errors.Concat(ResultCodsApliBarniz.Errors)
				.Concat(ResultBarniz.Errors);
			foreach (var error in errors)
			{
				ToastService.ShowError($"{error}");
			}
		}
		else
		{
			DatosMaquinas = ResultMaquinas.Data;
			var maquinaId = await localStorage.GetItemAsync<int?>("maquinaId");
			CurrentMaquina = DatosMaquinas.FirstOrDefault(o => o.Idmaquina == maquinaId) ?? DatosMaquinas.First();
			DatosCodsApliBarniz = ResultCodsApliBarniz.Data;
			DatosBarniz = ResultBarniz.Data;
			CurrentCodApli = DatosCodsApliBarniz.FirstOrDefault(c => c.Idcodigoaplicacion == CurrentMaquina.Idaplicacion);
			if (CurrentMaquina.Idproducto != null)
			{
				CurrentBarniz = DatosBarniz.FirstOrDefault(c => c.Idproducto == CurrentMaquina.Idproducto);
			}
			await UpdatePedidosBarnizado(CurrentCodApli.CodBarniz);

			ActualizarTextosAzul();
		}
		SpinnerService.Hide();
	}

	private void PopUpVisibleHandler(bool cerrar)
	{
		PopupBasicoVisible = false;
	}

	private async Task ActualizarDatosMaquina(bool soloProgramacion = false)
	{
		SpinnerService.Show();
		var newDatosMaquina = CurrentMaquina;
		newDatosMaquina.Idaplicacion = CurrentCodApli?.Idcodigoaplicacion;
		newDatosMaquina.Idproducto = CurrentBarniz?.Idproducto;
		var response = await Http.PostAsJsonAsync($"DatosGenerales/updatedatosmaquina", newDatosMaquina);
		var resultUpdateDatosMaquinas = await response.Content.ReadFromJsonAsync<SingleResult<int>>();
		if (resultUpdateDatosMaquinas.HasErrors)
		{
			ToastService.ShowError($"{resultUpdateDatosMaquinas.Errors.First()}");
			return;
		}

		//TODO: Estaría bien standarizar bien las respuestas y hacer algo tipo:https://stackoverflow.com/a/55741259
		ToastService.ShowSuccess($"Datos de la máquina {CurrentMaquina.IdmaquinaG21} actualizados.");
		var item = DatosMaquinas.Single(i => i.Idmaquina == CurrentMaquina.Idmaquina);
		int index = DatosMaquinas.IndexOf(item);
		DatosMaquinas[index] = newDatosMaquina;
		if (soloProgramacion)
		{
			await ActualizarGridProgramacion();
		}
		else
		{
			await UpdatePedidosBarnizado(newDatosMaquina.Idproducto ?? CurrentCodApli.CodBarniz);
		}
		SpinnerService.Hide();

	}

	private async Task UpdatePedidosBarnizado(int codProductoBarniz, bool limpiarFiltros = true)
	{
		SpinnerService.Show();
		ResultPedidos = await Http.GetFromJsonAsync<ListResult<DatosGeneralesPedidoDTO>>($"Pedido/getpedidosbyfiltro?codProductoBarniz={codProductoBarniz}");
		ResultProgramacion = await Http.GetFromJsonAsync<ListResult<TablaProgramacionDTO>>($"Pedido/getprogramacionbymaquina?maquina={CurrentMaquina?.Idmaquina}&ultimo={_checkUltimo}");
		if (ResultPedidos.Errors.Any() || ResultProgramacion.Errors.Any())
		{
			var errors = ResultPedidos.Errors.Concat(ResultProgramacion.Errors);
			foreach (var error in errors)
			{
				ToastService.ShowError($"{error}");
			}
		}
		else
		{
			if (limpiarFiltros)
			{
				GridProg.ClearFilter();
				GridBarnizado.ClearFilter();
			}
			DatosPedidos = ResultPedidos.Data;
			DatosProgramacion = new ObservableCollection<TablaProgramacionDTO>(ResultProgramacion.Data);
			_selectedAImprimirDataItems = new List<object>();
			_selectedPedidosBarnizDataItems = new List<object>();
			ActualizarTextosAzul();
		}
		SpinnerService.Hide();
	}

	private async Task OnUpdateCodApli(CodigoAplicacionDTO? ca)
	{
		if (ca != null)
		{
			if (ca != CurrentCodApli)
			{
				CurrentCodApli = ca;
				CurrentBarniz = DatosBarniz.FirstOrDefault(c => c.Idproducto == CurrentCodApli.CodBarniz);
				await ActualizarDatosMaquina();
			}
		}
		else
		{
			CurrentCodApli = ca;
			_selectedAImprimirDataItems = new List<object>();
		}
	}

	private async Task OnUpdateProducto(ProductoDTO? p)
	{
		if (p != CurrentBarniz)
		{
			CurrentBarniz = p;
			await ActualizarDatosMaquina();
		}

	}

	private async Task OnUpdateMaquina(MaquinaDTO m)
	{
		if (m != CurrentMaquina)
		{
			CurrentMaquina = m;
			CurrentMaquina.PosicionHasta = 9999999;
			CurrentBarniz = DatosBarniz.FirstOrDefault(c => c.Idproducto == CurrentMaquina.Idproducto);
			CurrentCodApli = DatosCodsApliBarniz.FirstOrDefault(o => o.Idcodigoaplicacion == CurrentMaquina.Idaplicacion);

			// await ActualizarDatosMaquina();
			// await localStorage.SetItemAsync("maquinaId", CurrentMaquina.Idmaquina);

			await UpdatePedidosBarnizado(m?.Idproducto ?? CurrentCodApli.CodBarniz);
			await localStorage.SetItemAsync("maquinaId", CurrentMaquina.Idmaquina);
			_selectedAImprimirDataItems = new List<object>();
			_selectedPedidosBarnizDataItems = new List<object>();
		}
	}

	private void ActualizarTextosAzul(bool usarFiltrados = false)
	{
		var datos = usarFiltrados ? DatosPedidosFiltrados : DatosPedidos;
		_currentTextoAzul.BarnizNecesario = datos?.Sum(o => o.BarnizNecesario) ?? 0;
		_currentTextoAzul.HTotales = datos?.Sum(o => o.HojasPedido) ?? 0;
		_currentTextoAzul.Solidos = CurrentBarniz?.Solidos / 100 ?? 0;
		_currentTextoAzul.SupTotal = datos?.Sum(o => o.Sup) ?? 0;
		_currentTextoAzul.Existencias = CurrentBarniz?.Existencias ?? 0;
	}

	void OnFocusedRowChanged(GridFocusedRowChangedEventArgs e)
	{
		//ProductsInOrder = e.DataItem is PedidoProgramarPedidoDTO order ? OrderProducts[order.OrderId] : null;
	}

	private async Task OpenPdfInNewTabAsync(Enums.TipoFicheros tipo)
	{
		var response = await Http.GetFromJsonAsync<ListResult<string>>($"Pedido/{(CurrentBarniz?.Idproducto ?? 0).ToString()}/getFileByType?tipo={tipo}");
		if (response.Errors.Any())
		{
			ToastService.ShowInfo($"{response.Errors.First()}");
		}
		else
		{
			foreach (var filePath in response.Data)
			{
				switch (tipo)
				{
					case Enums.TipoFicheros.FichaTecnica:
						await Js.InvokeVoidAsync("openPdfInNewTab", filePath);
						break;
					case Enums.TipoFicheros.Otro:
						break;
					default:
						throw new ArgumentOutOfRangeException(nameof(tipo), tipo, null);
				}
			}
		}
	}

	private async Task MoverEntreGrids(bool programar)
	{
		var error = false;
		SpinnerService.Show();
		if (programar)
		{
			//Puede no haber barniz seleccionado si es un proceso raro tipo secado, limpieza, etc
			//if (CurrentBarniz == null)
			//{
			//    ToastService.ShowWarning("Se debe seleccionar un Barniz.");
			//    await _componentBarniz.FocusAsync();
			//    error = true;
			//}
			if (CurrentCodApli == null)
			{
				ToastService.ShowWarning("Se debe seleccionar un Código Aplicación.");
				await _componentCodApli.FocusAsync();
				error = true;
			}
			else if (CurrentMaquina == null)
			{
				ToastService.ShowWarning("Se debe seleccionar una Máquina.");
				await _componentMaquina.FocusAsync();
				error = true;
			}
			else if (!_selectedPedidosBarnizDataItems.Any())
			{
				ToastService.ShowWarning("Debes seleccionar como mínimo un pedido.");
				error = true;
			}
			if (error)
			{
				SpinnerService.Hide();
				return;
			}
			var pedidosSeleccionados = _selectedPedidosBarnizDataItems.Cast<DatosGeneralesPedidoDTO>().ToList();


			foreach (var p in pedidosSeleccionados)
			{
				var texto = new StringBuilder();
				var obs = $"{p.Obs1}, {p.Obs2}, {p.Obs3}";
				if (obs.Contains("USAR") || obs.Contains("PARAD"))
				{
					texto.Append(ConstruirMensaje($"Observaciones: {obs}"));
				}
				if (!string.IsNullOrWhiteSpace(p.ObsProgramacion))
				{
					texto.Append(ConstruirMensaje($"Observaciones Programación: {p.ObsProgramacion}"));
				}
				if (!string.IsNullOrWhiteSpace(p.ObservacionesAplicacion))
				{
					texto.Append(ConstruirMensaje($"Observaciones Aplicación: {p.ObservacionesAplicacion}"));
				}
				//TODO:Aqui o en el origen del check de programacion (que es el stored procedure [sp_PedidosBarnizadoParaProgramar])
				//TODO:hay que ampliar esta consulta a buscar por codigo aplicación y idpedido
				if (p.YaProgramado.HasValue && p.YaProgramado.Value)
				{

					texto.Append(ConstruirMensaje($"ATENCIÓN: Este pedido YA ESTÁ PROGRAMADO."));
					texto.Append(ConstruirMensaje($"Linea: {p.YaProgramadoLinea} - Posicion: {p.YaProgramadoPosicion} - Fecha: {p.YaProgramadoFecha}"));
				}
				if (!string.IsNullOrEmpty(p.Estado) && p.Estado.Contains("PDTE HJLTA"))
				{
					texto.Append(ConstruirMensaje($"<strong>ATENCIÓN: Este pedido NO TIENE HOJALATA ASIGNADA.</strong>"));
				}
				if (!string.IsNullOrEmpty(p.Estado) && p.Estado.Contains("PARADO"))
				{
					texto.Append(ConstruirMensaje($"<strong>ATENCIÓN: Este pedido está PARADO.</strong>"));
				}
				if (p.Idproductoprioritario != null && p.Idproductoprioritario != 0)
				{
					texto.Append(ConstruirMensaje($"Hay un Producto Prioritario: {p.DenominacionPrioritario} - {p.Gramajeprioritario} gr/m2"));
				}
				if (texto.Length <= 0)
					continue;
				texto.AppendLine();
				_listadoNotificaciones.Append($"<strong>Pedido {p.IdPedido}</strong>:").Append("<br>");
				_listadoNotificaciones.Append(texto).AppendLine();
			}
			if (_listadoNotificaciones.Length > 0)
			{
				_notificacionesAceptadas = false;
				_isObsProgPopupVisible = true;
				SpinnerService.Hide();
				await Task.Run(async () =>
				{
					while (_isObsProgPopupVisible && !_notificacionesAceptadas)
					{
						await Task.Delay(250); // Espera un poco antes de verificar nuevamente si el popup está visible
					}
				});
			}
			else
			{
				_isObsProgPopupVisible = false;
				_notificacionesAceptadas = true;
			}

			if (!string.IsNullOrEmpty(_listadoNotificaciones.ToString()))
			{
				ToastService.ShowInfo($"Las notificaciones han sido: {(_notificacionesAceptadas ? "Aceptadas" : "Rechazadas")}");
			}
			if (!_notificacionesAceptadas)
			{
				return;
			}
			// LLAMADA_API: Realiza la última llamada al API para finalizar
			SpinnerService.Show();
			var datosProgramacion = new ProgramarPedidoDTO
			{
				Barniz = CurrentBarniz,
				CodigoAplicacion = CurrentCodApli,
				Maquina = CurrentMaquina,
				Pedidos = pedidosSeleccionados,
				WetOnWet = false
			};
			var jsonString = JsonSerializer.Serialize(datosProgramacion, jsonSerializerOptions);
			HttpContent content = new StringContent(jsonString, Encoding.UTF8, "application/json");

			var response = await Http.PostAsync($"Pedido/programarpedidosbytipo?tipo={Enums.TipoPedido.Barnizado}", content);
			var result = await response.Content.ReadFromJsonAsync<SingleResult<int>>();
			if (result.HasErrors)
			{
				ToastService.ShowError($"{result.Errors.First()}");
			}
			else
			{
				if (result.HasInfo)
					ToastService.ShowInfo($"{string.Join(" -- ", result.Info)}");
				else
				{
					ToastService.ShowSuccess($"Programación actualizada: {result.Data} pedidos nuevos.");
					await UpdatePedidosBarnizado(CurrentMaquina.Idproducto ?? CurrentCodApli.CodBarniz, false);
				}
				_selectedPedidosBarnizDataItems = new List<object>();
				_selectedAImprimirDataItems = new List<object>();
			}
		}
		else
		{
			var dict = _selectedAImprimirDataItems.Cast<TablaProgramacionDTO>().ToDictionary(item => item.Idprogramacion, item => item.Idpedido.Value);
			if (dict.Count > 10)
			{
				var confirmed = await Js.InvokeAsync<bool>("confirm", "Se van a subir mas de 10 registros. ¿Estás seguro?");
				if (!confirmed)
				{
					return;
				}
			}
			var jsonString = JsonSerializer.Serialize(dict);
			HttpContent content = new StringContent(jsonString, Encoding.UTF8, "application/json");
			var response = await Http.PostAsync($"Programacion/borrarprogramaciones/{Enums.TipoPedido.Barnizado}", content);
			var result = await response.Content.ReadFromJsonAsync<SingleResult<int>>();

			if (result.Errors.Any())
			{
				ToastService.ShowError($"{result.Errors.First()}");
			}
			else
			{
				_selectedPedidosBarnizDataItems = new List<object>();
				_selectedAImprimirDataItems = new List<object>();
				await UpdatePedidosBarnizado(CurrentMaquina.Idproducto ?? CurrentCodApli.CodBarniz, false);
				ToastService.ShowSuccess($"Programación eliminada y PROTEO actualizado.");
			}
		}
		SpinnerService.Hide();
	}

	private StringBuilder ConstruirMensaje(string texto)
	{
		var sb = new StringBuilder();
		sb.Append("&nbsp;&nbsp;&nbsp;").Append(texto).Append("<br>");
		return sb;
	}

	private void NotificacionesRevisadas(bool aceptadas)
	{
		_isObsProgPopupVisible = false;
		_notificacionesAceptadas = aceptadas;
		_listadoNotificaciones = new StringBuilder();
	}
	private async Task OnButtonGroupClick(string texto)
	{
		var newIndex = DatosMaquinas.IndexOf(CurrentMaquina) + (texto == "PrevMaquina" ? -1 : 1);
		if (newIndex >= 0 && newIndex < DatosMaquinas.Count)
		{
			var newMaquina = DatosMaquinas[newIndex];
			await OnUpdateMaquina(newMaquina);
		}
	}

	private async Task OnItemClick(ToolbarItemClickEventArgs e)
	{
		var clicked = e.ItemName;
		var newIndex = DatosMaquinas.IndexOf(CurrentMaquina) + (clicked == "PrevMaquina" ? -1 : 1);
		if (newIndex >= 0 && newIndex < DatosMaquinas.Count)
		{
			var newMaquina = DatosMaquinas[newIndex];
			await OnUpdateMaquina(newMaquina);
		}
	}


	public async ValueTask DisposeAsync()
	{
		if (_hubConnection is not null)
		{
			await _hubConnection.DisposeAsync();
		}

		if (DotNetHelper != null)
		{
			//Now dispose our object reference so our component can be garbage collected
			DotNetHelper.Dispose();
		}

		GC.SuppressFinalize(this);
	}

	private async Task UpdateCodApliByDblClick(int? codApli)
	{
		var apli = DatosCodsApliBarniz.FirstOrDefault(c => c.Idcodigoaplicacion == codApli);
		await OnUpdateCodApli(apli);

	}
	private async Task UpdateCodBarnizByDblClick(int? codBarniz)
	{
		var barniz = DatosBarniz.FirstOrDefault(c => c.Idproducto == codBarniz.Value);
		await OnUpdateProducto(barniz);
	}
	private async Task VerDatosPedido(string context)
	{
		await Js.InvokeVoidAsync("open", $"{Navigation.BaseUri}ProgramacionPedidos/{context}", "_blank");
	}
	private async Task GetObsAlmacenByDblClick(TablaProgramacionDTO item)
	{
		PanelVisible = true;
		//SpinnerService.Show();
		var jsonString = JsonSerializer.Serialize(item);
		HttpContent content = new StringContent(jsonString, Encoding.UTF8, "application/json");
		var response = await Http.PostAsync($"Programacion/forzarcargaobsalmacen", content);
		var result = await response.Content.ReadFromJsonAsync<SingleResult<int>>();

		if (result.Errors.Any())
		{
			ToastService.ShowError($"{result.Errors.First()}");
		}
		else
		{
			await ActualizarGridProgramacion();
			ToastService.ShowSuccess($"Obs Almacén actualizada.");
		}
		PanelVisible = false;
		//SpinnerService.Hide();
	}

	private async Task OnUpdateDesde(int value)
	{
		CurrentMaquina.PosicionDesde = value;
		if (!_checkUltimo)
			await ActualizarDatosMaquina(true);
	}

	private async Task OnUpdateHasta(int value)
	{
		CurrentMaquina.PosicionHasta = value;
		if (!_checkUltimo)
			await ActualizarDatosMaquina(true);
	}

	private async Task SetMaximaPosicion()
	{
		CurrentMaquina.PosicionHasta = 9999999;
		await ActualizarDatosMaquina(true);
	}

	async Task ProcesoImpresion(bool combinado, bool publicar)
	{
		SpinnerService.Show();
		var pedidosSeleccionados = DatosProgramacion.ToList();
		if (!pedidosSeleccionados.Any())
		{
			ToastService.ShowWarning("No hay pedidos seleccionados para imprimir");
			return;
		}
		var obj = new ImprimirPedidosDTO
		{
			Maquina = CurrentMaquina,
			Posiciones = pedidosSeleccionados.Select(o => o.Posicion.Value).ToList()
		};
		var jsonString = JsonSerializer.Serialize(obj, jsonSerializerOptions);
		HttpContent content = new StringContent(jsonString, Encoding.UTF8, "application/json");
		string nombreReporte = combinado ? "CombinadoReportV3" : "BarnizadoReportV2";
		var response = combinado
			? await Http.PostAsync($"Pedido/generardatosimpresion/{Enums.TipoPedido.Combinado}?esProgramacionPorPantalla={publicar}&nombreReporte={nombreReporte}", content)
			: await Http.PostAsync($"Pedido/generardatosimpresion/{Enums.TipoPedido.Barnizado}?esProgramacionPorPantalla={publicar}&nombreReporte={nombreReporte}", content);
		var result = await response.Content.ReadFromJsonAsync<SingleResult<int>>();

		if (result.HasErrors)
		{
			ToastService.ShowError($"{result.Errors.First()}");
			SpinnerService.Hide();
		}
		else
		{
			if (publicar)
			{
				try
				{
					// antes de mandar el mensaje de programación actualizada, nos aseguramos de que estamos conectados al hub
					if (_hubConnection?.State != HubConnectionState.Connected)
					{
						ToastService.ShowWarning("Reconectando con el servidor...");
						await _hubConnection.StartAsync();
					}
					await _hubConnection.InvokeAsync("SendProgramacionActualizada", CurrentMaquina.Idmaquina, nombreReporte, User.Identity?.Name);
				}
				catch (Exception ex)
				{
					// Mostrar el mensaje de error del servidor
					ToastService.ShowError($"{(!string.IsNullOrEmpty(ex.InnerException?.Message) ? ex.InnerException?.Message : ex.Message)}");
				}
			}
			else
			{
				ParameterNombreReport = combinado
					//? $"ReporteCombinado?MaquinaNombreParameter={CurrentMaquina.IdmaquinaG21}&VerCambiosParameter={CheckDesgloseHorarios}&WoWParameter={CurrentMaquina.WetonWet}"
					? $"ReporteCombinadoV3?MaquinaNombreParameter={CurrentMaquina.IdmaquinaG21}" +
					  $"&VerCambiosParameter={CheckDesgloseHorarios}" +
					  $"&WoWParameter={CurrentMaquina.WetonWet}" +
					  $"&OcultarCabeceraParameter={true.ToString()}" +
					  $"&IdMaquinaParameter={CurrentMaquina.Idmaquina}" +
					  $"&EsTipoProgramacionPorPantallaParameter={false}"
					: $"ReporteBarnizadoV2?MaquinaNombreParameter={CurrentMaquina.IdmaquinaG21}" +
					  $"&VerCambiosParameter={CheckDesgloseHorarios}" +
					  $"&IdMaquinaParameter={CurrentMaquina.Idmaquina}" +
					  $"&PosicionParameter=0" +
					  $"&EsTipoProgramacionPorPantallaParameter={false}";
				if (_windowVisible)
					await _windowRef.CloseAsync();
				else
					await _windowRef.ShowAtAsync(_popupTarget);
				_selectedAImprimirDataItems = new List<object>();
				CheckDesgloseHorarios = false;
				SpinnerService.Hide();
			}
		}

	}

	private async Task CalcularHorarios()
	{
		PanelVisible = true;
		ListaMensajes = new List<string>();
		var responseCalculoHorarios = await Http.GetFromJsonAsync<SingleResult<int>>($"Programacion/calcularhorarios?tipo={Enums.TipoPedido.Barnizado}&maquina={CurrentMaquina.Idmaquina}");
		if (responseCalculoHorarios.Data == -1)
		{
			//Error por no existencia de datos en tbllimpiezas
			_deIdProducto = int.Parse(responseCalculoHorarios.Info[0]);
			_deProducto = responseCalculoHorarios.Info[1];
			_aIdProducto = int.Parse(responseCalculoHorarios.Info[2]);
			_aProducto = responseCalculoHorarios.Info[3];
			_textoLimpias = $"No se han encontrado datos de Limpiezas para {_deIdProducto}/{_deProducto} => {_aIdProducto}/{_aProducto}";

			if (_textoLimpias.Length > 0)
			{
				_limpiasRegistradas = false;
				_isLimpiasPopupVisible = true;
				if (PopupLimpias.IsInitialized)
					await PopupLimpias.ShowAsync();

				SpinnerService.Hide();
				await Task.Run(async () =>
				{
					while (_isLimpiasPopupVisible && !_limpiasRegistradas)
					{
						await Task.Delay(250); // Espera un poco antes de verificar nuevamente si el popup está visible
					}
				});
				if (_isLimpiasPopupVisible == false)
				{
					await PopupLimpias.CloseAsync();
				}
				//Si hemos registrado nueva ínformación de limpias, volvemos a llamar a calcular horarios, por hacerlo más automático.
				if (_limpiasRegistradas)
				{
					await CalcularHorarios();
				}
			}
			else
			{
				_isLimpiasPopupVisible = false;
				_limpiasRegistradas = true;
			}

		}
		if (responseCalculoHorarios.Data != -1 && responseCalculoHorarios.HasErrors)
		{
			ListaMensajes.AddRange((List<string>?)responseCalculoHorarios.Errors);
			_contenido = "Mensajes";
			PopupBasicoVisible = true;
		}
		else
		{
			if (responseCalculoHorarios.HasInfo)
			{
				ListaMensajes.AddRange((List<string>?)responseCalculoHorarios.Info);
				_contenido = "Mensajes";
				PopupBasicoVisible = true;
			}
			ToastService.ShowSuccess($"Datos de Horarios actualizados.");
			_selectedAImprimirDataItems = new List<object>();
			DatosProgramacion = new ObservableCollection<TablaProgramacionDTO>();
			await ActualizarGridProgramacion();
		}
		_isLimpiasPopupVisible = false;
		_limpiasRegistradas = true;
		StateHasChanged();
		//SpinnerService.Hide();
		PanelVisible = false;
	}

	private async Task ActualizarGridProgramacion()
	{
		ResultProgramacion = await Http.GetFromJsonAsync<ListResult<TablaProgramacionDTO>>($"Pedido/getprogramacionbymaquina?maquina={CurrentMaquina?.Idmaquina}&ultimo={_checkUltimo}");
		DatosProgramacion = new ObservableCollection<TablaProgramacionDTO>(ResultProgramacion.Data);
	}
	private async Task AplicarCambiosMasivos(BulkChangesDTO? bulk)
	{
		_isCambiosMasivosPopUpVisible = false;
		if (bulk is not null)
		{
			bulk.Posiciones = _selectedAImprimirDataItems.Cast<TablaProgramacionDTO>().Select(o => o.Posicion.Value).ToList();
			bulk.CurrentMaquina = CurrentMaquina;
			var jsonString = JsonSerializer.Serialize(bulk, jsonSerializerOptions);
			HttpContent content = new StringContent(jsonString, Encoding.UTF8, "application/json");

			var response = await Http.PostAsync($"Programacion/bulkchanges", content);
			var resultInsertLimpias = await response.Content.ReadFromJsonAsync<SingleResult<int>>();
			if (resultInsertLimpias.HasErrors)
			{
				ToastService.ShowError($"{resultInsertLimpias.Errors.First()}");
			}
			else
			{
				ToastService.ShowSuccess("Cambio masivo correcto.");
				await ActualizarGridProgramacion();
			}
		}
		_selectedAImprimirDataItems = new List<object>();
	}
	private void PopUpBulkVisibleHandler(bool cerrar)
	{
		_isCambiosMasivosPopUpVisible = false;
	}

	private void AsignarTextoLimpias(string value)
	{
		_valorLimpia = value;
	}
	private async Task LimpiasRevisadas(bool revisadas)
	{
		_isLimpiasPopupVisible = false;
		_limpiasRegistradas = revisadas;
		_textoLimpias = "";
		if (revisadas)
		{
			var datosLimpias = new TblLimpiezasDTO
			{
				DeIdProducto = _deIdProducto,
				AidProducto = _aIdProducto,
				DeProductoxparatirar = _deProducto,
				Aproductoxparatirar = _aProducto,
				TipoLimpieza = _valorLimpia
			};
			var jsonString = JsonSerializer.Serialize(datosLimpias, jsonSerializerOptions);
			HttpContent content = new StringContent(jsonString, Encoding.UTF8, "application/json");

			var response = await Http.PostAsync($"Programacion/insertlimpias", content);
			var resultInsertLimpias = await response.Content.ReadFromJsonAsync<SingleResult<int>>();
			if (resultInsertLimpias.HasErrors)
			{
				ToastService.ShowError($"{resultInsertLimpias.Errors.First()}");
			}
			else
			{
				ToastService.ShowSuccess("Datos de limpiezas registrados correctamente.");
			}
		}
	}
	private void MostrarDatosLimpias()
	{
		_contenido = "Limpias";
		PopupBasicoVisible = true;
	}

	private async Task SetUltimaPosicion(MouseEventArgs obj)
	{
		PanelVisible = true;
		//SpinnerService.Show();
		_checkUltimo = true;

		await ActualizarGridProgramacion();

		if (ResultPedidos.Errors.Any())
		{
			foreach (var error in ResultPedidos.Errors)
			{
				ToastService.ShowError($"{error}");
			}
		}
		else
		{
			CurrentMaquina.PosicionHasta = 9999999;
			CurrentMaquina.PosicionDesde = DatosProgramacion.Max(o => o.Posicion.Value);
			var response = await Http.PostAsJsonAsync($"DatosGenerales/updatedatosmaquina", CurrentMaquina);
		}
		_checkUltimo = false;
		//SpinnerService.Hide();
		PanelVisible = false;
	}

	private async Task Renumerar()
	{
		PanelVisible = true;
		//SpinnerService.Show();
		if (DatosProgramacion.Count > 10)
		{
			var confirmed = await Js.InvokeAsync<bool>("confirm", "Se van a procesar mas de 10 registros. ¿Estás seguro?");
			if (!confirmed)
			{
				return;
			}
		}
		var response = await Http.GetFromJsonAsync<SingleResult<int>>($"Programacion/renumerar?maquina={CurrentMaquina.Idmaquina}&desde={CurrentMaquina.PosicionDesde.Value}");


		if (response.Errors.Any())
		{
			foreach (var error in response.Errors)
			{
				ToastService.ShowError($"{error}");
			}
		}
		else
		{
			await ActualizarGridProgramacion();
		}
		//SpinnerService.Hide();
		PanelVisible = false;
	}

	private async Task RecalcularPosiciones()
	{
		PanelVisible = true;
		//SpinnerService.Show();
		if (DatosProgramacion.Count > 10)
		{
			var confirmed = await Js.InvokeAsync<bool>("confirm", "Se van a procesar mas de 10 registros. ¿Estás seguro?");
			if (!confirmed)
			{
				PanelVisible = false;
				return;
			}
		}
		if (DatosProgramacion.Any(o => o.Posicion % 10 != 0))
		{
			var confirmed = await Js.InvokeAsync<bool>("confirm", "ATENCION Hay pedidos con posiciones intermedias para renumerar. ¿Fistro pecador, estás seguro?");
			if (!confirmed)
			{
				PanelVisible = false;
				return;
			}
		}
		var pedidoIndexCodigo = await Js.InvokeAsync<Dictionary<int, string>>("getIndexYCodigo");
		var pedidoIndexCodigoList = pedidoIndexCodigo.Select(kv => new PedidoIndexCodigoDTO { Index = kv.Key, Codigo = kv.Value }).ToList();

		var jsonString = JsonSerializer.Serialize(pedidoIndexCodigoList/*, jsonSerializerOptions*/);

		HttpContent content = new StringContent(jsonString, Encoding.UTF8, "application/json");
		Console.WriteLine(jsonString);
		var response = await Http.PostAsync($"Programacion/recalcular?maquina={CurrentMaquina.Idmaquina}&desde={CurrentMaquina.PosicionDesde}", content);
		var resultEdit = await response.Content.ReadFromJsonAsync<SingleResult<int>>();
		var testCheck = response.Content.ReadAsStringAsync();

		if (resultEdit.Errors.Any())
		{
			foreach (var error in resultEdit.Errors)
			{
				ToastService.ShowError($"{error}");
			}
		}
		else
		{
			DatosProgramacion = null;
			_selectedAImprimirDataItems = new List<object>();
			DatosProgramacion = new ObservableCollection<TablaProgramacionDTO>();
			StateHasChanged();
			await ActualizarGridProgramacion();
		}
		//SpinnerService.Hide();
		PanelVisible = false;
	}
	async Task Grid_EditModelSaving(GridEditModelSavingEventArgs e)
	{
		SpinnerService.Show();
		var editableProg = (TablaProgramacionDTO)e.EditModel;
		var currentProg = (TablaProgramacionDTO)e.DataItem;
		//Hack
		//editableProg.DatosPedProceso = null;

		var jsonString = JsonSerializer.Serialize(editableProg, jsonSerializerOptions);

		HttpContent content = new StringContent(jsonString, Encoding.UTF8, "application/json");
		Console.WriteLine(jsonString);

		var response = await Http.PutAsync($"Programacion/{editableProg.Posicion.Value}/edit", content);
		var resTest = await response.Content.ReadAsStringAsync();

		var resultEdit = await response.Content.ReadFromJsonAsync<SingleResult<int>>();
		var testCheck = response.Content.ReadAsStringAsync();

		if (resultEdit.Errors.Any())
		{
			ToastService.ShowError($"Error al editar la posicion {editableProg.Posicion.Value}: {resultEdit.Errors.First()}");
		}
		else
		{
			var extraTexto = CurrentMaquina.Idmaquina == 17 ? "Verificar temperatura de secado del T4, ¿debe ser la del T3?" : string.Empty;
			ToastService.ShowSuccess($"Posición {editableProg.Posicion.Value} editada correctamente." +
									 $"{extraTexto}");
			_selectedAImprimirDataItems = new List<object>();
			await ActualizarGridProgramacion();
		}
		SpinnerService.Hide();
	}

	void Grid_CustomizeElement(GridCustomizeElementEventArgs e)
	{
		if (e.ElementType == GridElementType.DataRow)
			e.Attributes["data-visible-index"] = e.VisibleIndex;
	}

	private async Task ComprobarProgramacion()
	{
		SpinnerService.Show();
		ListaMensajes = new List<string>();
		var response = await Http.GetFromJsonAsync<ListResult<string>>($"Programacion/comprobarprogramacion?maquina={CurrentMaquina.Idmaquina}");

		if (response.Errors.Any())
		{
			foreach (var error in response.Errors)
			{
				ToastService.ShowError($"{error}");
			}
		}
		else
		{
			ListaMensajes = response.Data;
			_contenido = "Mensajes";
			PopupBasicoVisible = true;
		}
		SpinnerService.Hide();
	}

	private async Task EnviarProteo()
	{
		SpinnerService.Show();
		var response = await Http.PostAsJsonAsync($"Programacion/enviarprogramacionproteobymaquina", CurrentMaquina.Idmaquina);
		//var resTest = await response.Content.ReadAsStringAsync();
		var result = await response.Content.ReadFromJsonAsync<SingleResult<int>>();
		if (result.HasErrors)
		{
			ToastService.ShowError($"{result.Errors.First()}");
		}
		else
		{
			ToastService.ShowSuccess($"Programación enviada a PROTEO");
		}
		SpinnerService.Hide();
	}
	private async Task StartEditing(int index)
	{
		_visibleIndex = index;
		await GridProg.StartEditRowAsync(index);
	}
	private async Task EditarRegistro(string posicion, TablaProgramacionDTO datosGrid)
	{
		await GridProg.SaveChangesAsync();

		if (posicion == "anterior")
			_visibleIndex--;
		else
			_visibleIndex++;

		if (_visibleIndex < 0)
			ToastService.ShowWarning($"Estás en el primer registro, no se puede retroceder más");
		else if (_visibleIndex >= DatosProgramacion.Count)
			ToastService.ShowWarning($"Estás en el último registro, no se puede avanzar más");
		else
		{
			await GridProg.StartEditRowAsync(_visibleIndex);
			_selectedAImprimirDataItems = new List<object>();
		}
	}
	async Task GridProg_ExportXlsx()
	{
		await GridProg.ExportToXlsxAsync($"{DateTime.Now:yyyyMMdd}_Programacion_Barnizado", new GridXlExportOptions()
		{
			//ExportSelectedRowsOnly = ExportSelectedRowsOnly,
			//CustomizeCell = OnCustomizeCell
		});
	}
	async Task GridBarnizado_ExportXlsx()
	{
		await GridBarnizado.ExportToXlsxAsync($"{DateTime.Now:yyyyMMdd}_Barnizado", new GridXlExportOptions()
		{
			//ExportSelectedRowsOnly = ExportSelectedRowsOnly,
			//CustomizeCell = OnCustomizeCell
		});
	}

	async Task OnFlejarChanged(MouseEventArgs e, TablaProgramacionDTO prog)
	{
		//El valor de Flejar es el que actualmente tiene en pantalla, no el nuevo tras el click que acabas de hacer,
		//por lo que se comprueba de esta forma, si estás en false es que has pulsado para marcar que si tiene flejado y
		//si está a true has marcado para quitar el flejado
		SpinnerService.Show();
		if (!prog.Flejar.Value)
		{
			var datosFlejado = await Http.GetFromJsonAsync<SingleResult<string>>($"Pedido/{prog.Idpedido.Value}/getdatosflejadodirecto");
			if (!datosFlejado.HasErrors)
			{
				prog.Obspaseposterior = datosFlejado.Data;
				prog.Posicionaplicacionposterior = string.Empty;
				prog.Idaplicacionposterior = -1;
			}
			else
			{
				ToastService.ShowError($"{datosFlejado.Errors.First()}");
			}
		}
		else
		{
			prog.Obspaseposterior = string.Empty;
			prog.Idaplicacionposterior = null;
		}
		SpinnerService.Hide();
	}
	private void FechaAutomatica(TablaProgramacionDTO prog)
	{
		prog.DiaReal = DateTime.Today;
	}
	private void HoraAutomatica(TablaProgramacionDTO prog)
	{
		if (prog.HoraReal != null && prog.HoraReal.Value == new TimeSpan(7, 0, 0))
		{
			prog.HoraReal = new TimeSpan(15, 0, 0);
		}
		else if (prog.HoraReal != null && prog.HoraReal.Value == new TimeSpan(15, 0, 0))
		{
			prog.HoraReal = new TimeSpan(23, 0, 0);
		}
		else
		{
			prog.HoraReal = new TimeSpan(7, 0, 0);
		}
	}

	private string AjustarTextoProductoSinPeso(string texto)
	{
		string result = string.Empty;
		int position = texto.IndexOf("Peso");
		result = position != -1 ? texto.Substring(0, position).Trim() : texto;
		return result;
	}

	private void GridDatosPedidos_FilterCriteriaChanged(GridFilterCriteriaChangedEventArgs arg)
	{
		DatosPedidosFiltrados = new();
		var numRows = GridBarnizado.GetVisibleRowCount();
		for (int i = 0; i < numRows; i++)
		{
			var item = GridBarnizado.GetDataItem(i);
			var OB = item as DatosGeneralesPedidoDTO;
			DatosPedidosFiltrados.Add(OB);
		}
		ActualizarTextosAzul(true);
	}
	async Task Grid_ItemsDropped(GridItemsDroppedEventArgs evt)
	{
		foreach (var item in evt.DroppedItems)
			DatosProgramacion.Remove((TablaProgramacionDTO)item);
		var index = await evt.GetTargetDataSourceIndexAsync();
		foreach (var item in evt.DroppedItems.Reverse())
			DatosProgramacion.Insert(index, (TablaProgramacionDTO)item);
	}
}
