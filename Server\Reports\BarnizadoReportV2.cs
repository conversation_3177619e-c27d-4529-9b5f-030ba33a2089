﻿using System;
using System.Collections;
using System.ComponentModel;
using System.Drawing;
using DevExpress.XtraReports.UI;

namespace ProgramadorGeneralBLZ.Server.Reports
{
    public partial class BarnizadoReportV2 : DevExpress.XtraReports.UI.XtraReport
    {
        public BarnizadoReportV2()
        {
            InitializeComponent();
            XRRichText richText = this.FindControl("xrRichText1", true) as XRRichText;
            if (richText != null)
            {
                // Asigna el manejador del evento BeforePrint
                richText.BeforePrint += RichText_BeforePrint;
            }
        }
        private void RichText_BeforePrint(object sender, CancelEventArgs e)
        {
            var richText = (XRRichText)sender;
            var fieldValue = GetCurrentColumnValue("Observaciones")?.ToString() ?? string.Empty;
            var fieldValue2 = GetCurrentColumnValue("Obspaseposterior")?.ToString() ?? string.Empty;
            var fullValue = $"{fieldValue} {fieldValue2}";
            // Reemplaza los saltos de línea con la secuencia RTF correspondiente
            fullValue = fullValue/*.Replace(Environment.NewLine, @"\par ")*/.Replace("\n", @"\par ");

            // Define el formato RTF básico con fuente Arial y tamaño 9
            const string rtfHeader = @"{\rtf1\ansi\deff0{\fonttbl{\f0 Arial;}}\uc1\pard\fs16"; // \fs16 corresponde a tamaño 8 en puntos
            const string rtfColorTable = @"{\colortbl ;\red255\green0\blue0;}";

            var rtfFormattedText =
                // Condicionalmente reemplaza "REVISAR REGISTROS DE VISIÓN ARTIFICIAL EN PREPRINT" con versión en RTF con color rojo
                fullValue?.Contains("REVISAR REGISTROS DE VISIÓN ARTIFICIAL EN PREPRINT") == true
                    ? fullValue.Replace("REVISAR REGISTROS DE VISIÓN ARTIFICIAL EN PREPRINT", @"{\cf1. REVISAR REGISTROS DE VISIÓN ARTIFICIAL EN PREPRINT}")
                    : fullValue;

            // Envolver el texto en un formato RTF completo
            var rtfText = rtfHeader + rtfColorTable + rtfFormattedText + @"}";

            // Asigna el texto formateado al richText
            richText.Rtf = rtfText;
        }
    }
}
