﻿using System.ComponentModel.DataAnnotations.Schema;

namespace ProgramadorGeneralBLZ.Shared.DTO
{
    public class ConsultaHojalataDTO
    {
        public string Refmtc { get; set; }
        public string Empmtc { get; set; }
        public int? Climtc { get; set; }
        public int? Tmamtc { get; set; }
        public double? Larmtc { get; set; }
        public double? Ancmtc { get; set; }
        public double? Espmtc { get; set; }
        public int? Tprmtc { get; set; }
        public string Promtc { get; set; }
        public int? Stimtc { get; set; }
        public double? SumaDestomtc { get; set; }
        public double? Expr1 { get; set; }
        public double? Disponible { get; set; }
    }
}