﻿::deep .dxbl-row {
    --dxbl-row-item-spacing-y: 0.25rem !important;
}

/*::deep .dxbl-pager-container {
    --dxbl-pager-container-padding-y: 0.1rem !important;
}*/

::deep .gridlayout-cs {
    padding-top: 5px;
    margin-top: 5px;
    /*background-color: var(--bs-orange);*/
}

::deep .gridlayout-gs {
    padding-top: 5px;
    margin-top: 5px;
    /*background-color: var(--bs-red);*/
}

::deep .gridlayout-cm {
/*    padding-top: 5px;
    margin-top: 5px;*/
    /*background-color: var(--bs-yellow);*/
}

::deep .gridlayout-gi {
    padding-top: 5px;
    margin-top: 5px;
    /*background-color: var(--bs-green);*/
}

::deep .gridlayout-ci {
/*    padding-top: 5px;
    margin-top: 5px;*/
    /*background-color: var(--bs-purple);*/
}

::deep .alTecho {
    margin-top: -30px;
}

::deep .btnLitoSizeM {
    width: 120px !important;
    height: 30px !important;
}
::deep .btnLitoSizeL {
    width: 150px !important;
    height: 30px !important;
}
::deep .btnLitoSizeXL {
    width: 172px !important;
    height: 30px !important;
}

::deep .btnWide {
    width: 90px !important;
}

::deep .btnSizeS {
    width: 70px !important;
    height: 25px !important;
}

::deep .ch-360 {
    height: 360px !important;
    max-height: 360px !important;
}

::deep .textoAzul {
    color: deepskyblue !important;
}

::deep .textoBlanco {
    color: white;
}

::deep .ch-240 {
    height: 240px !important;
}
::deep .ch-185 {
    height: 185px !important;
}

::deep .ch-315 {
    height: 305px !important;
}
::deep .ch-330 {
    height: 330px !important;
}


::deep .dxbl-scroll-viewer > .dxbl-scroll-viewer-hor-scroll-bar > .dxbl-scroll-viewer-scroll-thumb {
    background-color: #ba5bed;
    opacity: 1;
    border-radius: 0.1rem;
}

::deep .dxbl-scroll-viewer > .dxbl-scroll-viewer-vert-scroll-bar > .dxbl-scroll-viewer-scroll-thumb {
    background-color: #ba5bed;
    opacity: 1;
    border-radius: 0.1rem;
}

::deep .btnDoble {
    --dxbl-btn-padding-x: 0.65rem !important;
}

    ::deep .btnDoble.btnPrimeroDeDos {
        border-right: 1px whitesmoke solid !important;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        margin-left: -37px;
        width: 61px;
    }

    ::deep .btnDoble.btnSegundoDeDos {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        margin-left: -25px;
        width: 56px;
    }

::deep .smallFont {
    --dxbl-grid-font-size: 0.7rem !important;
    --dxbl-grid-text-cell-padding-x: 0rem;
    --dxbl-grid-text-cell-padding-y: 0rem;
    --dxbl-grid-editor-cell-padding-x: 0.1rem;
    --dxbl-grid-editor-cell-padding-y: 0.1rem;
}

@media (max-width: 575.98px) {
    ::deep .gridlayout-item {
        font-size: 0.9em;
    }
}
/*Para reducir aun mas el espacio dentro de las celdas del grid combinado con el padding de smallFont*/
.dxbl-grid tbody tr span {
    padding: 0 !important;
}

::deep td {
    padding: 0px !important;
    margin: 0px !important;
}
::deep .smallSize {
    font-size: 11px;
    padding: 0px !important;
}
::deep .dragging {
    background-color: whitesmoke;
    color: black;
}

::deep .multiple-sortable-selected {
    background-color: whitesmoke;
    color: black;
}

::deep .ajusteToolbar {
    display: inline-flex !important;
    height: 30px;
    margin-top: -13px;
}

::deep .ajusteExtra {
    display: inline-flex !important;
}

::deep .dxbl-btn-group-first {
    padding-top: 15px;
}
