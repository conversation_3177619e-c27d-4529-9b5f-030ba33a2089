﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace ProgramadorGeneralBLZ.Server.Models.DatoLita01
{
    public partial class MotivosDetallados
    {
        public string Codigo { get; set; }
        public string Empresa { get; set; }
        public int? Cliente { get; set; }
        public string Marca { get; set; }
        public string Envase { get; set; }
        public string Elemento { get; set; }
        public double? Diametro { get; set; }
        public string Numenvase { get; set; }
        public string MotivoStr { get; set; }
        public string Formato { get; set; }
        public double? Altura { get; set; }
        public string Embuticion { get; set; }
        public string Fotolitos { get; set; }
        public string Notafotomec { get; set; }
        public string Fechamodif { get; set; }
        public string Ultfecped { get; set; }
        public int? PRoint1 { get; set; }
        public string Proint2 { get; set; }
        public string Proint3 { get; set; }
        public int? Proext1 { get; set; }
        public int? Proext2 { get; set; }
        public string Proext3 { get; set; }
        public int? Color01 { get; set; }
        public int? Color02 { get; set; }
        public int? Color03 { get; set; }
        public int? Color04 { get; set; }
        public int? Color05 { get; set; }
        public int? Color06 { get; set; }
        public string Color07 { get; set; }
        public string Color08 { get; set; }
        public string Color09 { get; set; }
        public string Color10 { get; set; }
        public string Color11 { get; set; }
        public string Coldor1 { get; set; }
        public string Coldor2 { get; set; }
        public string Coldor3 { get; set; }
        public string Coldor4 { get; set; }
        public string Observac { get; set; }
        public int? Codnuevo { get; set; }
    }
}