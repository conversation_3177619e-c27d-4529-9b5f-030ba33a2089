﻿using MediatR;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Services.DatabaseManipulation;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Programaciones.Command;

public class ForzarCargaObsAlmacenCommand: IRequest<SingleResult<int>>
{
    public ForzarCargaObsAlmacenCommand(TablaProgramacionDTO tprog)
    {
        TablaProg = tprog;
    }

    public TablaProgramacionDTO TablaProg { get; set; }
}

public class ForzarCargaObsAlmacenCommandHandler : IRequestHandler<ForzarCargaObsAlmacenCommand, SingleResult<int>>
{
    private readonly ProgramadorLitalsaContext _contextProg;
    private readonly IDataManipulationService _dataManipulationService;
    public ForzarCargaObsAlmacenCommandHandler(ProgramadorLitalsaContext contextProg, IDataManipulationService dataManipulationService)
    {
        _contextProg = contextProg;
        _dataManipulationService = dataManipulationService;
    }

    public async Task<SingleResult<int>> Handle(ForzarCargaObsAlmacenCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<int> { Errors = new List<string>(), Data = 0 };
        try
        {
            var progOrigen = request.TablaProg;
            var prog =
                _contextProg.TablaProgramacion.FirstOrDefault(o =>
                    o.Idpedido == progOrigen.Idpedido &&
                    o.Idprogramacion == progOrigen.Idprogramacion &&
                    o.Idlinea == progOrigen.Idlinea);

            //YAPROGRAMADO - MIRAR EL ACCESS Y VER COMO LO PASA, CREO QUE DEBERIA SER FALSE
            prog.ObsAlmacen = string.IsNullOrEmpty(prog.ObsAlmacen)
            ? _dataManipulationService.GetObservacionesAlmacen(prog.Idpedido.Value, true, true, false)
            : string.Empty;

            var dbResult = await _contextProg.SaveChangesAsync(cancellationToken);
            result.Data = dbResult;
        }
        catch (Exception e)
        {
            var errorText =
                $"ERROR: ForzarCargaObsAlmacenCommand - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
            result.Errors.Add(errorText);
        }
        return result;
    }
}