﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa
{
    public partial class TablaProductos
    {
        public TablaProductos()
        {
            TablaProgramacion = new HashSet<TablaProgramacion>();
        }

        public int Idproducto { get; set; }
        public string TipoProducto { get; set; }
        public float? Existencias { get; set; }
        public int? StockMinimo { get; set; }
        public string NombreProducto { get; set; }
        public string Observaciones { get; set; }
        public string Denominacion { get; set; }
        public bool Activo { get; set; }
        public float? SolidosViejo { get; set; }
        public decimal? Precio { get; set; }
        public string Tóxico { get; set; }
        public string Inflamable { get; set; }
        public bool Corrosivo { get; set; }
        public int? PuntoEbullición { get; set; }
        public short? PuntoInflamacion { get; set; }
        public int? Idficha { get; set; }
        public float? CantidadPedida { get; set; }
        public DateTime? FechaEntrega { get; set; }
        public float? CantidadRechazada { get; set; }
        public int? Stockminimo1 { get; set; }
        public string Tratamientos { get; set; }
        public float? Solidos { get; set; }
        public bool Obsoleto { get; set; }
        public DateTime? UltAvisoInsuficiente { get; set; }
        public string CalleApq { get; set; }
        public double? TemperaturaSecado1 { get; set; }
        public double? TemperaturaSecado2 { get; set; }
        public double? TemperaturaSecado3 { get; set; }
        public double? TemperaturaSecado4 { get; set; }
        public double? Velocidad1 { get; set; }
        public double? Velocidad2 { get; set; }
        public double? Velocidad3 { get; set; }
        public double? Velocidad4 { get; set; }
        public bool? MarcadoVerde { get; set; }

        public virtual ICollection<TablaProgramacion> TablaProgramacion { get; set; }
    }
}