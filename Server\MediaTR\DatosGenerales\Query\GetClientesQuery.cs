﻿using MediatR;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;

namespace ProgramadorGeneralBLZ.Server.MediaTR.DatosGenerales.Query
{
    public class GetClientesQuery : IRequest<ListResult<ClienteDropdownDTO>>
    {
        public GetClientesQuery(string idPedidoCliente)
        {
            IdPedidoCliente = idPedidoCliente;
        }

        public string IdPedidoCliente { get; set; }
    }
    public class GetClientesQueryHandler : IRequestHandler<GetClientesQuery, ListResult<ClienteDropdownDTO>>
    {
        private readonly ProgramadorLitalsaContext _contextProg;
        public GetClientesQueryHandler(ProgramadorLitalsaContext contextProg)
        {
            _contextProg = contextProg;
        }
        public async Task<ListResult<ClienteDropdownDTO>> Handle(GetClientesQuery request, CancellationToken cancellationToken)
        {
            try
            {

                var result = new ListResult<ClienteDropdownDTO>()
                {
                    Data = new List<ClienteDropdownDTO>(),
                    Errors = new List<string>()
                };

                var query = _contextProg.Clientes.AsQueryable();
                if (!string.IsNullOrEmpty(request.IdPedidoCliente))
                {
                    var clientesConNumeroPedido = _contextProg.PedidoProcesado
                        .Where(o => o.Supedido.Contains(request.IdPedidoCliente)).Select(o => o.IdCliente);
                    query = query.Where(o => clientesConNumeroPedido.Contains(o.CodigoCliente));
                }

                if (!query.Any())
                {
                    result.Errors.Add("No se han encontrado los datos de Clientes");
                }
                else
                {
                    result.Data = query.Select(s => new ClienteDropdownDTO()
                    {
                        CodigoCliente = s.CodigoCliente,
                        NombreCliente = s.NombreCliente
                    }).ToList();
                }

                return result;
            }
            catch (Exception e)
            {
                var errorText = $"ERROR: GetClientesQuery - {(!string.IsNullOrEmpty(e.InnerException?.Message) ? e.InnerException?.Message : e.Message)}";
                throw new Exception(errorText, e);
            }
        }
    }
}
