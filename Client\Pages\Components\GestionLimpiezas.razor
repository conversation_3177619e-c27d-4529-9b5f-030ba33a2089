﻿@using ProgramadorGeneralBLZ.Shared
@using ProgramadorGeneralBLZ.Shared.DTO
@using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa
@using ProgramadorGeneralBLZ.Shared.ResponseModels
@inject HttpClient Http
@inject SpinnerService SpinnerService
@inject IToastService ToastService

<div class="h-100 overflow-auto px-2 py-1">
    <DxGrid Data="@limpiezas" SizeMode="SizeMode.Medium"
            CssClass="ch-320 smallFont progGrid" PageSize="18"
            KeyFieldName="Idlavada" Context="GridLimpiezas"
            AllowSort="true" EditNewRowPosition="GridEditNewRowPosition.Top"
            ShowFilterRow="true" ValidationEnabled="false"
            EditMode="GridEditMode.EditRow" @ref="Grid" PagerPosition="GridPagerPosition.TopAndBottom"
            EditorRenderMode="GridEditorRenderMode.Integrated"
            EditModelSaving="GridLimpiezas_EditModelSaving"
            DataItemDeleting="Grid_DataItemDeleting">
        <Columns>
            <DxGridCommandColumn Width="80px">
                <HeaderTemplate>
                    <a class="oi oi-plus" @onclick="@(() => Grid.StartEditNewRowAsync())" style="text-decoration: none;color: lightskyblue;" href="javascript:void(0);"></a>
                </HeaderTemplate>
                <CellDisplayTemplate>
                    <a class="oi oi-pencil" @onclick="@(() => Grid.StartEditRowAsync(context.VisibleIndex))" style="text-decoration: none; padding-right: 15px; color: #c75fff;" href="javascript:void(0);"></a>
                    <a class="oi oi-x" @onclick="@(() => Grid.ShowRowDeleteConfirmation(context.VisibleIndex))" style="text-decoration: none; color: red;" href="javascript:void(0);"></a>
                </CellDisplayTemplate>
                <CellEditTemplate>
                    <a class="oi oi-arrow-thick-bottom" @onclick="@(() => Grid.SaveChangesAsync())" style="text-decoration: none; padding-right: 15px; color: greenyellow; margin-right: 6px; margin-top: 3px;" href="javascript:void(0);"></a>
                    <a class="oi oi-x" @onclick="@(() => Grid.CancelEditAsync())" style="text-decoration: none; color: red;" href="javascript:void(0);"></a>
                </CellEditTemplate>
            </DxGridCommandColumn>
            <DxGridDataColumn FieldName="Idlavada" Visible="false" />
            <DxGridDataColumn FieldName="@nameof(TblLimpiezasDTO.DeProductoxparatirar)" Caption="De Producto" Width="230"
                              FilterRowOperatorType="GridFilterRowOperatorType.StartsWith" />
            <DxGridDataColumn FieldName="@nameof(TblLimpiezasDTO.Aproductoxparatirar)" Caption="A Producto" Width="230"
                              FilterRowOperatorType="GridFilterRowOperatorType.StartsWith" />
            <DxGridDataColumn FieldName="@nameof(TblLimpiezasDTO.TipoLimpieza)" Caption="A Producto" Width="180">
                <CellDisplayTemplate Context="cellText">
                    @{
                        string summary = cellText.Value.ToString();
                    }
                    <DxComboBox Data="@tipoLimpiezas" ReadOnly="true"
                                FilteringMode="DataGridFilteringMode.Contains"
                                TextFieldName="TipoLimpieza"
                                ValueFieldName="TipoLimpieza"
                                ListRenderMode="ListRenderMode.Virtual"
                                @bind-Value="summary">
                    </DxComboBox>
                </CellDisplayTemplate>
            </DxGridDataColumn>
            <DxGridDataColumn FieldName="@nameof(TblLimpiezasDTO.DeIdProducto)" DisplayFormat="F0" Caption="De ID Producto" 
                              TextAlignment="GridTextAlignment.Center" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="180" />
            <DxGridDataColumn FieldName="@nameof(TblLimpiezasDTO.AidProducto)" DisplayFormat="F0" Caption="A ID Producto" 
                              TextAlignment="GridTextAlignment.Center" FilterRowOperatorType="GridFilterRowOperatorType.Contains" Width="180" />
        </Columns>
        <DataColumnCellEditTemplate>
            @{
                var limpieza = (TblLimpiezasDTO)GridLimpiezas.EditModel;
            }
            @switch (GridLimpiezas.DataColumn.FieldName)
            {
                case "DeProductoxparatirar":
                    <DxTextBox @bind-Text="@limpieza.DeProductoxparatirar"
                               ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                               ShowValidationIcon="true" />
                    break;
                case "Aproductoxparatirar":
                    <DxTextBox @bind-Text="@limpieza.Aproductoxparatirar"
                               ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                               ShowValidationIcon="true" />
                    break;
                case "TipoLimpieza":
                    <DxComboBox Data="@tipoLimpiezas" ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                                FilteringMode="DataGridFilteringMode.Contains"
                                TextFieldName="TipoLimpieza"
                                ValueFieldName="TipoLimpieza"
                                ListRenderMode="ListRenderMode.Virtual"
                                @bind-Value="limpieza.TipoLimpieza">
                    </DxComboBox>
                    break;
                case "DeIdProducto":
                    <DxSpinEdit @bind-Value="@limpieza.DeIdProducto" ShowSpinButtons="false"
                               ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                               ShowValidationIcon="true" />
                    break;
                case "AidProducto":
                    <DxSpinEdit @bind-Value="@limpieza.AidProducto" ShowSpinButtons="false"
                               ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
                               ShowValidationIcon="true" />
                    break;
            }
        </DataColumnCellEditTemplate>
    </DxGrid>
</div>
@code
{
    DxGrid? Grid;
    List<TblLimpiezasDTO> limpiezas = new List<TblLimpiezasDTO>();
    List<TblTipoLimpiezaDTO> tipoLimpiezas = new List<TblTipoLimpiezaDTO>();

    protected override async Task OnInitializedAsync()
    {
        SpinnerService.Show();
        await LoadData();
        SpinnerService.Hide();
    }

    async Task LoadData()
    {
        var result = await Http.GetFromJsonAsync<ListResult<TblTipoLimpiezaDTO>>($"DatosGenerales/tipoLimpias");
        tipoLimpiezas = result.Data;
        limpiezas = await Http.GetFromJsonAsync<List<TblLimpiezasDTO>>("GestionTablasV2/TblLimpiezas");
    }

    async Task GridLimpiezas_EditModelSaving(GridEditModelSavingEventArgs e)
    {
        SpinnerService.Show();
        var dest = (TblLimpiezasDTO)e.EditModel;
        var response = e.IsNew == false
            ? await Http.PutAsJsonAsync($"GestionTablasV2/TblLimpiezas/{dest.Idlavada}", dest)
            : await Http.PostAsJsonAsync("GestionTablasV2/TblLimpiezas", dest);
        if (response.IsSuccessStatusCode)
            await LoadData();
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            ToastService.ShowError(error);
        }
        SpinnerService.Hide();
    }
    async Task Grid_DataItemDeleting(GridDataItemDeletingEventArgs e)
    {
        SpinnerService.Show();
        var item = (TblLimpiezasDTO)e.DataItem;
        var response = await Http.DeleteAsync($"GestionTablasV2/TblLimpiezas/{item.Idlavada}");
        if (response.IsSuccessStatusCode)
            await LoadData();
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            ToastService.ShowError(error);
        }
        SpinnerService.Hide();
    }
    async Task Delete(TblLimpiezasDTO item)
    {
        SpinnerService.Show();
        var response = await Http.DeleteAsync($"GestionTablasV2/TblLimpiezas/{item.Idlavada}");
        if (response.IsSuccessStatusCode)
            await LoadData();
        else
        {
            var error = await response.Content.ReadAsStringAsync();
            ToastService.ShowError(error);
        }
        SpinnerService.Hide();
    }
}
